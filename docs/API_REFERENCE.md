# API Reference & Database Operations

## 🔌 Database Operations API

This document provides a comprehensive reference for all database operations available in the Syrian Technical Solutions Platform.

## 📚 Table of Contents

- [User Operations](#user-operations)
- [Expert Operations](#expert-operations)
- [Problem Operations](#problem-operations)
- [Solution Operations](#solution-operations)
- [Webinar Operations](#webinar-operations)
- [Real-time Subscriptions](#real-time-subscriptions)
- [Storage Operations](#storage-operations)
- [Authentication Operations](#authentication-operations)

## 👤 User Operations

### `userOperations.getProfile(userId: string)`

Retrieves a user's profile information.

```typescript
const { data, error } = await userOperations.getProfile('user-uuid')

// Response
interface UserProfile {
  id: string
  email: string
  name: string
  role: 'expert' | 'ministry_user' | 'admin'
  avatar?: string
  bio?: string
  location: string
  phone_number?: string
  organization?: string
  position?: string
  languages: string[]
  is_active: boolean
  created_at: string
  updated_at: string
  last_login_at?: string
}
```

### `userOperations.updateProfile(userId: string, updates: Partial<UserRow>)`

Updates a user's profile information.

```typescript
const updates = {
  name: 'Updated Name',
  bio: 'Updated bio',
  location: 'Damascus, Syria'
}

const { data, error } = await userOperations.updateProfile('user-uuid', updates)
```

### `userOperations.getAllUsers(filters?)`

Retrieves all users with optional filtering.

```typescript
const filters = {
  role: 'expert',
  location: 'Damascus',
  includeDeleted: false
}

const { data, error } = await userOperations.getAllUsers(filters)
```

### `userOperations.softDeleteUser(userId: string)`

Soft deletes a user (marks as deleted without removing from database).

```typescript
const { data, error } = await userOperations.softDeleteUser('user-uuid')
```

### `userOperations.restoreUser(userId: string)`

Restores a soft-deleted user (admin only).

```typescript
const { data, error } = await userOperations.restoreUser('user-uuid')
```

## 👨‍💻 Expert Operations

### `expertOperations.getExpertProfile(userId: string)`

Retrieves an expert's detailed profile with user information.

```typescript
const { data, error } = await expertOperations.getExpertProfile('user-uuid')

// Response includes user data and expert-specific data
interface ExpertProfile {
  id: string
  user_id: string
  expertise_areas: Array<{
    category: string
    skills: string[]
    proficiencyLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert'
    yearsOfExperience: number
  }>
  experience_years: number
  availability: 'available' | 'busy' | 'unavailable'
  rating: number
  total_contributions: number
  success_rate: number
  response_time_hours: number
  portfolio: Array<{
    title: string
    description: string
    technologies: string[]
    url?: string
    completedAt: string
  }>
  certifications: Array<{
    name: string
    issuer: string
    issuedAt: string
    expiresAt?: string
    credentialId?: string
    url?: string
  }>
  users: UserProfile
}
```

### `expertOperations.createExpertProfile(expertData)`

Creates a new expert profile.

```typescript
const expertData = {
  user_id: 'user-uuid',
  expertise_areas: [
    {
      category: 'تطوير البرمجيات',
      skills: ['React', 'Node.js', 'TypeScript'],
      proficiencyLevel: 'expert',
      yearsOfExperience: 5
    }
  ],
  experience_years: 8,
  availability: 'available',
  response_time_hours: 24,
  portfolio: [
    {
      title: 'Hospital Management System',
      description: 'Complete hospital management solution',
      technologies: ['React', 'Node.js', 'PostgreSQL'],
      url: 'https://github.com/example/hospital-system',
      completedAt: '2024-01-15'
    }
  ],
  certifications: [
    {
      name: 'AWS Certified Solutions Architect',
      issuer: 'Amazon Web Services',
      issuedAt: '2023-06-01',
      expiresAt: '2026-06-01',
      credentialId: 'AWS-CSA-123456',
      url: 'https://aws.amazon.com/verification'
    }
  ]
}

const { data, error } = await expertOperations.createExpertProfile(expertData)
```

### `expertOperations.updateExpertProfile(userId: string, updates)`

Updates an expert's profile.

```typescript
const updates = {
  availability: 'busy',
  response_time_hours: 48,
  expertise_areas: [
    // Updated expertise areas
  ]
}

const { data, error } = await expertOperations.updateExpertProfile('user-uuid', updates)
```

### `expertOperations.getAllExperts(filters?)`

Retrieves all experts with optional filtering.

```typescript
const filters = {
  availability: 'available',
  expertise: 'تطوير البرمجيات',
  location: 'Damascus',
  minRating: 4.0,
  includeDeleted: false
}

const { data, error } = await expertOperations.getAllExperts(filters)
```

## 🔧 Problem Operations

### `problemOperations.createProblem(problemData)`

Creates a new technical problem.

```typescript
const problemData = {
  title: 'Hospital Management System Integration Issue',
  description: 'We need help integrating our new hospital management system with the existing patient database...',
  category: 'تطوير البرمجيات',
  sector: 'وزارة الصحة',
  urgency: 'high',
  submitted_by: 'user-uuid',
  tags: ['database', 'integration', 'healthcare'],
  attachments: [
    {
      id: 'file-uuid',
      name: 'system-diagram.pdf',
      size: 1024000,
      type: 'application/pdf',
      url: 'https://storage.supabase.co/...'
    }
  ]
}

const { data, error } = await problemOperations.createProblem(problemData)
```

### `problemOperations.getProblem(problemId: string)`

Retrieves a problem with its solutions and submitter information.

```typescript
const { data, error } = await problemOperations.getProblem('problem-uuid')

// Response includes problem data, user info, and solutions
interface ProblemWithSolutions {
  id: string
  title: string
  description: string
  category: string
  sector: string
  urgency: 'low' | 'medium' | 'high' | 'critical'
  status: 'open' | 'in_progress' | 'resolved' | 'closed'
  submitted_by: string
  assigned_experts: string[]
  tags: string[]
  attachments: any[]
  created_at: string
  updated_at: string
  resolved_at?: string
  users: {
    name: string
    organization?: string
  }
  solutions: Solution[]
}
```

### `problemOperations.getAllProblems(filters?)`

Retrieves all problems with optional filtering.

```typescript
const filters = {
  status: 'open',
  urgency: 'high',
  category: 'تطوير البرمجيات',
  sector: 'وزارة الصحة',
  submittedBy: 'user-uuid',
  includeDeleted: false
}

const { data, error } = await problemOperations.getAllProblems(filters)
```

### `problemOperations.updateProblem(problemId: string, updates)`

Updates a problem's information.

```typescript
const updates = {
  status: 'in_progress',
  assigned_experts: ['expert-uuid-1', 'expert-uuid-2'],
  tags: ['updated', 'tags']
}

const { data, error } = await problemOperations.updateProblem('problem-uuid', updates)
```

### `problemOperations.searchProblems(searchTerm: string, language?, includeDeleted?)`

Performs full-text search on problems.

```typescript
const { data, error } = await problemOperations.searchProblems(
  'hospital management',
  'ar', // or 'en'
  false
)
```

### `problemOperations.softDeleteProblem(problemId: string)`

Soft deletes a problem and its solutions.

```typescript
const { data, error } = await problemOperations.softDeleteProblem('problem-uuid')
```

## 💡 Solution Operations

### `solutionOperations.createSolution(solutionData)`

Creates a new solution for a problem.

```typescript
const solutionData = {
  problem_id: 'problem-uuid',
  expert_id: 'expert-uuid',
  content: 'Here is my detailed solution to the hospital management integration issue...',
  attachments: [
    {
      id: 'file-uuid',
      name: 'solution-code.zip',
      size: 2048000,
      type: 'application/zip',
      url: 'https://storage.supabase.co/...'
    }
  ],
  status: 'submitted'
}

const { data, error } = await solutionOperations.createSolution(solutionData)
```

### `solutionOperations.getSolutionsForProblem(problemId: string, includeDeleted?)`

Retrieves all solutions for a specific problem.

```typescript
const { data, error } = await solutionOperations.getSolutionsForProblem(
  'problem-uuid',
  false
)
```

### `solutionOperations.getSolutionsForExpert(expertId: string, includeDeleted?)`

Retrieves all solutions submitted by a specific expert.

```typescript
const { data, error } = await solutionOperations.getSolutionsForExpert(
  'expert-uuid',
  false
)
```

### `solutionOperations.updateSolution(solutionId: string, updates)`

Updates a solution's information.

```typescript
const updates = {
  content: 'Updated solution content...',
  status: 'approved',
  implementation_notes: 'Successfully implemented in production'
}

const { data, error } = await solutionOperations.updateSolution('solution-uuid', updates)
```

### `solutionOperations.voteSolution(solutionId: string, userId: string, voteType: 'up' | 'down')`

Votes on a solution (up or down vote).

```typescript
const { data, error } = await solutionOperations.voteSolution(
  'solution-uuid',
  'user-uuid',
  'up'
)
```

### `solutionOperations.rateSolution(solutionId: string, ratingData)`

Rates a solution with a star rating and optional feedback.

```typescript
const ratingData = {
  userId: 'user-uuid',
  rating: 5,
  feedback: 'Excellent solution, worked perfectly!'
}

const { data, error } = await solutionOperations.rateSolution('solution-uuid', ratingData)
```

### `solutionOperations.softDeleteSolution(solutionId: string)`

Soft deletes a solution.

```typescript
const { data, error } = await solutionOperations.softDeleteSolution('solution-uuid')
```

## 🎥 Webinar Operations

### `webinarOperations.createWebinar(webinarData)`

Creates a new webinar.

```typescript
const webinarData = {
  title: 'Modern Web Development with React',
  description: 'Learn the latest React patterns and best practices...',
  presenter: 'Dr. Ahmad Al-Khatib',
  scheduled_at: '2024-07-30T14:00:00Z',
  duration_minutes: 90,
  category: 'تطوير البرمجيات',
  tags: ['React', 'JavaScript', 'Frontend'],
  presentation_files: [
    {
      name: 'react-presentation.pdf',
      url: 'https://storage.supabase.co/...'
    }
  ]
}

const { data, error } = await webinarOperations.createWebinar(webinarData)
```

### `webinarOperations.getWebinar(webinarId: string)`

Retrieves a specific webinar.

```typescript
const { data, error } = await webinarOperations.getWebinar('webinar-uuid')
```

### `webinarOperations.getAllWebinars(filters?)`

Retrieves all webinars with optional filtering.

```typescript
const filters = {
  status: 'scheduled',
  category: 'تطوير البرمجيات',
  upcoming: true,
  includeDeleted: false
}

const { data, error } = await webinarOperations.getAllWebinars(filters)
```

### `webinarOperations.updateWebinar(webinarId: string, updates)`

Updates a webinar's information.

```typescript
const updates = {
  status: 'completed',
  recording_url: 'https://youtube.com/watch?v=...',
  transcript: 'Full webinar transcript...',
  qa_sessions: [
    {
      question: 'How do you handle state management?',
      answer: 'We recommend using Redux Toolkit...',
      timestamp: '00:45:30'
    }
  ]
}

const { data, error } = await webinarOperations.updateWebinar('webinar-uuid', updates)
```

## 🔄 Real-time Subscriptions

### `subscriptions.subscribeToProblems(callback)`

Subscribes to real-time updates on problems table.

```typescript
const subscription = subscriptions.subscribeToProblems((payload) => {
  console.log('Problem update:', payload)
  
  switch (payload.eventType) {
    case 'INSERT':
      // Handle new problem
      break
    case 'UPDATE':
      // Handle problem update
      break
    case 'DELETE':
      // Handle problem deletion
      break
  }
})

// Cleanup
subscription.unsubscribe()
```

### `subscriptions.subscribeToSolutions(problemId: string, callback)`

Subscribes to real-time updates on solutions for a specific problem.

```typescript
const subscription = subscriptions.subscribeToSolutions('problem-uuid', (payload) => {
  console.log('Solution update:', payload)
  // Handle solution updates
})
```

### `subscriptions.subscribeToWebinars(callback)`

Subscribes to real-time updates on webinars.

```typescript
const subscription = subscriptions.subscribeToWebinars((payload) => {
  console.log('Webinar update:', payload)
  // Handle webinar updates
})
```

## 📁 Storage Operations

### File Upload

```typescript
import { supabase } from '@/lib/supabase'

const uploadFile = async (file: File, bucket: string, path: string) => {
  const { data, error } = await supabase.storage
    .from(bucket)
    .upload(path, file, {
      cacheControl: '3600',
      upsert: false
    })
  
  if (error) throw error
  
  // Get public URL
  const { data: { publicUrl } } = supabase.storage
    .from(bucket)
    .getPublicUrl(path)
  
  return publicUrl
}

// Usage
const avatarUrl = await uploadFile(
  avatarFile,
  'avatars',
  `${userId}/avatar.jpg`
)

const attachmentUrl = await uploadFile(
  attachmentFile,
  'attachments',
  `problems/${problemId}/${fileName}`
)
```

### File Download

```typescript
const downloadFile = async (bucket: string, path: string) => {
  const { data, error } = await supabase.storage
    .from(bucket)
    .download(path)
  
  if (error) throw error
  return data
}
```

### File Deletion

```typescript
const deleteFile = async (bucket: string, path: string) => {
  const { data, error } = await supabase.storage
    .from(bucket)
    .remove([path])
  
  if (error) throw error
  return data
}
```

## 🔐 Authentication Operations

### Sign Up

```typescript
const { signUp } = useAuth()

const userData = {
  name: 'John Doe',
  role: 'expert',
  location: 'Damascus, Syria',
  organization: 'Tech Company',
  position: 'Senior Developer',
  phone_number: '+963 11 123 4567',
  bio: 'Experienced software developer...'
}

const { error } = await signUp('<EMAIL>', 'password123', userData)
```

### Sign In

```typescript
const { signIn } = useAuth()

const { error } = await signIn('<EMAIL>', 'password123')
```

### Sign Out

```typescript
const { signOut } = useAuth()

const { error } = await signOut()
```

### Reset Password

```typescript
const { resetPassword } = useAuth()

const { error } = await resetPassword('<EMAIL>')
```

### Update Profile

```typescript
const { updateProfile } = useAuth()

const updates = {
  name: 'Updated Name',
  avatar: 'https://storage.supabase.co/...'
}

const { error } = await updateProfile(updates)
```

## 🔍 Advanced Queries

### Complex Filtering

```typescript
// Get problems with multiple filters
const { data, error } = await supabase
  .from('problems')
  .select(`
    *,
    users!problems_submitted_by_fkey (name, organization),
    solutions (count)
  `)
  .eq('status', 'open')
  .in('urgency', ['high', 'critical'])
  .contains('tags', ['urgent'])
  .gte('created_at', '2024-01-01')
  .order('created_at', { ascending: false })
  .limit(20)
```

### Full-Text Search

```typescript
// Search with PostgreSQL full-text search
const { data, error } = await supabase
  .from('problems')
  .select('*')
  .textSearch('title', 'hospital management', {
    type: 'websearch',
    config: 'arabic'
  })
```

### Aggregation Queries

```typescript
// Get expert statistics
const { data, error } = await supabase
  .rpc('get_expert_stats', {
    expert_id: 'expert-uuid'
  })

// Custom RPC function in database:
/*
CREATE OR REPLACE FUNCTION get_expert_stats(expert_id UUID)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_solutions', COUNT(*),
    'approved_solutions', COUNT(*) FILTER (WHERE status = 'approved'),
    'average_rating', AVG(rating),
    'total_votes', SUM(jsonb_array_length(votes))
  )
  INTO result
  FROM solutions
  WHERE expert_id = $1 AND is_deleted = false;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;
*/
```

## 🚨 Error Handling

### Standard Error Response

```typescript
interface DatabaseError {
  message: string
  details?: string
  hint?: string
  code?: string
}

// Usage
const { data, error } = await userOperations.getProfile('user-uuid')

if (error) {
  switch (error.code) {
    case 'PGRST116':
      console.error('Permission denied')
      break
    case 'PGRST301':
      console.error('Resource not found')
      break
    default:
      console.error('Database error:', error.message)
  }
}
```

### Error Handling Patterns

```typescript
// Wrapper function with error handling
const safeDbOperation = async <T>(
  operation: () => Promise<{ data: T | null; error: any }>
): Promise<T> => {
  try {
    const { data, error } = await operation()
    
    if (error) {
      throw new Error(`Database error: ${error.message}`)
    }
    
    if (!data) {
      throw new Error('No data returned')
    }
    
    return data
  } catch (err) {
    console.error('Database operation failed:', err)
    throw err
  }
}

// Usage
const userProfile = await safeDbOperation(() => 
  userOperations.getProfile('user-uuid')
)
```

## 📊 Performance Optimization

### Query Optimization

```typescript
// Use select to limit returned columns
const { data, error } = await supabase
  .from('problems')
  .select('id, title, status, created_at')
  .eq('status', 'open')

// Use indexes for better performance
// Ensure these indexes exist in your schema:
// CREATE INDEX idx_problems_status ON problems(status);
// CREATE INDEX idx_problems_created_at ON problems(created_at DESC);
```

### Pagination

```typescript
const getPaginatedProblems = async (page: number, pageSize: number = 20) => {
  const from = page * pageSize
  const to = from + pageSize - 1
  
  const { data, error, count } = await supabase
    .from('problems')
    .select('*', { count: 'exact' })
    .range(from, to)
    .order('created_at', { ascending: false })
  
  return {
    data,
    error,
    totalCount: count,
    hasMore: count ? to < count - 1 : false
  }
}
```

### Caching Strategies

```typescript
// Simple in-memory cache
const cache = new Map()

const getCachedData = async (key: string, fetcher: () => Promise<any>) => {
  if (cache.has(key)) {
    return cache.get(key)
  }
  
  const data = await fetcher()
  cache.set(key, data)
  
  // Clear cache after 5 minutes
  setTimeout(() => cache.delete(key), 5 * 60 * 1000)
  
  return data
}

// Usage
const experts = await getCachedData('all-experts', () =>
  expertOperations.getAllExperts()
)
```

This API reference provides comprehensive documentation for all database operations available in the platform, including examples, error handling, and performance optimization techniques.