# Quick Reference: Utility Integration

## 🚀 Quick Start Templates

### Form Component Template
```typescript
import React, { useState, memo } from 'react';
import { LoadingStateManager, useLoadingState } from '@/components/common/LoadingStateManager';
import { FormErrorBoundary } from '@/utils/errorBoundaryHelpers';
import { useOptimizedCallback } from '@/hooks/useOptimization';
import { useTouchTargets, useScreenReader } from '@/hooks/useAccessibility';
import { FormSkeleton } from '@/components/ui/skeleton-variants';

const MyFormComponent = memo(function MyForm({ onSubmit }) {
  const { isLoading, startLoading, stopLoading, setLoadingError } = useLoadingState();
  const { ensureTouchTarget } = useTouchTargets();
  const { announce } = useScreenReader();
  
  const handleSubmit = useOptimizedCallback(async (e) => {
    e.preventDefault();
    startLoading();
    announce('Processing...', 'polite');
    
    try {
      await onSubmit();
      announce('Success!', 'polite');
      stopLoading();
    } catch (error) {
      setLoadingError(error.message);
      announce(`Error: ${error.message}`, 'assertive');
    }
  }, [onSubmit, startLoading, stopLoading, setLoadingError, announce]);
  
  return (
    <LoadingStateManager
      isLoading={isLoading}
      loadingComponent={<FormSkeleton fields={3} showSubmitButton={true} />}
      fadeTransition={true}
    >
      <form onSubmit={handleSubmit} noValidate>
        {/* Form content */}
      </form>
    </LoadingStateManager>
  );
});

export function MyForm(props) {
  return (
    <FormErrorBoundary formName="MyForm">
      <MyFormComponent {...props} />
    </FormErrorBoundary>
  );
}
```

### Dashboard Component Template
```typescript
import React, { useState, useEffect, memo } from 'react';
import { DataErrorBoundary } from '@/utils/errorBoundaryHelpers';
import { useOptimizedCallback } from '@/hooks/useOptimization';
import { MyDataSkeleton } from './MySkeleton';

const MyDashboardComponent = memo(function MyDashboard() {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  
  const fetchData = useOptimizedCallback(async () => {
    setLoading(true);
    try {
      const result = await api.getData();
      setData(result);
    } finally {
      setLoading(false);
    }
  }, []);
  
  if (loading) {
    return <MyDataSkeleton cardCount={6} animation="shimmer" />;
  }
  
  return (
    <div>
      {/* Dashboard content */}
    </div>
  );
});

export const MyDashboard = () => (
  <DataErrorBoundary dataType="MyData">
    <MyDashboardComponent />
  </DataErrorBoundary>
);
```

## 🎯 Essential Imports

```typescript
// Loading States
import { LoadingStateManager, useLoadingState } from '@/components/common/LoadingStateManager';
import { FormSkeleton, CardSkeleton } from '@/components/ui/skeleton-variants';

// Error Boundaries
import { FormErrorBoundary, DataErrorBoundary } from '@/utils/errorBoundaryHelpers';

// Performance
import { useOptimizedCallback } from '@/hooks/useOptimization';
import { memo } from 'react';

// Accessibility
import { useTouchTargets, useScreenReader } from '@/hooks/useAccessibility';
```

## 🔧 Common Patterns

### Loading State Pattern
```typescript
const { isLoading, startLoading, stopLoading, setLoadingError } = useLoadingState();

// In async function
startLoading();
try {
  await operation();
  stopLoading();
} catch (error) {
  setLoadingError(error.message);
}
```

### Optimized Callback Pattern
```typescript
const handleClick = useOptimizedCallback(async (e) => {
  // Handler logic
}, [dependencies]);
```

### Touch Target Pattern
```typescript
const { ensureTouchTarget } = useTouchTargets();

<Button
  ref={(el) => el && ensureTouchTarget(el)}
  className="min-h-[44px] touch-manipulation"
>
  Click Me
</Button>
```

### Screen Reader Pattern
```typescript
const { announce } = useScreenReader();

// Success announcement
announce('Operation completed successfully', 'polite');

// Error announcement
announce('Error: Something went wrong', 'assertive');
```

## 📋 CSS Classes Reference

### Touch-Friendly Elements
```css
.min-h-[44px]           /* Minimum 44px height for touch targets */
.min-w-[44px]           /* Minimum 44px width for touch targets */
.touch-manipulation     /* Optimizes touch interactions */
```

### Accessibility
```css
.sr-only                /* Screen reader only content */
.focus-visible:outline  /* Focus indicators */
```

## 🎨 Skeleton Components

### Available Skeletons
- `FormSkeleton` - For forms with configurable field count
- `CardSkeleton` - For individual cards
- `ListSkeleton` - For lists of items
- `ProblemDashboardSkeleton` - For problem listings
- `ExpertDirectorySkeleton` - For expert listings
- `SearchResultsSkeleton` - For search results

### Usage
```typescript
<FormSkeleton 
  fields={3} 
  showSubmitButton={true}
  submitButtonText="Loading..."
/>

<ProblemDashboardSkeleton 
  cardCount={6}
  showFilters={true}
  animation="shimmer"
/>
```

## 🚨 Error Boundary Types

### FormErrorBoundary
- Use for: Form components
- Features: Retry functionality, form-specific error handling
- Usage: `<FormErrorBoundary formName="LoginForm">`

### DataErrorBoundary
- Use for: Data display components
- Features: Graceful degradation, data-specific error handling
- Usage: `<DataErrorBoundary dataType="Problems">`

### ComponentErrorBoundary
- Use for: General components
- Features: Configurable retry, isolation options
- Usage: `<ComponentErrorBoundary componentName="MyComponent">`

## ✅ Integration Checklist

### New Form Component
- [ ] Import required utilities
- [ ] Wrap with `FormErrorBoundary`
- [ ] Add `LoadingStateManager` with `FormSkeleton`
- [ ] Use `useOptimizedCallback` for handlers
- [ ] Apply `useTouchTargets` to interactive elements
- [ ] Add `useScreenReader` announcements
- [ ] Include proper ARIA attributes
- [ ] Test with screen reader
- [ ] Verify touch target sizes

### New Dashboard Component
- [ ] Import required utilities
- [ ] Wrap with `DataErrorBoundary`
- [ ] Memoize with `React.memo`
- [ ] Add skeleton loading state
- [ ] Use `useOptimizedCallback` for data fetching
- [ ] Ensure accessibility compliance
- [ ] Test error scenarios
- [ ] Verify performance improvements

## 🔍 Debugging Tips

### Loading States
```typescript
// Debug loading state
console.log('Loading state:', { isLoading, error: loadingError });
```

### Performance
```typescript
// Monitor callback optimization
const handleClick = useOptimizedCallback((e) => {
  console.log('Callback executed');
}, [deps], 'handleClick'); // Debug key
```

### Accessibility
```typescript
// Test screen reader announcements
const { announce } = useScreenReader();
announce('Debug message', 'polite');
```

## 📊 Performance Monitoring

### Key Metrics to Track
- Component render count
- Callback execution frequency
- Loading state duration
- Error boundary activation
- Touch target compliance

### Monitoring Code
```typescript
// Add to components for monitoring
useEffect(() => {
  console.log('Component rendered:', componentName);
});
```

## 🎯 Best Practices

1. **Always wrap forms** with `FormErrorBoundary`
2. **Always wrap data components** with `DataErrorBoundary`
3. **Use skeleton loading** for better perceived performance
4. **Optimize callbacks** with `useOptimizedCallback`
5. **Ensure touch targets** are minimum 44px
6. **Announce state changes** to screen readers
7. **Test with accessibility tools**
8. **Monitor performance** in development

## 🚀 Quick Commands

```bash
# Check for missing error boundaries
grep -r "export.*function" src/components --include="*.tsx" | grep -v "ErrorBoundary"

# Find components without memoization
grep -r "export.*function" src/components --include="*.tsx" | grep -v "memo"

# Check touch target compliance
grep -r "min-h-\[44px\]" src/components --include="*.tsx"
```
