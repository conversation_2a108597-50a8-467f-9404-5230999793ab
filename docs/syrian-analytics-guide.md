# Syrian Identity Analytics Guide

## Overview

The Syrian Identity Analytics system provides lightweight, privacy-focused tracking for Syrian cultural features. It monitors usage patterns, performance metrics, and adoption rates to help optimize the Syrian identity design system.

## 🎯 Key Features

- **Component Usage Tracking**: Monitor which Syrian components are being used
- **Performance Monitoring**: Track pattern rendering performance and paint times
- **Error Tracking**: Capture Syrian feature-related errors
- **Adoption Metrics**: Measure Syrian identity feature adoption rates
- **Privacy-First**: No personal data collection, local storage only
- **Development-Focused**: Detailed analytics in development mode

## 🚀 Quick Start

### Basic Setup

```tsx
// In your app's entry point (main.tsx or App.tsx)
import { initSyrianAnalytics } from '@/lib/analytics/init';

// Initialize with default settings
initSyrianAnalytics();

// Or with custom configuration
initSyrianAnalytics({
  enabled: true,
  enablePerformanceMonitoring: true,
  batchSize: 10,
});
```

### Development Setup

```tsx
import { initSyrianAnalyticsDevelopment } from '@/lib/analytics/init';

// Initialize for development with enhanced logging
initSyrianAnalyticsDevelopment();
```

### Production Setup

```tsx
import { initSyrianAnalyticsProduction } from '@/lib/analytics/init';

// Initialize for production with endpoint
initSyrianAnalyticsProduction('https://your-analytics-endpoint.com/api/events');
```

## 📊 Analytics Dashboard

Access the development dashboard at `/syrian-analytics` (development only):

### Dashboard Features

- **Real-time Metrics**: Live usage and performance data
- **Component Usage**: Which Syrian components are most used
- **Pattern Performance**: Rendering times for each pattern
- **Error Monitoring**: Track and debug Syrian feature issues
- **Performance Status**: Visual indicators for optimization needs

### Dashboard Controls

- **Auto-refresh**: Automatic data updates every 5 seconds
- **Manual Refresh**: Update data on demand
- **Clear Data**: Reset all stored analytics
- **Export Logs**: Console logging for debugging

## 🔧 Configuration Options

### Analytics Config

```tsx
interface SyrianAnalyticsConfig {
  enabled: boolean;                    // Enable/disable analytics
  endpoint?: string;                   // Analytics endpoint URL
  batchSize: number;                   // Events per batch (default: 10)
  flushInterval: number;               // Flush interval in ms (default: 30000)
  enablePerformanceMonitoring: boolean; // Track performance metrics
  enableErrorTracking: boolean;        // Track errors
  enableUserPreferences: boolean;      // Track user preferences
  privacyMode: boolean;               // Privacy-compliant mode
}
```

### Environment Variables

```bash
# Enable analytics in production
ENABLE_SYRIAN_ANALYTICS=true

# Analytics endpoint for production
SYRIAN_ANALYTICS_ENDPOINT=https://your-endpoint.com/api/events
```

## 📈 Tracked Events

### Component Usage Events

```tsx
// Automatically tracked when using Syrian components
<Button variant="syrian">Syrian Button</Button>
<Card syrianStyle={true}>Syrian Card</Card>

// Event data:
{
  type: 'component_usage',
  component: 'Button',
  variant: 'syrian',
  pattern: undefined,
  intensity: undefined,
  timestamp: 1692454800000,
  sessionId: 'syrian_1692454800000_abc123'
}
```

### Pattern Render Events

```tsx
// Automatically tracked when patterns are rendered
<PatternBackground pattern="damascusStar" intensity="moderate">
  Content
</PatternBackground>

// Event data:
{
  type: 'pattern_render',
  pattern: 'damascusStar',
  intensity: 'moderate',
  performance: { paintTime: 12.5 },
  timestamp: 1692454800000,
  sessionId: 'syrian_1692454800000_abc123'
}
```

### Performance Events

```tsx
// Automatically tracked for performance monitoring
{
  type: 'performance',
  performance: {
    paintTime: 15.2,
    renderTime: 8.7,
    bundleSize: 4200
  },
  timestamp: 1692454800000,
  sessionId: 'syrian_1692454800000_abc123'
}
```

### Error Events

```tsx
// Automatically tracked when Syrian features encounter errors
{
  type: 'error',
  error: {
    message: 'Pattern failed to render',
    component: 'PatternBackground',
    stack: '...'
  },
  timestamp: 1692454800000,
  sessionId: 'syrian_1692454800000_abc123'
}
```

## 🎣 React Hooks

### Component Tracking

```tsx
import { useSyrianComponentTracking } from '@/lib/analytics/hooks';

function MyComponent() {
  // Track component usage
  useSyrianComponentTracking('MyComponent', 'syrian', 'damascusStar', 'moderate');
  
  return <div>Component content</div>;
}
```

### Pattern Performance Tracking

```tsx
import { useSyrianPatternTracking } from '@/lib/analytics/hooks';

function PatternComponent() {
  // Track pattern rendering performance
  useSyrianPatternTracking('damascusStar', 'moderate');
  
  return <div className="pattern-damascus-star">Pattern content</div>;
}
```

### Analytics Summary

```tsx
import { useSyrianAnalyticsSummary } from '@/lib/analytics/hooks';

function AnalyticsView() {
  const { getSummary, clearData, isEnabled } = useSyrianAnalyticsSummary();
  
  const summary = getSummary();
  
  return (
    <div>
      <p>Total Events: {summary.totalEvents}</p>
      <p>Average Paint Time: {summary.performanceMetrics.averagePaintTime}ms</p>
      <button onClick={clearData}>Clear Data</button>
    </div>
  );
}
```

### Interaction Tracking

```tsx
import { useSyrianInteractionTracking } from '@/lib/analytics/hooks';

function InteractiveComponent() {
  const { trackInteraction } = useSyrianInteractionTracking('Button');
  
  const handleClick = () => {
    trackInteraction('click', { pattern: 'damascusStar' });
  };
  
  return <button onClick={handleClick}>Track Click</button>;
}
```

## 🔒 Privacy & Security

### Privacy Features

- **No Personal Data**: No user identification or personal information collected
- **Local Storage**: Data stored locally in development mode
- **Session-Based**: Session IDs are temporary and non-identifying
- **Opt-In**: Analytics must be explicitly enabled
- **Transparent**: All tracked data is visible in development dashboard

### Data Retention

- **Development**: Data stored in localStorage, cleared manually
- **Production**: Data sent to configured endpoint, retention policy depends on endpoint
- **Maximum Local Storage**: 100 most recent events to prevent storage bloat

### GDPR Compliance

- No personal data collection
- No cookies or persistent identifiers
- Local processing only (unless endpoint configured)
- User can clear data at any time

## 📊 Performance Monitoring

### Metrics Tracked

- **Paint Time**: Time to render patterns (target: ≤16ms)
- **Bundle Size**: Total size of pattern assets
- **Render Count**: Number of pattern renders
- **Error Rate**: Frequency of Syrian feature errors

### Performance Thresholds

```tsx
// Performance targets
const PERFORMANCE_TARGETS = {
  paintTime: 16,      // 60fps budget
  bundleSize: 5000,   // 5KB maximum
  errorRate: 0,       // Zero errors target
};
```

### Optimization Alerts

The system automatically warns when:
- Paint time exceeds 16ms (60fps budget)
- Bundle size exceeds 5KB
- Errors are detected in Syrian features

## 🧪 Development Tools

### Console Logging

```tsx
import { useSyrianAnalyticsDebug } from '@/lib/analytics/hooks';

function DebugComponent() {
  const { logSummary, logEvents } = useSyrianAnalyticsDebug();
  
  return (
    <div>
      <button onClick={logSummary}>Log Summary</button>
      <button onClick={logEvents}>Log All Events</button>
    </div>
  );
}
```

### Manual Event Tracking

```tsx
import { trackSyrianComponent, trackSyrianPattern } from '@/lib/analytics/syrian-analytics';

// Manual component tracking
trackSyrianComponent('CustomComponent', 'syrian', 'damascusStar', 'moderate');

// Manual pattern tracking
trackSyrianPattern('damascusStar', 'moderate', 15.2);
```

## 🚀 Production Deployment

### Endpoint Setup

Your analytics endpoint should accept POST requests with this format:

```json
{
  "events": [
    {
      "type": "component_usage",
      "component": "Button",
      "variant": "syrian",
      "timestamp": 1692454800000,
      "sessionId": "syrian_1692454800000_abc123"
    }
  ]
}
```

### Production Configuration

```tsx
// Initialize for production
initSyrianAnalyticsProduction('https://analytics.yoursite.com/api/events', {
  batchSize: 20,
  flushInterval: 60000,
  enableUserPreferences: false, // Disable for privacy
});
```

### Monitoring Setup

Set up monitoring for:
- Analytics endpoint availability
- Event processing errors
- Performance metric trends
- Feature adoption rates

## 🔧 Troubleshooting

### Common Issues

**Analytics not working**
- Check if analytics is enabled in configuration
- Verify initialization is called before component usage
- Check browser console for errors

**Performance warnings**
- Review pattern complexity and usage
- Check mobile device performance
- Consider disabling complex patterns on low-end devices

**Missing events**
- Verify components are using Syrian styling
- Check if events are being batched (wait for flush interval)
- Review browser localStorage for stored events

### Debug Commands

```tsx
// Get analytics instance
const analytics = getSyrianAnalytics();

// Get summary
const summary = analytics?.getAnalyticsSummary();

// Get stored events
const events = analytics?.getStoredEvents();

// Clear data
analytics?.clearStoredEvents();
```

---

For more information, see the [Syrian Identity Design System Documentation](./syrian-identity-guide.md).
