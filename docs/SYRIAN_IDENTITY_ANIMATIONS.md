# Syrian Identity Animation Library

## Cultural Motion Language

Animations inspired by Syrian artistic traditions - flowing, gentle, and meaningful.

### CSS Animation Definitions

```css
/* Damascus Flow - Inspired by flowing calligraphy */
@keyframes damascus-flow {
  0% { 
    background-position: 0% 50%;
    transform: translateX(0);
  }
  50% { 
    background-position: 100% 50%;
    transform: translateX(2px);
  }
  100% { 
    background-position: 0% 50%;
    transform: translateX(0);
  }
}

/* Gentle Weave - Inspired by textile patterns */
@keyframes gentle-weave {
  0% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.02); }
  100% { opacity: 0.3; transform: scale(1); }
}

/* Qasioun Shimmer - Inspired by mountain light */
@keyframes qasioun-shimmer {
  0% { 
    background-position: -200% center;
    opacity: 0;
  }
  50% {
    opacity: 0.8;
  }
  100% { 
    background-position: 200% center;
    opacity: 0;
  }
}

/* Palmyra Rise - Inspired by ancient columns */
@keyframes palmyra-rise {
  0% { 
    transform: translateY(20px);
    opacity: 0;
  }
  100% { 
    transform: translateY(0);
    opacity: 1;
  }
}

/* Orontes Flow - Inspired by river movement */
@keyframes orontes-flow {
  0% { transform: translateX(-10px) rotate(-1deg); }
  50% { transform: translateX(5px) rotate(0.5deg); }
  100% { transform: translateX(-10px) rotate(-1deg); }
}
```

### Animation Utility Classes

```css
/* Motion tokens */
:root {
  --motion-fast: 150ms;
  --motion-medium: 250ms;
  --motion-slow: 400ms;
  --motion-ease-gentle: cubic-bezier(0.4, 0.0, 0.2, 1);
  --motion-ease-bounce: cubic-bezier(0.2, 0.8, 0.2, 1.2);
}

/* Utility classes */
.animate-damascus-flow {
  animation: damascus-flow 8s var(--motion-ease-gentle) infinite;
}

.animate-gentle-weave {
  animation: gentle-weave 6s var(--motion-ease-gentle) infinite;
}

.animate-qasioun-shimmer {
  animation: qasioun-shimmer 3s var(--motion-ease-gentle);
}

.animate-palmyra-rise {
  animation: palmyra-rise var(--motion-medium) var(--motion-ease-bounce);
}

.animate-orontes-flow {
  animation: orontes-flow 12s var(--motion-ease-gentle) infinite;
}

/* Hover animations */
.hover-lift {
  transition: transform var(--motion-fast) var(--motion-ease-bounce);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-glow {
  transition: box-shadow var(--motion-medium) var(--motion-ease-gentle);
}

.hover-glow:hover {
  box-shadow: 0 0 20px hsla(var(--syrian-qasioun-gold-500), 0.3);
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-damascus-flow,
  .animate-gentle-weave,
  .animate-orontes-flow {
    animation: none;
  }
  
  .animate-qasioun-shimmer,
  .animate-palmyra-rise {
    animation-duration: 0.01ms;
  }
  
  .hover-lift:hover {
    transform: none;
  }
}
```

### React Animation Components

```typescript
// components/animations/SyrianMotion.tsx
import React from 'react';
import { cn } from '@/lib/utils';

interface SyrianMotionProps {
  type: 'damascusFlow' | 'gentleWeave' | 'qasiounShimmer' | 'palmyraRise' | 'orontesFlow';
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

export function SyrianMotion({ type, children, className, disabled }: SyrianMotionProps) {
  const animationClasses = {
    damascusFlow: 'animate-damascus-flow',
    gentleWeave: 'animate-gentle-weave', 
    qasiounShimmer: 'animate-qasioun-shimmer',
    palmyraRise: 'animate-palmyra-rise',
    orontesFlow: 'animate-orontes-flow'
  };
  
  return (
    <div 
      className={cn(
        !disabled && animationClasses[type],
        className
      )}
    >
      {children}
    </div>
  );
}

// Hook for motion preferences
export function useMotionPreference() {
  const [prefersReducedMotion, setPrefersReducedMotion] = React.useState(false);
  
  React.useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);
    
    const handler = (e: MediaQueryListEvent) => setPrefersReducedMotion(e.matches);
    mediaQuery.addEventListener('change', handler);
    
    return () => mediaQuery.removeEventListener('change', handler);
  }, []);
  
  return prefersReducedMotion;
}
```

### Loading Animations

```css
/* Syrian-themed loading states */
.loading-damascus {
  position: relative;
  overflow: hidden;
}

.loading-damascus::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    hsla(var(--syrian-qasioun-gold-500), 0.2),
    transparent
  );
  animation: qasioun-shimmer 2s infinite;
}

.loading-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M20 5 L24 15 L35 15 L26 22 L30 32 L20 25 L10 32 L14 22 L5 15 L16 15 Z' fill='none' stroke='%23D4AF37' stroke-width='0.5' opacity='0.3'/%3E%3C/svg%3E");
  background-size: 40px 40px;
  animation: gentle-weave 3s infinite;
}
```

## Performance Considerations

### Animation Performance Budget
- **Target**: 60fps (16.67ms per frame)
- **Maximum duration**: 400ms for page transitions
- **Preferred properties**: transform, opacity (hardware accelerated)
- **Avoid**: animating layout properties (width, height, padding)

### Implementation Guidelines

1. **Use CSS transforms only**:
```css
/* Good */
.syrian-hover {
  transform: translateY(-2px) scale(1.02);
}

/* Avoid */
.syrian-hover {
  top: -2px;
  width: 102%;
}
```

2. **Batch animations**:
```typescript
// Good - single animation trigger
const animateCard = () => {
  element.style.transform = 'translateY(-2px) scale(1.02)';
  element.style.opacity = '0.9';
};

// Avoid - multiple reflows
const animateCard = () => {
  element.style.transform = 'translateY(-2px)';
  element.style.transform += ' scale(1.02)'; // Triggers reflow
};
```

3. **Use will-change sparingly**:
```css
.syrian-interactive {
  will-change: transform;
  transition: transform var(--motion-fast);
}

.syrian-interactive:hover {
  transform: translateY(-2px);
}

/* Remove will-change after animation */
.syrian-interactive:not(:hover) {
  will-change: auto;
}
```

### Animation Testing

```typescript
// tests/animations/syrian-motion.test.ts
describe('Syrian Motion System', () => {
  it('respects prefers-reduced-motion', () => {
    // Mock reduced motion preference
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });
    
    const { container } = render(
      <SyrianMotion type="damascusFlow">
        <div>Test content</div>
      </SyrianMotion>
    );
    
    expect(container.firstChild).not.toHaveClass('animate-damascus-flow');
  });
  
  it('maintains 60fps performance', async () => {
    const startTime = performance.now();
    
    // Trigger animation
    fireEvent.mouseEnter(screen.getByTestId('syrian-card'));
    
    // Wait for animation to complete
    await waitFor(() => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      expect(duration).toBeLessThan(16.67); // 60fps budget
    });
  });
});
```