# نظام إدارة المشاكل التقنية - دليل شامل

## نظرة عامة

نظام إدارة المشاكل التقنية هو جزء أساسي من منصة مركز سوريا الذكي، مصمم لتسهيل التواصل بين الوزارات والمؤسسات الحكومية السورية والخبراء التقنيين لحل المشاكل التقنية المختلفة.

## الميزات الرئيسية

### 1. صفحة عرض المشاكل (`/problems`)
- **عرض شامل**: قائمة بجميع المشاكل التقنية المطروحة
- **البحث المتقدم**: إمكانية البحث في العناوين والأوصاف والكلمات المفتاحية
- **التصفية المتعددة**: حسب الحالة، الأولوية، الفئة التقنية، والقطاع
- **الترتيب المرن**: حسب التاريخ، العنوان، أو آخر تحديث
- **واجهة سهلة الاستخدام**: تصميم متجاوب يدعم العربية والإنجليزية

### 2. نموذج إرسال المشاكل (`/problems/new`)
- **نموذج شامل**: جمع جميع المعلومات اللازمة لوصف المشكلة
- **التحقق من البيانات**: التأكد من صحة واكتمال البيانات المدخلة
- **رفع المرفقات**: إمكانية إرفاق ملفات داعمة (PDF, DOC, صور)
- **الكلمات المفتاحية**: نظام تاغ لتسهيل البحث والتصنيف
- **مستويات الأولوية**: تحديد مدى إلحاح المشكلة

### 3. صفحة تفاصيل المشكلة (`/problems/:id`)
- **عرض تفصيلي**: جميع معلومات المشكلة والمرفقات
- **نظام الحلول**: عرض الحلول المقترحة من الخبراء
- **التفاعل**: إمكانية التصويت على الحلول وإضافة تعليقات
- **تتبع الحالة**: متابعة تطور حل المشكلة

## هيكل البيانات

### جدول المشاكل (problems)

```sql
CREATE TABLE public.problems (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,                    -- عنوان المشكلة
    description TEXT NOT NULL,              -- وصف تفصيلي للمشكلة
    category TEXT NOT NULL,                 -- الفئة التقنية
    sector TEXT NOT NULL,                   -- القطاع/الوزارة
    urgency problem_urgency DEFAULT 'medium', -- مستوى الأولوية
    status problem_status DEFAULT 'open',   -- حالة المشكلة
    submitted_by UUID REFERENCES public.users(id) NOT NULL, -- مقدم المشكلة
    assigned_experts UUID[] DEFAULT ARRAY[]::UUID[], -- الخبراء المكلفون
    tags TEXT[] DEFAULT ARRAY[]::TEXT[],    -- الكلمات المفتاحية
    attachments JSONB DEFAULT '[]'::jsonb,  -- المرفقات
    is_deleted BOOLEAN DEFAULT false,       -- حذف ناعم
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE    -- تاريخ الحل
);
```

### أنواع البيانات المخصصة

```sql
-- مستويات الأولوية
CREATE TYPE problem_urgency AS ENUM ('low', 'medium', 'high', 'critical');

-- حالات المشكلة
CREATE TYPE problem_status AS ENUM ('open', 'in_progress', 'resolved', 'closed');
```

### هيكل المرفقات (attachments)

```json
{
  "id": "unique-file-id",
  "name": "filename.pdf",
  "size": 1024000,
  "type": "application/pdf",
  "url": "https://storage-url/path/to/file"
}
```

## القطاعات المدعومة

النظام يدعم مجموعة شاملة من القطاعات السورية:

### الوزارات الحكومية
- وزارة الصحة
- وزارة التربية والتعليم العالي
- وزارة المالية
- وزارة الاتصالات والتقانة
- وزارة الصناعة والمعادن
- وزارة الزراعة والإصلاح الزراعي
- وزارة النقل
- وزارة الداخلية
- وزارة العدل
- وزارة الخارجية والمغتربين
- وزارة الدفاع
- وزارة الثقافة
- وزارة السياحة
- وزارة الشؤون الاجتماعية والعمل
- وزارة الإسكان والتعمير
- وزارة البيئة
- وزارة الطاقة
- وزارة المياه
- وزارة التجارة الداخلية وحماية المستهلك
- وزارة الاقتصاد والتجارة الخارجية
- وزارة الأوقاف
- وزارة الإعلام
- وزارة التعليم العالي والبحث العلمي
- وزارة الشباب والرياضة

### المؤسسات الحكومية
- رئاسة مجلس الوزراء
- مجلس الشعب
- المحكمة الدستورية العليا
- ديوان المحاسبة
- الهيئة العامة للرقابة والتفتيش
- المصرف المركزي السوري
- هيئة الأوراق والأسواق المالية
- الهيئة العامة للاستثمار
- مؤسسة الإذاعة والتلفزيون
- الهيئة العامة للطيران المدني
- الهيئة العامة للاتصالات والبريد

### القطاعات الاقتصادية
- القطاع المصرفي
- قطاع التأمين
- قطاع الطاقة والنفط
- قطاع الصناعات الغذائية
- قطاع الصناعات النسيجية
- قطاع الصناعات الكيماوية
- قطاع الصناعات المعدنية
- قطاع البناء والإنشاءات
- قطاع السياحة والفندقة
- قطاع النقل واللوجستيات

## الفئات التقنية المدعومة

النظام يغطي مجموعة واسعة من المجالات التقنية:

### تطوير البرمجيات
- تطوير تطبيقات الويب
- تطوير تطبيقات الهاتف المحمول
- تطوير أنظمة سطح المكتب
- تطوير واجهات برمجة التطبيقات (APIs)
- هندسة البرمجيات
- إدارة المشاريع التقنية

### قواعد البيانات وإدارة البيانات
- تصميم قواعد البيانات
- إدارة قواعد البيانات
- تحليل البيانات الضخمة
- ذكاء الأعمال (BI)
- مستودعات البيانات
- تكامل البيانات

### أمن المعلومات والحماية
- أمن الشبكات
- أمن التطبيقات
- الحماية من الفيروسات
- إدارة الهوية والوصول
- التشفير وحماية البيانات
- اختبار الاختراق
- الامتثال والحوكمة

### الشبكات والبنية التحتية
- تصميم الشبكات
- إدارة الخوادم
- الحوسبة السحابية
- المحاكاة الافتراضية
- النسخ الاحتياطي والاستعادة
- مراقبة الأداء

### الذكاء الاصطناعي والتقنيات الحديثة
- الذكاء الاصطناعي
- تعلم الآلة
- معالجة اللغات الطبيعية
- الرؤية الحاسوبية
- إنترنت الأشياء (IoT)
- البلوك تشين
- الواقع المعزز والافتراضي

### الأنظمة المؤسسية
- أنظمة إدارة المحتوى (CMS)
- أنظمة إدارة الموارد البشرية
- أنظمة إدارة العلاقات مع العملاء (CRM)
- أنظمة تخطيط موارد المؤسسة (ERP)
- أنظمة إدارة الوثائق
- أنظمة المحاسبة والمالية

### الحكومة الإلكترونية
- الخدمات الحكومية الرقمية
- أنظمة إدارة الهوية الرقمية
- منصات المشاركة المواطنية
- أنظمة الأرشفة الإلكترونية
- التوقيع الإلكتروني

### الصحة الرقمية
- أنظمة المعلومات الصحية
- السجلات الطبية الإلكترونية
- التطبيب عن بُعد
- أجهزة المراقبة الطبية
- تحليل البيانات الطبية

## مستويات الأولوية

### منخفضة (Low)
- مشاكل غير عاجلة
- تحسينات وتطويرات
- مشاكل تجميلية في الواجهات
- **اللون**: أخضر 🟢

### متوسطة (Medium)
- مشاكل تؤثر على بعض الوظائف
- مشاكل تحتاج حل في وقت معقول
- **اللون**: أصفر 🟡
- **افتراضي**: هذا هو المستوى الافتراضي

### عالية (High)
- مشاكل تؤثر على العمل بشكل كبير
- مشاكل تحتاج حل سريع
- مشاكل أمنية متوسطة
- **اللون**: برتقالي 🟠

### حرجة (Critical)
- مشاكل تعطل النظام بالكامل
- مشاكل أمنية خطيرة
- فقدان بيانات
- تحتاج حل فوري
- **اللون**: أحمر 🔴

## حالات المشكلة

### مفتوحة (Open)
- المشكلة جديدة ولم يتم العمل عليها بعد
- متاحة للخبراء لتقديم الحلول
- **اللون**: أزرق

### قيد المعالجة (In Progress)
- يتم العمل على حل المشكلة
- تم تعيين خبراء للعمل عليها
- **اللون**: أصفر

### محلولة (Resolved)
- تم إيجاد حل للمشكلة
- الحل تم اعتماده وتطبيقه
- **اللون**: أخضر

### مغلقة (Closed)
- المشكلة تم إغلاقها نهائياً
- لا تقبل حلول جديدة
- **اللون**: رمادي

## التكامل مع Supabase

### المصادقة والتخويل
- استخدام Supabase Auth للمصادقة
- Row Level Security (RLS) لحماية البيانات
- أذونات مختلفة حسب نوع المستخدم

### تخزين الملفات
- استخدام Supabase Storage للمرفقات
- دعم أنواع ملفات متعددة
- حد أقصى 10MB لكل ملف
- تشفير الملفات أثناء التخزين

### البحث النصي
- فهرسة النصوص العربية والإنجليزية
- بحث سريع في العناوين والأوصاف
- دعم البحث الضبابي (Fuzzy Search)

### الإشعارات الفورية
- إشعارات فورية عند إضافة مشاكل جديدة
- إشعارات للخبراء المناسبين
- تحديثات فورية لحالة المشاكل

## أمثلة على الاستخدام

### مثال 1: مشكلة في نظام إدارة المرضى
```
العنوان: مشكلة في تكامل نظام إدارة المرضى مع قاعدة البيانات المركزية
الفئة: أنظمة المعلومات الصحية
القطاع: وزارة الصحة
الأولوية: عالية
الوصف: يواجه نظام إدارة المرضى في مستشفى دمشق مشكلة في الاتصال مع قاعدة البيانات المركزية...
الكلمات المفتاحية: قاعدة بيانات، تكامل، مستشفى، مرضى
```

### مثال 2: مشكلة أمنية في موقع إلكتروني
```
العنوان: ثغرة أمنية في موقع وزارة التربية الإلكتروني
الفئة: أمن التطبيقات
القطاع: وزارة التربية والتعليم العالي
الأولوية: حرجة
الوصف: تم اكتشاف ثغرة أمنية تسمح بالوصول غير المصرح به إلى بيانات الطلاب...
الكلمات المفتاحية: أمن، ثغرة، موقع، طلاب، بيانات
```

## أفضل الممارسات

### لمقدمي المشاكل
1. **كن واضحاً ومفصلاً**: اشرح المشكلة بأكبر قدر من التفاصيل
2. **أرفق الملفات الداعمة**: صور، لقطات شاشة، ملفات السجل
3. **استخدم الكلمات المفتاحية**: لتسهيل العثور على مشكلتك
4. **حدد الأولوية بدقة**: لا تبالغ في تقدير الأولوية
5. **تابع المشكلة**: راجع الحلول المقترحة وقدم التغذية الراجعة

### للخبراء
1. **اقرأ المشكلة بعناية**: تأكد من فهم المشكلة قبل تقديم الحل
2. **قدم حلول عملية**: حلول قابلة للتطبيق وواضحة
3. **استخدم أمثلة**: أكواد، صور، روابط مفيدة
4. **كن مهذباً ومهنياً**: في التعامل مع مقدمي المشاكل
5. **تابع تطبيق الحل**: تأكد من نجاح الحل المقترح

## الصيانة والتطوير

### النسخ الاحتياطي
- نسخ احتياطية يومية لقاعدة البيانات
- نسخ احتياطية للملفات المرفقة
- خطة استعادة في حالة الطوارئ

### المراقبة والتحليل
- مراقبة أداء النظام
- تحليل أنماط المشاكل الشائعة
- إحصائيات الاستخدام والفعالية

### التحديثات المستقبلية
- إضافة المزيد من القطاعات والفئات
- تحسين خوارزميات البحث
- إضافة ميزات الذكاء الاصطناعي للتصنيف التلقائي
- تطوير تطبيق الهاتف المحمول

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل في النظام:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +963-11-XXXXXXX
- الدعم الفني: متاح 24/7 للمشاكل الحرجة

---

**آخر تحديث**: يناير 2025
**الإصدار**: 1.0
**المطور**: فريق مركز سوريا الذكي