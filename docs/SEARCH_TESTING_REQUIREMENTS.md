# Search Testing Requirements and Database Integration

## Overview

The search system tests are currently failing because the database integration layer requires specific database functions and setup that are not yet implemented. This document explains what's needed to make the tests fully functional.

## Current Test Status

### ✅ Working Tests
- **SearchService Unit Tests** - All 17 tests passing
- **SearchFiltering Tests** - All 22 tests passing  
- **SearchIntegration Tests** - Component integration tests working

### ❌ Failing Tests
- **DatabaseSearchIntegration Tests** - 10/11 tests failing
- **SearchWorkflow Tests** - Would fail without proper database setup

## Root Cause Analysis

### 1. Missing Database Function

The `SearchService.performDatabaseSearch()` method calls:
```typescript
const { data, error } = await supabase.rpc('enhanced_search', {
  search_query: query.text,
  content_types: query.filters.contentType || null,
  sectors: query.filters.sectors || null,
  // ... other parameters
})
```

**Problem**: The `enhanced_search` PostgreSQL function doesn't exist in the database yet.

### 2. Test Mocking Issues

The database integration tests mock the Supabase client but not correctly:
```typescript
vi.mock('@/lib/database', () => ({
  supabase: {
    rpc: vi.fn(),  // This is mocked but returns empty data
    // ...
  }
}))
```

**Problem**: The mock returns empty arrays instead of the expected test data.

### 3. Missing Database Schema

The search system expects specific database tables and structures that may not be fully set up.

## What's Needed to Fix the Tests

### 1. Create the Enhanced Search Database Function

Create a PostgreSQL function in Supabase:

```sql
-- File: supabase/migrations/002_enhanced_search_function.sql
CREATE OR REPLACE FUNCTION enhanced_search(
  search_query TEXT,
  content_types TEXT[] DEFAULT NULL,
  sectors TEXT[] DEFAULT NULL,
  categories TEXT[] DEFAULT NULL,
  statuses TEXT[] DEFAULT NULL,
  date_from TIMESTAMP DEFAULT NULL,
  date_to TIMESTAMP DEFAULT NULL,
  min_rating DECIMAL DEFAULT NULL,
  location_filter TEXT DEFAULT NULL,
  language TEXT DEFAULT 'ar',
  sort_by TEXT DEFAULT 'relevance',
  sort_order TEXT DEFAULT 'desc',
  limit_count INTEGER DEFAULT 20,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
  id TEXT,
  content_type TEXT,
  title TEXT,
  description TEXT,
  category TEXT,
  sector TEXT,
  tags TEXT[],
  relevance_score DECIMAL,
  similarity_score DECIMAL,
  metadata JSONB
)
LANGUAGE plpgsql
AS $$
BEGIN
  -- Implementation would go here
  -- This is a complex function that needs to:
  -- 1. Search across problems, experts, solutions tables
  -- 2. Apply full-text search with Arabic/English support
  -- 3. Calculate relevance scores
  -- 4. Apply filters and sorting
  -- 5. Return unified results
  
  RETURN QUERY
  SELECT 
    p.id::TEXT,
    'problem'::TEXT as content_type,
    p.title,
    p.description,
    p.category,
    p.sector,
    p.tags,
    -- Calculate relevance score based on text similarity
    ts_rank(to_tsvector('arabic', p.title || ' ' || p.description), plainto_tsquery('arabic', search_query))::DECIMAL as relevance_score,
    similarity(p.title, search_query)::DECIMAL as similarity_score,
    jsonb_build_object(
      'status', p.status,
      'urgency', p.urgency,
      'created_at', p.created_at,
      'updated_at', p.updated_at,
      'submitter_name', u.name,
      'organization', u.organization
    ) as metadata
  FROM problems p
  JOIN users u ON p.submitted_by = u.id
  WHERE 
    (content_types IS NULL OR 'problem' = ANY(content_types))
    AND (sectors IS NULL OR p.sector = ANY(sectors))
    AND (categories IS NULL OR p.category = ANY(categories))
    AND (statuses IS NULL OR p.status = ANY(statuses))
    AND (date_from IS NULL OR p.created_at >= date_from)
    AND (date_to IS NULL OR p.created_at <= date_to)
    AND (
      to_tsvector('arabic', p.title || ' ' || p.description) @@ plainto_tsquery('arabic', search_query)
      OR similarity(p.title, search_query) > 0.3
    )
  
  UNION ALL
  
  -- Similar queries for experts, solutions, etc.
  
  ORDER BY relevance_score DESC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$;
```

### 2. Fix Test Mocking

Update the database integration tests to properly mock the RPC call:

```typescript
// In DatabaseSearchIntegration.test.ts
beforeEach(() => {
  const { supabase } = await import('@/lib/database')
  
  // Mock the enhanced_search RPC call specifically
  vi.mocked(supabase.rpc).mockImplementation((functionName, params) => {
    if (functionName === 'enhanced_search') {
      return Promise.resolve({
        data: mockResults, // Return the expected test data
        error: null
      })
    }
    return Promise.resolve({ data: [], error: null })
  })
})
```

### 3. Database Setup Requirements

The following database setup is required:

#### Tables Needed:
- `problems` - Technical problems/issues
- `experts` - Expert profiles  
- `solutions` - Solution proposals
- `users` - User information
- `search_analytics` - Search tracking (optional)

#### Extensions Needed:
```sql
-- Enable full-text search extensions
CREATE EXTENSION IF NOT EXISTS pg_trgm;  -- For similarity matching
CREATE EXTENSION IF NOT EXISTS unaccent; -- For accent-insensitive search
```

#### Indexes Needed:
```sql
-- Full-text search indexes
CREATE INDEX idx_problems_search ON problems USING gin(to_tsvector('arabic', title || ' ' || description));
CREATE INDEX idx_experts_search ON expert_profiles USING gin(to_tsvector('arabic', bio || ' ' || specialties));

-- Similarity indexes
CREATE INDEX idx_problems_title_trgm ON problems USING gin(title gin_trgm_ops);
CREATE INDEX idx_experts_name_trgm ON users USING gin(name gin_trgm_ops);
```

### 4. Environment Setup

Ensure the following environment variables are set:

```bash
# .env.local
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# For testing
VITE_SUPABASE_SERVICE_ROLE_KEY=your_service_role_key  # If needed for tests
```

## Test Data Requirements

### Sample Data Structure

The tests expect data in this format:

```typescript
interface TestSearchResult {
  id: string
  content_type: 'problem' | 'expert' | 'solution'
  title: string
  description: string
  category: string
  sector: string
  tags: string[]
  relevance_score: number
  similarity_score: number
  metadata: {
    status?: string
    urgency?: string
    rating?: number
    availability?: string
    created_at: string
    updated_at?: string
    submitter_name?: string
    organization?: string
  }
}
```

### Mock Data for Tests

```typescript
const mockSearchResults = [
  {
    id: '1',
    content_type: 'problem',
    title: 'مشكلة في تطوير البرمجيات',
    description: 'وصف المشكلة التقنية',
    category: 'تطوير',
    sector: 'technology',
    tags: ['برمجة', 'تطوير'],
    relevance_score: 0.9,
    similarity_score: 0.85,
    metadata: {
      status: 'open',
      urgency: 'high',
      created_at: '2024-01-15T10:30:00Z',
      submitter_name: 'أحمد محمد',
      organization: 'وزارة التقنية'
    }
  }
  // ... more test data
]
```

## Implementation Priority

### Phase 1: Basic Database Function (High Priority)
1. Create minimal `enhanced_search` function that returns basic results
2. Set up required database tables and indexes
3. Fix test mocking to return expected data

### Phase 2: Advanced Search Features (Medium Priority)  
1. Implement full-text search with Arabic support
2. Add relevance scoring algorithms
3. Implement advanced filtering and sorting

### Phase 3: Performance Optimization (Low Priority)
1. Add search result caching
2. Implement query optimization
3. Add search analytics tracking

## Running Tests After Setup

Once the database function is implemented:

```bash
# Run all search tests
npm run test src/lib/search/

# Run specific test suites
npm run test src/lib/search/__tests__/DatabaseSearchIntegration.test.ts
npm run test src/components/search/__tests__/SearchWorkflow.test.tsx
```

## Troubleshooting

### Common Issues:

1. **"enhanced_search function does not exist"**
   - Solution: Run the database migration to create the function

2. **"Tests return empty results"**
   - Solution: Check that test mocking is properly configured
   - Verify that mock data matches expected format

3. **"Database connection failed"**
   - Solution: Check environment variables and Supabase setup
   - Verify database is accessible from test environment

4. **"Arabic text search not working"**
   - Solution: Ensure PostgreSQL has Arabic language support
   - Check that `pg_trgm` extension is installed

## Next Steps

1. **Immediate**: Create the basic `enhanced_search` database function
2. **Short-term**: Fix test mocking and get database integration tests passing
3. **Long-term**: Implement full search functionality with Arabic support

## Related Files

- `src/lib/search/SearchService.ts` - Main search service implementation
- `src/lib/search/__tests__/DatabaseSearchIntegration.test.ts` - Failing database tests
- `supabase/migrations/001_enhanced_search_infrastructure.sql` - Database schema
- `docs/SETUP_AND_TESTING_GUIDE.md` - General testing setup

---

**Note**: This document should be updated as the database integration is implemented and tests are fixed.