# Database Architecture & Supabase Integration Guide

## 🏗️ Project Overview

The **Syrian Technical Solutions Platform** is a comprehensive system that connects technical experts with government ministries to solve technical challenges. This document explains the complete database architecture, Supabase integration, and development patterns.

## 📊 Database Schema Overview

### Core Tables Structure

```mermaid
erDiagram
    users ||--o{ experts : "one-to-one"
    users ||--o{ problems : "one-to-many"
    users ||--o{ solutions : "one-to-many"
    problems ||--o{ solutions : "one-to-many"
    users ||--o{ webinars : "one-to-many"
    
    users {
        uuid id PK
        text email
        text name
        user_role role
        text avatar
        text bio
        text location
        text phone_number
        text organization
        text position
        text[] languages
        boolean is_active
        boolean is_deleted
        timestamp created_at
        timestamp updated_at
    }
    
    experts {
        uuid id PK
        uuid user_id FK
        jsonb expertise_areas
        integer experience_years
        expert_availability availability
        decimal rating
        integer total_contributions
        decimal success_rate
        integer response_time_hours
        jsonb portfolio
        jsonb certifications
    }
    
    problems {
        uuid id PK
        text title
        text description
        text category
        text sector
        problem_urgency urgency
        problem_status status
        uuid submitted_by FK
        uuid[] assigned_experts
        text[] tags
        jsonb attachments
        timestamp resolved_at
    }
    
    solutions {
        uuid id PK
        uuid problem_id FK
        uuid expert_id FK
        text content
        jsonb attachments
        solution_status status
        jsonb votes
        decimal rating
        text implementation_notes
    }
    
    webinars {
        uuid id PK
        text title
        text description
        text presenter
        timestamp scheduled_at
        integer duration_minutes
        text category
        text[] tags
        jsonb presentation_files
        text recording_url
        text transcript
        jsonb qa_sessions
        uuid[] attendees
        webinar_status status
    }
```

### Custom Types (Enums)

```sql
-- User roles in the system
CREATE TYPE user_role AS ENUM ('expert', 'ministry_user', 'admin');

-- Problem urgency levels
CREATE TYPE problem_urgency AS ENUM ('low', 'medium', 'high', 'critical');

-- Problem status workflow
CREATE TYPE problem_status AS ENUM ('open', 'in_progress', 'resolved', 'closed');

-- Solution status workflow
CREATE TYPE solution_status AS ENUM ('draft', 'submitted', 'approved', 'implemented');

-- Expert availability status
CREATE TYPE expert_availability AS ENUM ('available', 'busy', 'unavailable');

-- Webinar status
CREATE TYPE webinar_status AS ENUM ('scheduled', 'live', 'completed', 'cancelled');
```

## 🔐 Authentication & Authorization

### Row Level Security (RLS) Implementation

The system uses Supabase's Row Level Security to ensure data protection:

#### Users Table Policies
```sql
-- Users can view active profiles
CREATE POLICY "Users can view active profiles" ON public.users
    FOR SELECT USING (is_deleted = false);

-- Users can update own profile
CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id AND is_deleted = false);

-- Admins can view all users including deleted
CREATE POLICY "Admins can view all users including deleted" ON public.users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin' AND is_deleted = false
        )
    );
```

#### Problems Table Policies
```sql
-- Anyone can view active problems
CREATE POLICY "Anyone can view active problems" ON public.problems
    FOR SELECT USING (is_deleted = false);

-- Ministry users can create problems
CREATE POLICY "Ministry users can create problems" ON public.problems
    FOR INSERT WITH CHECK (
        auth.uid() = submitted_by AND
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role IN ('ministry_user', 'admin') AND is_deleted = false
        )
    );
```

### Role-Based Access Control

The system implements three main user roles:

1. **Ministry User** (`ministry_user`)
   - Can submit problems
   - Can approve solutions
   - Can mark solutions as implemented

2. **Expert** (`expert`)
   - Can submit solutions
   - Can vote on solutions
   - Has detailed profile with expertise areas

3. **Admin** (`admin`)
   - Full system access
   - Can manage users
   - Can moderate content
   - Can view deleted records

## 🔌 Supabase Integration Layers

### 1. Direct Client Connection (`src/lib/supabase.ts`)

```typescript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})
```

**Purpose**: Direct frontend-to-Supabase communication for real-time features, authentication, and basic CRUD operations.

### 2. Database Operations Layer (`src/lib/database.ts`)

```typescript
// Abstracted database operations
export const userOperations = {
  async getProfile(userId: string) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()
    return { data, error }
  },
  // ... more operations
}
```

**Purpose**: Provides a clean API layer over raw Supabase calls with:
- Type safety
- Error handling
- Consistent patterns
- Business logic encapsulation

### 3. MCP Server Integration (`.kiro/settings/mcp.json`)

```json
{
  "mcpServers": {
    "supabase": {
      "command": "npx",
      "args": ["-y", "@supabase/mcp-server-supabase@latest"],
      "env": {
        "SUPABASE_URL": "https://xptegoszrnglzvfvvypq.supabase.co",
        "SUPABASE_ANON_KEY": "...",
        "SUPABASE_ACCESS_TOKEN": "********************************************"
      }
    }
  }
}
```

**Purpose**: Enables AI-assisted development with direct database access for:
- Schema management
- Data analysis
- Automated testing
- Development assistance

## 🔄 Connection Types Comparison

### Local Development vs MCP Token Access

| Aspect | Local Development | MCP Token Access |
|--------|------------------|------------------|
| **Authentication** | Anonymous key + RLS | Personal Access Token |
| **Permissions** | Limited by RLS policies | Full admin access |
| **Use Case** | Frontend application | Development tools |
| **Security** | Row-level security | Token-based admin |
| **Real-time** | ✅ Supported | ❌ Not applicable |
| **File Upload** | ✅ Through storage API | ❌ Not applicable |

### When to Use Each

**Local Development Connection:**
- Frontend React components
- User authentication flows
- Real-time subscriptions
- File uploads to storage
- Respects user permissions

**MCP Token Access:**
- Database schema changes
- Bulk data operations
- Development assistance
- Testing and debugging
- Administrative tasks

## 📁 Project Structure

```
src/
├── lib/
│   ├── supabase.ts          # Direct Supabase client
│   ├── database.ts          # Database operations layer
│   └── storage.ts           # File storage operations
├── hooks/
│   └── useAuth.ts           # Authentication hook
├── components/
│   ├── auth/                # Authentication components
│   ├── problems/            # Problem management
│   ├── experts/             # Expert directory & profiles
│   ├── solutions/           # Solution management
│   └── search/              # Search functionality
└── pages/                   # Route components
```

## 🛠️ Key Features Implementation

### 1. Authentication System

**Components:**
- `AuthProvider.tsx` - Context provider with role-based access
- `LoginForm.tsx` - User login interface
- `RegisterForm.tsx` - User registration with role selection
- `UserProfile.tsx` - Profile management

**Key Features:**
- Role-based access control
- Automatic profile creation on signup
- Session persistence
- Password reset functionality

### 2. Problem Management System

**Components:**
- `ProblemSubmissionForm.tsx` - Rich problem submission
- `ProblemDashboard.tsx` - Problem listing with filters
- `ProblemDetailView.tsx` - Detailed problem view with solutions
- `FileUpload.tsx` - Drag-and-drop file attachments

**Key Features:**
- Rich text problem descriptions
- File attachment support
- Tagging system
- Urgency levels
- Status tracking

### 3. Expert Directory System

**Components:**
- `ExpertDirectory.tsx` - Searchable expert listing
- `ExpertProfileForm.tsx` - Comprehensive profile creation
- `ExpertDashboard.tsx` - Expert contribution management

**Key Features:**
- Expertise area management
- Skill tagging
- Portfolio showcase
- Certification tracking
- Availability status

### 4. Solution Management System

**Components:**
- `SolutionManagement.tsx` - Solution CRUD operations
- `SolutionRating.tsx` - Voting and rating system
- `SolutionStatusTracker.tsx` - Status workflow management

**Key Features:**
- Solution submission and editing
- Voting system (up/down votes)
- Rating system with feedback
- Status workflow (draft → submitted → approved → implemented)
- Implementation notes

### 5. Search & Discovery System

**Components:**
- `GlobalSearch.tsx` - Unified search interface
- `SearchResults.tsx` - Comprehensive search results

**Key Features:**
- Full-text search across problems and experts
- Advanced filtering
- Search suggestions
- Recent searches
- Relevance scoring

## 🗄️ Storage Architecture

### File Storage Buckets

1. **Attachments Bucket** (`attachments`)
   - Problem attachments
   - Solution attachments
   - 10MB file size limit
   - Supports: PDF, DOC, PPT, Images

2. **Avatars Bucket** (`avatars`)
   - User profile pictures
   - 2MB file size limit
   - Supports: Images only

### Storage Policies

```sql
-- Allow authenticated users to upload
CREATE POLICY "Authenticated users can upload" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'attachments' AND 
  auth.role() = 'authenticated'
);

-- Allow anyone to view
CREATE POLICY "Anyone can view" ON storage.objects
FOR SELECT USING (bucket_id = 'attachments');
```

## 🧪 Testing Infrastructure

### Database Tests (`src/lib/__tests__/database.test.ts`)

```typescript
describe('Database Operations', () => {
  test('should create user profile', async () => {
    const userData = {
      email: '<EMAIL>',
      name: 'Test User',
      location: 'Damascus, Syria'
    }
    
    const { data, error } = await userOperations.createProfile(userData)
    expect(error).toBeNull()
    expect(data.email).toBe(userData.email)
  })
})
```

### Connection Test Script (`scripts/test-supabase-connection.js`)

```javascript
// Tests basic connectivity and schema presence
async function testConnection() {
  const { data, error } = await supabase.from('users').select('count').limit(1)
  if (error) {
    console.error('❌ Connection failed:', error.message)
  } else {
    console.log('✅ Connection successful!')
  }
}
```

## 🚀 Future Improvements & Smart Implementations

### 1. Real-time Features Enhancement

**Current State**: Basic real-time subscriptions
**Future Enhancement**:
```typescript
// Real-time collaboration on solutions
const useRealtimeSolution = (solutionId: string) => {
  useEffect(() => {
    const subscription = supabase
      .channel(`solution:${solutionId}`)
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'solutions' },
        (payload) => {
          // Handle real-time updates
          updateSolutionState(payload.new)
        }
      )
      .subscribe()
      
    return () => subscription.unsubscribe()
  }, [solutionId])
}
```

### 2. AI-Powered Features

**Planned Integrations**:
- **Smart Problem Categorization**: Auto-categorize problems using NLP
- **Expert Matching**: AI-powered expert recommendation system
- **Solution Quality Assessment**: Automated solution scoring
- **Content Moderation**: AI-powered content filtering

### 3. Advanced Search Implementation

**Current**: Basic text search
**Future**: Vector search with embeddings
```typescript
// Vector similarity search for better matching
const searchWithEmbeddings = async (query: string) => {
  const { data } = await supabase.rpc('match_problems', {
    query_embedding: await generateEmbedding(query),
    match_threshold: 0.8,
    match_count: 10
  })
  return data
}
```

### 4. Performance Optimizations

**Database Optimizations**:
- Implement database connection pooling
- Add read replicas for search operations
- Implement caching layer with Redis
- Add database query optimization

**Frontend Optimizations**:
- Implement virtual scrolling for large lists
- Add service worker for offline functionality
- Implement progressive loading
- Add image optimization

### 5. Security Enhancements

**Planned Security Features**:
- Two-factor authentication
- API rate limiting
- Advanced audit logging
- Content encryption for sensitive data
- GDPR compliance features

### 6. Integration Capabilities

**External Integrations**:
- Government systems integration
- Email notification system (n8n workflows)
- Document management systems
- Video conferencing for webinars
- Mobile app development

### 7. Analytics & Reporting

**Planned Analytics**:
- Problem resolution metrics
- Expert performance analytics
- System usage statistics
- Trend analysis and reporting
- Automated report generation

## 📋 Development Workflow

### 1. Database Schema Changes

1. Update `supabase/schema.sql`
2. Apply changes in Supabase dashboard
3. Update TypeScript types in `src/lib/supabase.ts`
4. Update database operations in `src/lib/database.ts`
5. Test changes with `scripts/test-supabase-connection.js`

### 2. Adding New Features

1. Design database schema changes
2. Implement backend operations
3. Create React components
4. Add authentication/authorization
5. Implement tests
6. Update documentation

### 3. Testing Strategy

- **Unit Tests**: Component and function testing
- **Integration Tests**: Database operation testing
- **E2E Tests**: Full user workflow testing
- **Performance Tests**: Load and stress testing

## 🔧 Configuration Management

### Environment Variables

```bash
# Frontend Configuration
VITE_SUPABASE_URL=https://xptegoszrnglzvfvvypq.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# MCP Server Configuration (in .kiro/settings/mcp.json)
SUPABASE_ACCESS_TOKEN=********************************************
```

### Development vs Production

**Development**:
- Uses development Supabase project
- Relaxed RLS policies for testing
- Debug logging enabled
- Mock data for testing

**Production**:
- Strict RLS policies
- Error tracking and monitoring
- Performance optimization
- Backup and disaster recovery

This architecture provides a solid foundation for a scalable, secure, and maintainable technical solutions platform with room for future enhancements and integrations.