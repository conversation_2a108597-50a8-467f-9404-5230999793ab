# Skeleton Loading System Documentation

## Overview

The skeleton loading system provides comprehensive loading states for all major UI sections in the application. It includes reusable skeleton components, smooth transitions, and various animation options to enhance user experience during data loading.

## Features

- ✅ **Reusable skeleton components** for cards, lists, and forms
- ✅ **Skeleton variants** for different content types (problems, experts, search results)
- ✅ **Smooth transitions** between loading and loaded states
- ✅ **Multiple animation options** (pulse, wave, none)
- ✅ **Mobile-responsive** skeleton layouts
- ✅ **Accessibility-compliant** loading states
- ✅ **TypeScript support** with full type safety

## Components

### Core Skeleton Components

#### `EnhancedSkeleton`
Base skeleton component with enhanced animation options.

```tsx
import { EnhancedSkeleton } from '@/components/ui/skeleton-variants'

<EnhancedSkeleton 
  animation="wave" 
  className="h-4 w-32" 
/>
```

**Props:**
- `animation`: 'pulse' | 'wave' | 'none' (default: 'pulse')
- `className`: Custom CSS classes

#### `CardSkeleton`
Generic card skeleton with configurable options.

```tsx
import { CardSkeleton } from '@/components/ui/skeleton-variants'

<CardSkeleton
  rows={3}
  avatar={true}
  animation="pulse"
  showActions={true}
  showMetadata={true}
/>
```

**Props:**
- `rows`: Number of content rows (default: 3)
- `avatar`: Show avatar placeholder (default: false)
- `animation`: Animation type (default: 'pulse')
- `showActions`: Show action buttons area (default: false)
- `showMetadata`: Show metadata section (default: true)

#### `ListSkeleton`
Skeleton for list-based layouts.

```tsx
import { ListSkeleton } from '@/components/ui/skeleton-variants'

<ListSkeleton
  items={5}
  itemHeight="md"
  showDividers={true}
  animation="wave"
/>
```

**Props:**
- `items`: Number of list items (default: 5)
- `itemHeight`: 'sm' | 'md' | 'lg' (default: 'md')
- `showDividers`: Show dividers between items (default: true)

#### `FormSkeleton`
Skeleton for form layouts.

```tsx
import { FormSkeleton } from '@/components/ui/skeleton-variants'

<FormSkeleton
  fields={4}
  fieldTypes={['input', 'textarea', 'select', 'checkbox']}
  showSubmit={true}
  animation="pulse"
/>
```

**Props:**
- `fields`: Number of form fields (default: 4)
- `fieldTypes`: Array of field types
- `showSubmit`: Show submit button area (default: true)

### Specialized Skeleton Components

#### `ProblemSkeleton`
Skeleton components specifically for problem-related UI.

```tsx
import { 
  ProblemDashboardSkeleton, 
  ProblemCardSkeleton,
  ProblemDetailSkeleton,
  ProblemFormSkeleton 
} from '@/components/problems/ProblemSkeleton'

// Dashboard with multiple problem cards
<ProblemDashboardSkeleton itemCount={6} animation="wave" />

// Individual problem card
<ProblemCardSkeleton animation="pulse" />

// Problem detail page
<ProblemDetailSkeleton animation="wave" />

// Problem submission form
<ProblemFormSkeleton animation="pulse" />
```

#### `ExpertSkeleton`
Skeleton components for expert-related UI.

```tsx
import { 
  ExpertDirectorySkeleton,
  ExpertCardSkeleton,
  ExpertProfileSkeleton,
  ExpertFormSkeleton 
} from '@/components/experts/ExpertSkeleton'

// Expert directory with multiple cards
<ExpertDirectorySkeleton itemCount={9} animation="pulse" />

// Individual expert card
<ExpertCardSkeleton animation="wave" />

// Expert profile page
<ExpertProfileSkeleton animation="pulse" />

// Expert profile form
<ExpertFormSkeleton animation="wave" />
```

#### `SearchSkeleton`
Skeleton components for search-related UI.

```tsx
import { 
  SearchResultsSkeleton,
  SearchFiltersSkeleton,
  EnhancedSearchSkeleton 
} from '@/components/search/SearchSkeleton'

// Search results with tabs and pagination
<SearchResultsSkeleton 
  resultCount={5} 
  showTabs={true} 
  showFilters={true}
  animation="wave" 
/>

// Search filters panel
<SearchFiltersSkeleton animation="pulse" />

// Enhanced search interface
<EnhancedSearchSkeleton animation="wave" />
```

## Loading State Management

### `LoadingStateManager`
Component for managing smooth transitions between loading and loaded states.

```tsx
import { LoadingStateManager } from '@/components/common/LoadingStateManager'
import { ProblemCardSkeleton } from '@/components/problems/ProblemSkeleton'

<LoadingStateManager
  isLoading={isLoading}
  loadingComponent={<ProblemCardSkeleton animation="wave" />}
  fadeTransition={true}
  slideTransition={false}
  scaleTransition={false}
  transitionDuration={400}
  minLoadingTime={800}
>
  <ActualContent />
</LoadingStateManager>
```

**Props:**
- `isLoading`: Boolean loading state
- `loadingComponent`: Skeleton component to show
- `fadeTransition`: Enable fade transition (default: true)
- `slideTransition`: Enable slide transition (default: false)
- `scaleTransition`: Enable scale transition (default: false)
- `transitionDuration`: Transition duration in ms (default: 300)
- `minLoadingTime`: Minimum loading time in ms (default: 500)

### `useLoadingState` Hook
Hook for managing loading states with error handling.

```tsx
import { useLoadingState } from '@/components/common/LoadingStateManager'

function MyComponent() {
  const { isLoading, error, startLoading, stopLoading, setLoadingError } = useLoadingState()

  const loadData = async () => {
    startLoading()
    try {
      const data = await fetchData()
      // Process data
      stopLoading()
    } catch (err) {
      setLoadingError('Failed to load data')
    }
  }

  return (
    <LoadingStateManager
      isLoading={isLoading}
      loadingComponent={<CardSkeleton />}
    >
      {error ? <ErrorMessage error={error} /> : <Content />}
    </LoadingStateManager>
  )
}
```

### `LoadingBoundary`
Simple boundary component for conditional loading states.

```tsx
import { LoadingBoundary } from '@/components/common/LoadingStateManager'

<LoadingBoundary
  isLoading={isLoading}
  fallback={<CardSkeleton animation="pulse" />}
  delay={200}
>
  <Content />
</LoadingBoundary>
```

## Animation Types

### Pulse Animation
Default CSS pulse animation with opacity changes.

```css
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
```

### Wave Animation (Shimmer)
Custom shimmer effect that moves across the skeleton.

```css
.animate-shimmer {
  animation: shimmer 2s infinite linear;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}
```

### No Animation
Static skeleton without any animation effects.

## Usage Examples

### Basic Problem Dashboard

```tsx
import { useState, useEffect } from 'react'
import { LoadingStateManager } from '@/components/common/LoadingStateManager'
import { ProblemDashboardSkeleton } from '@/components/problems/ProblemSkeleton'

function ProblemDashboard() {
  const [problems, setProblems] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadProblems()
  }, [])

  const loadProblems = async () => {
    setIsLoading(true)
    try {
      const data = await fetchProblems()
      setProblems(data)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <LoadingStateManager
      isLoading={isLoading}
      loadingComponent={
        <ProblemDashboardSkeleton 
          itemCount={6} 
          animation="wave" 
        />
      }
      fadeTransition={true}
      transitionDuration={400}
    >
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {problems.map(problem => (
          <ProblemCard key={problem.id} problem={problem} />
        ))}
      </div>
    </LoadingStateManager>
  )
}
```

### Expert Directory with Search

```tsx
import { useState } from 'react'
import { LoadingStateManager } from '@/components/common/LoadingStateManager'
import { ExpertDirectorySkeleton } from '@/components/experts/ExpertSkeleton'

function ExpertDirectory() {
  const [experts, setExperts] = useState([])
  const [isLoading, setIsLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  const handleSearch = async (query: string) => {
    setIsLoading(true)
    setSearchQuery(query)
    
    try {
      const results = await searchExperts(query)
      setExperts(results)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <SearchInput onSearch={handleSearch} />
      
      <LoadingStateManager
        isLoading={isLoading}
        loadingComponent={
          <ExpertDirectorySkeleton 
            itemCount={9} 
            animation="pulse" 
          />
        }
        minLoadingTime={600}
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {experts.map(expert => (
            <ExpertCard key={expert.id} expert={expert} />
          ))}
        </div>
      </LoadingStateManager>
    </div>
  )
}
```

### Form with Loading States

```tsx
import { useState } from 'react'
import { LoadingBoundary } from '@/components/common/LoadingStateManager'
import { ProblemFormSkeleton } from '@/components/problems/ProblemSkeleton'

function ProblemSubmissionForm() {
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState(null)

  useEffect(() => {
    // Load form configuration
    loadFormConfig()
  }, [])

  const loadFormConfig = async () => {
    setIsLoading(true)
    try {
      const config = await fetchFormConfig()
      setFormData(config)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <LoadingBoundary
      isLoading={isLoading}
      fallback={<ProblemFormSkeleton animation="wave" />}
    >
      <ProblemForm data={formData} />
    </LoadingBoundary>
  )
}
```

## Best Practices

### 1. Choose Appropriate Animation
- **Pulse**: Good for general loading states, less distracting
- **Wave**: Better for longer loading times, more engaging
- **None**: Use for very fast loading or when animation might be distracting

### 2. Match Skeleton to Content
- Use the same layout structure as the actual content
- Match the number of items/rows to expected content
- Include placeholders for images, avatars, and actions

### 3. Transition Timing
- Use `minLoadingTime` to prevent flashing for very fast loads
- Set appropriate `transitionDuration` for smooth transitions
- Consider user perception of loading speed

### 4. Mobile Considerations
- Skeleton components automatically adapt to mobile layouts
- Use appropriate item counts for mobile screens
- Consider touch-friendly sizing

### 5. Accessibility
- Skeleton components are presentational and don't interfere with screen readers
- Maintain proper semantic structure in actual content
- Consider users with motion sensitivity (provide option to disable animations)

## Testing

The skeleton system includes comprehensive tests covering:

- Component rendering with different props
- Animation class application
- Responsive behavior
- Accessibility compliance
- Custom styling integration

Run tests with:
```bash
npm test -- --run src/components/ui/__tests__/skeleton-variants.test.tsx
```

## Performance Considerations

- Skeleton components are lightweight and optimized for performance
- CSS animations are hardware-accelerated where possible
- Components use React.memo internally to prevent unnecessary re-renders
- Minimal DOM manipulation during transitions

## Browser Support

- Modern browsers with CSS animation support
- Graceful degradation for older browsers (static skeletons)
- Respects user's `prefers-reduced-motion` setting

## Future Enhancements

- [ ] Add more animation variants (fade-in, slide-up, etc.)
- [ ] Implement skeleton auto-generation from component structure
- [ ] Add skeleton caching for repeated loading states
- [ ] Integrate with React Suspense for automatic skeleton display
- [ ] Add skeleton performance metrics and optimization suggestions