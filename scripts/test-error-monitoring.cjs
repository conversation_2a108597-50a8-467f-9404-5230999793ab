#!/usr/bin/env node

/**
 * Error Monitoring Test Script
 * Tests all error logging and monitoring functionality
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Error Monitoring Test');
console.log('========================\n');

// Test results tracking
let totalTests = 0;
let passedTests = 0;
let failedTests = 0;

function runTest(testName, testFn) {
  totalTests++;
  try {
    const result = testFn();
    if (result) {
      console.log(`✅ ${testName}`);
      passedTests++;
    } else {
      console.log(`❌ ${testName}`);
      failedTests++;
    }
  } catch (error) {
    console.log(`❌ ${testName} - Error: ${error.message}`);
    failedTests++;
  }
}

// Test 1: Error logger exists and has required methods
runTest('Error logger implementation exists', () => {
  const errorLoggerPath = 'src/lib/errorLogger.ts';
  if (!fs.existsSync(errorLoggerPath)) return false;
  
  const content = fs.readFileSync(errorLoggerPath, 'utf8');
  const requiredMethods = [
    'logError',
    'reportError',
    'getErrorStats',
    'clearQueue',
    'forceFlush'
  ];
  
  return requiredMethods.every(method => content.includes(method));
});

// Test 2: Import/export monitor exists
runTest('Import/export monitor implementation exists', () => {
  const monitorPath = 'src/lib/importExportMonitor.ts';
  if (!fs.existsSync(monitorPath)) return false;
  
  const content = fs.readFileSync(monitorPath, 'utf8');
  const requiredFeatures = [
    'logImportFailure',
    'validateExports',
    'safeImport',
    'getImportFailureStats',
    'generateReport'
  ];
  
  return requiredFeatures.every(feature => content.includes(feature));
});

// Test 3: Translation monitor exists
runTest('Translation monitor implementation exists', () => {
  const monitorPath = 'src/lib/translationMonitor.ts';
  if (!fs.existsSync(monitorPath)) return false;
  
  const content = fs.readFileSync(monitorPath, 'utf8');
  const requiredFeatures = [
    'logMissingKey',
    'trackUsage',
    'getFallback',
    'translate',
    'getMissingKeyStats'
  ];
  
  return requiredFeatures.every(feature => content.includes(feature));
});

// Test 4: Component monitor exists
runTest('Component monitor implementation exists', () => {
  const monitorPath = 'src/lib/componentMonitor.ts';
  if (!fs.existsSync(monitorPath)) return false;
  
  const content = fs.readFileSync(monitorPath, 'utf8');
  const requiredFeatures = [
    'logComponentFailure',
    'logComponentRecovery',
    'trackRenderPerformance',
    'getComponentHealth',
    'getFailurePatterns'
  ];
  
  return requiredFeatures.every(feature => content.includes(feature));
});

// Test 5: Development tools exist
runTest('Development tools implementation exists', () => {
  const toolsPath = 'src/lib/developmentTools.ts';
  if (!fs.existsSync(toolsPath)) return false;
  
  const content = fs.readFileSync(toolsPath, 'utf8');
  const requiredFeatures = [
    'startDebugSession',
    'endDebugSession',
    'generateComprehensiveReport',
    'clearAllMonitoringData',
    'exportMonitoringData'
  ];
  
  return requiredFeatures.every(feature => content.includes(feature));
});

// Test 6: LanguageContext uses translation monitor
runTest('LanguageContext integrates with translation monitor', () => {
  const contextPath = 'src/contexts/LanguageContext.tsx';
  if (!fs.existsSync(contextPath)) return false;
  
  const content = fs.readFileSync(contextPath, 'utf8');
  return content.includes('translationMonitor') && 
         content.includes('translationMonitor.translate');
});

// Test 7: Error logger has global error handlers
runTest('Error logger has global error handlers', () => {
  const errorLoggerPath = 'src/lib/errorLogger.ts';
  const content = fs.readFileSync(errorLoggerPath, 'utf8');
  
  return content.includes('setupGlobalErrorHandlers') &&
         content.includes('window.addEventListener(\'error\'') &&
         content.includes('window.addEventListener(\'unhandledrejection\'');
});

// Test 8: Import monitor has retry logic
runTest('Import monitor has retry logic and fallbacks', () => {
  const monitorPath = 'src/lib/importExportMonitor.ts';
  const content = fs.readFileSync(monitorPath, 'utf8');
  
  return content.includes('safeImport') &&
         content.includes('maxRetries') &&
         content.includes('fallback');
});

// Test 9: Translation monitor has fallback strategies
runTest('Translation monitor has fallback strategies', () => {
  const monitorPath = 'src/lib/translationMonitor.ts';
  const content = fs.readFileSync(monitorPath, 'utf8');
  
  return content.includes('fallbackStrategies') &&
         content.includes('titleCase') &&
         content.includes('arabicFallback');
});

// Test 10: Component monitor tracks performance
runTest('Component monitor tracks performance metrics', () => {
  const monitorPath = 'src/lib/componentMonitor.ts';
  const content = fs.readFileSync(monitorPath, 'utf8');
  
  return content.includes('trackRenderPerformance') &&
         content.includes('SLOW_RENDER_THRESHOLD') &&
         content.includes('averageRenderTime');
});

// Test 11: Development tools have keyboard shortcuts
runTest('Development tools have keyboard shortcuts', () => {
  const toolsPath = 'src/lib/developmentTools.ts';
  const content = fs.readFileSync(toolsPath, 'utf8');
  
  return content.includes('setupKeyboardShortcuts') &&
         content.includes('keydown') &&
         content.includes('Ctrl+Shift');
});

// Test 12: Error boundaries use error logger
runTest('Error boundaries integrate with error logger', () => {
  const boundaryPath = 'src/components/common/RouteErrorBoundary.tsx';
  if (!fs.existsSync(boundaryPath)) return false;
  
  const content = fs.readFileSync(boundaryPath, 'utf8');
  return content.includes('errorLogger');
});

// Test 13: All monitoring utilities export properly
runTest('All monitoring utilities have proper exports', () => {
  const files = [
    'src/lib/errorLogger.ts',
    'src/lib/importExportMonitor.ts',
    'src/lib/translationMonitor.ts',
    'src/lib/componentMonitor.ts',
    'src/lib/developmentTools.ts'
  ];
  
  return files.every(file => {
    if (!fs.existsSync(file)) return false;
    const content = fs.readFileSync(file, 'utf8');
    return content.includes('export') && content.includes('export const');
  });
});

// Test 14: Error logging has proper TypeScript interfaces
runTest('Error logging has proper TypeScript interfaces', () => {
  const errorLoggerPath = 'src/lib/errorLogger.ts';
  const content = fs.readFileSync(errorLoggerPath, 'utf8');
  
  return content.includes('interface ErrorLogData') &&
         content.includes('interface ErrorReportData') &&
         content.includes('class ErrorLogger');
});

// Test 15: Development tools are conditionally loaded
runTest('Development tools are conditionally loaded in development', () => {
  const toolsPath = 'src/lib/developmentTools.ts';
  const content = fs.readFileSync(toolsPath, 'utf8');
  
  return content.includes('process.env.NODE_ENV === \'development\'') &&
         content.includes('developmentTools') &&
         content.includes('null');
});

console.log('\n📊 Test Results');
console.log('================');
console.log(`Total Tests: ${totalTests}`);
console.log(`Passed: ${passedTests}`);
console.log(`Failed: ${failedTests}`);
console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

if (failedTests === 0) {
  console.log('\n🎉 All error monitoring tests passed!');
  console.log('✅ Task 9: Add Error Logging and Monitoring - COMPLETED');
  
  console.log('\n📋 Implemented Features:');
  console.log('• Comprehensive error logging system');
  console.log('• Import/export failure monitoring');
  console.log('• Translation key tracking and fallbacks');
  console.log('• Component failure rate monitoring');
  console.log('• Development debugging tools');
  console.log('• Global error handlers');
  console.log('• Performance metrics tracking');
  console.log('• Visual debugging indicators');
  console.log('• Keyboard shortcuts for debugging');
  console.log('• Data export and reporting');
  
  console.log('\n🛠️  Development Tools Available:');
  console.log('• Press Ctrl+Shift+D to open debug panel');
  console.log('• Press Ctrl+Shift+E to show error summary');
  console.log('• Press Ctrl+Shift+T to show translation issues');
  console.log('• Press Ctrl+Shift+C to show component health');
  console.log('• Access window.debugTools in browser console');
  
} else {
  console.log('\n⚠️  Some tests failed. Please review the issues above.');
}