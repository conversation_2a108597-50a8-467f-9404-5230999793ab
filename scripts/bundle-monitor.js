#!/usr/bin/env node

/**
 * Bundle Size Monitor
 * Analyzes bundle sizes and reports on performance metrics
 */

import fs from 'fs';
import path from 'path';
import { gzipSync } from 'zlib';
import { bundleSizeConfig } from '../bundle-size.config.js';

const DIST_DIR = 'dist';
const ASSETS_DIR = path.join(DIST_DIR, 'assets');

/**
 * Get file size in KB
 */
function getFileSizeKB(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return Math.round(stats.size / 1024 * 100) / 100;
  } catch (error) {
    return 0;
  }
}

/**
 * Get gzipped file size in KB
 */
function getGzippedSizeKB(filePath) {
  try {
    const content = fs.readFileSync(filePath);
    const gzipped = gzipSync(content);
    return Math.round(gzipped.length / 1024 * 100) / 100;
  } catch (error) {
    return 0;
  }
}

/**
 * Analyze bundle files
 */
function analyzeBundles() {
  if (!fs.existsSync(DIST_DIR)) {
    console.error('❌ Build directory not found. Run "npm run build" first.');
    process.exit(1);
  }

  const results = {
    files: [],
    totalSize: 0,
    totalGzippedSize: 0,
    warnings: [],
    errors: [],
  };

  // Analyze JavaScript files
  if (fs.existsSync(ASSETS_DIR)) {
    const files = fs.readdirSync(ASSETS_DIR, { recursive: true });
    
    files.forEach(file => {
      const filePath = path.join(ASSETS_DIR, file);
      const stats = fs.statSync(filePath);
      
      if (stats.isFile() && (file.endsWith('.js') || file.endsWith('.css'))) {
        const size = getFileSizeKB(filePath);
        const gzippedSize = getGzippedSizeKB(filePath);
        
        results.files.push({
          name: file,
          path: filePath,
          size,
          gzippedSize,
          type: file.endsWith('.js') ? 'javascript' : 'css',
        });
        
        results.totalSize += size;
        results.totalGzippedSize += gzippedSize;
      }
    });
  }

  return results;
}

/**
 * Check size limits and generate warnings/errors
 */
function checkLimits(results) {
  const { limits, warningThreshold, criticalThreshold } = bundleSizeConfig;
  
  // Check total bundle size
  if (results.totalGzippedSize > limits.total) {
    results.errors.push(
      `Total bundle size (${results.totalGzippedSize}KB) exceeds limit (${limits.total}KB)`
    );
  } else if (results.totalGzippedSize > limits.total * warningThreshold) {
    results.warnings.push(
      `Total bundle size (${results.totalGzippedSize}KB) approaching limit (${limits.total}KB)`
    );
  }
  
  // Check individual file sizes
  results.files.forEach(file => {
    let limit = limits.componentChunk; // default
    
    // Determine appropriate limit based on file name
    if (file.name.includes('vendor-react')) limit = limits['vendor-react'];
    else if (file.name.includes('vendor-ui')) limit = limits['vendor-ui'];
    else if (file.name.includes('vendor-data')) limit = limits['vendor-data'];
    else if (file.name.includes('vendor-forms')) limit = limits['vendor-forms'];
    else if (file.name.includes('vendor-utils')) limit = limits['vendor-utils'];
    else if (file.name.includes('vendor-charts')) limit = limits['vendor-charts'];
    else if (file.name.includes('pages')) limit = limits.routeChunk;
    else if (file.type === 'css') limit = limits.css;
    
    if (file.gzippedSize > limit) {
      results.errors.push(
        `File ${file.name} (${file.gzippedSize}KB) exceeds limit (${limit}KB)`
      );
    } else if (file.gzippedSize > limit * warningThreshold) {
      results.warnings.push(
        `File ${file.name} (${file.gzippedSize}KB) approaching limit (${limit}KB)`
      );
    }
  });
  
  return results;
}

/**
 * Generate report
 */
function generateReport(results) {
  console.log('\n📊 Bundle Size Analysis Report');
  console.log('================================\n');
  
  // Summary
  console.log('📈 Summary:');
  console.log(`   Total files: ${results.files.length}`);
  console.log(`   Total size: ${results.totalSize}KB (uncompressed)`);
  console.log(`   Total size: ${results.totalGzippedSize}KB (gzipped)`);
  console.log(`   Size limit: ${bundleSizeConfig.limits.total}KB\n`);
  
  // File breakdown
  console.log('📁 File Breakdown:');
  results.files
    .sort((a, b) => b.gzippedSize - a.gzippedSize)
    .forEach(file => {
      const sizeStr = `${file.gzippedSize}KB (gzipped)`;
      console.log(`   ${file.name}: ${sizeStr}`);
    });
  
  // Warnings
  if (results.warnings.length > 0) {
    console.log('\n⚠️  Warnings:');
    results.warnings.forEach(warning => {
      console.log(`   ${warning}`);
    });
  }
  
  // Errors
  if (results.errors.length > 0) {
    console.log('\n❌ Errors:');
    results.errors.forEach(error => {
      console.log(`   ${error}`);
    });
  }
  
  // Status
  if (results.errors.length === 0) {
    if (results.warnings.length === 0) {
      console.log('\n✅ All bundle sizes are within limits!');
    } else {
      console.log('\n⚠️  Bundle analysis completed with warnings.');
    }
  } else {
    console.log('\n❌ Bundle analysis failed due to size limit violations.');
  }
  
  console.log('\n💡 Tips:');
  console.log('   - Use dynamic imports for route-based code splitting');
  console.log('   - Consider lazy loading heavy components');
  console.log('   - Review vendor dependencies for optimization opportunities');
  console.log('   - Use tree shaking to eliminate unused code\n');
}

/**
 * Main execution
 */
function main() {
  console.log('🔍 Analyzing bundle sizes...\n');
  
  try {
    let results = analyzeBundles();
    results = checkLimits(results);
    generateReport(results);
    
    // Exit with error code if there are errors
    if (results.errors.length > 0) {
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Bundle analysis failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { analyzeBundles, checkLimits, generateReport };