#!/usr/bin/env node

/**
 * Test script to verify Supabase storage setup
 * Run with: node scripts/test-storage-setup.js
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testStorageSetup() {
  console.log('🧪 Testing Supabase Storage Setup...\n');

  try {
    // Test 1: List storage buckets
    console.log('1️⃣ Testing bucket access...');
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      console.error('❌ Failed to list buckets:', bucketsError.message);
      return false;
    }

    const expectedBuckets = ['attachments', 'avatars'];
    const foundBuckets = buckets.map(b => b.name);
    
    console.log('   Found buckets:', foundBuckets);
    
    for (const expectedBucket of expectedBuckets) {
      if (foundBuckets.includes(expectedBucket)) {
        console.log(`   ✅ ${expectedBucket} bucket exists`);
      } else {
        console.log(`   ❌ ${expectedBucket} bucket missing`);
        return false;
      }
    }

    // Test 2: Test file upload (create a small test file)
    console.log('\n2️⃣ Testing file upload...');
    const testContent = 'This is a test file for storage verification';
    const testFile = new Blob([testContent], { type: 'text/plain' });
    const testFileName = `test-${Date.now()}.txt`;
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('attachments')
      .upload(`test/${testFileName}`, testFile);

    if (uploadError) {
      console.error('❌ Failed to upload test file:', uploadError.message);
      return false;
    }

    console.log('   ✅ Test file uploaded successfully');
    console.log('   📁 File path:', uploadData.path);

    // Test 3: Get public URL
    console.log('\n3️⃣ Testing public URL generation...');
    const { data: { publicUrl } } = supabase.storage
      .from('attachments')
      .getPublicUrl(`test/${testFileName}`);

    console.log('   ✅ Public URL generated:', publicUrl);

    // Test 4: Download file to verify
    console.log('\n4️⃣ Testing file download...');
    const { data: downloadData, error: downloadError } = await supabase.storage
      .from('attachments')
      .download(`test/${testFileName}`);

    if (downloadError) {
      console.error('❌ Failed to download test file:', downloadError.message);
      return false;
    }

    const downloadedContent = await downloadData.text();
    if (downloadedContent === testContent) {
      console.log('   ✅ File content verified');
    } else {
      console.log('   ❌ File content mismatch');
      return false;
    }

    // Test 5: Clean up test file
    console.log('\n5️⃣ Cleaning up test file...');
    const { error: deleteError } = await supabase.storage
      .from('attachments')
      .remove([`test/${testFileName}`]);

    if (deleteError) {
      console.error('❌ Failed to delete test file:', deleteError.message);
      // Don't return false here, cleanup failure isn't critical
    } else {
      console.log('   ✅ Test file cleaned up');
    }

    console.log('\n🎉 All storage tests passed! Your FileUpload component should work now.');
    return true;

  } catch (error) {
    console.error('❌ Unexpected error during testing:', error.message);
    return false;
  }
}

// Run the test
testStorageSetup().then(success => {
  if (success) {
    console.log('\n✅ Storage setup is working correctly!');
    console.log('\n📋 Next steps:');
    console.log('   1. Your FileUpload component should now work');
    console.log('   2. Test file uploads in your problem submission form');
    console.log('   3. Test avatar uploads in user profiles');
    process.exit(0);
  } else {
    console.log('\n❌ Storage setup has issues. Please check the errors above.');
    process.exit(1);
  }
});