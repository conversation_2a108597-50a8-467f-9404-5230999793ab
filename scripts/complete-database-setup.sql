CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('expert', 'ministry_user', 'admin');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE problem_urgency AS ENUM ('low', 'medium', 'high', 'critical');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE problem_status AS ENUM ('open', 'in_progress', 'resolved', 'closed');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE solution_status AS ENUM ('draft', 'submitted', 'approved', 'implemented');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE expert_availability AS ENUM ('available', 'busy', 'unavailable');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE webinar_status AS ENUM ('scheduled', 'live', 'completed', 'cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

CREATE TABLE IF NOT EXISTS public.votes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    solution_id UUID REFERENCES public.solutions(id) ON DELETE CASCADE NOT NULL,
    vote_type TEXT CHECK (vote_type IN ('up', 'down')) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, solution_id)
);

CREATE TABLE IF NOT EXISTS public.ratings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    expert_id UUID REFERENCES public.experts(id) ON DELETE CASCADE NOT NULL,
    rated_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    solution_id UUID REFERENCES public.solutions(id) ON DELETE CASCADE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5) NOT NULL,
    feedback TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(expert_id, rated_by, solution_id)
);

CREATE INDEX IF NOT EXISTS idx_users_role ON public.users(role);
CREATE INDEX IF NOT EXISTS idx_users_location ON public.users(location);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON public.users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_is_deleted ON public.users(is_deleted);

CREATE INDEX IF NOT EXISTS idx_experts_user_id ON public.experts(user_id);
CREATE INDEX IF NOT EXISTS idx_experts_availability ON public.experts(availability);
CREATE INDEX IF NOT EXISTS idx_experts_rating ON public.experts(rating);
CREATE INDEX IF NOT EXISTS idx_experts_is_deleted ON public.experts(is_deleted);

CREATE INDEX IF NOT EXISTS idx_problems_category ON public.problems(category);
CREATE INDEX IF NOT EXISTS idx_problems_sector ON public.problems(sector);
CREATE INDEX IF NOT EXISTS idx_problems_status ON public.problems(status);
CREATE INDEX IF NOT EXISTS idx_problems_urgency ON public.problems(urgency);
CREATE INDEX IF NOT EXISTS idx_problems_submitted_by ON public.problems(submitted_by);
CREATE INDEX IF NOT EXISTS idx_problems_assigned_experts ON public.problems(assigned_experts);
CREATE INDEX IF NOT EXISTS idx_problems_is_deleted ON public.problems(is_deleted);
CREATE INDEX IF NOT EXISTS idx_problems_created_at ON public.problems(created_at);

CREATE INDEX IF NOT EXISTS idx_solutions_problem_id ON public.solutions(problem_id);
CREATE INDEX IF NOT EXISTS idx_solutions_expert_id ON public.solutions(expert_id);
CREATE INDEX IF NOT EXISTS idx_solutions_status ON public.solutions(status);

CREATE INDEX IF NOT EXISTS idx_solutions_is_deleted ON public.solutions(is_deleted);
CREATE INDEX IF NOT EXISTS idx_solutions_created_at ON public.solutions(created_at);

CREATE INDEX IF NOT EXISTS idx_webinars_category ON public.webinars(category);
CREATE INDEX IF NOT EXISTS idx_webinars_status ON public.webinars(status);
CREATE INDEX IF NOT EXISTS idx_webinars_scheduled_at ON public.webinars(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_webinars_is_deleted ON public.webinars(is_deleted);

CREATE INDEX IF NOT EXISTS idx_votes_solution_id ON public.votes(solution_id);
CREATE INDEX IF NOT EXISTS idx_votes_user_id ON public.votes(user_id);

CREATE INDEX IF NOT EXISTS idx_ratings_expert_id ON public.ratings(expert_id);
CREATE INDEX IF NOT EXISTS idx_ratings_solution_id ON public.ratings(solution_id);

CREATE INDEX IF NOT EXISTS idx_problems_search ON public.problems USING GIN(
    to_tsvector('arabic', title || ' ' || description)
);
CREATE INDEX IF NOT EXISTS idx_problems_search_en ON public.problems USING GIN(
    to_tsvector('english', title || ' ' || description)
);

CREATE INDEX IF NOT EXISTS idx_solutions_search ON public.solutions USING GIN(
    to_tsvector('arabic', content)
);
CREATE INDEX IF NOT EXISTS idx_solutions_search_en ON public.solutions USING GIN(
    to_tsvector('english', content)
);

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_experts_updated_at ON public.experts;
CREATE TRIGGER update_experts_updated_at BEFORE UPDATE ON public.experts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_problems_updated_at ON public.problems;
CREATE TRIGGER update_problems_updated_at BEFORE UPDATE ON public.problems
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_solutions_updated_at ON public.solutions;
CREATE TRIGGER update_solutions_updated_at BEFORE UPDATE ON public.solutions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_webinars_updated_at ON public.webinars;
CREATE TRIGGER update_webinars_updated_at BEFORE UPDATE ON public.webinars
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.experts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.problems ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.solutions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.webinars ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ratings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view all active users" ON public.users
    FOR SELECT USING (is_active = true AND is_deleted = false);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Anyone can view active experts" ON public.experts
    FOR SELECT USING (is_deleted = false);

CREATE POLICY "Experts can update own profile" ON public.experts
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND id = experts.user_id
        )
    );

CREATE POLICY "Anyone can view active problems" ON public.problems
    FOR SELECT USING (is_deleted = false);

CREATE POLICY "Authenticated users can create problems" ON public.problems
    FOR INSERT WITH CHECK (auth.uid() = submitted_by);

CREATE POLICY "Problem submitters can update own problems" ON public.problems
    FOR UPDATE USING (auth.uid() = submitted_by);

CREATE POLICY "Anyone can view active solutions" ON public.solutions
    FOR SELECT USING (is_deleted = false);

CREATE POLICY "Experts can create solutions" ON public.solutions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.experts
            WHERE id = solutions.expert_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Solution authors can update own solutions" ON public.solutions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.experts
            WHERE id = solutions.expert_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Anyone can view active webinars" ON public.webinars
    FOR SELECT USING (is_deleted = false);

CREATE POLICY "Authenticated users can create webinars" ON public.webinars
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can manage own votes" ON public.votes
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own ratings" ON public.ratings
    FOR ALL USING (auth.uid() = rated_by);

CREATE TABLE IF NOT EXISTS public.search_analytics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    query_text TEXT NOT NULL,
    query_hash TEXT NOT NULL,
    user_id UUID REFERENCES public.users(id),
    filters JSONB DEFAULT '{}'::jsonb,
    results_count INTEGER DEFAULT 0,
    response_time_ms INTEGER DEFAULT 0,
    clicked_result_id UUID,
    clicked_result_type TEXT,
    session_id TEXT,
    language TEXT DEFAULT 'ar',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.search_suggestions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    suggestion_text TEXT NOT NULL UNIQUE,
    category TEXT,
    frequency INTEGER DEFAULT 1,
    language TEXT DEFAULT 'ar',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE MATERIALIZED VIEW IF NOT EXISTS public.search_index AS
SELECT 
    'problem' as content_type,
    p.id,
    p.title,
    p.description as content,
    p.category,
    p.sector,
    p.tags,
    p.created_at,
    p.updated_at,
    setweight(to_tsvector('arabic', p.title), 'A') ||
    setweight(to_tsvector('arabic', p.description), 'B') ||
    setweight(to_tsvector('arabic', array_to_string(p.tags, ' ')), 'C') as search_vector,
    setweight(to_tsvector('english', p.title), 'A') ||
    setweight(to_tsvector('english', p.description), 'B') ||
    setweight(to_tsvector('english', array_to_string(p.tags, ' ')), 'C') as search_vector_en
FROM public.problems p
WHERE p.is_deleted = false

UNION ALL

SELECT 
    'solution' as content_type,
    s.id,
    'Solution: ' || COALESCE(p.title, 'Untitled') as title,
    s.content,
    p.category,
    p.sector,
    p.tags,
    s.created_at,
    s.updated_at,
    setweight(to_tsvector('arabic', COALESCE(p.title, '')), 'A') ||
    setweight(to_tsvector('arabic', s.content), 'B') ||
    setweight(to_tsvector('arabic', array_to_string(COALESCE(p.tags, ARRAY[]::TEXT[]), ' ')), 'C') as search_vector,
    setweight(to_tsvector('english', COALESCE(p.title, '')), 'A') ||
    setweight(to_tsvector('english', s.content), 'B') ||
    setweight(to_tsvector('english', array_to_string(COALESCE(p.tags, ARRAY[]::TEXT[]), ' ')), 'C') as search_vector_en
FROM public.solutions s
LEFT JOIN public.problems p ON s.problem_id = p.id
WHERE s.is_deleted = false

UNION ALL

SELECT 
    'expert' as content_type,
    e.id,
    u.name as title,
    COALESCE(u.bio, '') as content,
    'expert' as category,
    'general' as sector,
    ARRAY[]::TEXT[] as tags,
    e.created_at,
    e.updated_at,
    setweight(to_tsvector('arabic', u.name), 'A') ||
    setweight(to_tsvector('arabic', COALESCE(u.bio, '')), 'B') as search_vector,
    setweight(to_tsvector('english', u.name), 'A') ||
    setweight(to_tsvector('english', COALESCE(u.bio, '')), 'B') as search_vector_en
FROM public.experts e
JOIN public.users u ON e.user_id = u.id
WHERE e.is_deleted = false AND u.is_deleted = false

UNION ALL

SELECT 
    'webinar' as content_type,
    w.id,
    w.title,
    w.description as content,
    w.category,
    'education' as sector,
    w.tags,
    w.created_at,
    w.updated_at,
    setweight(to_tsvector('arabic', w.title), 'A') ||
    setweight(to_tsvector('arabic', w.description), 'B') ||
    setweight(to_tsvector('arabic', array_to_string(w.tags, ' ')), 'C') as search_vector,
    setweight(to_tsvector('english', w.title), 'A') ||
    setweight(to_tsvector('english', w.description), 'B') ||
    setweight(to_tsvector('english', array_to_string(w.tags, ' ')), 'C') as search_vector_en
FROM public.webinars w
WHERE w.is_deleted = false;

CREATE INDEX IF NOT EXISTS idx_search_vector ON public.search_index USING GIN(search_vector);
CREATE INDEX IF NOT EXISTS idx_search_vector_en ON public.search_index USING GIN(search_vector_en);
CREATE INDEX IF NOT EXISTS idx_search_category ON public.search_index(category);
CREATE INDEX IF NOT EXISTS idx_search_sector ON public.search_index(sector);
CREATE INDEX IF NOT EXISTS idx_search_type ON public.search_index(content_type);
CREATE INDEX IF NOT EXISTS idx_search_created ON public.search_index(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_search_analytics_user_id ON public.search_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_search_analytics_created_at ON public.search_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_search_analytics_query_hash ON public.search_analytics(query_hash);

CREATE INDEX IF NOT EXISTS idx_search_suggestions_text ON public.search_suggestions(suggestion_text);
CREATE INDEX IF NOT EXISTS idx_search_suggestions_frequency ON public.search_suggestions(frequency DESC);
CREATE INDEX IF NOT EXISTS idx_search_suggestions_category ON public.search_suggestions(category);

CREATE OR REPLACE FUNCTION public.refresh_search_index()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.search_index;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.trigger_search_index_refresh()
RETURNS trigger AS $$
BEGIN
    PERFORM pg_notify('search_index_refresh', 'refresh');
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION public.advanced_search(
    search_query TEXT,
    content_types TEXT[] DEFAULT ARRAY['problem', 'solution', 'expert', 'webinar'],
    categories TEXT[] DEFAULT NULL,
    sectors TEXT[] DEFAULT NULL,
    language TEXT DEFAULT 'ar',
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    content_type TEXT,
    id UUID,
    title TEXT,
    content TEXT,
    category TEXT,
    sector TEXT,
    tags TEXT[],
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    rank REAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        si.content_type,
        si.id,
        si.title,
        si.content,
        si.category,
        si.sector,
        si.tags,
        si.created_at,
        si.updated_at,
        CASE 
            WHEN language = 'ar' THEN ts_rank(si.search_vector, plainto_tsquery('arabic', search_query))
            ELSE ts_rank(si.search_vector_en, plainto_tsquery('english', search_query))
        END as rank
    FROM public.search_index si
    WHERE 
        si.content_type = ANY(content_types)
        AND (categories IS NULL OR si.category = ANY(categories))
        AND (sectors IS NULL OR si.sector = ANY(sectors))
        AND (
            CASE 
                WHEN language = 'ar' THEN si.search_vector @@ plainto_tsquery('arabic', search_query)
                ELSE si.search_vector_en @@ plainto_tsquery('english', search_query)
            END
        )
    ORDER BY rank DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;

CREATE TABLE IF NOT EXISTS public.error_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id),
    type TEXT NOT NULL,
    source TEXT NOT NULL,
    message TEXT NOT NULL,
    stack_trace TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    severity TEXT DEFAULT 'error',
    resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.error_reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    error_type TEXT,
    steps_to_reproduce TEXT,
    expected_behavior TEXT,
    actual_behavior TEXT,
    browser_info JSONB DEFAULT '{}'::jsonb,
    system_info JSONB DEFAULT '{}'::jsonb,
    attachments JSONB DEFAULT '[]'::jsonb,
    status TEXT DEFAULT 'open',
    priority TEXT DEFAULT 'medium',
    assigned_to UUID REFERENCES public.users(id),
    assigned_at TIMESTAMP WITH TIME ZONE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolution_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE OR REPLACE VIEW public.error_statistics AS
SELECT 
    type,
    source,
    severity,
    COUNT(*) as error_count,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '24 hours') as last_24h,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days') as last_7d,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as last_30d,
    COUNT(*) FILTER (WHERE resolved = true) as resolved_count,
    AVG(EXTRACT(EPOCH FROM (resolved_at - created_at))) as avg_resolution_time_seconds
FROM public.error_logs
GROUP BY type, source, severity
ORDER BY error_count DESC;

CREATE INDEX IF NOT EXISTS idx_error_logs_type ON public.error_logs(type);
CREATE INDEX IF NOT EXISTS idx_error_logs_source ON public.error_logs(source);
CREATE INDEX IF NOT EXISTS idx_error_logs_severity ON public.error_logs(severity);
CREATE INDEX IF NOT EXISTS idx_error_logs_created_at ON public.error_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_error_logs_user_id ON public.error_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_error_logs_resolved ON public.error_logs(resolved);

CREATE INDEX IF NOT EXISTS idx_error_reports_status ON public.error_reports(status);
CREATE INDEX IF NOT EXISTS idx_error_reports_priority ON public.error_reports(priority);
CREATE INDEX IF NOT EXISTS idx_error_reports_assigned_to ON public.error_reports(assigned_to);
CREATE INDEX IF NOT EXISTS idx_error_reports_user_id ON public.error_reports(user_id);
CREATE INDEX IF NOT EXISTS idx_error_reports_created_at ON public.error_reports(created_at);

DROP TRIGGER IF EXISTS refresh_search_on_problem_change ON public.problems;
CREATE TRIGGER refresh_search_on_problem_change
    AFTER INSERT OR UPDATE OR DELETE ON public.problems
    FOR EACH STATEMENT
    EXECUTE FUNCTION public.trigger_search_index_refresh();

DROP TRIGGER IF EXISTS refresh_search_on_expert_change ON public.experts;
CREATE TRIGGER refresh_search_on_expert_change
    AFTER INSERT OR UPDATE OR DELETE ON public.experts
    FOR EACH STATEMENT
    EXECUTE FUNCTION public.trigger_search_index_refresh();

DROP TRIGGER IF EXISTS refresh_search_on_solution_change ON public.solutions;
CREATE TRIGGER refresh_search_on_solution_change
    AFTER INSERT OR UPDATE OR DELETE ON public.solutions
    FOR EACH STATEMENT
    EXECUTE FUNCTION public.trigger_search_index_refresh();

DROP TRIGGER IF EXISTS refresh_search_on_webinar_change ON public.webinars;
CREATE TRIGGER refresh_search_on_webinar_change
    AFTER INSERT OR UPDATE OR DELETE ON public.webinars
    FOR EACH STATEMENT
    EXECUTE FUNCTION public.trigger_search_index_refresh();

DROP TRIGGER IF EXISTS refresh_search_on_user_change ON public.users;
CREATE TRIGGER refresh_search_on_user_change
    AFTER INSERT OR UPDATE OR DELETE ON public.users
    FOR EACH STATEMENT
    EXECUTE FUNCTION public.trigger_search_index_refresh();

ALTER TABLE public.search_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.search_suggestions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.error_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.error_reports ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own search analytics" ON public.search_analytics
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert own search analytics" ON public.search_analytics
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Anyone can view active search suggestions" ON public.search_suggestions
  FOR SELECT USING (is_active = true);

CREATE POLICY "Users can view own error logs" ON public.error_logs
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert error logs" ON public.error_logs
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can manage own error reports" ON public.error_reports
  FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Admins can view all error logs" ON public.error_logs
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() 
        AND role = 'admin'
    )
  );

CREATE POLICY "Admins can view all error reports" ON public.error_reports
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() 
        AND role = 'admin'
    )
  );

CREATE INDEX IF NOT EXISTS idx_error_logs_composite 
  ON public.error_logs(created_at, type, source, user_id);

CREATE INDEX IF NOT EXISTS idx_error_logs_message_gin 
  ON public.error_logs USING gin(to_tsvector('english', message));

CREATE INDEX IF NOT EXISTS idx_error_logs_user_id 
  ON public.error_logs(user_id);

CREATE INDEX IF NOT EXISTS idx_error_logs_type 
  ON public.error_logs(type);

CREATE INDEX IF NOT EXISTS idx_error_logs_source 
  ON public.error_logs(source);

CREATE INDEX IF NOT EXISTS idx_error_reports_status 
  ON public.error_reports(status);

CREATE INDEX IF NOT EXISTS idx_error_reports_priority 
  ON public.error_reports(priority);

CREATE INDEX IF NOT EXISTS idx_error_reports_assigned_to 
  ON public.error_reports(assigned_to);

CREATE INDEX IF NOT EXISTS idx_error_reports_user_id 
  ON public.error_reports(user_id);

INSERT INTO public.search_suggestions (suggestion_text, category, frequency, language) VALUES
('مشاكل تقنية', 'problem', 10, 'ar'),
('حلول برمجية', 'solution', 8, 'ar'),
('خبراء تقنيون', 'expert', 5, 'ar'),
('ندوات تعليمية', 'webinar', 3, 'ar'),
('Technical problems', 'problem', 10, 'en'),
('Software solutions', 'solution', 8, 'en'),
('Technical experts', 'expert', 5, 'en'),
('Educational webinars', 'webinar', 3, 'en')
ON CONFLICT (suggestion_text) DO NOTHING; 