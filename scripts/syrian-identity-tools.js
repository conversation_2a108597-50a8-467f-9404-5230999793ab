#!/usr/bin/env node

/**
 * Syrian Identity Development Tools
 * Utilities for managing Syrian identity features in development
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Color definitions for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}\n`)
};

class SyrianIdentityTools {
  constructor() {
    this.projectRoot = process.cwd();
    this.configPath = path.join(this.projectRoot, '.syrian-identity.json');
  }

  // Load configuration
  loadConfig() {
    try {
      if (fs.existsSync(this.configPath)) {
        return JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
      }
    } catch (error) {
      log.warning('Could not load Syrian identity config, using defaults');
    }
    
    return {
      enabled: false,
      patternLevel: 'minimal',
      motionEnabled: true,
      simplifyOnMobile: true
    };
  }

  // Save configuration
  saveConfig(config) {
    try {
      fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
      log.success('Configuration saved');
    } catch (error) {
      log.error(`Failed to save configuration: ${error.message}`);
    }
  }

  // Optimize SVG patterns
  optimizeSVGs() {
    log.header('Optimizing Syrian SVG Patterns');
    
    const svgDir = path.join(this.projectRoot, 'src/assets/patterns');
    if (!fs.existsSync(svgDir)) {
      log.warning('SVG patterns directory not found');
      return;
    }

    try {
      // Install SVGO if not present
      execSync('npx svgo --version', { stdio: 'ignore' });
    } catch {
      log.info('Installing SVGO...');
      execSync('npm install -D svgo', { stdio: 'inherit' });
    }

    const svgFiles = fs.readdirSync(svgDir).filter(file => file.endsWith('.svg'));
    
    svgFiles.forEach(file => {
      const filePath = path.join(svgDir, file);
      const originalSize = fs.statSync(filePath).size;
      
      try {
        execSync(`npx svgo "${filePath}" --config=svgo.config.js`, { stdio: 'ignore' });
        const optimizedSize = fs.statSync(filePath).size;
        const savings = ((originalSize - optimizedSize) / originalSize * 100).toFixed(1);
        
        log.success(`${file}: ${originalSize}B → ${optimizedSize}B (${savings}% smaller)`);
      } catch (error) {
        log.error(`Failed to optimize ${file}: ${error.message}`);
      }
    });
  }

  // Generate CSS from SVG patterns
  generatePatternCSS() {
    log.header('Generating Pattern CSS');
    
    const patterns = {
      'damascus-star': this.generateSVGDataURL('damascus-star.svg'),
      'palmyra-columns': this.generateSVGDataURL('palmyra-columns.svg'),
      'ebla-script': this.generateSVGDataURL('ebla-script.svg'),
      'geometric-weave': this.generateSVGDataURL('geometric-weave.svg')
    };

    let css = '/* Generated Syrian Identity Pattern CSS */\n\n';
    
    Object.entries(patterns).forEach(([name, dataURL]) => {
      if (dataURL) {
        css += `.pattern-${name} {\n`;
        css += `  background-image: url("${dataURL}");\n`;
        css += `  background-repeat: repeat;\n`;
        css += `  background-size: var(--pattern-size, 60px);\n`;
        css += `}\n\n`;
      }
    });

    // Add responsive rules
    css += '/* Responsive pattern adjustments */\n';
    css += '@media (max-width: 480px) {\n';
    css += '  .pattern-geometric-weave { display: none; }\n';
    css += '  .pattern-damascus-star,\n';
    css += '  .pattern-palmyra-columns,\n';
    css += '  .pattern-ebla-script {\n';
    css += '    opacity: 0.5;\n';
    css += '    --pattern-size: 40px;\n';
    css += '  }\n';
    css += '}\n';

    const outputPath = path.join(this.projectRoot, 'src/styles/generated/patterns.css');
    const outputDir = path.dirname(outputPath);
    
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    fs.writeFileSync(outputPath, css);
    log.success(`Pattern CSS generated: ${outputPath}`);
  }

  // Generate SVG data URL
  generateSVGDataURL(filename) {
    const svgPath = path.join(this.projectRoot, 'src/assets/patterns', filename);
    
    if (!fs.existsSync(svgPath)) {
      log.warning(`SVG file not found: ${filename}`);
      return null;
    }

    try {
      let svgContent = fs.readFileSync(svgPath, 'utf8');
      
      // Clean up SVG for data URL
      svgContent = svgContent
        .replace(/\s+/g, ' ')
        .replace(/"/g, "'")
        .trim();
      
      return `data:image/svg+xml,${encodeURIComponent(svgContent)}`;
    } catch (error) {
      log.error(`Failed to process ${filename}: ${error.message}`);
      return null;
    }
  }

  // Run performance audit
  async performanceAudit() {
    log.header('Running Syrian Identity Performance Audit');
    
    try {
      // Check if Lighthouse is available
      execSync('npx lighthouse --version', { stdio: 'ignore' });
    } catch {
      log.info('Installing Lighthouse...');
      execSync('npm install -D lighthouse', { stdio: 'inherit' });
    }

    const urls = [
      'http://localhost:3000',
      'http://localhost:3000/problems',
      'http://localhost:3000/experts'
    ];

    for (const url of urls) {
      log.info(`Auditing: ${url}`);
      
      try {
        // Run Lighthouse audit
        const output = execSync(
          `npx lighthouse "${url}" --only-categories=performance --output=json --quiet`,
          { encoding: 'utf8' }
        );
        
        const report = JSON.parse(output);
        const score = Math.round(report.lhr.categories.performance.score * 100);
        
        if (score >= 90) {
          log.success(`Performance score: ${score}/100`);
        } else if (score >= 70) {
          log.warning(`Performance score: ${score}/100`);
        } else {
          log.error(`Performance score: ${score}/100`);
        }
        
        // Check specific metrics
        const metrics = report.lhr.audits;
        const lcp = metrics['largest-contentful-paint'].numericValue;
        const cls = metrics['cumulative-layout-shift'].numericValue;
        const tbt = metrics['total-blocking-time'].numericValue;
        
        log.info(`  LCP: ${(lcp / 1000).toFixed(2)}s (target: ≤2.5s)`);
        log.info(`  CLS: ${cls.toFixed(3)} (target: ≤0.1)`);
        log.info(`  TBT: ${tbt.toFixed(0)}ms (target: ≤200ms)`);
        
      } catch (error) {
        log.error(`Failed to audit ${url}: ${error.message}`);
      }
    }
  }

  // Bundle size analysis
  bundleAnalysis() {
    log.header('Analyzing Syrian Identity Bundle Impact');
    
    try {
      // Build the project
      log.info('Building project...');
      execSync('npm run build', { stdio: 'inherit' });
      
      // Analyze bundle
      const buildDir = path.join(this.projectRoot, 'dist');
      if (!fs.existsSync(buildDir)) {
        log.error('Build directory not found');
        return;
      }
      
      const jsFiles = this.getJSFiles(buildDir);
      let totalSize = 0;
      let syrianSize = 0;
      
      jsFiles.forEach(file => {
        const content = fs.readFileSync(file, 'utf8');
        const fileSize = Buffer.byteLength(content, 'utf8');
        totalSize += fileSize;
        
        // Estimate Syrian identity code size (rough heuristic)
        const syrianMatches = content.match(/syrian|qasioun|palmyra|ebla|damascus/gi);
        if (syrianMatches) {
          syrianSize += syrianMatches.length * 50; // Rough estimate
        }
      });
      
      log.info(`Total bundle size: ${(totalSize / 1024).toFixed(1)}KB`);
      log.info(`Estimated Syrian identity size: ${(syrianSize / 1024).toFixed(1)}KB`);
      log.info(`Syrian identity impact: ${((syrianSize / totalSize) * 100).toFixed(1)}%`);
      
      if (syrianSize > 15 * 1024) { // 15KB limit
        log.warning('Syrian identity bundle size exceeds 15KB target');
      } else {
        log.success('Syrian identity bundle size within target');
      }
      
    } catch (error) {
      log.error(`Bundle analysis failed: ${error.message}`);
    }
  }

  // Get all JS files recursively
  getJSFiles(dir) {
    let files = [];
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files = files.concat(this.getJSFiles(fullPath));
      } else if (item.endsWith('.js')) {
        files.push(fullPath);
      }
    });
    
    return files;
  }

  // Main CLI handler
  run() {
    const command = process.argv[2];
    
    switch (command) {
      case 'optimize-svg':
        this.optimizeSVGs();
        break;
        
      case 'generate-css':
        this.generatePatternCSS();
        break;
        
      case 'audit':
        this.performanceAudit();
        break;
        
      case 'bundle':
        this.bundleAnalysis();
        break;
        
      case 'config':
        const config = this.loadConfig();
        console.log(JSON.stringify(config, null, 2));
        break;
        
      default:
        this.showHelp();
    }
  }

  showHelp() {
    log.header('Syrian Identity Development Tools');
    console.log('Usage: node scripts/syrian-identity-tools.js <command>\n');
    console.log('Commands:');
    console.log('  optimize-svg    Optimize SVG pattern files');
    console.log('  generate-css    Generate CSS from SVG patterns');
    console.log('  audit          Run performance audit');
    console.log('  bundle         Analyze bundle size impact');
    console.log('  config         Show current configuration');
    console.log('');
  }
}

// Run the tool
if (require.main === module) {
  const tools = new SyrianIdentityTools();
  tools.run();
}

module.exports = SyrianIdentityTools;