# Database Setup Instructions

## Step 1: Apply the Database Schema

1. Go to your Supabase dashboard: https://supabase.com/dashboard/project/xptegoszrnglzvfvvypq
2. Navigate to the SQL Editor
3. Copy and paste the entire content from `supabase/schema.sql` into the SQL Editor
4. Click "Run" to execute the schema

## Step 2: Set up Storage Buckets

1. Go to Storage in your Supabase dashboard
2. Create the following buckets:

### Attachments Bucket
- Name: `attachments`
- Public: Yes
- File size limit: 10MB
- Allowed MIME types: 
  - `application/pdf`
  - `application/msword`
  - `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
  - `image/jpeg`
  - `image/png`
  - `image/gif`
  - `image/webp`
  - `text/plain`

### Avatars Bucket
- Name: `avatars`
- Public: Yes
- File size limit: 2MB
- Allowed MIME types:
  - `image/jpeg`
  - `image/png`
  - `image/gif`
  - `image/webp`

## Step 3: Configure Storage Policies

After creating the buckets, add these policies in the Storage > Policies section:

### For attachments bucket:
```sql
-- Allow authenticated users to upload files
CREATE POLICY "Authenticated users can upload attachments" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'attachments' AND 
  auth.role() = 'authenticated'
);

-- Allow anyone to view attachments
CREATE POLICY "Anyone can view attachments" ON storage.objects
FOR SELECT USING (bucket_id = 'attachments');

-- Allow users to update their own attachments
CREATE POLICY "Users can update own attachments" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'attachments' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Allow users to delete their own attachments
CREATE POLICY "Users can delete own attachments" ON storage.objects
FOR DELETE USING (
  bucket_id = 'attachments' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);
```

### For avatars bucket:
```sql
-- Allow authenticated users to upload avatars
CREATE POLICY "Users can upload avatars" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'avatars' AND 
  auth.role() = 'authenticated'
);

-- Allow anyone to view avatars
CREATE POLICY "Anyone can view avatars" ON storage.objects
FOR SELECT USING (bucket_id = 'avatars');

-- Allow users to update their own avatars
CREATE POLICY "Users can update own avatars" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'avatars' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Allow users to delete their own avatars
CREATE POLICY "Users can delete own avatars" ON storage.objects
FOR DELETE USING (
  bucket_id = 'avatars' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);
```

## Step 4: Test the Connection

After setting up the database, you can test the connection by running:

```bash
npm run dev
```

The application should now connect to Supabase without errors.

## Step 5: Create Test Data (Optional)

You can create some test data by registering users through the application or by running these SQL commands in the SQL Editor:

```sql
-- Insert a test admin user (replace with your actual auth user ID)
INSERT INTO public.users (id, email, name, role, location) VALUES 
('your-auth-user-id-here', '<EMAIL>', 'Test Admin', 'admin', 'Damascus, Syria');

-- Insert a test expert user
INSERT INTO public.users (id, email, name, role, location, bio) VALUES 
('another-auth-user-id', '<EMAIL>', 'Test Expert', 'expert', 'Damascus, Syria', 'Software development expert');

-- Insert expert profile for the expert user
INSERT INTO public.experts (user_id, expertise_areas, experience_years) VALUES 
('another-auth-user-id', '[{"category": "تطوير البرمجيات", "skills": ["React", "Node.js", "TypeScript"], "proficiencyLevel": "expert", "yearsOfExperience": 5}]', 5);
```

## Troubleshooting

If you encounter any issues:

1. Check that all environment variables are set correctly in `.env`
2. Verify that the database schema was applied without errors
3. Ensure storage buckets are created and policies are applied
4. Check the browser console for any JavaScript errors
5. Verify that RLS (Row Level Security) is enabled on all tables