#!/usr/bin/env node

/**
 * Test script for Problem Submission Form Database Integration
 * Tests the complete flow from form submission to database storage
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Test data
const testProblemData = {
  title: 'مشكلة تقنية في نظام إدارة المرضى - اختبار',
  description: 'هذه مشكلة تقنية تجريبية لاختبار النظام. يواجه المستخدمون مشكلة في تسجيل الدخول إلى نظام إدارة المرضى، حيث تظهر رسالة خطأ عند محاولة الدخول باستخدام بيانات الاعتماد الصحيحة. المشكلة تحدث بشكل متقطع وتؤثر على حوالي 30% من المستخدمين.',
  category: 'أنظمة إدارة المحتوى (CMS)',
  sector: 'وزارة الصحة',
  urgency: 'high',
  tags: ['تسجيل الدخول', 'نظام إدارة المرضى', 'مصادقة'],
  attachments: []
};

async function testProblemSubmission() {
  console.log('🧪 بدء اختبار إرسال المشاكل...\n');

  try {
    // Test 1: Create a test user (simulate authenticated user)
    console.log('1️⃣ اختبار إنشاء مستخدم تجريبي...');
    
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: `test-${Date.now()}@example.com`,
      password: 'testpassword123',
      options: {
        data: {
          name: 'مستخدم تجريبي',
          location: 'دمشق، سوريا'
        }
      }
    });

    if (authError) {
      console.log('⚠️ تخطي إنشاء المستخدم (قد يكون موجود مسبقاً)');
      console.log('   استخدام مستخدم افتراضي للاختبار...');
    } else {
      console.log('✅ تم إنشاء المستخدم التجريبي بنجاح');
    }

    // Test 2: Get existing user for testing
    console.log('\n2️⃣ البحث عن مستخدم للاختبار...');
    
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .limit(1);

    if (usersError || !users || users.length === 0) {
      console.error('❌ لم يتم العثور على مستخدمين للاختبار');
      return;
    }

    const testUser = users[0];
    console.log(`✅ تم العثور على مستخدم للاختبار: ${testUser.name}`);

    // Test 3: Submit problem
    console.log('\n3️⃣ اختبار إرسال المشكلة...');
    
    const problemData = {
      ...testProblemData,
      submitted_by: testUser.id,
      status: 'open'
    };

    const { data: problem, error: problemError } = await supabase
      .from('problems')
      .insert(problemData)
      .select()
      .single();

    if (problemError) {
      console.error('❌ فشل في إرسال المشكلة:', problemError.message);
      return;
    }

    console.log('✅ تم إرسال المشكلة بنجاح');
    console.log(`   معرف المشكلة: ${problem.id}`);
    console.log(`   العنوان: ${problem.title}`);

    // Test 4: Validate problem data
    console.log('\n4️⃣ التحقق من صحة البيانات المحفوظة...');
    
    const { data: savedProblem, error: fetchError } = await supabase
      .from('problems')
      .select(`
        *,
        users!problems_submitted_by_fkey (name, organization)
      `)
      .eq('id', problem.id)
      .single();

    if (fetchError) {
      console.error('❌ فشل في استرجاع المشكلة:', fetchError.message);
      return;
    }

    // Validate required fields
    const validations = [
      { field: 'title', value: savedProblem.title, expected: problemData.title },
      { field: 'description', value: savedProblem.description, expected: problemData.description },
      { field: 'category', value: savedProblem.category, expected: problemData.category },
      { field: 'sector', value: savedProblem.sector, expected: problemData.sector },
      { field: 'urgency', value: savedProblem.urgency, expected: problemData.urgency },
      { field: 'status', value: savedProblem.status, expected: 'open' },
      { field: 'submitted_by', value: savedProblem.submitted_by, expected: testUser.id }
    ];

    let validationsPassed = 0;
    for (const validation of validations) {
      if (validation.value === validation.expected) {
        console.log(`   ✅ ${validation.field}: صحيح`);
        validationsPassed++;
      } else {
        console.log(`   ❌ ${validation.field}: خطأ (متوقع: ${validation.expected}, فعلي: ${validation.value})`);
      }
    }

    // Test tags array
    if (Array.isArray(savedProblem.tags) && savedProblem.tags.length === problemData.tags.length) {
      console.log('   ✅ tags: صحيح');
      validationsPassed++;
    } else {
      console.log('   ❌ tags: خطأ');
    }

    // Test attachments array
    if (Array.isArray(savedProblem.attachments)) {
      console.log('   ✅ attachments: صحيح (مصفوفة)');
      validationsPassed++;
    } else {
      console.log('   ❌ attachments: خطأ (ليس مصفوفة)');
    }

    // Test user relationship
    if (savedProblem.users && savedProblem.users.name) {
      console.log('   ✅ user relationship: صحيح');
      validationsPassed++;
    } else {
      console.log('   ❌ user relationship: خطأ');
    }

    console.log(`\n📊 نتائج التحقق: ${validationsPassed}/${validations.length + 3} اختبار نجح`);

    // Test 5: Test form validation constraints
    console.log('\n5️⃣ اختبار قيود التحقق...');
    
    const invalidTests = [
      {
        name: 'عنوان قصير جداً',
        data: { ...problemData, title: 'قصير' },
        expectedError: true
      },
      {
        name: 'وصف قصير جداً',
        data: { ...problemData, description: 'قصير' },
        expectedError: true
      },
      {
        name: 'فئة فارغة',
        data: { ...problemData, category: '' },
        expectedError: true
      },
      {
        name: 'قطاع فارغ',
        data: { ...problemData, sector: '' },
        expectedError: true
      }
    ];

    let constraintTests = 0;
    for (const test of invalidTests) {
      try {
        const { error } = await supabase
          .from('problems')
          .insert(test.data)
          .select()
          .single();

        if (error && test.expectedError) {
          console.log(`   ✅ ${test.name}: تم رفض البيانات الخاطئة بنجاح`);
          constraintTests++;
        } else if (!error && !test.expectedError) {
          console.log(`   ✅ ${test.name}: تم قبول البيانات الصحيحة`);
          constraintTests++;
        } else {
          console.log(`   ❌ ${test.name}: نتيجة غير متوقعة`);
        }
      } catch (err) {
        if (test.expectedError) {
          console.log(`   ✅ ${test.name}: تم رفض البيانات الخاطئة`);
          constraintTests++;
        } else {
          console.log(`   ❌ ${test.name}: خطأ غير متوقع`);
        }
      }
    }

    console.log(`\n📊 نتائج اختبار القيود: ${constraintTests}/${invalidTests.length} اختبار نجح`);

    // Test 6: Test search functionality
    console.log('\n6️⃣ اختبار وظيفة البحث...');
    
    const { data: searchResults, error: searchError } = await supabase
      .from('problems')
      .select('*')
      .textSearch('title', 'نظام إدارة', { config: 'arabic' });

    if (searchError) {
      console.log('   ❌ فشل في البحث:', searchError.message);
    } else {
      console.log(`   ✅ البحث يعمل بنجاح (${searchResults.length} نتيجة)`);
    }

    // Test 7: Cleanup - Delete test problem
    console.log('\n7️⃣ تنظيف البيانات التجريبية...');
    
    const { error: deleteError } = await supabase
      .from('problems')
      .delete()
      .eq('id', problem.id);

    if (deleteError) {
      console.log('   ⚠️ تحذير: لم يتم حذف المشكلة التجريبية:', deleteError.message);
    } else {
      console.log('   ✅ تم حذف المشكلة التجريبية بنجاح');
    }

    // Final summary
    console.log('\n🎉 اكتمل اختبار إرسال المشاكل!');
    console.log('📋 الملخص:');
    console.log(`   • إرسال المشكلة: ✅ نجح`);
    console.log(`   • التحقق من البيانات: ${validationsPassed}/${validations.length + 3} نجح`);
    console.log(`   • اختبار القيود: ${constraintTests}/${invalidTests.length} نجح`);
    console.log(`   • البحث: ${searchError ? '❌' : '✅'}`);
    console.log(`   • التنظيف: ${deleteError ? '⚠️' : '✅'}`);

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
    console.error(error);
  }
}

// Run the test
testProblemSubmission().catch(console.error);