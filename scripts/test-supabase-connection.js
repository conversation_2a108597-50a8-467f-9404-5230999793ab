import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'https://xptegoszrnglzvfvvypq.supabase.co'
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwdGVnb3N6cm5nbHp2ZnZ2eXBxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM3MDU4NDcsImV4cCI6MjA2OTI4MTg0N30.VJBAOl8jkFbnnkjO9MtsDPviQGjOd9yIXS_2flY19wg'

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testConnection() {
  console.log('Testing Supabase connection...')
  
  try {
    // Test basic connection
    const { data, error } = await supabase.from('users').select('count').limit(1)
    
    if (error) {
      console.error('❌ Connection failed:', error.message)
      
      if (error.message.includes('relation "public.users" does not exist')) {
        console.log('📝 Database schema not applied yet. Please run the schema from supabase/schema.sql in your Supabase dashboard.')
      }
    } else {
      console.log('✅ Connection successful!')
      console.log('📊 Database is ready')
    }
    
    // Test auth
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    if (authError) {
      console.error('❌ Auth test failed:', authError.message)
    } else {
      console.log('✅ Auth system ready')
    }
    
  } catch (err) {
    console.error('❌ Unexpected error:', err.message)
  }
}

testConnection()