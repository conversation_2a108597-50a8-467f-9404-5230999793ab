#!/usr/bin/env node

/**
 * Syrian Identity Naming Consistency Codemod
 * 
 * This script ensures consistent naming across all Syrian identity variables:
 * - Converts legacy naming patterns to standardized format
 * - Updates CSS variables to use proper kebab-case with --syrian- prefix
 * - Updates TypeScript constants to use proper camelCase
 * - Maintains backward compatibility where possible
 * 
 * Usage: node scripts/codemod-syrian-naming.cjs
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

// Configuration
const config = {
  // Directories to process
  includeDirs: ['src', 'docs', '.kiro/specs'],
  // File extensions to process
  extensions: ['.ts', '.tsx', '.js', '.jsx', '.css', '.md'],
  // Files to exclude
  excludeFiles: ['node_modules', '.git', 'dist', 'build'],
  // Dry run mode (set to false to actually make changes)
  dryRun: false,
};

// Naming transformation rules
const transformationRules = {
  // CSS Variable transformations
  css: [
    // Legacy patterns to standardized format
    { from: /--qasioun-gold(?!-\d)/g, to: '--syrian-qasioun-gold-500' },
    { from: /--ebla-bronze(?!-\d)/g, to: '--syrian-ebla-blue-500' },
    { from: /--palmyra-sand(?!-\d)/g, to: '--syrian-palmyra-stone-500' },
    { from: /--orontes-green(?!-\d)/g, to: '--syrian-umayyad-green-500' },
    { from: /--mediterranean-blue(?!-\d)/g, to: '--syrian-ebla-blue-500' },
    { from: /--basalt-black(?!-\d)/g, to: '--syrian-palmyra-stone-900' },
    
    // Ensure proper --syrian- prefix
    { from: /--(?!syrian-)([a-z-]+)-(gold|red|green|stone|blue|purple)(?!-\d)/g, to: '--syrian-$1-$2-500' },
    
    // Fix any double prefixes
    { from: /--syrian-syrian-/g, to: '--syrian-' },
  ],
  
  // TypeScript/JavaScript transformations
  typescript: [
    // Legacy camelCase to proper camelCase
    { from: /\bqasioungold\b/g, to: 'qasiounGold' },
    { from: /\beblabronze\b/g, to: 'eblaBlue' },
    { from: /\bpalmirasand\b/g, to: 'palmyraStone' },
    { from: /\borontesgreen\b/g, to: 'umayyadGreen' },
    { from: /\bmediterraneanblue\b/g, to: 'eblaBlue' },
    { from: /\bbasaltblack\b/g, to: 'palmyraStone' },
    
    // Ensure proper Syrian color references
    { from: /\b(qasioun|damascus|umayyad|palmyra|ebla|heritage)([A-Z][a-z]+)\b/g, to: '$1$2' },
  ],
  
  // Markdown documentation transformations
  markdown: [
    // Update documentation examples
    { from: /`--qasioun-gold`/g, to: '`--syrian-qasioun-gold-500`' },
    { from: /`qasioungold`/g, to: '`qasiounGold`' },
    { from: /qasioun-gold(?!-\d)/g, to: 'syrian-qasioun-gold-500' },
  ],
};

// Statistics tracking
const stats = {
  filesProcessed: 0,
  filesChanged: 0,
  transformationsApplied: 0,
  errors: 0,
  changes: [],
};

/**
 * Get all files recursively from a directory
 */
async function getAllFiles(dir, allFiles = []) {
  try {
    const files = await readdir(dir);
    
    for (const file of files) {
      const fullPath = path.join(dir, file);
      
      // Skip excluded files/directories
      if (config.excludeFiles.some(exclude => fullPath.includes(exclude))) {
        continue;
      }
      
      const fileStat = await stat(fullPath);
      
      if (fileStat.isDirectory()) {
        await getAllFiles(fullPath, allFiles);
      } else if (config.extensions.some(ext => fullPath.endsWith(ext))) {
        allFiles.push(fullPath);
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
    stats.errors++;
  }
  
  return allFiles;
}

/**
 * Apply transformations to file content
 */
function applyTransformations(content, filePath) {
  let transformedContent = content;
  let changesInFile = 0;
  const fileChanges = [];
  
  // Determine file type
  const ext = path.extname(filePath);
  let rules = [];
  
  if (['.css'].includes(ext)) {
    rules = transformationRules.css;
  } else if (['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
    rules = transformationRules.typescript;
  } else if (['.md'].includes(ext)) {
    rules = transformationRules.markdown;
  }
  
  // Apply transformation rules
  for (const rule of rules) {
    const matches = transformedContent.match(rule.from);
    if (matches) {
      const beforeTransform = transformedContent;
      transformedContent = transformedContent.replace(rule.from, rule.to);
      
      if (beforeTransform !== transformedContent) {
        changesInFile += matches.length;
        fileChanges.push({
          rule: rule.from.toString(),
          replacement: rule.to,
          matches: matches.length,
        });
      }
    }
  }
  
  if (changesInFile > 0) {
    stats.transformationsApplied += changesInFile;
    stats.changes.push({
      file: filePath,
      changesCount: changesInFile,
      changes: fileChanges,
    });
  }
  
  return {
    content: transformedContent,
    hasChanges: changesInFile > 0,
  };
}

/**
 * Process a single file
 */
async function processFile(filePath) {
  try {
    stats.filesProcessed++;
    
    const originalContent = await readFile(filePath, 'utf8');
    const { content: transformedContent, hasChanges } = applyTransformations(originalContent, filePath);
    
    if (hasChanges) {
      stats.filesChanged++;
      
      if (!config.dryRun) {
        await writeFile(filePath, transformedContent, 'utf8');
        console.log(`✅ Updated: ${filePath}`);
      } else {
        console.log(`🔍 Would update: ${filePath}`);
      }
    }
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    stats.errors++;
  }
}

/**
 * Generate codemod report
 */
function generateReport() {
  const reportContent = `# Syrian Identity Naming Codemod Report

## Summary

- **Files Processed**: ${stats.filesProcessed}
- **Files Changed**: ${stats.filesChanged}
- **Total Transformations**: ${stats.transformationsApplied}
- **Errors**: ${stats.errors}
- **Mode**: ${config.dryRun ? 'Dry Run' : 'Applied Changes'}

## Files Changed

${stats.changes.map(change => `
### ${change.file}
- **Changes**: ${change.changesCount}
${change.changes.map(c => `- ${c.rule} → ${c.replacement} (${c.matches} matches)`).join('\n')}
`).join('\n')}

Generated on: ${new Date().toISOString()}
`;

  return reportContent;
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Starting Syrian Identity Naming Codemod...');
  console.log(`Mode: ${config.dryRun ? 'DRY RUN' : 'APPLY CHANGES'}`);
  console.log('');
  
  try {
    // Get all files to process
    let allFiles = [];
    for (const dir of config.includeDirs) {
      if (fs.existsSync(dir)) {
        const dirFiles = await getAllFiles(dir);
        allFiles = allFiles.concat(dirFiles);
      }
    }
    
    console.log(`📁 Found ${allFiles.length} files to process`);
    console.log('');
    
    // Process each file
    for (const filePath of allFiles) {
      await processFile(filePath);
    }
    
    // Generate and save report
    const report = generateReport();
    const reportPath = 'codemod-report.md';
    
    if (!config.dryRun) {
      await writeFile(reportPath, report, 'utf8');
    }
    
    console.log('');
    console.log('📊 Codemod Complete!');
    console.log(`📄 Report saved to: ${reportPath}`);
    console.log('');
    console.log('Summary:');
    console.log(`- Files processed: ${stats.filesProcessed}`);
    console.log(`- Files changed: ${stats.filesChanged}`);
    console.log(`- Transformations applied: ${stats.transformationsApplied}`);
    console.log(`- Errors: ${stats.errors}`);
    
    if (config.dryRun) {
      console.log('');
      console.log('🔍 This was a dry run. To apply changes, set dryRun: false in the config.');
    }
    
  } catch (error) {
    console.error('❌ Codemod failed:', error.message);
    process.exit(1);
  }
}

// Run the codemod
if (require.main === module) {
  main();
}

module.exports = {
  applyTransformations,
  transformationRules,
  config,
};
