#!/usr/bin/env node

/**
 * Navigation Flow Test
 * Tests the complete navigation and routing system
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Navigation Flow Test');
console.log('========================\n');

// Test results tracking
let totalTests = 0;
let passedTests = 0;
let failedTests = 0;

function runTest(testName, testFn) {
  totalTests++;
  try {
    const result = testFn();
    if (result) {
      console.log(`✅ ${testName}`);
      passedTests++;
    } else {
      console.log(`❌ ${testName}`);
      failedTests++;
    }
  } catch (error) {
    console.log(`❌ ${testName} - Error: ${error.message}`);
    failedTests++;
  }
}

// Test 1: All route components are properly imported in App.tsx
runTest('All route components imported in App.tsx', () => {
  const appContent = fs.readFileSync('src/App.tsx', 'utf8');
  const requiredImports = [
    'ExpertMatchingPreferencesPage',
    'Index',
    'Problems',
    'Experts',
    'Login',
    'Register',
    'NotFound'
  ];
  
  return requiredImports.every(component => 
    appContent.includes(component)
  );
});

// Test 2: All lazy routes are properly exported
runTest('All lazy routes properly exported', () => {
  const lazyRoutesContent = fs.readFileSync('src/routes/lazyRoutes.tsx', 'utf8');
  const requiredExports = [
    'export const Index',
    'export const Problems',
    'export const Experts',
    'export const ExpertMatchingPreferencesPage',
    'export const Login',
    'export const Register',
    'export const NotFound'
  ];
  
  return requiredExports.every(exportStatement => 
    lazyRoutesContent.includes(exportStatement)
  );
});

// Test 3: Protected routes are properly wrapped
runTest('Protected routes use ProtectedRoute component', () => {
  const protectedPages = [
    'src/pages/Profile.tsx',
    'src/pages/Settings.tsx',
    'src/pages/ExpertDashboardPage.tsx',
    'src/pages/ExpertProfileCreate.tsx'
  ];
  
  return protectedPages.every(page => {
    if (!fs.existsSync(page)) return false;
    const content = fs.readFileSync(page, 'utf8');
    return content.includes('ProtectedRoute');
  });
});

// Test 4: Authentication imports are consistent
runTest('Authentication imports are consistent', () => {
  const pages = [
    'src/pages/ExpertDashboardPage.tsx',
    'src/pages/ExpertProfileCreate.tsx',
    'src/pages/ExpertMatchingPreferencesPage.tsx',
    'src/pages/ProblemDetail.tsx'
  ];
  
  return pages.every(page => {
    if (!fs.existsSync(page)) return false;
    const content = fs.readFileSync(page, 'utf8');
    // Should use useAuthContext, not useAuth
    return !content.includes('useAuth') || content.includes('useAuthContext');
  });
});

// Test 5: Navigation links are present in header
runTest('Navigation links present in header', () => {
  const headerContent = fs.readFileSync('src/components/layout/Header.tsx', 'utf8');
  const requiredLinks = [
    'to="/"',
    'to="/problems"',
    'to="/experts"',
    'to="/search"'
  ];
  
  return requiredLinks.every(link => 
    headerContent.includes(link)
  );
});

// Test 6: Expert matching preferences route is accessible
runTest('Expert matching preferences route accessible', () => {
  const appContent = fs.readFileSync('src/App.tsx', 'utf8');
  const dashboardContent = fs.readFileSync('src/pages/ExpertDashboardPage.tsx', 'utf8');
  
  // Check route is defined in App.tsx
  const routeDefined = appContent.includes('path="/experts/matching-preferences"');
  
  // Check link exists in expert dashboard
  const linkExists = dashboardContent.includes('/experts/matching-preferences');
  
  return routeDefined && linkExists;
});

// Test 7: Error boundaries are properly configured
runTest('Error boundaries properly configured', () => {
  const appContent = fs.readFileSync('src/App.tsx', 'utf8');
  
  // Check that routes are wrapped with RouteErrorBoundary
  const hasErrorBoundaries = appContent.includes('RouteErrorBoundary');
  const hasLazyErrorBoundary = appContent.includes('LazyLoadErrorBoundary');
  
  return hasErrorBoundaries && hasLazyErrorBoundary;
});

// Test 8: All page components exist
runTest('All page components exist', () => {
  const requiredPages = [
    'src/pages/Index.tsx',
    'src/pages/Problems.tsx',
    'src/pages/Experts.tsx',
    'src/pages/ExpertDashboardPage.tsx',
    'src/pages/ExpertMatchingPreferencesPage.tsx',
    'src/pages/Profile.tsx',
    'src/pages/Settings.tsx',
    'src/pages/NotFound.tsx',
    'src/pages/auth/Login.tsx',
    'src/pages/auth/Register.tsx'
  ];
  
  return requiredPages.every(page => fs.existsSync(page));
});

// Test 9: Route preloading is configured
runTest('Route preloading configured', () => {
  const lazyRoutesContent = fs.readFileSync('src/routes/lazyRoutes.tsx', 'utf8');
  const appContent = fs.readFileSync('src/App.tsx', 'utf8');
  
  // Check preload strategies exist
  const hasPreloadStrategies = lazyRoutesContent.includes('preloadStrategies');
  
  // Check component preloader is set up
  const hasPreloaderSetup = appContent.includes('setupNavigationPreloading');
  
  return hasPreloadStrategies && hasPreloaderSetup;
});

// Test 10: Authentication provider is properly configured
runTest('Authentication provider properly configured', () => {
  const appContent = fs.readFileSync('src/App.tsx', 'utf8');
  const authProviderContent = fs.readFileSync('src/components/auth/AuthProvider.tsx', 'utf8');
  
  // Check AuthProvider is used in App
  const hasAuthProvider = appContent.includes('<AuthProvider>');
  
  // Check ProtectedRoute component exists
  const hasProtectedRoute = authProviderContent.includes('export function ProtectedRoute');
  
  return hasAuthProvider && hasProtectedRoute;
});

console.log('\n📊 Test Results');
console.log('================');
console.log(`Total Tests: ${totalTests}`);
console.log(`Passed: ${passedTests}`);
console.log(`Failed: ${failedTests}`);
console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

if (failedTests === 0) {
  console.log('\n🎉 All navigation and routing tests passed!');
  console.log('✅ Task 8: Fix Navigation and Routing Errors - COMPLETED');
} else {
  console.log('\n⚠️  Some tests failed. Please review the issues above.');
}