/**
 * Test Expert Matching System
 * Creates sample data and tests the matching functionality
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Sample data
const sampleUsers = [
  {
    email: '<EMAIL>',
    name: 'أحمد محمد',
    role: 'expert',
    location: 'Damascus, Syria',
    organization: 'Syrian Tech Solutions',
    position: 'Senior Software Developer'
  },
  {
    email: '<EMAIL>',
    name: 'فاطمة أحمد',
    role: 'expert',
    location: 'Aleppo, Syria',
    organization: 'Digital Innovation Hub',
    position: 'AI/ML Specialist'
  },
  {
    email: '<EMAIL>',
    name: 'محمد علي',
    role: 'expert',
    location: 'Homs, Syria',
    organization: 'CyberSec Syria',
    position: 'Cybersecurity Expert'
  }
];

const sampleExpertProfiles = [
  {
    expertise_areas: [
      { category: 'Software Development', level: 'expert' },
      { category: 'Web Development', level: 'advanced' },
      { category: 'Mobile Development', level: 'intermediate' }
    ],
    experience_years: 8,
    availability: 'available',
    rating: 4.5,
    total_contributions: 25,
    success_rate: 88.5,
    response_time_hours: 12
  },
  {
    expertise_areas: [
      { category: 'AI/Machine Learning', level: 'expert' },
      { category: 'Data Analysis', level: 'advanced' },
      { category: 'Python Development', level: 'expert' }
    ],
    experience_years: 6,
    availability: 'available',
    rating: 4.8,
    total_contributions: 18,
    success_rate: 92.3,
    response_time_hours: 8
  },
  {
    expertise_areas: [
      { category: 'Cybersecurity', level: 'expert' },
      { category: 'Network Security', level: 'advanced' },
      { category: 'Penetration Testing', level: 'expert' }
    ],
    experience_years: 10,
    availability: 'busy',
    rating: 4.7,
    total_contributions: 32,
    success_rate: 90.1,
    response_time_hours: 24
  }
];

const sampleProblems = [
  {
    title: 'تطوير نظام إدارة المستشفيات الذكي',
    description: 'نحتاج إلى تطوير نظام شامل لإدارة المستشفيات يتضمن إدارة المرضى، المواعيد، والسجلات الطبية مع دعم الذكاء الاصطناعي للتشخيص المساعد.',
    category: 'Software Development',
    sector: 'Healthcare',
    urgency: 'high',
    status: 'open',
    tags: ['healthcare', 'ai', 'database', 'web-app']
  },
  {
    title: 'تحليل البيانات التعليمية وتطوير نموذج التنبؤ',
    description: 'مطلوب تحليل بيانات الطلاب الأكاديمية وتطوير نموذج ذكي للتنبؤ بالأداء الأكاديمي وتقديم توصيات شخصية للتحسين.',
    category: 'AI/Machine Learning',
    sector: 'Education',
    urgency: 'medium',
    status: 'open',
    tags: ['machine-learning', 'data-analysis', 'education', 'python']
  },
  {
    title: 'تأمين الشبكة الحكومية ضد الهجمات السيبرانية',
    description: 'تقييم أمان الشبكة الحكومية وتطوير استراتيجية شاملة للحماية من الهجمات السيبرانية مع تطبيق أفضل الممارسات الأمنية.',
    category: 'Cybersecurity',
    sector: 'Government',
    urgency: 'critical',
    status: 'open',
    tags: ['cybersecurity', 'network-security', 'government', 'penetration-testing']
  }
];

async function createSampleData() {
  console.log('🚀 Creating sample data for expert matching system...');

  try {
    // Create sample users (this would normally be done through auth signup)
    console.log('📝 Creating sample users...');
    const createdUsers = [];
    
    for (const userData of sampleUsers) {
      // In a real scenario, users would be created through Supabase Auth
      // For testing, we'll insert directly into the users table
      const { data: user, error } = await supabase
        .from('users')
        .insert({
          id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          ...userData
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating user:', error);
        continue;
      }

      createdUsers.push(user);
      console.log(`✅ Created user: ${user.name}`);
    }

    // Create expert profiles
    console.log('👨‍💼 Creating expert profiles...');
    const createdExperts = [];

    for (let i = 0; i < createdUsers.length; i++) {
      const user = createdUsers[i];
      const expertData = sampleExpertProfiles[i];

      const { data: expert, error } = await supabase
        .from('experts')
        .insert({
          user_id: user.id,
          ...expertData
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating expert profile:', error);
        continue;
      }

      createdExperts.push(expert);
      console.log(`✅ Created expert profile for: ${user.name}`);
    }

    // Create expert matching preferences
    console.log('⚙️ Creating expert matching preferences...');
    
    const preferences = [
      {
        preferred_problem_categories: ['Software Development', 'Web Development', 'Mobile Development'],
        preferred_sectors: ['Healthcare', 'Education', 'Finance'],
        max_problems_per_month: 8,
        min_compensation: 500,
        response_time_preference: 12
      },
      {
        preferred_problem_categories: ['AI/Machine Learning', 'Data Analysis'],
        preferred_sectors: ['Education', 'Healthcare', 'Research'],
        max_problems_per_month: 6,
        min_compensation: 800,
        response_time_preference: 8
      },
      {
        preferred_problem_categories: ['Cybersecurity', 'Network Security'],
        preferred_sectors: ['Government', 'Finance', 'Defense'],
        max_problems_per_month: 5,
        min_compensation: 1000,
        response_time_preference: 24
      }
    ];

    for (let i = 0; i < createdExperts.length; i++) {
      const expert = createdExperts[i];
      const prefData = preferences[i];

      const { error } = await supabase
        .from('expert_matching_preferences')
        .insert({
          expert_id: expert.id,
          ...prefData
        });

      if (error) {
        console.error('Error creating preferences:', error);
        continue;
      }

      console.log(`✅ Created preferences for expert: ${expert.id}`);
    }

    // Create sample problems
    console.log('🔧 Creating sample problems...');
    const createdProblems = [];

    for (const problemData of sampleProblems) {
      const { data: problem, error } = await supabase
        .from('problems')
        .insert({
          ...problemData,
          submitted_by: createdUsers[0].id // Use first user as submitter
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating problem:', error);
        continue;
      }

      createdProblems.push(problem);
      console.log(`✅ Created problem: ${problem.title}`);
    }

    // Test the matching algorithm
    console.log('🎯 Testing expert matching algorithm...');
    
    for (const problem of createdProblems) {
      console.log(`\n🔍 Finding matches for: ${problem.title}`);
      
      const { data: matches, error } = await supabase
        .rpc('find_matching_experts', {
          p_problem_id: problem.id,
          p_limit: 5
        });

      if (error) {
        console.error('Error finding matches:', error);
        continue;
      }

      console.log(`Found ${matches.length} matching experts:`);
      matches.forEach((match, index) => {
        console.log(`  ${index + 1}. Expert ${match.expert_id.slice(0, 8)}... - Score: ${(match.match_score * 100).toFixed(1)}%`);
        console.log(`     Reasons: ${match.match_reasons.join(', ')}`);
      });

      // Auto-assign experts
      const { data: assignedCount, error: assignError } = await supabase
        .rpc('auto_assign_experts_to_problem', {
          p_problem_id: problem.id,
          p_max_assignments: 2
        });

      if (assignError) {
        console.error('Error auto-assigning experts:', assignError);
      } else {
        console.log(`✅ Auto-assigned ${assignedCount} experts to this problem`);
      }
    }

    console.log('\n🎉 Sample data creation completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`- Created ${createdUsers.length} users`);
    console.log(`- Created ${createdExperts.length} expert profiles`);
    console.log(`- Created ${createdProblems.length} problems`);
    console.log(`- Set up matching preferences for all experts`);
    console.log(`- Tested automatic expert matching`);

  } catch (error) {
    console.error('❌ Error creating sample data:', error);
  }
}

async function testMatchingFunctions() {
  console.log('\n🧪 Testing matching functions...');

  try {
    // Test algorithm configuration
    const { data: config, error: configError } = await supabase
      .from('matching_algorithm_config')
      .select('*')
      .eq('is_active', true)
      .single();

    if (configError) {
      console.error('Error fetching algorithm config:', configError);
    } else {
      console.log('✅ Active algorithm configuration:');
      console.log(`   Name: ${config.name}`);
      console.log(`   Weights:`, config.weights);
    }

    // Test workload tracking
    const { data: workloads, error: workloadError } = await supabase
      .from('expert_workload')
      .select('*');

    if (workloadError) {
      console.error('Error fetching workloads:', workloadError);
    } else {
      console.log(`✅ Found ${workloads.length} workload records`);
    }

    // Test problem-expert matches
    const { data: matches, error: matchError } = await supabase
      .from('problem_expert_matches')
      .select('*');

    if (matchError) {
      console.error('Error fetching matches:', matchError);
    } else {
      console.log(`✅ Found ${matches.length} problem-expert matches`);
      
      const pendingMatches = matches.filter(m => m.expert_response_status === 'pending');
      console.log(`   - ${pendingMatches.length} pending responses`);
      
      const acceptedMatches = matches.filter(m => m.expert_response_status === 'accepted');
      console.log(`   - ${acceptedMatches.length} accepted matches`);
    }

  } catch (error) {
    console.error('❌ Error testing functions:', error);
  }
}

async function main() {
  console.log('🎯 Expert Matching System Test Suite');
  console.log('=====================================\n');

  await createSampleData();
  await testMatchingFunctions();

  console.log('\n✨ Test completed! You can now:');
  console.log('1. Log in as an expert to see matching preferences');
  console.log('2. View problems to see matched experts');
  console.log('3. Test the admin matching algorithm configuration');
  console.log('4. Check expert workload dashboards');
}

// Run the test
main().catch(console.error);