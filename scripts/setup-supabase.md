# Supabase Setup Guide

This guide will help you set up the Supabase backend infrastructure for the Technical Solutions Platform.

## Prerequisites

1. Create a Supabase account at [supabase.com](https://supabase.com)
2. Install Supabase CLI: `npm install -g supabase`

## Step 1: Create Supabase Project

1. Go to [supabase.com/dashboard](https://supabase.com/dashboard)
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - Name: "Technical Solutions Platform"
   - Database Password: (generate a strong password)
   - Region: Choose closest to your users
5. Click "Create new project"

## Step 2: Configure Environment Variables

1. Copy `.env.example` to `.env`
2. In your Supabase dashboard, go to Settings > API
3. Copy the following values to your `.env` file:
   - `VITE_SUPABASE_URL`: Your project URL
   - `VITE_SUPABASE_ANON_KEY`: Your anon/public key

## Step 3: Set Up Database Schema

1. In Supabase dashboard, go to SQL Editor
2. Copy the contents of `supabase/schema.sql`
3. Paste and run the SQL script
4. This will create all tables, indexes, and RLS policies

## Step 4: Configure Authentication

1. Go to Authentication > Settings in Supabase dashboard
2. Configure the following:

### Email Settings
- Enable "Enable email confirmations"
- Set site URL to your domain (e.g., `http://localhost:5173` for development)
- Add redirect URLs for production

### Social Providers (Optional)
Enable any social providers you want:
- Google OAuth
- GitHub OAuth
- Microsoft OAuth

### Security Settings
- Enable "Enable phone confirmations" if needed
- Set password requirements
- Configure session timeout

## Step 5: Set Up Storage Buckets

Run the following SQL in the SQL Editor to create storage buckets:

```sql
-- Create storage buckets
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('attachments', 'attachments', true, 10485760, ARRAY[
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'application/pdf', 'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain', 'application/zip'
  ]),
  ('avatars', 'avatars', true, 2097152, ARRAY[
    'image/jpeg', 'image/png', 'image/gif', 'image/webp'
  ]),
  ('presentations', 'presentations', true, 52428800, ARRAY[
    'application/pdf', 'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  ]),
  ('webinar-recordings', 'webinar-recordings', true, 1073741824, ARRAY[
    'video/mp4', 'video/webm', 'video/quicktime'
  ]);
```

## Step 6: Configure Storage Policies

Add these RLS policies for storage in SQL Editor:

```sql
-- Attachments bucket policies
CREATE POLICY "Anyone can view attachments" ON storage.objects
  FOR SELECT USING (bucket_id = 'attachments');

CREATE POLICY "Authenticated users can upload attachments" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'attachments' AND 
    auth.role() = 'authenticated'
  );

CREATE POLICY "Users can update own attachments" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'attachments' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete own attachments" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'attachments' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Avatars bucket policies
CREATE POLICY "Anyone can view avatars" ON storage.objects
  FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Users can upload own avatar" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'avatars' AND 
    auth.role() = 'authenticated' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update own avatar" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'avatars' AND 
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Presentations bucket policies
CREATE POLICY "Anyone can view presentations" ON storage.objects
  FOR SELECT USING (bucket_id = 'presentations');

CREATE POLICY "Admins can manage presentations" ON storage.objects
  FOR ALL USING (
    bucket_id = 'presentations' AND
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Webinar recordings policies
CREATE POLICY "Anyone can view webinar recordings" ON storage.objects
  FOR SELECT USING (bucket_id = 'webinar-recordings');

CREATE POLICY "Admins can manage webinar recordings" ON storage.objects
  FOR ALL USING (
    bucket_id = 'webinar-recordings' AND
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );
```

## Step 7: Test the Setup

1. Start your development server: `npm run dev`
2. Try creating a user account
3. Check that the user appears in the `users` table
4. Test file upload functionality

## Step 8: Configure Real-time (Optional)

If you need real-time features:

1. Go to Database > Replication in Supabase dashboard
2. Enable replication for tables that need real-time updates:
   - `problems`
   - `solutions`
   - `webinars`

## Security Checklist

- [ ] RLS is enabled on all tables
- [ ] Appropriate policies are in place
- [ ] Storage buckets have proper access controls
- [ ] Environment variables are secure
- [ ] Database passwords are strong
- [ ] Email confirmation is enabled
- [ ] Rate limiting is configured

## Production Considerations

1. **Custom Domain**: Set up a custom domain for your Supabase project
2. **SSL Certificates**: Ensure SSL is properly configured
3. **Backup Strategy**: Set up automated backups
4. **Monitoring**: Enable logging and monitoring
5. **Performance**: Monitor query performance and add indexes as needed

## Troubleshooting

### Common Issues

1. **RLS Policies**: If you can't access data, check RLS policies
2. **CORS Issues**: Make sure your domain is added to allowed origins
3. **File Upload Issues**: Check storage bucket policies and file size limits
4. **Authentication Issues**: Verify redirect URLs and email settings

### Useful SQL Queries

```sql
-- Check user roles
SELECT id, email, role FROM public.users;

-- Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies WHERE schemaname = 'public';

-- Check storage usage
SELECT bucket_id, count(*), sum(metadata->>'size')::bigint as total_size 
FROM storage.objects GROUP BY bucket_id;
```

## Next Steps

After completing this setup:

1. Test all authentication flows
2. Verify file upload/download works
3. Test RLS policies with different user roles
4. Set up monitoring and alerts
5. Configure backup procedures

Your Supabase backend infrastructure is now ready for the Technical Solutions Platform!