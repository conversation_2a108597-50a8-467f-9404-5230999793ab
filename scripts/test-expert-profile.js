#!/usr/bin/env node

/**
 * Test script for Expert Profile Database Integration
 * Tests the complete flow from profile creation to database storage and retrieval
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Test data
const testExpertData = {
  expertise_areas: [
    {
      category: 'تطوير البرمجيات',
      skills: ['JavaScript', 'React', 'Node.js', 'TypeScript'],
      proficiencyLevel: 'expert',
      yearsOfExperience: 5
    },
    {
      category: 'الذكاء الاصطناعي',
      skills: ['Python', 'TensorFlow', 'Machine Learning', 'Deep Learning'],
      proficiencyLevel: 'advanced',
      yearsOfExperience: 3
    }
  ],
  experience_years: 8,
  availability: 'available',
  response_time_hours: 12,
  portfolio: [
    {
      title: 'نظام إدارة المستشفيات',
      description: 'نظام شامل لإدارة المستشفيات يتضمن إدارة المرضى والمواعيد والفواتير',
      technologies: ['React', 'Node.js', 'PostgreSQL', 'Docker'],
      url: 'https://example.com/hospital-system',
      completedAt: '2023-12-01'
    },
    {
      title: 'تطبيق التجارة الإلكترونية',
      description: 'تطبيق متكامل للتجارة الإلكترونية مع نظام دفع آمن وإدارة المخزون',
      technologies: ['Vue.js', 'Express.js', 'MongoDB', 'Stripe'],
      url: 'https://example.com/ecommerce-app',
      completedAt: '2023-08-15'
    }
  ],
  certifications: [
    {
      name: 'AWS Certified Solutions Architect',
      issuer: 'Amazon Web Services',
      issuedAt: '2023-06-01',
      expiresAt: '2026-06-01',
      credentialId: 'AWS-CSA-123456',
      url: 'https://aws.amazon.com/certification/verify'
    },
    {
      name: 'Google Cloud Professional Developer',
      issuer: 'Google Cloud',
      issuedAt: '2023-03-15',
      expiresAt: '2025-03-15',
      credentialId: 'GCP-PD-789012',
      url: 'https://cloud.google.com/certification/verify'
    }
  ]
};

async function testExpertProfileIntegration() {
  console.log('🧪 بدء اختبار ملف الخبير الشخصي...\n');

  try {
    // Test 1: Get or create a test user
    console.log('1️⃣ البحث عن مستخدم للاختبار...');
    
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .limit(1);

    if (usersError || !users || users.length === 0) {
      console.error('❌ لم يتم العثور على مستخدمين للاختبار');
      return;
    }

    const testUser = users[0];
    console.log(`✅ تم العثور على مستخدم للاختبار: ${testUser.name}`);

    // Test 2: Create expert profile
    console.log('\n2️⃣ اختبار إنشاء ملف الخبير الشخصي...');
    
    const expertProfileData = {
      user_id: testUser.id,
      ...testExpertData,
      rating: 0.0,
      total_contributions: 0,
      success_rate: 0.0
    };

    const { data: expertProfile, error: createError } = await supabase
      .from('experts')
      .insert(expertProfileData)
      .select(`
        *,
        users (*)
      `)
      .single();

    if (createError) {
      if (createError.message.includes('duplicate')) {
        console.log('⚠️ ملف الخبير موجود مسبقاً، سيتم اختبار التحديث...');
        
        // Test update instead
        const { data: updatedProfile, error: updateError } = await supabase
          .from('experts')
          .update({
            ...testExpertData,
            updated_at: new Date().toISOString()
          })
          .eq('user_id', testUser.id)
          .select(`
            *,
            users (*)
          `)
          .single();

        if (updateError) {
          console.error('❌ فشل في تحديث ملف الخبير:', updateError.message);
          return;
        }

        console.log('✅ تم تحديث ملف الخبير بنجاح');
        console.log(`   معرف الخبير: ${updatedProfile.id}`);
      } else {
        console.error('❌ فشل في إنشاء ملف الخبير:', createError.message);
        return;
      }
    } else {
      console.log('✅ تم إنشاء ملف الخبير بنجاح');
      console.log(`   معرف الخبير: ${expertProfile.id}`);
    }

    // Test 3: Retrieve expert profile with user data
    console.log('\n3️⃣ اختبار استرجاع ملف الخبير...');
    
    const { data: retrievedProfile, error: retrieveError } = await supabase
      .from('experts')
      .select(`
        *,
        users (*)
      `)
      .eq('user_id', testUser.id)
      .single();

    if (retrieveError) {
      console.error('❌ فشل في استرجاع ملف الخبير:', retrieveError.message);
      return;
    }

    console.log('✅ تم استرجاع ملف الخبير بنجاح');

    // Test 4: Validate data structure
    console.log('\n4️⃣ التحقق من صحة هيكل البيانات...');
    
    const validations = [
      { field: 'user_id', value: retrievedProfile.user_id, expected: testUser.id },
      { field: 'experience_years', value: retrievedProfile.experience_years, expected: testExpertData.experience_years },
      { field: 'availability', value: retrievedProfile.availability, expected: testExpertData.availability },
      { field: 'response_time_hours', value: retrievedProfile.response_time_hours, expected: testExpertData.response_time_hours }
    ];

    let validationsPassed = 0;
    for (const validation of validations) {
      if (validation.value === validation.expected) {
        console.log(`   ✅ ${validation.field}: صحيح`);
        validationsPassed++;
      } else {
        console.log(`   ❌ ${validation.field}: خطأ (متوقع: ${validation.expected}, فعلي: ${validation.value})`);
      }
    }

    // Test expertise areas structure
    if (Array.isArray(retrievedProfile.expertise_areas) && retrievedProfile.expertise_areas.length > 0) {
      console.log('   ✅ expertise_areas: صحيح (مصفوفة بها بيانات)');
      validationsPassed++;
      
      // Check first expertise area structure
      const firstArea = retrievedProfile.expertise_areas[0];
      if (firstArea.category && Array.isArray(firstArea.skills) && firstArea.proficiencyLevel && typeof firstArea.yearsOfExperience === 'number') {
        console.log('   ✅ expertise_areas structure: صحيح');
        validationsPassed++;
      } else {
        console.log('   ❌ expertise_areas structure: خطأ');
      }
    } else {
      console.log('   ❌ expertise_areas: خطأ (ليس مصفوفة أو فارغة)');
    }

    // Test portfolio structure
    if (Array.isArray(retrievedProfile.portfolio) && retrievedProfile.portfolio.length > 0) {
      console.log('   ✅ portfolio: صحيح (مصفوفة بها بيانات)');
      validationsPassed++;
    } else {
      console.log('   ❌ portfolio: خطأ');
    }

    // Test certifications structure
    if (Array.isArray(retrievedProfile.certifications) && retrievedProfile.certifications.length > 0) {
      console.log('   ✅ certifications: صحيح (مصفوفة بها بيانات)');
      validationsPassed++;
    } else {
      console.log('   ❌ certifications: خطأ');
    }

    // Test user relationship
    if (retrievedProfile.users && retrievedProfile.users.name) {
      console.log('   ✅ user relationship: صحيح');
      validationsPassed++;
    } else {
      console.log('   ❌ user relationship: خطأ');
    }

    console.log(`\n📊 نتائج التحقق: ${validationsPassed}/9 اختبار نجح`);

    // Test 5: Test expert directory query
    console.log('\n5️⃣ اختبار استعلام دليل الخبراء...');
    
    const { data: allExperts, error: directoryError } = await supabase
      .from('experts')
      .select(`
        *,
        users (*)
      `)
      .eq('is_deleted', false)
      .order('rating', { ascending: false });

    if (directoryError) {
      console.log('   ❌ فشل في استعلام دليل الخبراء:', directoryError.message);
    } else {
      console.log(`   ✅ دليل الخبراء يعمل بنجاح (${allExperts.length} خبير)`);
    }

    // Test 6: Test filtering by expertise
    console.log('\n6️⃣ اختبار الفلترة حسب الخبرة...');
    
    const { data: filteredExperts, error: filterError } = await supabase
      .from('experts')
      .select(`
        *,
        users (*)
      `)
      .contains('expertise_areas', [{ category: 'تطوير البرمجيات' }])
      .eq('is_deleted', false);

    if (filterError) {
      console.log('   ❌ فشل في فلترة الخبراء:', filterError.message);
    } else {
      console.log(`   ✅ فلترة الخبراء تعمل بنجاح (${filteredExperts.length} خبير)`);
    }

    // Test 7: Test availability filter
    console.log('\n7️⃣ اختبار فلترة التوفر...');
    
    const { data: availableExperts, error: availabilityError } = await supabase
      .from('experts')
      .select(`
        *,
        users (*)
      `)
      .eq('availability', 'available')
      .eq('is_deleted', false);

    if (availabilityError) {
      console.log('   ❌ فشل في فلترة التوفر:', availabilityError.message);
    } else {
      console.log(`   ✅ فلترة التوفر تعمل بنجاح (${availableExperts.length} خبير متاح)`);
    }

    // Test 8: Test rating and sorting
    console.log('\n8️⃣ اختبار الترتيب حسب التقييم...');
    
    const { data: topExperts, error: sortError } = await supabase
      .from('experts')
      .select(`
        *,
        users (*)
      `)
      .eq('is_deleted', false)
      .gte('rating', 0)
      .order('rating', { ascending: false })
      .limit(5);

    if (sortError) {
      console.log('   ❌ فشل في ترتيب الخبراء:', sortError.message);
    } else {
      console.log(`   ✅ ترتيب الخبراء يعمل بنجاح (أفضل ${topExperts.length} خبراء)`);
      topExperts.forEach((expert, index) => {
        console.log(`      ${index + 1}. ${expert.users.name} - تقييم: ${expert.rating}`);
      });
    }

    // Final summary
    console.log('\n🎉 اكتمل اختبار ملف الخبير الشخصي!');
    console.log('📋 الملخص:');
    console.log(`   • إنشاء/تحديث الملف: ✅ نجح`);
    console.log(`   • استرجاع البيانات: ✅ نجح`);
    console.log(`   • التحقق من الهيكل: ${validationsPassed}/9 نجح`);
    console.log(`   • دليل الخبراء: ${directoryError ? '❌' : '✅'}`);
    console.log(`   • فلترة الخبرة: ${filterError ? '❌' : '✅'}`);
    console.log(`   • فلترة التوفر: ${availabilityError ? '❌' : '✅'}`);
    console.log(`   • ترتيب التقييم: ${sortError ? '❌' : '✅'}`);

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
    console.error(error);
  }
}

// Run the test
testExpertProfileIntegration().catch(console.error);