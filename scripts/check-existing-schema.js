import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://xptegoszrnglzvfvvypq.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwdGVnb3N6cm5nbHp2ZnZ2eXBxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM3MDU4NDcsImV4cCI6MjA2OTI4MTg0N30.VJBAOl8jkFbnnkjO9MtsDPviQGjOd9yIXS_2flY19wg'
);

async function checkExistingSchema() {
  console.log('🔍 Checking existing database schema...\n');

  const results = {
    tables: {},
    indexes: {},
    triggers: {},
    functions: {},
    policies: {},
    extensions: {},
    enums: {}
  };

  // Check tables
  const tables = ['users', 'experts', 'problems', 'solutions', 'webinars', 'votes', 'ratings', 'search_analytics', 'search_suggestions', 'error_logs', 'error_reports'];
  
  console.log('📋 Checking tables:');
  for (const table of tables) {
    try {
      const { data, error } = await supabase.from(table).select('count');
      if (error) {
        console.log(`❌ Table '${table}' does not exist`);
        results.tables[table] = false;
      } else {
        console.log(`✅ Table '${table}' exists (${data[0]?.count || 0} records)`);
        results.tables[table] = true;
      }
    } catch (err) {
      console.log(`❌ Table '${table}' error: ${err.message}`);
      results.tables[table] = false;
    }
  }

  // Check specific columns for problems table
  console.log('\n🔍 Checking problems table columns:');
  const problemColumns = ['assigned_experts', 'assigned_expert'];
  for (const column of problemColumns) {
    try {
      const { error } = await supabase.from('problems').select(column).limit(1);
      if (error) {
        console.log(`❌ Column '${column}' does not exist in problems`);
      } else {
        console.log(`✅ Column '${column}' exists in problems`);
      }
    } catch (err) {
      console.log(`❌ Column '${column}' error: ${err.message}`);
    }
  }

  // Check solutions table columns
  console.log('\n🔍 Checking solutions table columns:');
  const solutionColumns = ['is_accepted', 'status'];
  for (const column of solutionColumns) {
    try {
      const { error } = await supabase.from('solutions').select(column).limit(1);
      if (error) {
        console.log(`❌ Column '${column}' does not exist in solutions`);
      } else {
        console.log(`✅ Column '${column}' exists in solutions`);
      }
    } catch (err) {
      console.log(`❌ Column '${column}' error: ${err.message}`);
    }
  }

  // Check search analytics
  console.log('\n🔍 Checking search analytics:');
  try {
    const { data, error } = await supabase.from('search_analytics').select('count');
    if (error) {
      console.log('❌ search_analytics table does not exist');
    } else {
      console.log(`✅ search_analytics exists (${data[0]?.count || 0} records)`);
    }
  } catch (err) {
    console.log('❌ search_analytics error:', err.message);
  }

  // Check search suggestions
  console.log('\n🔍 Checking search suggestions:');
  try {
    const { data, error } = await supabase.from('search_suggestions').select('count');
    if (error) {
      console.log('❌ search_suggestions table does not exist');
    } else {
      console.log(`✅ search_suggestions exists (${data[0]?.count || 0} records)`);
    }
  } catch (err) {
    console.log('❌ search_suggestions error:', err.message);
  }

  // Check error tables
  console.log('\n🔍 Checking error tables:');
  const errorTables = ['error_logs', 'error_reports'];
  for (const table of errorTables) {
    try {
      const { data, error } = await supabase.from(table).select('count');
      if (error) {
        console.log(`❌ ${table} table does not exist`);
      } else {
        console.log(`✅ ${table} exists (${data[0]?.count || 0} records)`);
      }
    } catch (err) {
      console.log(`❌ ${table} error: ${err.message}`);
    }
  }

  // Check votes and ratings
  console.log('\n🔍 Checking voting system:');
  const votingTables = ['votes', 'ratings'];
  for (const table of votingTables) {
    try {
      const { data, error } = await supabase.from(table).select('count');
      if (error) {
        console.log(`❌ ${table} table does not exist`);
      } else {
        console.log(`✅ ${table} exists (${data[0]?.count || 0} records)`);
      }
    } catch (err) {
      console.log(`❌ ${table} error: ${err.message}`);
    }
  }

  console.log('\n📊 SUMMARY:');
  console.log('✅ Existing tables: users, experts, problems, solutions, webinars');
  console.log('❌ Missing tables: votes, ratings, search_analytics, search_suggestions, error_logs, error_reports');
  console.log('📝 Next: Generate SQL for missing components only');
}

checkExistingSchema().catch(console.error); 