-- Database Schema Verification Script
-- Run this in Supabase SQL Editor to check if everything is set up correctly

-- Check all tables exist
SELECT 
    'TABLES CHECK' as check_type,
    table_name,
    CASE 
        WHEN table_name IN ('users', 'experts', 'problems', 'solutions', 'webinars', 'votes', 'ratings', 'search_analytics', 'search_suggestions', 'error_logs', 'error_reports') 
        THEN 'EXISTS' 
        ELSE 'MISSING' 
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
    AND table_name IN ('users', 'experts', 'problems', 'solutions', 'webinars', 'votes', 'ratings', 'search_analytics', 'search_suggestions', 'error_logs', 'error_reports')
ORDER BY table_name;

-- Check problems table columns
SELECT 
    'PROBLEMS COLUMNS' as check_type,
    column_name,
    data_type,
    'EXISTS' as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'problems' 
    AND column_name IN ('assigned_experts', 'status', 'urgency', 'category')
ORDER BY column_name;

-- Check solutions table columns
SELECT 
    'SOLUTIONS COLUMNS' as check_type,
    column_name,
    data_type,
    'EXISTS' as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'solutions' 
    AND column_name IN ('status', 'content', 'problem_id', 'expert_id')
ORDER BY column_name;

-- Check search suggestions data
SELECT 
    'SEARCH SUGGESTIONS' as check_type,
    COUNT(*) as suggestion_count,
    'DATA CHECK' as status
FROM public.search_suggestions 
WHERE is_active = true;

-- Check search analytics data
SELECT 
    'SEARCH ANALYTICS' as check_type,
    COUNT(*) as analytics_count,
    'DATA CHECK' as status
FROM public.search_analytics;

-- Check voting system tables
SELECT 
    'VOTING SYSTEM' as check_type,
    'votes' as table_name,
    COUNT(*) as record_count,
    'READY' as status
FROM public.votes

UNION ALL

SELECT 
    'VOTING SYSTEM' as check_type,
    'ratings' as table_name,
    COUNT(*) as record_count,
    'READY' as status
FROM public.ratings;

-- Check error tracking tables
SELECT 
    'ERROR TRACKING' as check_type,
    'error_logs' as table_name,
    COUNT(*) as record_count,
    'READY' as status
FROM public.error_logs

UNION ALL

SELECT 
    'ERROR TRACKING' as check_type,
    'error_reports' as table_name,
    COUNT(*) as record_count,
    'READY' as status
FROM public.error_reports;

-- Check RLS policies
SELECT 
    'RLS POLICIES' as check_type,
    policyname as policy_name,
    tablename as table_name,
    'ACTIVE' as status
FROM pg_policies 
WHERE schemaname = 'public'
    AND tablename IN ('votes', 'ratings', 'search_suggestions', 'error_logs', 'error_reports')
ORDER BY tablename, policyname;

-- Check indexes
SELECT 
    'INDEXES' as check_type,
    indexname as index_name,
    tablename as table_name,
    'EXISTS' as status
FROM pg_indexes 
WHERE schemaname = 'public' 
    AND indexname LIKE 'idx_%'
    AND tablename IN ('votes', 'ratings', 'search_suggestions', 'error_logs', 'error_reports')
ORDER BY tablename, indexname;

-- Final summary
SELECT 
    'SUMMARY' as check_type,
    'DATABASE STATUS' as item,
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_name IN ('users', 'experts', 'problems', 'solutions', 'webinars', 'votes', 'ratings', 'search_analytics', 'search_suggestions', 'error_logs', 'error_reports')) = 11
        THEN 'COMPLETE'
        ELSE 'INCOMPLETE'
    END as status; 