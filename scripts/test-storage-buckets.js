#!/usr/bin/env node

/**
 * Test script to verify Supabase storage buckets configuration
 * This script tests the three storage buckets: attachments, avatars, presentations
 */

import { createClient } from '@supabase/supabase-js'
import { readFileSync } from 'fs'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Load environment variables
const envPath = join(__dirname, '..', '.env')
let supabaseUrl, supabaseKey

try {
  const envContent = readFileSync(envPath, 'utf8')
  const envLines = envContent.split('\n')
  
  for (const line of envLines) {
    if (line.startsWith('VITE_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1]
    }
    if (line.startsWith('VITE_SUPABASE_ANON_KEY=')) {
      supabaseKey = line.split('=')[1]
    }
  }
} catch (error) {
  console.error('❌ Error reading .env file:', error.message)
  process.exit(1)
}

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase credentials in .env file')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseKey)

console.log('🧪 Testing Supabase Storage Buckets Configuration\n')

async function testStorageBuckets() {
  try {
    // Test 1: Check if buckets exist
    console.log('📋 Test 1: Checking bucket existence...')
    const { data: buckets, error: bucketsError } = await supabase
      .storage
      .listBuckets()

    if (bucketsError) {
      throw new Error(`Failed to list buckets: ${bucketsError.message}`)
    }

    const expectedBuckets = ['attachments', 'avatars', 'presentations']
    const existingBuckets = buckets.map(b => b.name)
    
    console.log('   Existing buckets:', existingBuckets.join(', '))
    
    for (const bucket of expectedBuckets) {
      if (existingBuckets.includes(bucket)) {
        console.log(`   ✅ ${bucket} bucket exists`)
      } else {
        console.log(`   ❌ ${bucket} bucket missing`)
      }
    }

    // Test 2: Check bucket configurations
    console.log('\n🔧 Test 2: Checking bucket configurations...')
    
    const { data: bucketDetails, error: detailsError } = await supabase
      .from('storage.buckets')
      .select('*')
      .in('name', expectedBuckets)

    if (detailsError) {
      console.log('   ⚠️  Could not fetch bucket details from database')
    } else {
      for (const bucket of bucketDetails) {
        console.log(`   📁 ${bucket.name}:`)
        console.log(`      - Public: ${bucket.public}`)
        console.log(`      - Size limit: ${formatBytes(bucket.file_size_limit)}`)
        console.log(`      - Allowed types: ${bucket.allowed_mime_types?.length || 0} types`)
        
        // Verify size limits
        const expectedLimits = {
          'attachments': 10485760,  // 10MB
          'avatars': 2097152,       // 2MB
          'presentations': 52428800 // 50MB
        }
        
        if (bucket.file_size_limit === expectedLimits[bucket.name]) {
          console.log(`      ✅ Size limit correct`)
        } else {
          console.log(`      ❌ Size limit incorrect (expected ${formatBytes(expectedLimits[bucket.name])})`)
        }
      }
    }

    // Test 3: Test file upload (create a small test file)
    console.log('\n📤 Test 3: Testing file upload...')
    
    const testFile = new Blob(['Test file content for storage verification'], { 
      type: 'text/plain' 
    })
    
    for (const bucketName of expectedBuckets) {
      const fileName = `test-${Date.now()}.txt`
      
      try {
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from(bucketName)
          .upload(fileName, testFile)

        if (uploadError) {
          console.log(`   ❌ ${bucketName}: Upload failed - ${uploadError.message}`)
          continue
        }

        console.log(`   ✅ ${bucketName}: Upload successful`)

        // Test public URL generation
        const { data: urlData } = supabase.storage
          .from(bucketName)
          .getPublicUrl(fileName)

        if (urlData.publicUrl) {
          console.log(`   ✅ ${bucketName}: Public URL generated`)
        } else {
          console.log(`   ❌ ${bucketName}: Public URL generation failed`)
        }

        // Clean up test file
        const { error: deleteError } = await supabase.storage
          .from(bucketName)
          .remove([fileName])

        if (!deleteError) {
          console.log(`   ✅ ${bucketName}: Test file cleaned up`)
        }

      } catch (error) {
        console.log(`   ❌ ${bucketName}: Test failed - ${error.message}`)
      }
    }

    // Test 4: Check storage policies
    console.log('\n🔒 Test 4: Checking storage policies...')
    
    try {
      const { data: policies, error: policiesError } = await supabase
        .from('pg_policies')
        .select('policyname, tablename')
        .eq('schemaname', 'storage')
        .eq('tablename', 'objects')

      if (policiesError) {
        console.log('   ⚠️  Could not fetch storage policies')
      } else {
        const policyCount = policies.length
        console.log(`   📋 Found ${policyCount} storage policies`)
        
        const expectedPolicies = [
          'Public read access',
          'Authenticated users can upload',
          'Users can update own',
          'Users can delete own'
        ]
        
        for (const expectedPolicy of expectedPolicies) {
          const found = policies.some(p => p.policyname.includes(expectedPolicy.toLowerCase()))
          if (found) {
            console.log(`   ✅ ${expectedPolicy} policies exist`)
          } else {
            console.log(`   ⚠️  ${expectedPolicy} policies may be missing`)
          }
        }
      }
    } catch (error) {
      console.log('   ⚠️  Could not check policies:', error.message)
    }

    // Test 5: Check storage statistics function
    console.log('\n📊 Test 5: Testing storage statistics...')
    
    try {
      const { data: stats, error: statsError } = await supabase
        .rpc('get_storage_stats')

      if (statsError) {
        console.log('   ❌ Storage stats function failed:', statsError.message)
      } else {
        console.log('   ✅ Storage stats function working')
        if (stats && stats.length > 0) {
          for (const stat of stats) {
            console.log(`   📁 ${stat.bucket_id}: ${stat.count} files, ${formatBytes(stat.total_size)}`)
          }
        } else {
          console.log('   📁 No files in storage yet')
        }
      }
    } catch (error) {
      console.log('   ❌ Storage stats test failed:', error.message)
    }

    console.log('\n🎉 Storage bucket testing completed!')
    console.log('\n📋 Summary:')
    console.log('   - attachments bucket: 10MB limit, documents and images')
    console.log('   - avatars bucket: 2MB limit, images only')
    console.log('   - presentations bucket: 50MB limit, presentations and documents')
    console.log('   - All buckets have public read access')
    console.log('   - Authenticated users can upload files')
    console.log('   - Users can manage their own files')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    process.exit(1)
  }
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Run the tests
testStorageBuckets()