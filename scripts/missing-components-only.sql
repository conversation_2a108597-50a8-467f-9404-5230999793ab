CREATE TABLE IF NOT EXISTS public.votes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    solution_id UUID REFERENCES public.solutions(id) ON DELETE CASCADE NOT NULL,
    vote_type TEXT CHECK (vote_type IN ('up', 'down')) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, solution_id)
);

CREATE TABLE IF NOT EXISTS public.ratings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    expert_id UUID REFERENCES public.experts(id) ON DELETE CASCADE NOT NULL,
    rated_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    solution_id UUID REFERENCES public.solutions(id) ON DELETE CASCADE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5) NOT NULL,
    feedback TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(expert_id, rated_by, solution_id)
);

CREATE TABLE IF NOT EXISTS public.search_suggestions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    suggestion_text TEXT NOT NULL UNIQUE,
    category TEXT,
    frequency INTEGER DEFAULT 1,
    language TEXT DEFAULT 'ar',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.error_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id),
    type TEXT NOT NULL,
    source TEXT NOT NULL,
    message TEXT NOT NULL,
    stack_trace TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    severity TEXT DEFAULT 'error',
    resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.error_reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    error_type TEXT,
    steps_to_reproduce TEXT,
    expected_behavior TEXT,
    actual_behavior TEXT,
    browser_info JSONB DEFAULT '{}'::jsonb,
    system_info JSONB DEFAULT '{}'::jsonb,
    attachments JSONB DEFAULT '[]'::jsonb,
    status TEXT DEFAULT 'open',
    priority TEXT DEFAULT 'medium',
    assigned_to UUID REFERENCES public.users(id),
    assigned_at TIMESTAMP WITH TIME ZONE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolution_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_votes_solution_id ON public.votes(solution_id);
CREATE INDEX IF NOT EXISTS idx_votes_user_id ON public.votes(user_id);
CREATE INDEX IF NOT EXISTS idx_ratings_expert_id ON public.ratings(expert_id);
CREATE INDEX IF NOT EXISTS idx_ratings_solution_id ON public.ratings(solution_id);

CREATE INDEX IF NOT EXISTS idx_search_suggestions_text ON public.search_suggestions(suggestion_text);
CREATE INDEX IF NOT EXISTS idx_search_suggestions_frequency ON public.search_suggestions(frequency DESC);
CREATE INDEX IF NOT EXISTS idx_search_suggestions_category ON public.search_suggestions(category);

CREATE INDEX IF NOT EXISTS idx_error_logs_type ON public.error_logs(type);
CREATE INDEX IF NOT EXISTS idx_error_logs_source ON public.error_logs(source);
CREATE INDEX IF NOT EXISTS idx_error_logs_severity ON public.error_logs(severity);
CREATE INDEX IF NOT EXISTS idx_error_logs_created_at ON public.error_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_error_logs_user_id ON public.error_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_error_logs_resolved ON public.error_logs(resolved);

CREATE INDEX IF NOT EXISTS idx_error_reports_status ON public.error_reports(status);
CREATE INDEX IF NOT EXISTS idx_error_reports_priority ON public.error_reports(priority);
CREATE INDEX IF NOT EXISTS idx_error_reports_assigned_to ON public.error_reports(assigned_to);
CREATE INDEX IF NOT EXISTS idx_error_reports_user_id ON public.error_reports(user_id);
CREATE INDEX IF NOT EXISTS idx_error_reports_created_at ON public.error_reports(created_at);

ALTER TABLE public.votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ratings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.search_suggestions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.error_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.error_reports ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage own votes" ON public.votes
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own ratings" ON public.ratings
    FOR ALL USING (auth.uid() = rated_by);

CREATE POLICY "Anyone can view active search suggestions" ON public.search_suggestions
    FOR SELECT USING (is_active = true);

CREATE POLICY "Users can view own error logs" ON public.error_logs
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert error logs" ON public.error_logs
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can manage own error reports" ON public.error_reports
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Admins can view all error logs" ON public.error_logs
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() 
                AND role = 'admin'
        )
    );

CREATE POLICY "Admins can view all error reports" ON public.error_reports
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() 
                AND role = 'admin'
        )
    );

INSERT INTO public.search_suggestions (suggestion_text, category, frequency, language) VALUES
('مشاكل تقنية', 'problem', 10, 'ar'),
('حلول برمجية', 'solution', 8, 'ar'),
('خبراء تقنيون', 'expert', 5, 'ar'),
('ندوات تعليمية', 'webinar', 3, 'ar'),
('Technical problems', 'problem', 10, 'en'),
('Software solutions', 'solution', 8, 'en'),
('Technical experts', 'expert', 5, 'en'),
('Educational webinars', 'webinar', 3, 'en')
ON CONFLICT (suggestion_text) DO NOTHING; 