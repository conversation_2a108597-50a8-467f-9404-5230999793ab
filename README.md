# Syrian Technical Solutions Platform 🇸🇾

A comprehensive platform connecting technical experts with government ministries to solve technical challenges and foster innovation in Syria.

## 🌟 Overview

The Syrian Technical Solutions Platform is a modern web application built with React, TypeScript, and Supabase that enables:

- **Government ministries** to submit technical problems and challenges
- **Technical experts** to provide solutions and expertise
- **Real-time collaboration** on problem-solving
- **Knowledge sharing** through webinars and documentation
- **Expert networking** and skill development

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- Supabase account
- Modern web browser

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd silia-tech-hub

# Install dependencies
npm install

# Copy environment variables
cp .env.example .env
```

### Configuration

1. **Update `.env` with your Supabase credentials:**
```bash
VITE_SUPABASE_URL=https://xptegoszrnglzvfvvypq.supabase.co
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here
```

2. **Apply database schema:**
   - Go to [Supabase Dashboard](https://supabase.com/dashboard/project/xptegoszrnglzvfvvypq)
   - Navigate to SQL Editor
   - Copy and execute content from `supabase/schema.sql`

3. **Create storage buckets:**
   - Create `attachments` bucket (public, 10MB limit)
   - Create `avatars` bucket (public, 2MB limit)
   - Apply storage policies from `docs/SETUP_AND_TESTING_GUIDE.md`

### Development

```bash
# Start development server
npm run dev

# Run tests
npm run test

# Build for production
npm run build
```

## 🏗️ Architecture

```mermaid
graph TB
    subgraph "Frontend"
        A[React + TypeScript]
        B[Tailwind CSS]
        C[shadcn/ui]
    end
    
    subgraph "Backend"
        D[Supabase Database]
        E[Supabase Auth]
        F[Supabase Storage]
        G[Real-time Subscriptions]
    end
    
    subgraph "Features"
        H[Problem Management]
        I[Expert Directory]
        J[Solution System]
        K[Search & Discovery]
    end
    
    A --> D
    E --> A
    F --> A
    G --> A
    D --> H
    D --> I
    D --> J
    D --> K
```

## ✨ Key Features

### 🔐 Authentication & Authorization
- Multi-role system (Expert, Ministry User, Admin)
- Row Level Security (RLS) policies
- Profile management with avatars
- Password reset functionality

### 🔧 Problem Management
- Rich problem submission with file attachments
- Advanced filtering and search
- Status tracking workflow
- Tag-based categorization

### 👨‍💻 Expert Directory
- Comprehensive expert profiles
- Skill and expertise management
- Portfolio showcase
- Rating and review system
- Availability tracking

### 💡 Solution System
- Solution submission with rich text
- Voting and rating system
- Status workflow management
- Implementation tracking

### 🔍 Search & Discovery
- Global search across all content
- Advanced filtering capabilities
- Search suggestions and autocomplete
- Relevance-based ranking

### 📁 File Management
- Drag-and-drop file upload
- Multiple file type support
- Progress tracking
- Secure storage with policies

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **shadcn/ui** component library
- **React Router** for navigation
- **React Hook Form** for forms
- **Zod** for validation

### Backend
- **Supabase** (PostgreSQL + Auth + Storage + Real-time)
- **Row Level Security** for authorization
- **Custom database functions** for complex operations

### Development Tools
- **MCP Server** for AI-assisted development
- **Vitest** for testing
- **Playwright** for E2E testing
- **ESLint** and **Prettier** for code quality

## 📊 Database Schema

### Core Tables
- `users` - User profiles and authentication
- `experts` - Expert-specific information
- `problems` - Technical problems and challenges
- `solutions` - Expert solutions and responses
- `webinars` - Educational content and presentations

### Security Features
- Row Level Security on all tables
- Soft delete functionality
- Audit logging with timestamps
- Role-based access policies

## 🧪 Testing

```bash
# Run unit tests
npm run test

# Run integration tests
npm run test:integration

# Run E2E tests
npm run test:e2e

# Generate coverage report
npm run test:coverage

# Test Supabase connection
node scripts/test-supabase-connection.js
```

## 📚 Documentation

- **[Database Architecture](docs/DATABASE_ARCHITECTURE.md)** - Complete database design and schema
- **[Setup & Testing Guide](docs/SETUP_AND_TESTING_GUIDE.md)** - Comprehensive setup instructions
- **[API Reference](docs/API_REFERENCE.md)** - Complete API documentation
- **[Project Summary](docs/PROJECT_SUMMARY.md)** - Detailed project overview

## 🔧 MCP Server Integration

The project includes MCP (Model Context Protocol) server integration for AI-assisted development:

```json
{
  "mcpServers": {
    "supabase": {
      "command": "npx",
      "args": ["-y", "@supabase/mcp-server-supabase@latest"],
      "env": {
        "SUPABASE_URL": "https://xptegoszrnglzvfvvypq.supabase.co",
        "SUPABASE_ANON_KEY": "your_anon_key",
        "SUPABASE_ACCESS_TOKEN": "your_personal_access_token"
      }
    }
  }
}
```

## 🚀 Deployment

### Production Build
```bash
npm run build
```

### Environment Variables
```bash
VITE_SUPABASE_URL=your_production_supabase_url
VITE_SUPABASE_ANON_KEY=your_production_anon_key
```

### Hosting Platforms
- **Vercel** (recommended)
- **Netlify**
- **AWS Amplify**
- **Firebase Hosting**

## 🔒 Security

### Implemented Security Features
- Row Level Security (RLS) policies
- Input validation and sanitization
- File upload restrictions
- Authentication with JWT tokens
- CORS configuration
- Rate limiting (planned)

### Security Best Practices
- Never commit sensitive credentials
- Use environment variables for configuration
- Regularly update dependencies
- Monitor for security vulnerabilities
- Implement proper error handling

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Code Standards
- Use TypeScript for all new code
- Follow existing code style
- Add proper documentation
- Include tests for new features
- Ensure responsive design

## 📈 Performance

### Optimization Features
- Code splitting with React.lazy
- Memoization with React.memo
- Virtual scrolling for large lists
- Image optimization
- Database query optimization
- Caching strategies

### Monitoring
- Error tracking with detailed logging
- Performance monitoring
- User analytics
- System health dashboards

## 🌍 Internationalization

### Language Support
- **Arabic** (primary) - RTL support
- **English** (secondary) - LTR support
- Localized date/time formatting
- Cultural considerations for UI/UX

### RTL Implementation
- Proper text direction handling
- Mirrored layouts for RTL
- Icon and image positioning
- Form field alignment

## 🔮 Future Roadmap

### Planned Features
- **Mobile application** (React Native)
- **Advanced AI integration** for problem categorization
- **Video conferencing** for webinars
- **Advanced analytics** dashboard
- **Government system** integrations
- **Multi-language** support expansion

### Technical Improvements
- GraphQL implementation
- Redis caching layer
- Microservices architecture
- Advanced monitoring
- Performance optimizations

## 📞 Support

### Getting Help
- Check the [documentation](docs/)
- Review [common issues](docs/SETUP_AND_TESTING_GUIDE.md#debugging-guide)
- Test your setup with provided scripts
- Check browser console for errors

### Reporting Issues
- Use GitHub Issues for bug reports
- Include detailed reproduction steps
- Provide environment information
- Include relevant error messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Supabase** for providing excellent backend infrastructure
- **shadcn/ui** for beautiful and accessible components
- **Tailwind CSS** for utility-first styling
- **React community** for amazing ecosystem
- **Syrian tech community** for inspiration and support

---

**Built with ❤️ for the Syrian technical community**

*Connecting expertise, solving challenges, building the future.*