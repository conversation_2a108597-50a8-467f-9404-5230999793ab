#!/usr/bin/env node

// Test MCP HTTP endpoint
import fetch from 'node-fetch';

async function testMCPEndpoint() {
  console.log('🧪 Testing MCP HTTP endpoint...');
  
  try {
    // Test if the server is responding
    const response = await fetch('http://localhost:8932/mcp', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 1,
        method: 'tools/list',
        params: {}
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ MCP endpoint is responding');
      console.log('📋 Available tools:', data.result?.tools?.length || 0);
      
      if (data.result?.tools) {
        data.result.tools.forEach(tool => {
          console.log(`  - ${tool.name}: ${tool.description}`);
        });
      }
    } else {
      console.log('❌ MCP endpoint returned error:', response.status, response.statusText);
    }
  } catch (error) {
    console.error('❌ Failed to connect to MCP endpoint:', error.message);
    console.log('💡 Make sure the Playwright MCP server is running on port 8932');
    console.log('   Run: node start-playwright-mcp.js');
  }
}

testMCPEndpoint();