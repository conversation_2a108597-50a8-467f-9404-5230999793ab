// Test script to verify Playwright MCP is working
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('Testing Playwright MCP connection...');

try {
  // Check if config file exists
  const configPath = path.join(__dirname, 'proxy-mcp.config.json');
  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
  
  console.log('✅ Proxy config file found and valid');
  console.log('Browser:', config.browser.browserName);
  console.log('Proxy server:', config.browser.launchOptions.proxy.server);
  console.log('Headless mode:', config.browser.launchOptions.headless);
  console.log('Server port:', config.server.port);
  
  // Check MCP config
  const mcpConfigPath = path.join(__dirname, '.kiro/settings/mcp.json');
  const mcpConfig = JSON.parse(fs.readFileSync(mcpConfigPath, 'utf8'));
  
  if (mcpConfig.mcpServers.playwright) {
    console.log('✅ Playwright MCP server configured');
    console.log('Command:', mcpConfig.mcpServers.playwright.command);
    console.log('Args:', mcpConfig.mcpServers.playwright.args.join(' '));
    console.log('Disabled:', mcpConfig.mcpServers.playwright.disabled);
  } else {
    console.log('❌ Playwright MCP server not found in config');
  }
  
  console.log('\n🎉 Playwright MCP setup appears to be working!');
  console.log('You should now be able to use Playwright tools in Kiro for:');
  console.log('- Web browsing and navigation');
  console.log('- Taking screenshots');
  console.log('- Clicking elements');
  console.log('- Filling forms');
  console.log('- Extracting text');
  console.log('- Testing web applications');
  
} catch (error) {
  console.error('❌ Error testing Playwright MCP setup:', error.message);
}