#!/usr/bin/env node

// Simple test for Playwright MCP without config file
import { spawn } from 'child_process';
import { setTimeout } from 'timers/promises';

console.log('🧪 Testing Playwright MCP with inline configuration...');

const server = spawn('npx', ['@playwright/mcp', '--headless', '--browser=chromium', '--no-sandbox'], {
  stdio: 'pipe',
  env: {
    ...process.env,
    FASTMCP_LOG_LEVEL: 'INFO'
  }
});

let serverStarted = false;
let output = '';

server.stdout.on('data', (data) => {
  const text = data.toString();
  output += text;
  console.log('📤 Server output:', text.trim());
  
  if (text.includes('Listening on') || text.includes('Put this in your client config')) {
    serverStarted = true;
    console.log('✅ Server started successfully!');
  }
});

server.stderr.on('data', (data) => {
  const text = data.toString();
  output += text;
  console.log('📤 Server stderr:', text.trim());
  
  if (text.includes('Listening on') || text.includes('Put this in your client config')) {
    serverStarted = true;
    console.log('✅ Server started successfully!');
  }
});

server.on('close', (code) => {
  console.log(`🔚 Server process exited with code ${code}`);
});

// Wait 10 seconds then kill the server
setTimeout(10000).then(() => {
  if (serverStarted) {
    console.log('✅ Test passed - server started successfully');
    console.log('📋 Full output:', output);
  } else {
    console.log('❌ Test failed - server did not start properly');
    console.log('📋 Full output:', output);
  }
  
  server.kill('SIGTERM');
  process.exit(serverStarted ? 0 : 1);
});