# Syrian Identity UI Design System
## Minimal, Performance-First Cultural Enhancement

### Overview

This design system introduces Syrian cultural identity elements to the Silia Tech Hub while maintaining performance, accessibility, and usability as primary concerns. All identity elements are **opt-in**, **measurable**, and **degradable** on constrained devices.

### Design Principles

1. **Performance First**: Identity never compromises core functionality
2. **Progressive Enhancement**: Base experience works without identity elements
3. **Cultural Authenticity**: Respectful representation of Syrian heritage
4. **Accessibility**: WCAG AA compliance maintained throughout
5. **Minimalism**: Subtle integration, not overwhelming decoration

## Conventions & Naming

### CSS Variables (kebab-case)
```css
:root {
  /* Syrian Identity Colors */
  --syrian-qasioun-gold-50: 45 100% 95%;
  --syrian-qasioun-gold-500: 45 100% 50%;
  --syrian-qasioun-gold-900: 45 100% 20%;
  
  --syrian-ebla-bronze-50: 30 60% 90%;
  --syrian-ebla-bronze-500: 30 60% 45%;
  --syrian-ebla-bronze-900: 30 60% 15%;
  
  --syrian-palmyra-sand-50: 35 40% 95%;
  --syrian-palmyra-sand-500: 35 40% 85%;
  --syrian-palmyra-sand-900: 35 40% 60%;
  
  --syrian-orontes-green-50: 120 30% 90%;
  --syrian-orontes-green-500: 120 30% 40%;
  --syrian-orontes-green-900: 120 30% 15%;
  
  --syrian-mediterranean-blue-50: 200 70% 90%;
  --syrian-mediterranean-blue-500: 200 70% 50%;
  --syrian-mediterranean-blue-900: 200 70% 20%;
  
  --syrian-basalt-black-50: 0 0% 90%;
  --syrian-basalt-black-500: 0 0% 15%;
  --syrian-basalt-black-900: 0 0% 5%;
}
```

### TypeScript Constants (camelCase)
```typescript
export const syrianColors = {
  qasiounGold: {
    50: 'hsl(45 100% 95%)',
    500: 'hsl(45 100% 50%)',
    900: 'hsl(45 100% 20%)'
  },
  eblaBronze: {
    50: 'hsl(30 60% 90%)',
    500: 'hsl(30 60% 45%)',
    900: 'hsl(30 60% 15%)'
  },
  // ... rest of colors
} as const;
```

## Performance & Motion Budgets (as tokens)

### Core Web Vitals Targets
```typescript
export const performanceBudgets = {
  lcp: 2500, // ms - Largest Contentful Paint ≤ 2.5s
  cls: 0.1,  // Cumulative Layout Shift ≤ 0.1
  tbt: 200,  // ms - Total Blocking Time ≤ 200ms
  svgPaint: 16, // ms/frame - SVG paint time @60fps
  lighthouseVariance: 5 // % - Score variance between runs
} as const;

export const motionTokens = {
  fast: 150,    // ms - Micro-interactions
  medium: 250,  // ms - Component transitions
  slow: 400,    // ms - Page transitions (max)
  none: 0       // ms - Reduced motion fallback
} as const;
```

### Usage Example
```css
.syrian-button {
  transition: all var(--motion-fast) ease-out;
}

@media (prefers-reduced-motion: reduce) {
  .syrian-button {
    transition-duration: var(--motion-none);
  }
}
```

## Pattern Usage Matrix

### Pattern-to-Area Mapping

**damascusStar**
- Areas: Headers, hero sections, stat cards
- Desktop opacity: 0.03–0.06
- Mobile opacity: 0.02
- Breakpoints: All devices

**palmyraColumns**
- Areas: Section dividers, empty states
- Desktop opacity: 0.03
- Mobile: Disabled <480px
- Breakpoints: ≥480px only

**eblaScript**
- Areas: Footers, metadata panes
- Desktop opacity: 0.03–0.05
- Mobile opacity: 0.02
- Constraint: Avoid behind body text

**geometricWeave**
- Areas: Large banners only
- Desktop opacity: 0.04
- Mobile: Disabled
- Breakpoints: ≥1024px only

### Implementation
```css
.pattern-damascus-star {
  background-image: url('data:image/svg+xml,...');
  background-size: 60px 60px;
  opacity: 0.06;
}

@media (max-width: 480px) {
  .pattern-damascus-star {
    opacity: 0.02;
  }
  
  .pattern-palmyra-columns,
  .pattern-geometric-weave {
    display: none;
  }
}
```

## Responsive Degradation Strategy

### Breakpoint Rules
- **<480px**: Disable geometricWeave; reduce all opacity by 50%
- **<768px**: Only low/medium complexity patterns; no background-position animations
- **≥1024px**: Full pattern library available

### Feature Flag Gating
```typescript
type PatternLevel = 'minimal' | 'moderate' | 'rich';

const config = {
  SYRIAN_PATTERNS_LEVEL: 'minimal' as PatternLevel,
  SYRIAN_MOTION_ENABLED: true,
  SIMPLIFY_ON_MOBILE: true
};

// Usage
if (config.SYRIAN_PATTERNS_LEVEL === 'rich' && viewport.width >= 1024) {
  enablePattern('geometricWeave');
}
```

## Theme Mapping (Light/Dark)

### Text Colors
- text.primary: basaltBlack[900] / palmyraSand[50]
- text.secondary: basaltBlack[500] / palmyraSand[200]
- text.accent: qasiounGold[500] / qasiounGold[400]

### Surface Colors
- background: palmyraSand[50] / basaltBlack[900]
- surface: palmyraSand[100] / basaltBlack[800]
- overlay: palmyraSand[200] / basaltBlack[700]

### Interactive States
- border.default: palmyraSand[300] / basaltBlack[600]
- border.focus: qasiounGold[500] / qasiounGold[400]
- state.info: mediterraneanBlue[500] / mediterraneanBlue[400]
- state.success: orontesGreen[500] / orontesGreen[400]
- state.warning: qasiounGold[500] / qasiounGold[400]
- state.error: eblaBronze[700] / eblaBronze[400]

## Arabic/RTL Guidelines (Do/Don't)

### Typography Do's
- Use Cairo for UI text, Noto Kufi Arabic for headings
- Enable font-feature-settings: "liga", "calt", "dlig"
- Set line-height ≥1.6 for Arabic body text
- Use kashida justification for long paragraphs only
- Default to Arabic-Indic numerals in Arabic UI

### Typography Don'ts
- No full-justification on narrow columns (<300px)
- No long Latin strings in Arabic UI without word-wrap
- No decorative patterns behind dense Arabic paragraphs
- No mixing numeral systems within same data context

### Implementation
```css
.arabic-text {
  font-family: 'Cairo', sans-serif;
  font-feature-settings: "liga", "calt", "dlig";
  line-height: 1.6;
  direction: rtl;
  text-align: start;
}

.arabic-numerals {
  font-variant-numeric: normal;
  unicode-bidi: plaintext;
}
```

## Feature Flags & Config

### Configuration Schema
```typescript
interface SyrianIdentityConfig {
  SYRIAN_IDENTITY_ENABLED: boolean;
  SYRIAN_PATTERNS_LEVEL: 'minimal' | 'moderate' | 'rich';
  SYRIAN_MOTION_ENABLED: boolean;
  SIMPLIFY_ON_MOBILE: boolean;
}

const defaultConfig: SyrianIdentityConfig = {
  SYRIAN_IDENTITY_ENABLED: false,
  SYRIAN_PATTERNS_LEVEL: 'minimal',
  SYRIAN_MOTION_ENABLED: true,
  SIMPLIFY_ON_MOBILE: true
};
```

### SSR-Safe Implementation
```typescript
// config/identity.ts
export function getSyrianConfig(): SyrianIdentityConfig {
  if (typeof window === 'undefined') {
    return defaultConfig; // SSR fallback
  }
  
  return {
    ...defaultConfig,
    ...window.__SYRIAN_CONFIG__,
    // Override based on device capabilities
    SYRIAN_PATTERNS_LEVEL: getDeviceCapabilityLevel()
  };
}

function getDeviceCapabilityLevel(): PatternLevel {
  const connection = (navigator as any).connection;
  const memory = (navigator as any).deviceMemory;
  
  if (memory && memory <= 2) return 'minimal';
  if (connection && connection.effectiveType === 'slow-2g') return 'minimal';
  
  return 'moderate';
}
```

## Instrumentation & KPIs

### Event Schema
```typescript
interface SyrianIdentityEvents {
  view_problem: { category: string; sector: string; has_identity: boolean };
  open_solution: { solution_id: string; identity_level: PatternLevel };
  contact_expert: { expert_id: string; ui_theme: 'default' | 'syrian' };
  search_success: { query: string; results_count: number; identity_enabled: boolean };
  identity_toggle: { level: PatternLevel; device_type: string };
  pattern_paint_time: { pattern: string; duration_ms: number };
}
```

### Minimal Dashboard Schema
```sql
CREATE TABLE identity_metrics (
  id SERIAL PRIMARY KEY,
  event_type VARCHAR(50) NOT NULL,
  properties JSONB,
  user_agent TEXT,
  viewport_width INTEGER,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_identity_metrics_event ON identity_metrics(event_type);
CREATE INDEX idx_identity_metrics_created ON identity_metrics(created_at);
```

## Storybook/Ladle Coverage

### Required Stories
```typescript
// Card.stories.tsx
export default {
  title: 'Components/Card',
  component: Card,
  parameters: {
    docs: { description: { component: 'Card with optional Syrian identity styling' } }
  }
};

export const Default = {};
export const SyrianLight = { args: { syrianStyle: true } };
export const SyrianDark = { 
  args: { syrianStyle: true },
  parameters: { backgrounds: { default: 'dark' } }
};
export const RTLArabic = {
  args: { syrianStyle: true },
  parameters: { direction: 'rtl' }
};
export const WithPattern = {
  args: { syrianStyle: true, pattern: 'damascusStar' }
};
export const ReducedMotion = {
  args: { syrianStyle: true },
  parameters: { 
    viewport: { defaultViewport: 'mobile1' },
    prefersReducedMotion: 'reduce'
  }
};
```

### Coverage Requirements
- All Syrian variants must have light/dark stories
- RTL variants for text-heavy components
- Reduced motion variants for animated components
- Mobile viewport testing for pattern degradation

## Component Implementation Examples

### Enhanced Button Component
```typescript
// components/ui/button.tsx
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'syrian' | 'outline' | 'ghost';
  syrianIntensity?: 'subtle' | 'moderate' | 'rich';
}

const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        syrian: "bg-[hsl(var(--syrian-qasioun-gold-500))] text-[hsl(var(--syrian-basalt-black-900))] hover:bg-[hsl(var(--syrian-qasioun-gold-600))]",
        outline: "border border-input hover:bg-accent",
        ghost: "hover:bg-accent hover:text-accent-foreground"
      }
    }
  }
);
```

### Pattern Background Component
```typescript
// components/ui/pattern-background.tsx
interface PatternBackgroundProps {
  pattern: 'damascusStar' | 'palmyraColumns' | 'eblaScript' | 'geometricWeave';
  intensity?: 'minimal' | 'moderate' | 'rich';
  children: React.ReactNode;
}

export function PatternBackground({ pattern, intensity = 'minimal', children }: PatternBackgroundProps) {
  const config = useSyrianConfig();
  
  if (!config.SYRIAN_IDENTITY_ENABLED) {
    return <>{children}</>;
  }
  
  const opacityMap = {
    minimal: 0.02,
    moderate: 0.04,
    rich: 0.06
  };
  
  return (
    <div 
      className={cn('relative', `pattern-${pattern}`)}
      style={{ '--pattern-opacity': opacityMap[intensity] }}
      aria-hidden="true"
    >
      {children}
    </div>
  );
}
```

## Performance Monitoring

### Paint Time Measurement
```typescript
// lib/performance-monitor.ts
export function measurePatternPaint(patternName: string) {
  return new Promise<number>((resolve) => {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const paintEntry = entries.find(entry => 
        entry.name.includes('paint') && 
        entry.duration > 0
      );
      
      if (paintEntry && paintEntry.duration <= 16) {
        trackEvent('pattern_paint_time', {
          pattern: patternName,
          duration_ms: paintEntry.duration
        });
      }
      
      resolve(paintEntry?.duration || 0);
      observer.disconnect();
    });
    
    observer.observe({ entryTypes: ['paint'] });
  });
}
```

## Non-Goals

- Complex 3D animations or parallax effects
- Heavy image assets or video backgrounds  
- Decorative elements that interfere with core functionality
- Cultural elements that require external font loading
- Patterns that significantly impact bundle size (>5KB)

## Change Log

### v1.0.0 - Initial Design System
- Established Syrian color palette with semantic naming
- Created pattern library with performance constraints
- Implemented responsive degradation strategy
- Added comprehensive accessibility guidelines
- Defined feature flag architecture
- Established instrumentation and monitoring
- Created Storybook coverage requirements

---

*This design system prioritizes performance and accessibility while respectfully incorporating Syrian cultural identity. All enhancements are opt-in and measurable, ensuring the core platform remains fast and usable for all users.*