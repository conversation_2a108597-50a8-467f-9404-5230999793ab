import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import { visualizer } from "rollup-plugin-visualizer";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
    mode === 'development' && componentTagger(),
    // Bundle analyzer - generates stats.html in dist folder
    mode === 'production' && visualizer({
      filename: 'dist/stats.html',
      open: false,
      gzipSize: true,
      brotliSize: true,
      template: 'treemap', // 'treemap', 'sunburst', 'network'
    }),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    // Target modern browsers for better optimization
    target: 'es2020',
    
    // Enable source maps for production debugging
    sourcemap: mode === 'development',
    
    // Optimize chunk size limits
    chunkSizeWarningLimit: 1000,
    
    // Advanced minification options
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: mode === 'production',
        drop_debugger: mode === 'production',
        pure_funcs: mode === 'production' ? ['console.log', 'console.info'] : [],
      },
      mangle: {
        safari10: true,
      },
    },
    
    rollupOptions: {
      output: {
        // Enhanced manual chunk splitting strategy
        manualChunks: (id) => {
          // Vendor libraries - Core React ecosystem
          if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
            return 'vendor-react';
          }
          
          // UI Component libraries
          if (id.includes('@radix-ui') || id.includes('lucide-react')) {
            return 'vendor-ui';
          }
          
          // Form handling libraries
          if (id.includes('react-hook-form') || id.includes('@hookform') || id.includes('zod')) {
            return 'vendor-forms';
          }
          
          // Data fetching and state management
          if (id.includes('@tanstack/react-query') || id.includes('@supabase/supabase-js')) {
            return 'vendor-data';
          }
          
          // Utility libraries
          if (id.includes('clsx') || id.includes('class-variance-authority') || 
              id.includes('tailwind-merge') || id.includes('date-fns')) {
            return 'vendor-utils';
          }
          
          // Chart libraries (lazy loaded)
          if (id.includes('recharts') || id.includes('chart')) {
            return 'vendor-charts';
          }
          
          // Admin-specific components
          if (id.includes('/admin/') || id.includes('AdminDashboard') || 
              id.includes('AdminStats') || id.includes('AdminUsers')) {
            return 'chunk-admin';
          }
          
          // Problem-related components
          if (id.includes('/problems/') || id.includes('ProblemDashboard') || 
              id.includes('ProblemList') || id.includes('ProblemCard')) {
            return 'chunk-problems';
          }
          
          // Expert-related components
          if (id.includes('/experts/') || id.includes('ExpertDirectory') || 
              id.includes('ExpertProfile') || id.includes('ExpertDashboard')) {
            return 'chunk-experts';
          }
          
          // Search-related components
          if (id.includes('/search/') || id.includes('SearchResults') || 
              id.includes('GlobalSearch') || id.includes('SearchFilters')) {
            return 'chunk-search';
          }
          
          // Authentication components
          if (id.includes('/auth/') || id.includes('AuthProvider') || 
              id.includes('LoginForm') || id.includes('RegisterForm')) {
            return 'chunk-auth';
          }
          
          // Common/shared components
          if (id.includes('/common/') || id.includes('/ui/') || 
              id.includes('/layout/') || id.includes('ErrorBoundary')) {
            return 'chunk-common';
          }
          
          // Heavy utility libraries (lazy loaded)
          if (id.includes('pdf-lib') || id.includes('jszip') || 
              id.includes('papaparse') || id.includes('marked') || 
              id.includes('dompurify')) {
            return 'vendor-heavy';
          }
          
          // Default vendor chunk for other node_modules
          if (id.includes('node_modules')) {
            return 'vendor-misc';
          }
        },
        
        // Dynamic chunk naming for better caching
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
            ? chunkInfo.facadeModuleId.split('/').pop()?.replace(/\.[^.]*$/, '')
            : 'chunk';
          
          // Route-based chunks
          if (facadeModuleId?.includes('pages')) {
            return `assets/pages/[name]-[hash].js`;
          }
          
          // Component chunks
          if (facadeModuleId?.includes('components')) {
            return `assets/components/[name]-[hash].js`;
          }
          
          // Default chunk naming
          return `assets/[name]-[hash].js`;
        },
        
        // Asset naming for better organization
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || [];
          const ext = info[info.length - 1];
          
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name || '')) {
            return `assets/images/[name]-[hash].${ext}`;
          }
          
          if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name || '')) {
            return `assets/fonts/[name]-[hash].${ext}`;
          }
          
          return `assets/[name]-[hash].${ext}`;
        },
      },
      
      // External dependencies (if needed for specific use cases)
      external: [],
    },
    
    // CSS code splitting
    cssCodeSplit: true,
    
    // Asset inlining threshold (4kb)
    assetsInlineLimit: 4096,
    
    // Enable CSS minification
    cssMinify: true,
  },
  
  // Optimize dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query',
      '@supabase/supabase-js',
    ],
    exclude: ['@vite/client', '@vite/env'],
  },
  
  // Performance optimizations
  esbuild: {
    // Remove console logs in production
    drop: mode === 'production' ? ['console', 'debugger'] : [],
    // Enable tree shaking
    treeShaking: true,
  },
}));
