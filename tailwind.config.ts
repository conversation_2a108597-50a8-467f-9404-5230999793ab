
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			fontFamily: {
				'cairo': ['Cairo', 'sans-serif'],
				'sans': ['Cairo', 'sans-serif'],
			},
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				// Syrian Identity Colors
				syrian: {
					gold: {
						50: 'hsl(var(--syrian-qasioun-gold-50))',
						500: 'hsl(var(--syrian-qasioun-gold-500))',
						900: 'hsl(var(--syrian-qasioun-gold-900))',
						DEFAULT: 'hsl(var(--syrian-qasioun-gold-500))'
					},
					red: {
						50: 'hsl(var(--syrian-damascus-red-50))',
						500: 'hsl(var(--syrian-damascus-red-500))',
						900: 'hsl(var(--syrian-damascus-red-900))',
						DEFAULT: 'hsl(var(--syrian-damascus-red-500))'
					},
					green: {
						50: 'hsl(var(--syrian-umayyad-green-50))',
						500: 'hsl(var(--syrian-umayyad-green-500))',
						900: 'hsl(var(--syrian-umayyad-green-900))',
						DEFAULT: 'hsl(var(--syrian-umayyad-green-500))'
					},
					stone: {
						50: 'hsl(var(--syrian-palmyra-stone-50))',
						500: 'hsl(var(--syrian-palmyra-stone-500))',
						900: 'hsl(var(--syrian-palmyra-stone-900))',
						DEFAULT: 'hsl(var(--syrian-palmyra-stone-500))'
					},
					blue: {
						50: 'hsl(var(--syrian-ebla-blue-50))',
						500: 'hsl(var(--syrian-ebla-blue-500))',
						900: 'hsl(var(--syrian-ebla-blue-900))',
						DEFAULT: 'hsl(var(--syrian-ebla-blue-500))'
					},
					purple: {
						50: 'hsl(var(--syrian-heritage-purple-50))',
						500: 'hsl(var(--syrian-heritage-purple-500))',
						900: 'hsl(var(--syrian-heritage-purple-900))',
						DEFAULT: 'hsl(var(--syrian-heritage-purple-500))'
					}
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
