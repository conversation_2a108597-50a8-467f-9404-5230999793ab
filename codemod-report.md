# Syrian Identity Naming Codemod Report

## Summary

- **Files Processed**: 292
- **Files Changed**: 5
- **Total Transformations**: 36
- **Errors**: 0
- **Mode**: Applied Changes

## Files Changed


### docs/SYRIAN_IDENTITY_PATTERNS_DECORATIVE.md
- **Changes**: 10
- /qasioun-gold(?!-\d)/g → syrian-qasioun-gold-500 (10 matches)


### docs/SYRIAN_IDENTITY_TYPOGRAPHY_ANIMATION.md
- **Changes**: 8
- /qasioun-gold(?!-\d)/g → syrian-qasioun-gold-500 (8 matches)


### docs/SYRIAN_IDENTITY_UI_ENHANCEMENTS.md
- **Changes**: 8
- /qasioun-gold(?!-\d)/g → syrian-qasioun-gold-500 (8 matches)


### docs/SYRIAN_IDENTITY_UI_ENHANCEMENT_SUMMARY.md
- **Changes**: 8
- /`--qasioun-gold`/g → `--syrian-qasioun-gold-500` (1 matches)
- /qasioun-gold(?!-\d)/g → syrian-qasioun-gold-500 (7 matches)


### .kiro/specs/syrian-identity-ui-transformation/tasks.md
- **Changes**: 2
- /`--qasioun-gold`/g → `--syrian-qasioun-gold-500` (1 matches)
- /`qasioungold`/g → `qasiounGold` (1 matches)


Generated on: 2025-08-19T09:44:38.695Z
