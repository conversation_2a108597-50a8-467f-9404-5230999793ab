-- Expert Matching & Recommendation System Migration
-- This adds intelligent matching between experts and problems

-- Expert matching preferences table
CREATE TABLE public.expert_matching_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    expert_id UUID REFERENCES public.experts(id) ON DELETE CASCADE NOT NULL,
    preferred_problem_categories TEXT[] DEFAULT ARRAY[]::TEXT[],
    preferred_sectors TEXT[] DEFAULT ARRAY[]::TEXT[],
    max_problems_per_month INTEGER DEFAULT 5 CHECK (max_problems_per_month > 0),
    min_compensation DECIMAL(10,2) DEFAULT 0.00,
    availability_schedule JSONB DEFAULT '{
        "monday": {"start": "09:00", "end": "17:00", "available": true},
        "tuesday": {"start": "09:00", "end": "17:00", "available": true},
        "wednesday": {"start": "09:00", "end": "17:00", "available": true},
        "thursday": {"start": "09:00", "end": "17:00", "available": true},
        "friday": {"start": "09:00", "end": "17:00", "available": true},
        "saturday": {"start": "09:00", "end": "17:00", "available": false},
        "sunday": {"start": "09:00", "end": "17:00", "available": false}
    }'::jsonb,
    response_time_preference INTEGER DEFAULT 24 CHECK (response_time_preference > 0), -- hours
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one preference record per expert
    UNIQUE(expert_id)
);

-- Problem-expert matches table
CREATE TABLE public.problem_expert_matches (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    problem_id UUID REFERENCES public.problems(id) ON DELETE CASCADE NOT NULL,
    expert_id UUID REFERENCES public.experts(id) ON DELETE CASCADE NOT NULL,
    match_score DECIMAL(3,2) DEFAULT 0.00 CHECK (match_score >= 0.00 AND match_score <= 1.00),
    match_reasons JSONB DEFAULT '[]'::jsonb, -- Array of reasons for the match
    expert_interest_level INTEGER CHECK (expert_interest_level >= 1 AND expert_interest_level <= 5),
    auto_assigned BOOLEAN DEFAULT false,
    expert_response_status TEXT DEFAULT 'pending' CHECK (expert_response_status IN ('pending', 'accepted', 'declined', 'expired')),
    responded_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '72 hours'), -- 3 days to respond
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Prevent duplicate matches for same problem-expert pair
    UNIQUE(problem_id, expert_id)
);

-- Expert workload tracking table
CREATE TABLE public.expert_workload (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    expert_id UUID REFERENCES public.experts(id) ON DELETE CASCADE NOT NULL,
    month_year DATE NOT NULL, -- First day of the month
    problems_assigned INTEGER DEFAULT 0,
    problems_completed INTEGER DEFAULT 0,
    average_response_time_hours DECIMAL(5,2) DEFAULT 0.00,
    total_compensation DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- One record per expert per month
    UNIQUE(expert_id, month_year)
);

-- Matching algorithm configuration table
CREATE TABLE public.matching_algorithm_config (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    weights JSONB DEFAULT '{
        "category_match": 0.30,
        "sector_match": 0.25,
        "expertise_match": 0.20,
        "availability": 0.15,
        "response_time": 0.10
    }'::jsonb,
    is_active BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default matching algorithm
INSERT INTO public.matching_algorithm_config (name, description, is_active) VALUES 
('default', 'Default expert matching algorithm with balanced weights', true);

-- Create indexes for performance
CREATE INDEX idx_expert_matching_preferences_expert_id ON public.expert_matching_preferences(expert_id);
CREATE INDEX idx_expert_matching_preferences_categories ON public.expert_matching_preferences USING GIN(preferred_problem_categories);
CREATE INDEX idx_expert_matching_preferences_sectors ON public.expert_matching_preferences USING GIN(preferred_sectors);
CREATE INDEX idx_expert_matching_preferences_active ON public.expert_matching_preferences(is_active);

CREATE INDEX idx_problem_expert_matches_problem_id ON public.problem_expert_matches(problem_id);
CREATE INDEX idx_problem_expert_matches_expert_id ON public.problem_expert_matches(expert_id);
CREATE INDEX idx_problem_expert_matches_score ON public.problem_expert_matches(match_score DESC);
CREATE INDEX idx_problem_expert_matches_status ON public.problem_expert_matches(expert_response_status);
CREATE INDEX idx_problem_expert_matches_expires_at ON public.problem_expert_matches(expires_at);
CREATE INDEX idx_problem_expert_matches_auto_assigned ON public.problem_expert_matches(auto_assigned);

CREATE INDEX idx_expert_workload_expert_id ON public.expert_workload(expert_id);
CREATE INDEX idx_expert_workload_month_year ON public.expert_workload(month_year);

-- Add updated_at triggers
CREATE TRIGGER update_expert_matching_preferences_updated_at 
    BEFORE UPDATE ON public.expert_matching_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_problem_expert_matches_updated_at 
    BEFORE UPDATE ON public.problem_expert_matches
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_expert_workload_updated_at 
    BEFORE UPDATE ON public.expert_workload
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_matching_algorithm_config_updated_at 
    BEFORE UPDATE ON public.matching_algorithm_config
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Expert matching algorithm function
CREATE OR REPLACE FUNCTION public.calculate_expert_match_score(
    p_problem_id UUID,
    p_expert_id UUID
) RETURNS DECIMAL(3,2) AS $
DECLARE
    v_score DECIMAL(3,2) := 0.00;
    v_problem RECORD;
    v_expert RECORD;
    v_preferences RECORD;
    v_weights JSONB;
    v_category_score DECIMAL(3,2) := 0.00;
    v_sector_score DECIMAL(3,2) := 0.00;
    v_expertise_score DECIMAL(3,2) := 0.00;
    v_availability_score DECIMAL(3,2) := 0.00;
    v_response_time_score DECIMAL(3,2) := 0.00;
BEGIN
    -- Get problem details
    SELECT * INTO v_problem FROM public.problems WHERE id = p_problem_id AND is_deleted = false;
    IF NOT FOUND THEN
        RETURN 0.00;
    END IF;
    
    -- Get expert details
    SELECT * INTO v_expert FROM public.experts WHERE id = p_expert_id AND is_deleted = false;
    IF NOT FOUND THEN
        RETURN 0.00;
    END IF;
    
    -- Get expert preferences
    SELECT * INTO v_preferences FROM public.expert_matching_preferences 
    WHERE expert_id = p_expert_id AND is_active = true;
    
    -- Get algorithm weights
    SELECT weights INTO v_weights FROM public.matching_algorithm_config 
    WHERE is_active = true LIMIT 1;
    
    -- Calculate category match score
    IF v_preferences.preferred_problem_categories IS NOT NULL AND 
       v_problem.category = ANY(v_preferences.preferred_problem_categories) THEN
        v_category_score := 1.00;
    END IF;
    
    -- Calculate sector match score
    IF v_preferences.preferred_sectors IS NOT NULL AND 
       v_problem.sector = ANY(v_preferences.preferred_sectors) THEN
        v_sector_score := 1.00;
    END IF;
    
    -- Calculate expertise match score (check if problem category/sector is in expert's expertise areas)
    IF v_expert.expertise_areas IS NOT NULL THEN
        -- Check if any expertise area matches problem category or sector
        IF EXISTS (
            SELECT 1 FROM jsonb_array_elements_text(v_expert.expertise_areas) AS expertise
            WHERE expertise ILIKE '%' || v_problem.category || '%' 
               OR expertise ILIKE '%' || v_problem.sector || '%'
        ) THEN
            v_expertise_score := 1.00;
        END IF;
    END IF;
    
    -- Calculate availability score
    IF v_expert.availability = 'available' THEN
        v_availability_score := 1.00;
    ELSIF v_expert.availability = 'busy' THEN
        v_availability_score := 0.50;
    END IF;
    
    -- Calculate response time score (inverse relationship - faster response = higher score)
    IF v_expert.response_time_hours <= 24 THEN
        v_response_time_score := 1.00;
    ELSIF v_expert.response_time_hours <= 48 THEN
        v_response_time_score := 0.75;
    ELSIF v_expert.response_time_hours <= 72 THEN
        v_response_time_score := 0.50;
    ELSE
        v_response_time_score := 0.25;
    END IF;
    
    -- Calculate weighted final score
    v_score := (
        v_category_score * COALESCE((v_weights->>'category_match')::DECIMAL, 0.30) +
        v_sector_score * COALESCE((v_weights->>'sector_match')::DECIMAL, 0.25) +
        v_expertise_score * COALESCE((v_weights->>'expertise_match')::DECIMAL, 0.20) +
        v_availability_score * COALESCE((v_weights->>'availability')::DECIMAL, 0.15) +
        v_response_time_score * COALESCE((v_weights->>'response_time')::DECIMAL, 0.10)
    );
    
    RETURN LEAST(v_score, 1.00); -- Ensure score doesn't exceed 1.00
END;
$ LANGUAGE plpgsql;

-- Function to find matching experts for a problem
CREATE OR REPLACE FUNCTION public.find_matching_experts(
    p_problem_id UUID,
    p_limit INTEGER DEFAULT 5
) RETURNS TABLE (
    expert_id UUID,
    match_score DECIMAL(3,2),
    match_reasons JSONB
) AS $
DECLARE
    v_expert RECORD;
    v_score DECIMAL(3,2);
    v_reasons JSONB;
BEGIN
    -- Loop through all available experts
    FOR v_expert IN 
        SELECT e.* FROM public.experts e
        JOIN public.users u ON e.user_id = u.id
        WHERE e.is_deleted = false 
          AND u.is_deleted = false 
          AND u.is_active = true
          AND e.availability IN ('available', 'busy')
    LOOP
        -- Calculate match score
        v_score := public.calculate_expert_match_score(p_problem_id, v_expert.id);
        
        -- Skip if score is too low
        IF v_score < 0.20 THEN
            CONTINUE;
        END IF;
        
        -- Build match reasons
        v_reasons := '[]'::jsonb;
        
        -- Add reasons based on score components
        IF v_score >= 0.80 THEN
            v_reasons := v_reasons || '["High expertise match", "Strong category alignment"]'::jsonb;
        ELSIF v_score >= 0.60 THEN
            v_reasons := v_reasons || '["Good expertise match", "Category alignment"]'::jsonb;
        ELSIF v_score >= 0.40 THEN
            v_reasons := v_reasons || '["Moderate expertise match"]'::jsonb;
        ELSE
            v_reasons := v_reasons || '["Basic qualification match"]'::jsonb;
        END IF;
        
        -- Return the match
        expert_id := v_expert.id;
        match_score := v_score;
        match_reasons := v_reasons;
        RETURN NEXT;
    END LOOP;
    
    RETURN;
END;
$ LANGUAGE plpgsql;

-- Function to auto-assign experts to problems
CREATE OR REPLACE FUNCTION public.auto_assign_experts_to_problem(
    p_problem_id UUID,
    p_max_assignments INTEGER DEFAULT 3
) RETURNS INTEGER AS $
DECLARE
    v_assigned_count INTEGER := 0;
    v_match RECORD;
BEGIN
    -- Find and assign matching experts
    FOR v_match IN 
        SELECT * FROM public.find_matching_experts(p_problem_id, p_max_assignments * 2)
        ORDER BY match_score DESC
        LIMIT p_max_assignments
    LOOP
        -- Insert match record
        INSERT INTO public.problem_expert_matches (
            problem_id, 
            expert_id, 
            match_score, 
            match_reasons, 
            auto_assigned
        ) VALUES (
            p_problem_id,
            v_match.expert_id,
            v_match.match_score,
            v_match.match_reasons,
            true
        ) ON CONFLICT (problem_id, expert_id) DO NOTHING;
        
        IF FOUND THEN
            v_assigned_count := v_assigned_count + 1;
        END IF;
    END LOOP;
    
    RETURN v_assigned_count;
END;
$ LANGUAGE plpgsql;

-- Function to update expert workload
CREATE OR REPLACE FUNCTION public.update_expert_workload(
    p_expert_id UUID,
    p_month_year DATE DEFAULT DATE_TRUNC('month', NOW())::DATE
) RETURNS VOID AS $
DECLARE
    v_problems_assigned INTEGER;
    v_problems_completed INTEGER;
    v_avg_response_time DECIMAL(5,2);
BEGIN
    -- Calculate problems assigned this month
    SELECT COUNT(*) INTO v_problems_assigned
    FROM public.problem_expert_matches pem
    WHERE pem.expert_id = p_expert_id
      AND DATE_TRUNC('month', pem.created_at)::DATE = p_month_year;
    
    -- Calculate problems completed this month
    SELECT COUNT(*) INTO v_problems_completed
    FROM public.problem_expert_matches pem
    JOIN public.problems p ON pem.problem_id = p.id
    WHERE pem.expert_id = p_expert_id
      AND p.status = 'resolved'
      AND DATE_TRUNC('month', p.resolved_at)::DATE = p_month_year;
    
    -- Calculate average response time
    SELECT AVG(EXTRACT(EPOCH FROM (pem.responded_at - pem.created_at)) / 3600) INTO v_avg_response_time
    FROM public.problem_expert_matches pem
    WHERE pem.expert_id = p_expert_id
      AND pem.responded_at IS NOT NULL
      AND DATE_TRUNC('month', pem.created_at)::DATE = p_month_year;
    
    -- Insert or update workload record
    INSERT INTO public.expert_workload (
        expert_id,
        month_year,
        problems_assigned,
        problems_completed,
        average_response_time_hours
    ) VALUES (
        p_expert_id,
        p_month_year,
        v_problems_assigned,
        v_problems_completed,
        COALESCE(v_avg_response_time, 0.00)
    ) ON CONFLICT (expert_id, month_year) DO UPDATE SET
        problems_assigned = EXCLUDED.problems_assigned,
        problems_completed = EXCLUDED.problems_completed,
        average_response_time_hours = EXCLUDED.average_response_time_hours,
        updated_at = NOW();
END;
$ LANGUAGE plpgsql;

-- Trigger to auto-assign experts when a new problem is created
CREATE OR REPLACE FUNCTION public.trigger_auto_assign_experts()
RETURNS TRIGGER AS $
BEGIN
    -- Only auto-assign for new problems
    IF TG_OP = 'INSERT' THEN
        -- Perform auto-assignment in the background (async)
        PERFORM public.auto_assign_experts_to_problem(NEW.id);
    END IF;
    
    RETURN NEW;
END;
$ LANGUAGE plpgsql;

CREATE TRIGGER auto_assign_experts_on_problem_creation
    AFTER INSERT ON public.problems
    FOR EACH ROW EXECUTE FUNCTION public.trigger_auto_assign_experts();

-- RLS Policies for new tables

-- Enable RLS
ALTER TABLE public.expert_matching_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.problem_expert_matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.expert_workload ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.matching_algorithm_config ENABLE ROW LEVEL SECURITY;

-- Expert matching preferences policies
CREATE POLICY "Experts can view and manage own preferences" ON public.expert_matching_preferences
    FOR ALL USING (
        expert_id IN (
            SELECT e.id FROM public.experts e 
            WHERE e.user_id = auth.uid() AND e.is_deleted = false
        )
    );

CREATE POLICY "Admins can view all expert preferences" ON public.expert_matching_preferences
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin' AND is_deleted = false
        )
    );

-- Problem expert matches policies
CREATE POLICY "Experts can view matches for their problems" ON public.problem_expert_matches
    FOR SELECT USING (
        expert_id IN (
            SELECT e.id FROM public.experts e 
            WHERE e.user_id = auth.uid() AND e.is_deleted = false
        ) OR
        problem_id IN (
            SELECT p.id FROM public.problems p 
            WHERE p.submitted_by = auth.uid() AND p.is_deleted = false
        )
    );

CREATE POLICY "Experts can update their match responses" ON public.problem_expert_matches
    FOR UPDATE USING (
        expert_id IN (
            SELECT e.id FROM public.experts e 
            WHERE e.user_id = auth.uid() AND e.is_deleted = false
        )
    );

CREATE POLICY "Admins can view all matches" ON public.problem_expert_matches
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin' AND is_deleted = false
        )
    );

-- Expert workload policies
CREATE POLICY "Experts can view own workload" ON public.expert_workload
    FOR SELECT USING (
        expert_id IN (
            SELECT e.id FROM public.experts e 
            WHERE e.user_id = auth.uid() AND e.is_deleted = false
        )
    );

CREATE POLICY "Admins can view all workloads" ON public.expert_workload
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin' AND is_deleted = false
        )
    );

-- Matching algorithm config policies
CREATE POLICY "Anyone can view active algorithm config" ON public.matching_algorithm_config
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage algorithm config" ON public.matching_algorithm_config
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin' AND is_deleted = false
        )
    );