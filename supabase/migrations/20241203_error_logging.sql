-- Error logging table for comprehensive error tracking
CREATE TABLE IF NOT EXISTS error_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Error details
  message TEXT NOT NULL,
  stack TEXT,
  component_stack TEXT,
  
  -- Context information
  component_name TEXT,
  route_name TEXT,
  error_boundary TEXT,
  source TEXT,
  type TEXT,
  
  -- Request/API details
  endpoint TEXT,
  method TEXT,
  status_code INTEGER,
  
  -- User and session information
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  session_id TEXT,
  user_agent TEXT,
  
  -- Location and environment
  url TEXT,
  error_id TEXT,
  retry_count INTEGER DEFAULT 0,
  
  -- Additional context (JSON)
  context JSONB,
  
  -- <PERSON><PERSON><PERSON>
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes for performance
  INDEX idx_error_logs_created_at ON error_logs(created_at),
  INDEX idx_error_logs_user_id ON error_logs(user_id),
  INDEX idx_error_logs_type ON error_logs(type),
  INDEX idx_error_logs_source ON error_logs(source),
  INDEX idx_error_logs_component_name ON error_logs(component_name),
  INDEX idx_error_logs_route_name ON error_logs(route_name)
);

-- Error reports table for user-submitted error reports
CREATE TABLE IF NOT EXISTS error_reports (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  
  -- Reference to error log
  error_log_id UUID REFERENCES error_logs(id) ON DELETE CASCADE,
  
  -- Report details
  user_description TEXT,
  steps_to_reproduce TEXT,
  expected_behavior TEXT,
  actual_behavior TEXT,
  
  -- User information
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  user_email TEXT,
  
  -- Report status
  status TEXT DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'resolved', 'closed')),
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  
  -- Assignment
  assigned_to UUID REFERENCES users(id) ON DELETE SET NULL,
  
  -- Resolution
  resolution TEXT,
  resolved_at TIMESTAMP WITH TIME ZONE,
  
  -- Metadata
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Indexes
  INDEX idx_error_reports_status ON error_reports(status),
  INDEX idx_error_reports_priority ON error_reports(priority),
  INDEX idx_error_reports_user_id ON error_reports(user_id),
  INDEX idx_error_reports_created_at ON error_reports(created_at)
);

-- Error statistics view for monitoring
CREATE OR REPLACE VIEW error_statistics AS
SELECT 
  DATE_TRUNC('hour', created_at) as hour,
  type,
  source,
  component_name,
  route_name,
  COUNT(*) as error_count,
  COUNT(DISTINCT user_id) as affected_users,
  AVG(retry_count) as avg_retry_count
FROM error_logs 
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY 
  DATE_TRUNC('hour', created_at),
  type,
  source,
  component_name,
  route_name
ORDER BY hour DESC, error_count DESC;

-- Function to clean up old error logs
CREATE OR REPLACE FUNCTION cleanup_old_error_logs()
RETURNS void AS $$
BEGIN
  -- Delete error logs older than 30 days
  DELETE FROM error_logs 
  WHERE created_at < NOW() - INTERVAL '30 days';
  
  -- Delete resolved error reports older than 90 days
  DELETE FROM error_reports 
  WHERE status = 'resolved' 
    AND resolved_at < NOW() - INTERVAL '90 days';
END;
$$ LANGUAGE plpgsql;

-- Function to get error trends
CREATE OR REPLACE FUNCTION get_error_trends(
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '7 days',
  end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE (
  date DATE,
  total_errors BIGINT,
  unique_errors BIGINT,
  affected_users BIGINT,
  top_error_type TEXT,
  top_component TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    DATE(el.created_at) as date,
    COUNT(*) as total_errors,
    COUNT(DISTINCT el.message) as unique_errors,
    COUNT(DISTINCT el.user_id) as affected_users,
    (
      SELECT type 
      FROM error_logs 
      WHERE DATE(created_at) = DATE(el.created_at) 
        AND type IS NOT NULL
      GROUP BY type 
      ORDER BY COUNT(*) DESC 
      LIMIT 1
    ) as top_error_type,
    (
      SELECT component_name 
      FROM error_logs 
      WHERE DATE(created_at) = DATE(el.created_at) 
        AND component_name IS NOT NULL
      GROUP BY component_name 
      ORDER BY COUNT(*) DESC 
      LIMIT 1
    ) as top_component
  FROM error_logs el
  WHERE el.created_at >= start_date 
    AND el.created_at <= end_date
  GROUP BY DATE(el.created_at)
  ORDER BY date DESC;
END;
$$ LANGUAGE plpgsql;

-- RLS policies for error logs
ALTER TABLE error_logs ENABLE ROW LEVEL SECURITY;

-- Users can only see their own error logs
CREATE POLICY "Users can view own error logs" ON error_logs
  FOR SELECT USING (user_id = auth.uid());

-- Admins can see all error logs
CREATE POLICY "Admins can view all error logs" ON error_logs
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
        AND role = 'admin'
    )
  );

-- Service role can insert error logs
CREATE POLICY "Service can insert error logs" ON error_logs
  FOR INSERT WITH CHECK (true);

-- RLS policies for error reports
ALTER TABLE error_reports ENABLE ROW LEVEL SECURITY;

-- Users can view and create their own error reports
CREATE POLICY "Users can manage own error reports" ON error_reports
  FOR ALL USING (user_id = auth.uid());

-- Admins can see all error reports
CREATE POLICY "Admins can view all error reports" ON error_reports
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
        AND role = 'admin'
    )
  );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_error_logs_composite 
  ON error_logs(created_at, type, source, user_id);

CREATE INDEX IF NOT EXISTS idx_error_logs_message_gin 
  ON error_logs USING gin(to_tsvector('english', message));

CREATE INDEX IF NOT EXISTS idx_error_reports_composite 
  ON error_reports(status, priority, created_at);

-- Schedule cleanup job (if pg_cron is available)
-- SELECT cron.schedule('cleanup-error-logs', '0 2 * * *', 'SELECT cleanup_old_error_logs();');

-- Grant necessary permissions
GRANT SELECT, INSERT ON error_logs TO authenticated;
GRANT SELECT, INSERT, UPDATE ON error_reports TO authenticated;
GRANT ALL ON error_logs TO service_role;
GRANT ALL ON error_reports TO service_role;
