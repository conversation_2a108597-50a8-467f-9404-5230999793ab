-- Enhanced Search Infrastructure Migration
-- This migration adds comprehensive search capabilities to the Syrian Technical Solutions Platform

-- Enable additional extensions for advanced search
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Create search analytics table
CREATE TABLE IF NOT EXISTS public.search_analytics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    query_text TEXT NOT NULL,
    query_hash TEXT NOT NULL,
    user_id UUID REFERENCES public.users(id),
    filters JSONB DEFAULT '{}'::jsonb,
    results_count INTEGER DEFAULT 0,
    response_time_ms INTEGER DEFAULT 0,
    clicked_result_id UUID,
    clicked_result_type TEXT,
    session_id TEXT,
    language TEXT DEFAULT 'ar',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for search analytics
CREATE INDEX IF NOT EXISTS idx_search_analytics_query_hash ON public.search_analytics(query_hash);
CREATE INDEX IF NOT EXISTS idx_search_analytics_user ON public.search_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_search_analytics_created ON public.search_analytics(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_search_analytics_language ON public.search_analytics(language);

-- Create materialized view for optimized search
CREATE MATERIALIZED VIEW IF NOT EXISTS public.search_index AS
SELECT 
    'problem'::TEXT as content_type,
    p.id,
    p.title,
    p.description,
    p.category,
    p.sector,
    p.tags,
    p.status,
    p.urgency,
    p.created_at,
    p.updated_at,
    u.name as submitter_name,
    u.location as submitter_location,
    -- Create search vectors for both Arabic and English
    setweight(to_tsvector('arabic', p.title), 'A') ||
    setweight(to_tsvector('arabic', p.description), 'B') ||
    setweight(to_tsvector('arabic', array_to_string(p.tags, ' ')), 'C') as search_vector_ar,
    setweight(to_tsvector('english', p.title), 'A') ||
    setweight(to_tsvector('english', p.description), 'B') ||
    setweight(to_tsvector('english', array_to_string(p.tags, ' ')), 'C') as search_vector_en,
    jsonb_build_object(
        'status', p.status,
        'urgency', p.urgency,
        'submitter_name', u.name,
        'submitter_location', u.location,
        'created_at', p.created_at,
        'updated_at', p.updated_at
    ) as metadata
FROM public.problems p
JOIN public.users u ON p.submitted_by = u.id
WHERE p.is_deleted = false AND u.is_deleted = false

UNION ALL

SELECT 
    'expert'::TEXT as content_type,
    e.user_id as id,
    u.name as title,
    COALESCE(u.bio, 'خبير تقني') as description,
    (e.expertise_areas->0->>'category') as category,
    NULL as sector,
    ARRAY(SELECT jsonb_array_elements_text(
        jsonb_path_query_array(e.expertise_areas, '$[*].skills[*]')
    )) as tags,
    e.availability::TEXT as status,
    NULL as urgency,
    u.created_at,
    u.updated_at,
    u.name as submitter_name,
    u.location as submitter_location,
    -- Create search vectors for expert profiles
    setweight(to_tsvector('arabic', u.name), 'A') ||
    setweight(to_tsvector('arabic', COALESCE(u.bio, '')), 'B') ||
    setweight(to_tsvector('arabic', 
        COALESCE(
            (SELECT string_agg(skill, ' ') 
             FROM jsonb_array_elements_text(
               jsonb_path_query_array(e.expertise_areas, '$[*].skills[*]')
             ) as skill), 
            ''
        )
    ), 'C') as search_vector_ar,
    setweight(to_tsvector('english', u.name), 'A') ||
    setweight(to_tsvector('english', COALESCE(u.bio, '')), 'B') ||
    setweight(to_tsvector('english', 
        COALESCE(
            (SELECT string_agg(skill, ' ') 
             FROM jsonb_array_elements_text(
               jsonb_path_query_array(e.expertise_areas, '$[*].skills[*]')
             ) as skill), 
            ''
        )
    ), 'C') as search_vector_en,
    jsonb_build_object(
        'rating', e.rating,
        'experience_years', e.experience_years,
        'availability', e.availability,
        'total_contributions', e.total_contributions,
        'location', u.location,
        'expertise_areas', e.expertise_areas
    ) as metadata
FROM public.experts e
JOIN public.users u ON e.user_id = u.id
WHERE e.is_deleted = false AND u.is_deleted = false

UNION ALL

SELECT 
    'solution'::TEXT as content_type,
    s.id,
    CONCAT('حل للمشكلة: ', p.title) as title,
    s.content as description,
    p.category,
    p.sector,
    p.tags,
    s.status::TEXT as status,
    NULL as urgency,
    s.created_at,
    s.updated_at,
    u.name as submitter_name,
    u.location as submitter_location,
    -- Create search vectors for solutions
    setweight(to_tsvector('arabic', s.content), 'A') ||
    setweight(to_tsvector('arabic', p.title), 'B') ||
    setweight(to_tsvector('arabic', array_to_string(p.tags, ' ')), 'C') as search_vector_ar,
    setweight(to_tsvector('english', s.content), 'A') ||
    setweight(to_tsvector('english', p.title), 'B') ||
    setweight(to_tsvector('english', array_to_string(p.tags, ' ')), 'C') as search_vector_en,
    jsonb_build_object(
        'status', s.status,
        'rating', s.rating,
        'problem_title', p.title,
        'problem_id', s.problem_id,
        'expert_name', u.name,
        'expert_id', s.expert_id
    ) as metadata
FROM public.solutions s
JOIN public.problems p ON s.problem_id = p.id
JOIN public.users u ON s.expert_id = u.id
WHERE s.is_deleted = false AND p.is_deleted = false AND u.is_deleted = false

UNION ALL

SELECT 
    'webinar'::TEXT as content_type,
    w.id,
    w.title,
    w.description,
    w.category,
    NULL as sector,
    w.tags,
    w.status::TEXT as status,
    NULL as urgency,
    w.created_at,
    w.updated_at,
    w.presenter as submitter_name,
    NULL as submitter_location,
    -- Create search vectors for webinars
    setweight(to_tsvector('arabic', w.title), 'A') ||
    setweight(to_tsvector('arabic', w.description), 'B') ||
    setweight(to_tsvector('arabic', array_to_string(w.tags, ' ')), 'C') as search_vector_ar,
    setweight(to_tsvector('english', w.title), 'A') ||
    setweight(to_tsvector('english', w.description), 'B') ||
    setweight(to_tsvector('english', array_to_string(w.tags, ' ')), 'C') as search_vector_en,
    jsonb_build_object(
        'status', w.status,
        'presenter', w.presenter,
        'scheduled_at', w.scheduled_at,
        'duration_minutes', w.duration_minutes,
        'recording_url', w.recording_url
    ) as metadata
FROM public.webinars w
WHERE w.is_deleted = false;

-- Create indexes on the materialized view for optimal search performance
CREATE UNIQUE INDEX IF NOT EXISTS idx_search_index_unique ON public.search_index(content_type, id);
CREATE INDEX IF NOT EXISTS idx_search_vector_ar ON public.search_index USING GIN(search_vector_ar);
CREATE INDEX IF NOT EXISTS idx_search_vector_en ON public.search_index USING GIN(search_vector_en);
CREATE INDEX IF NOT EXISTS idx_search_category ON public.search_index(category);
CREATE INDEX IF NOT EXISTS idx_search_sector ON public.search_index(sector);
CREATE INDEX IF NOT EXISTS idx_search_type ON public.search_index(content_type);
CREATE INDEX IF NOT EXISTS idx_search_status ON public.search_index(status);
CREATE INDEX IF NOT EXISTS idx_search_created ON public.search_index(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_search_updated ON public.search_index(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_search_tags ON public.search_index USING GIN(tags);

-- Create trigram indexes for fuzzy matching
CREATE INDEX IF NOT EXISTS idx_search_title_trgm ON public.search_index USING GIN(title gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_search_description_trgm ON public.search_index USING GIN(description gin_trgm_ops);

-- Enhanced search function with comprehensive filtering and ranking
CREATE OR REPLACE FUNCTION public.enhanced_search(
    search_query TEXT,
    content_types TEXT[] DEFAULT NULL,
    sectors TEXT[] DEFAULT NULL,
    categories TEXT[] DEFAULT NULL,
    statuses TEXT[] DEFAULT NULL,
    date_from TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    date_to TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    min_rating DECIMAL DEFAULT NULL,
    location_filter TEXT DEFAULT NULL,
    language TEXT DEFAULT 'ar',
    sort_by TEXT DEFAULT 'relevance',
    sort_order TEXT DEFAULT 'desc',
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    content_type TEXT,
    title TEXT,
    description TEXT,
    category TEXT,
    sector TEXT,
    tags TEXT[],
    status TEXT,
    relevance_score REAL,
    similarity_score REAL,
    metadata JSONB
) AS $$
DECLARE
    search_vector tsvector;
    query_vector tsquery;
BEGIN
    -- Prepare search vectors based on language
    IF language = 'en' THEN
        query_vector := plainto_tsquery('english', search_query);
    ELSE
        query_vector := plainto_tsquery('arabic', search_query);
    END IF;

    RETURN QUERY
    WITH search_results AS (
        SELECT 
            si.id,
            si.content_type,
            si.title,
            si.description,
            si.category,
            si.sector,
            si.tags,
            si.status,
            -- Calculate relevance score based on text search ranking
            CASE 
                WHEN language = 'en' THEN
                    ts_rank_cd(si.search_vector_en, query_vector, 32)
                ELSE
                    ts_rank_cd(si.search_vector_ar, query_vector, 32)
            END as base_relevance,
            -- Calculate similarity score using trigram matching
            GREATEST(
                similarity(si.title, search_query),
                similarity(si.description, search_query)
            ) as similarity_score,
            -- Boost factors
            CASE 
                WHEN si.content_type = 'expert' AND (si.metadata->>'rating')::DECIMAL > 4.0 THEN 1.2
                WHEN si.content_type = 'solution' AND (si.metadata->>'rating')::DECIMAL > 4.0 THEN 1.15
                WHEN si.content_type = 'problem' AND si.status = 'open' THEN 1.1
                ELSE 1.0
            END as quality_boost,
            -- Recency boost (newer content gets slight boost)
            CASE 
                WHEN si.updated_at > NOW() - INTERVAL '30 days' THEN 1.1
                WHEN si.updated_at > NOW() - INTERVAL '90 days' THEN 1.05
                ELSE 1.0
            END as recency_boost,
            si.metadata,
            si.created_at,
            si.updated_at
        FROM public.search_index si
        WHERE 
            -- Content type filter
            (content_types IS NULL OR si.content_type = ANY(content_types))
            -- Sector filter
            AND (sectors IS NULL OR si.sector = ANY(sectors))
            -- Category filter
            AND (categories IS NULL OR si.category = ANY(categories))
            -- Status filter
            AND (statuses IS NULL OR si.status = ANY(statuses))
            -- Date range filter
            AND (date_from IS NULL OR si.created_at >= date_from)
            AND (date_to IS NULL OR si.created_at <= date_to)
            -- Rating filter (for experts and solutions)
            AND (min_rating IS NULL OR 
                 (si.content_type IN ('expert', 'solution') AND (si.metadata->>'rating')::DECIMAL >= min_rating))
            -- Location filter
            AND (location_filter IS NULL OR 
                 si.submitter_location ILIKE '%' || location_filter || '%')
            -- Text search condition
            AND (
                -- Full-text search match
                (language = 'en' AND si.search_vector_en @@ query_vector) OR
                (language = 'ar' AND si.search_vector_ar @@ query_vector) OR
                -- Fuzzy matching fallback
                similarity(si.title, search_query) > 0.2 OR
                similarity(si.description, search_query) > 0.1
            )
    ),
    ranked_results AS (
        SELECT 
            *,
            -- Final relevance score combining all factors
            (base_relevance * quality_boost * recency_boost + similarity_score * 0.3) as final_relevance_score
        FROM search_results
    )
    SELECT 
        rr.id,
        rr.content_type,
        rr.title,
        rr.description,
        rr.category,
        rr.sector,
        rr.tags,
        rr.status,
        rr.final_relevance_score as relevance_score,
        rr.similarity_score,
        rr.metadata
    FROM ranked_results rr
    ORDER BY 
        CASE 
            WHEN sort_by = 'relevance' AND sort_order = 'desc' THEN rr.final_relevance_score
            WHEN sort_by = 'relevance' AND sort_order = 'asc' THEN -rr.final_relevance_score
            WHEN sort_by = 'date' AND sort_order = 'desc' THEN EXTRACT(EPOCH FROM rr.updated_at)
            WHEN sort_by = 'date' AND sort_order = 'asc' THEN -EXTRACT(EPOCH FROM rr.updated_at)
            WHEN sort_by = 'rating' AND sort_order = 'desc' THEN COALESCE((rr.metadata->>'rating')::DECIMAL, 0)
            WHEN sort_by = 'rating' AND sort_order = 'asc' THEN -COALESCE((rr.metadata->>'rating')::DECIMAL, 0)
            ELSE rr.final_relevance_score
        END DESC
    LIMIT limit_count OFFSET offset_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get search suggestions
CREATE OR REPLACE FUNCTION public.get_search_suggestions(
    partial_query TEXT,
    suggestion_type TEXT DEFAULT 'all',
    limit_count INTEGER DEFAULT 10
)
RETURNS TABLE (
    suggestion TEXT,
    type TEXT,
    count INTEGER,
    category TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH suggestions AS (
        -- Category suggestions
        SELECT DISTINCT 
            si.category as suggestion,
            'category'::TEXT as type,
            COUNT(*)::INTEGER as count,
            si.category as category
        FROM public.search_index si
        WHERE 
            si.category IS NOT NULL 
            AND si.category ILIKE '%' || partial_query || '%'
            AND (suggestion_type = 'all' OR suggestion_type = 'category')
        GROUP BY si.category
        
        UNION ALL
        
        -- Tag suggestions
        SELECT DISTINCT 
            tag as suggestion,
            'tag'::TEXT as type,
            COUNT(*)::INTEGER as count,
            NULL as category
        FROM public.search_index si,
             unnest(si.tags) as tag
        WHERE 
            tag ILIKE '%' || partial_query || '%'
            AND (suggestion_type = 'all' OR suggestion_type = 'tag')
        GROUP BY tag
        
        UNION ALL
        
        -- Title suggestions (for popular content)
        SELECT DISTINCT 
            si.title as suggestion,
            'title'::TEXT as type,
            1 as count,
            si.category as category
        FROM public.search_index si
        WHERE 
            si.title ILIKE '%' || partial_query || '%'
            AND (suggestion_type = 'all' OR suggestion_type = 'title')
            AND similarity(si.title, partial_query) > 0.3
    )
    SELECT 
        s.suggestion,
        s.type,
        s.count,
        s.category
    FROM suggestions s
    ORDER BY s.count DESC, similarity(s.suggestion, partial_query) DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Function to refresh search index
CREATE OR REPLACE FUNCTION public.refresh_search_index()
RETURNS VOID AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.search_index;
END;
$$ LANGUAGE plpgsql;

-- Function to log search analytics
CREATE OR REPLACE FUNCTION public.log_search_analytics(
    query_text TEXT,
    filters JSONB DEFAULT '{}'::jsonb,
    results_count INTEGER DEFAULT 0,
    response_time_ms INTEGER DEFAULT 0,
    language TEXT DEFAULT 'ar',
    session_id TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    analytics_id UUID;
    query_hash TEXT;
BEGIN
    -- Generate query hash for caching and analytics
    query_hash := encode(digest(query_text || COALESCE(filters::TEXT, ''), 'md5'), 'hex');
    
    INSERT INTO public.search_analytics (
        query_text,
        query_hash,
        user_id,
        filters,
        results_count,
        response_time_ms,
        language,
        session_id
    ) VALUES (
        query_text,
        query_hash,
        auth.uid(),
        filters,
        results_count,
        response_time_ms,
        language,
        session_id
    ) RETURNING id INTO analytics_id;
    
    RETURN analytics_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update search analytics with click data
CREATE OR REPLACE FUNCTION public.update_search_analytics_click(
    analytics_id UUID,
    clicked_result_id UUID,
    clicked_result_type TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE public.search_analytics 
    SET 
        clicked_result_id = clicked_result_id,
        clicked_result_type = clicked_result_type
    WHERE id = analytics_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Triggers to automatically refresh search index when data changes
CREATE OR REPLACE FUNCTION public.trigger_search_index_refresh()
RETURNS TRIGGER AS $$
BEGIN
    -- Use pg_notify to trigger async refresh
    PERFORM pg_notify('search_index_refresh', '');
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic index refresh
DROP TRIGGER IF EXISTS refresh_search_on_problem_change ON public.problems;
CREATE TRIGGER refresh_search_on_problem_change
    AFTER INSERT OR UPDATE OR DELETE ON public.problems
    FOR EACH STATEMENT
    EXECUTE FUNCTION public.trigger_search_index_refresh();

DROP TRIGGER IF EXISTS refresh_search_on_expert_change ON public.experts;
CREATE TRIGGER refresh_search_on_expert_change
    AFTER INSERT OR UPDATE OR DELETE ON public.experts
    FOR EACH STATEMENT
    EXECUTE FUNCTION public.trigger_search_index_refresh();

DROP TRIGGER IF EXISTS refresh_search_on_solution_change ON public.solutions;
CREATE TRIGGER refresh_search_on_solution_change
    AFTER INSERT OR UPDATE OR DELETE ON public.solutions
    FOR EACH STATEMENT
    EXECUTE FUNCTION public.trigger_search_index_refresh();

DROP TRIGGER IF EXISTS refresh_search_on_webinar_change ON public.webinars;
CREATE TRIGGER refresh_search_on_webinar_change
    AFTER INSERT OR UPDATE OR DELETE ON public.webinars
    FOR EACH STATEMENT
    EXECUTE FUNCTION public.trigger_search_index_refresh();

DROP TRIGGER IF EXISTS refresh_search_on_user_change ON public.users;
CREATE TRIGGER refresh_search_on_user_change
    AFTER INSERT OR UPDATE OR DELETE ON public.users
    FOR EACH STATEMENT
    EXECUTE FUNCTION public.trigger_search_index_refresh();

-- Initial refresh of the materialized view
SELECT public.refresh_search_index();

-- Grant necessary permissions
GRANT SELECT ON public.search_index TO authenticated;
GRANT SELECT ON public.search_analytics TO authenticated;
GRANT INSERT ON public.search_analytics TO authenticated;
GRANT UPDATE ON public.search_analytics TO authenticated;
GRANT EXECUTE ON FUNCTION public.enhanced_search TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_search_suggestions TO authenticated;
GRANT EXECUTE ON FUNCTION public.log_search_analytics TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_search_analytics_click TO authenticated;

-- RLS policies for search analytics
ALTER TABLE public.search_analytics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own search analytics" ON public.search_analytics
    FOR SELECT USING (user_id = auth.uid() OR user_id IS NULL);

CREATE POLICY "Users can insert own search analytics" ON public.search_analytics
    FOR INSERT WITH CHECK (user_id = auth.uid() OR user_id IS NULL);

CREATE POLICY "Users can update own search analytics" ON public.search_analytics
    FOR UPDATE USING (user_id = auth.uid() OR user_id IS NULL);

CREATE POLICY "Admins can view all search analytics" ON public.search_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin' AND is_deleted = false
        )
    );