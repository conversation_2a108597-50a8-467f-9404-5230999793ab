-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create custom types/enums
CREATE TYPE user_role AS ENUM ('expert', 'ministry_user', 'admin');
CREATE TYPE problem_urgency AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE problem_status AS ENUM ('open', 'in_progress', 'resolved', 'closed');
CREATE TYPE solution_status AS ENUM ('draft', 'submitted', 'approved', 'implemented');
CREATE TYPE expert_availability AS ENUM ('available', 'busy', 'unavailable');
CREATE TYPE webinar_status AS ENUM ('scheduled', 'live', 'completed', 'cancelled');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    role user_role DEFAULT 'ministry_user',
    avatar TEXT,
    bio TEXT,
    location TEXT NOT NULL,
    phone_number TEXT,
    organization TEXT,
    position TEXT,
    languages TEXT[] DEFAULT ARRAY['ar'],
    is_active BOOLEAN DEFAULT true,
    is_deleted BOOLEAN DEFAULT false,
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- Experts table (additional data for expert users)
CREATE TABLE public.experts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE UNIQUE NOT NULL,
    expertise_areas JSONB DEFAULT '[]'::jsonb,
    experience_years INTEGER DEFAULT 0,
    availability expert_availability DEFAULT 'available',
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_contributions INTEGER DEFAULT 0,
    success_rate DECIMAL(5,2) DEFAULT 0.00,
    response_time_hours INTEGER DEFAULT 24,
    portfolio JSONB DEFAULT '[]'::jsonb,
    certifications JSONB DEFAULT '[]'::jsonb,
    is_deleted BOOLEAN DEFAULT false,
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Problems table
CREATE TABLE public.problems (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    category TEXT NOT NULL,
    sector TEXT NOT NULL,
    urgency problem_urgency DEFAULT 'medium',
    status problem_status DEFAULT 'open',
    submitted_by UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    assigned_experts UUID[] DEFAULT ARRAY[]::UUID[],
    tags TEXT[] DEFAULT ARRAY[]::TEXT[],
    attachments JSONB DEFAULT '[]'::jsonb,
    is_deleted BOOLEAN DEFAULT false,
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE
);

-- Solutions table
CREATE TABLE public.solutions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    problem_id UUID REFERENCES public.problems(id) ON DELETE CASCADE NOT NULL,
    expert_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    content TEXT NOT NULL,
    attachments JSONB DEFAULT '[]'::jsonb,
    status solution_status DEFAULT 'draft',
    votes JSONB DEFAULT '[]'::jsonb,
    rating DECIMAL(3,2) DEFAULT 0.00,
    implementation_notes TEXT,
    is_deleted BOOLEAN DEFAULT false,
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Webinars table
CREATE TABLE public.webinars (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    presenter TEXT NOT NULL,
    scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_minutes INTEGER NOT NULL,
    category TEXT NOT NULL,
    tags TEXT[] DEFAULT ARRAY[]::TEXT[],
    presentation_files JSONB DEFAULT '[]'::jsonb,
    recording_url TEXT,
    transcript TEXT,
    qa_sessions JSONB DEFAULT '[]'::jsonb,
    attendees UUID[] DEFAULT ARRAY[]::UUID[],
    status webinar_status DEFAULT 'scheduled',
    is_deleted BOOLEAN DEFAULT false,
    deleted_at TIMESTAMP WITH TIME ZONE,
    deleted_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_role ON public.users(role);
CREATE INDEX idx_users_location ON public.users(location);
CREATE INDEX idx_users_is_deleted ON public.users(is_deleted);
CREATE INDEX idx_users_deleted_at ON public.users(deleted_at);

CREATE INDEX idx_experts_user_id ON public.experts(user_id);
CREATE INDEX idx_experts_availability ON public.experts(availability);
CREATE INDEX idx_experts_rating ON public.experts(rating DESC);
CREATE INDEX idx_experts_is_deleted ON public.experts(is_deleted);
CREATE INDEX idx_experts_deleted_at ON public.experts(deleted_at);

CREATE INDEX idx_problems_status ON public.problems(status);
CREATE INDEX idx_problems_urgency ON public.problems(urgency);
CREATE INDEX idx_problems_category ON public.problems(category);
CREATE INDEX idx_problems_sector ON public.problems(sector);
CREATE INDEX idx_problems_submitted_by ON public.problems(submitted_by);
CREATE INDEX idx_problems_created_at ON public.problems(created_at DESC);
CREATE INDEX idx_problems_tags ON public.problems USING GIN(tags);
CREATE INDEX idx_problems_is_deleted ON public.problems(is_deleted);
CREATE INDEX idx_problems_deleted_at ON public.problems(deleted_at);

CREATE INDEX idx_solutions_problem_id ON public.solutions(problem_id);
CREATE INDEX idx_solutions_expert_id ON public.solutions(expert_id);
CREATE INDEX idx_solutions_status ON public.solutions(status);
CREATE INDEX idx_solutions_rating ON public.solutions(rating DESC);
CREATE INDEX idx_solutions_is_deleted ON public.solutions(is_deleted);
CREATE INDEX idx_solutions_deleted_at ON public.solutions(deleted_at);

CREATE INDEX idx_webinars_status ON public.webinars(status);
CREATE INDEX idx_webinars_category ON public.webinars(category);
CREATE INDEX idx_webinars_scheduled_at ON public.webinars(scheduled_at);
CREATE INDEX idx_webinars_tags ON public.webinars USING GIN(tags);
CREATE INDEX idx_webinars_is_deleted ON public.webinars(is_deleted);
CREATE INDEX idx_webinars_deleted_at ON public.webinars(deleted_at);

-- Full-text search indexes for Arabic and English content
CREATE INDEX idx_problems_search ON public.problems USING GIN(
    to_tsvector('arabic', title || ' ' || description)
);
CREATE INDEX idx_problems_search_en ON public.problems USING GIN(
    to_tsvector('english', title || ' ' || description)
);

CREATE INDEX idx_solutions_search ON public.solutions USING GIN(
    to_tsvector('arabic', content)
);
CREATE INDEX idx_solutions_search_en ON public.solutions USING GIN(
    to_tsvector('english', content)
);

-- Triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_experts_updated_at BEFORE UPDATE ON public.experts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_problems_updated_at BEFORE UPDATE ON public.problems
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_solutions_updated_at BEFORE UPDATE ON public.solutions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_webinars_updated_at BEFORE UPDATE ON public.webinars
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically create user profile when auth user is created
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, name, location)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', NEW.email),
        COALESCE(NEW.raw_user_meta_data->>'location', 'Damascus, Syria')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile on auth signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Soft delete functions
CREATE OR REPLACE FUNCTION public.soft_delete_user(user_id UUID)
RETURNS BOOLEAN AS $
BEGIN
    UPDATE public.users 
    SET is_deleted = true, 
        deleted_at = NOW(), 
        deleted_by = auth.uid()
    WHERE id = user_id AND is_deleted = false;
    
    -- Also soft delete expert profile if exists
    UPDATE public.experts 
    SET is_deleted = true, 
        deleted_at = NOW(), 
        deleted_by = auth.uid()
    WHERE user_id = user_id AND is_deleted = false;
    
    RETURN FOUND;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.soft_delete_problem(problem_id UUID)
RETURNS BOOLEAN AS $
BEGIN
    UPDATE public.problems 
    SET is_deleted = true, 
        deleted_at = NOW(), 
        deleted_by = auth.uid()
    WHERE id = problem_id AND is_deleted = false;
    
    -- Also soft delete all solutions for this problem
    UPDATE public.solutions 
    SET is_deleted = true, 
        deleted_at = NOW(), 
        deleted_by = auth.uid()
    WHERE problem_id = problem_id AND is_deleted = false;
    
    RETURN FOUND;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.soft_delete_solution(solution_id UUID)
RETURNS BOOLEAN AS $
BEGIN
    UPDATE public.solutions 
    SET is_deleted = true, 
        deleted_at = NOW(), 
        deleted_by = auth.uid()
    WHERE id = solution_id AND is_deleted = false;
    
    RETURN FOUND;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.soft_delete_webinar(webinar_id UUID)
RETURNS BOOLEAN AS $
BEGIN
    UPDATE public.webinars 
    SET is_deleted = true, 
        deleted_at = NOW(), 
        deleted_by = auth.uid()
    WHERE id = webinar_id AND is_deleted = false;
    
    RETURN FOUND;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Restore functions (for admins)
CREATE OR REPLACE FUNCTION public.restore_user(user_id UUID)
RETURNS BOOLEAN AS $
BEGIN
    -- Only admins can restore
    IF NOT EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = auth.uid() AND role = 'admin'
    ) THEN
        RAISE EXCEPTION 'Only admins can restore deleted users';
    END IF;
    
    UPDATE public.users 
    SET is_deleted = false, 
        deleted_at = NULL, 
        deleted_by = NULL
    WHERE id = user_id AND is_deleted = true;
    
    RETURN FOUND;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION public.restore_problem(problem_id UUID)
RETURNS BOOLEAN AS $
BEGIN
    -- Only admins can restore
    IF NOT EXISTS (
        SELECT 1 FROM public.users 
        WHERE id = auth.uid() AND role = 'admin'
    ) THEN
        RAISE EXCEPTION 'Only admins can restore deleted problems';
    END IF;
    
    UPDATE public.problems 
    SET is_deleted = false, 
        deleted_at = NULL, 
        deleted_by = NULL
    WHERE id = problem_id AND is_deleted = true;
    
    RETURN FOUND;
END;
$ LANGUAGE plpgsql SECURITY DEFINER;

-- Row Level Security (RLS) Policies

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.experts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.problems ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.solutions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.webinars ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view active profiles" ON public.users
    FOR SELECT USING (is_deleted = false);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id AND is_deleted = false);

CREATE POLICY "Admins can view all users including deleted" ON public.users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin' AND is_deleted = false
        )
    );

CREATE POLICY "Admins can manage all users" ON public.users
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin' AND is_deleted = false
        )
    );

-- Experts table policies
CREATE POLICY "Anyone can view active expert profiles" ON public.experts
    FOR SELECT USING (is_deleted = false);

CREATE POLICY "Experts can update own profile" ON public.experts
    FOR UPDATE USING (
        (user_id = auth.uid() AND is_deleted = false) OR
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin' AND is_deleted = false
        )
    );

CREATE POLICY "Users can create expert profile" ON public.experts
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Admins can view all experts including deleted" ON public.experts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin' AND is_deleted = false
        )
    );

-- Problems table policies
CREATE POLICY "Anyone can view active problems" ON public.problems
    FOR SELECT USING (is_deleted = false);

CREATE POLICY "Ministry users can create problems" ON public.problems
    FOR INSERT WITH CHECK (
        auth.uid() = submitted_by AND
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role IN ('ministry_user', 'admin') AND is_deleted = false
        )
    );

CREATE POLICY "Problem submitters and admins can update problems" ON public.problems
    FOR UPDATE USING (
        (auth.uid() = submitted_by AND is_deleted = false) OR
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin' AND is_deleted = false
        )
    );

CREATE POLICY "Admins can view all problems including deleted" ON public.problems
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin' AND is_deleted = false
        )
    );

-- Solutions table policies
CREATE POLICY "Anyone can view active solutions" ON public.solutions
    FOR SELECT USING (is_deleted = false);

CREATE POLICY "Experts can create solutions" ON public.solutions
    FOR INSERT WITH CHECK (
        auth.uid() = expert_id AND
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role IN ('expert', 'admin') AND is_deleted = false
        )
    );

CREATE POLICY "Solution authors can update own solutions" ON public.solutions
    FOR UPDATE USING (
        (auth.uid() = expert_id AND is_deleted = false) OR
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin' AND is_deleted = false
        )
    );

CREATE POLICY "Admins can view all solutions including deleted" ON public.solutions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin' AND is_deleted = false
        )
    );

-- Webinars table policies
CREATE POLICY "Anyone can view active webinars" ON public.webinars
    FOR SELECT USING (is_deleted = false);

CREATE POLICY "Admins can manage webinars" ON public.webinars
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin' AND is_deleted = false
        )
    );

CREATE POLICY "Admins can view all webinars including deleted" ON public.webinars
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin' AND is_deleted = false
        )
    );

-- Storage bucket policies will be created separately in Supabase dashboard
-- But here's the SQL for reference:

-- CREATE POLICY "Anyone can view attachments" ON storage.objects
--     FOR SELECT USING (bucket_id = 'attachments');

-- CREATE POLICY "Authenticated users can upload attachments" ON storage.objects
--     FOR INSERT WITH CHECK (
--         bucket_id = 'attachments' AND 
--         auth.role() = 'authenticated'
--     );

-- CREATE POLICY "Users can update own attachments" ON storage.objects
--     FOR UPDATE USING (
--         bucket_id = 'attachments' AND 
--         auth.uid()::text = (storage.foldername(name))[1]
--     );