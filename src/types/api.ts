/**
 * API-related type definitions for the Syrian Technical Solutions Platform
 */

// Generic API response wrapper for successful responses
export interface ApiResponse<T> {
  data: T;
  error: null;
  status: 'success';
  message?: string;
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
    hasMore?: boolean;
  };
}

// Generic API error response
export interface ApiError {
  data: null;
  error: {
    message: string;
    code: string;
    details?: unknown;
    field?: string;
    statusCode?: number;
  };
  status: 'error';
}

// Union type for all API responses
export type ApiResult<T> = ApiResponse<T> | ApiError;

// Supabase-specific error interface
export interface SupabaseError {
  message: string;
  details: string;
  hint: string;
  code: string;
}

// Database operation result
export interface DatabaseResult<T> {
  data: T | null;
  error: SupabaseError | null;
}

// Pagination parameters
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

// Sorting parameters
export interface SortParams {
  field: string;
  direction: 'asc' | 'desc';
}

// Query parameters for list endpoints
export interface QueryParams extends PaginationParams {
  search?: string;
  sort?: SortParams;
  filters?: Record<string, any>;
}

// File upload response
export interface FileUploadResponse {
  id: string;
  filename: string;
  file_path: string;
  file_size: number;
  file_type: string;
  url: string;
  uploaded_at: string;
}

// Bulk operation result
export interface BulkOperationResult<T> {
  successful: T[];
  failed: Array<{
    item: T;
    error: string;
  }>;
  total: number;
  successCount: number;
  failureCount: number;
}

// Authentication response
export interface AuthResponse {
  user: {
    id: string;
    email: string;
    email_confirmed_at?: string;
    created_at: string;
    updated_at: string;
  } | null;
  session: {
    access_token: string;
    refresh_token: string;
    expires_in: number;
    expires_at: number;
    token_type: string;
  } | null;
}

// Validation error details
export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: any;
}

// Form validation result
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// API endpoint configuration
export interface ApiEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  path: string;
  requiresAuth?: boolean;
  requiredRole?: string[];
}

// Request configuration
export interface RequestConfig {
  headers?: Record<string, string>;
  timeout?: number;
  retries?: number;
  cache?: boolean;
}

// Cache configuration
export interface CacheConfig {
  key: string;
  ttl: number; // Time to live in seconds
  staleWhileRevalidate?: number;
}

// Real-time subscription configuration
export interface SubscriptionConfig {
  table: string;
  event: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  filter?: string;
  schema?: string;
}

// WebSocket message types
export interface WebSocketMessage<T = any> {
  type: string;
  payload: T;
  timestamp: string;
  id: string;
}

// Search result with highlighting
export interface SearchResult<T> {
  item: T;
  score: number;
  highlights?: Record<string, string[]>;
}

// Search response
export interface SearchResponse<T> {
  results: SearchResult<T>[];
  total: number;
  query: string;
  took: number; // Time taken in milliseconds
  facets?: Record<string, Array<{ value: string; count: number }>>;
}

// Analytics data point
export interface AnalyticsDataPoint {
  timestamp: string;
  value: number;
  label?: string;
  metadata?: Record<string, any>;
}

// Analytics response
export interface AnalyticsResponse {
  data: AnalyticsDataPoint[];
  period: string;
  total: number;
  average: number;
  change?: {
    value: number;
    percentage: number;
    direction: 'up' | 'down' | 'stable';
  };
}

// Health check response
export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  services: Record<string, {
    status: 'up' | 'down';
    responseTime?: number;
    error?: string;
  }>;
  version: string;
}



// Utility types for API operations
export type CreateOperation<T> = Omit<T, 'id' | 'created_at' | 'updated_at'>;
export type UpdateOperation<T> = Partial<Omit<T, 'id' | 'created_at'>>;
export type ListOperation<T> = T[];

// HTTP status codes
export enum HttpStatusCode {
  OK = 200,
  CREATED = 201,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  INTERNAL_SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503,
}

// Error codes specific to the application
export enum AppErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  DUPLICATE_ENTRY = 'DUPLICATE_ENTRY',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}