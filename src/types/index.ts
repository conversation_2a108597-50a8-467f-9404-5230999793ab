/**
 * Central export file for all type definitions
 */

// User types
export * from './user';

// Problem types  
export * from './problem';

// API types
export * from './api';

// Type guards
export * from './guards';

// Re-export commonly used types for convenience
export type {
  UserData,
  UserRole,
  ExpertData,
  Problem,
  Solution,
  ProblemStatus,
  SolutionStatus,
  ApiResponse,
  ApiError,
  ApiResult,
} from './user';

export type {
  ProblemCategory,
  ProblemSector,
  ProblemUrgency,
} from './problem';

export type {
  DatabaseResult,
  ValidationError,
  ValidationResult,
} from './api';