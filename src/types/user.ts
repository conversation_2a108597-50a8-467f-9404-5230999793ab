/**
 * User-related type definitions for the Syrian Technical Solutions Platform
 */

// User role enumeration
export type UserRole = 'expert' | 'ministry_user' | 'admin';

// User profile interface
export interface UserProfile {
  avatar_url?: string;
  bio?: string;
  expertise_areas: string[];
  location?: string;
  phone?: string;
  organization?: string;
  position?: string;
  languages: string[];
}

// Core user data interface
export interface UserData {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  avatar?: string;
  bio?: string;
  location: string;
  phone_number?: string;
  organization?: string;
  position?: string;
  languages: string[];
  is_active: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  last_login_at?: string;
}

// Expert-specific data interface
export interface ExpertData {
  id: string;
  user_id: string;
  expertise_areas: ExpertiseArea[];
  experience_years: number;
  availability: ExpertAvailability;
  rating: number;
  total_contributions: number;
  success_rate: number;
  response_time_hours: number;
  portfolio: PortfolioItem[];
  certifications: Certification[];
  is_verified: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  // Joined user data
  users?: UserData;
}

// Expert availability status
export type ExpertAvailability = 'available' | 'busy' | 'unavailable';

// Expertise area interface
export interface ExpertiseArea {
  category: string;
  subcategory?: string;
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
}

// Portfolio item interface
export interface PortfolioItem {
  id: string;
  title: string;
  description: string;
  url?: string;
  image_url?: string;
  technologies: string[];
  created_at: string;
}

// Certification interface
export interface Certification {
  id: string;
  name: string;
  issuer: string;
  issue_date: string;
  expiry_date?: string;
  credential_id?: string;
  credential_url?: string;
}

// User registration data
export interface UserRegistrationData {
  email: string;
  password: string;
  name: string;
  role: UserRole;
  location?: string;
  organization?: string;
  position?: string;
  phone_number?: string;
  bio?: string;
}

// User update data
export interface UserUpdateData {
  name?: string;
  bio?: string;
  location?: string;
  phone_number?: string;
  organization?: string;
  position?: string;
  languages?: string[];
  avatar?: string;
}

// Expert profile creation data
export interface ExpertProfileData {
  user_id: string;
  expertise_areas: ExpertiseArea[];
  experience_years: number;
  availability: ExpertAvailability;
  portfolio?: PortfolioItem[];
  certifications?: Certification[];
}

// Expert profile update data
export interface ExpertProfileUpdateData {
  expertise_areas?: ExpertiseArea[];
  experience_years?: number;
  availability?: ExpertAvailability;
  portfolio?: PortfolioItem[];
  certifications?: Certification[];
  bio?: string;
}

// User filters for queries
export interface UserFilters {
  role?: UserRole;
  location?: string;
  organization?: string;
  is_active?: boolean;
  includeDeleted?: boolean;
}

// Expert filters for queries
export interface ExpertFilters {
  availability?: ExpertAvailability;
  expertise?: string;
  location?: string;
  minRating?: number;
  minExperience?: number;
  includeDeleted?: boolean;
}

// Expert matching preferences interface
export interface ExpertMatchingPreferences {
  id: string;
  expert_id: string;
  preferred_problem_categories: string[];
  preferred_sectors: string[];
  max_problems_per_month: number;
  min_compensation: number;
  availability_schedule: AvailabilitySchedule;
  response_time_preference: number; // hours
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Availability schedule interface
export interface AvailabilitySchedule {
  monday: DaySchedule;
  tuesday: DaySchedule;
  wednesday: DaySchedule;
  thursday: DaySchedule;
  friday: DaySchedule;
  saturday: DaySchedule;
  sunday: DaySchedule;
}

// Day schedule interface
export interface DaySchedule {
  start: string; // HH:MM format
  end: string; // HH:MM format
  available: boolean;
}

// Problem-expert match interface
export interface ProblemExpertMatch {
  id: string;
  problem_id: string;
  expert_id: string;
  match_score: number;
  match_reasons: string[];
  expert_interest_level?: number;
  auto_assigned: boolean;
  expert_response_status: 'pending' | 'accepted' | 'declined' | 'expired';
  responded_at?: string;
  expires_at: string;
  created_at: string;
  updated_at: string;
  // Joined data
  problems?: any; // Will be defined in problem.ts
  experts?: ExpertData;
}

// Expert workload tracking interface
export interface ExpertWorkload {
  id: string;
  expert_id: string;
  month_year: string; // YYYY-MM-DD format (first day of month)
  problems_assigned: number;
  problems_completed: number;
  average_response_time_hours: number;
  total_compensation: number;
  created_at: string;
  updated_at: string;
  // Joined data
  experts?: ExpertData;
}

// Matching algorithm configuration interface
export interface MatchingAlgorithmConfig {
  id: string;
  name: string;
  description?: string;
  weights: MatchingWeights;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Matching weights interface
export interface MatchingWeights {
  category_match: number;
  sector_match: number;
  expertise_match: number;
  availability: number;
  response_time: number;
}

// Expert matching result interface
export interface ExpertMatchResult {
  expert_id: string;
  match_score: number;
  match_reasons: string[];
  expert?: ExpertData;
}

// Expert matching preferences update data
export interface ExpertMatchingPreferencesUpdateData {
  preferred_problem_categories?: string[];
  preferred_sectors?: string[];
  max_problems_per_month?: number;
  min_compensation?: number;
  availability_schedule?: AvailabilitySchedule;
  response_time_preference?: number;
  is_active?: boolean;
}

// Expert match response data
export interface ExpertMatchResponseData {
  expert_response_status: 'accepted' | 'declined';
  expert_interest_level?: number;
}

