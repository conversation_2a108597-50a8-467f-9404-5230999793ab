/**
 * Problem-related type definitions for the Syrian Technical Solutions Platform
 */

import { UserData } from './user';

// Problem status enumeration
export type ProblemStatus = 'open' | 'in_progress' | 'resolved' | 'closed' | 'archived';

// Problem urgency levels
export type ProblemUrgency = 'low' | 'medium' | 'high' | 'critical';

// Problem categories
export type ProblemCategory = 
  | 'technical' 
  | 'infrastructure' 
  | 'policy' 
  | 'resource' 
  | 'training' 
  | 'communication'
  | 'other';

// Problem sectors
export type ProblemSector = 
  | 'health' 
  | 'education' 
  | 'transportation' 
  | 'energy' 
  | 'water' 
  | 'agriculture'
  | 'telecommunications'
  | 'finance'
  | 'governance'
  | 'other';

// File attachment interface
export interface Attachment {
  id: string;
  filename: string;
  file_path: string;
  file_size: number;
  file_type: string;
  uploaded_by: string;
  uploaded_at: string;
}

// Problem interface
export interface Problem {
  id: string;
  title: string;
  description: string;
  category: ProblemCategory;
  sector: ProblemSector;
  status: ProblemStatus;
  urgency: ProblemUrgency;
  submitted_by: string;
  assigned_to?: string;
  attachments: Attachment[];
  tags: string[];
  location?: string;
  affected_population?: number;
  estimated_cost?: number;
  deadline?: string;
  is_public: boolean;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  // Joined user data
  users?: UserData;
  solutions?: Solution[];
}

// Solution status enumeration
export type SolutionStatus = 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected' | 'implemented';

// Solution interface
export interface Solution {
  id: string;
  problem_id: string;
  expert_id: string;
  title: string;
  description: string;
  implementation_plan: string;
  estimated_cost?: number;
  estimated_timeline?: string;
  required_resources: string[];
  status: SolutionStatus;
  rating: number;
  votes: Vote[];
  attachments: Attachment[];
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
  submitted_at?: string;
  approved_at?: string;
  implemented_at?: string;
  // Joined data
  users?: UserData;
  problems?: Problem;
}

// Vote interface
export interface Vote {
  userId: string;
  type: 'up' | 'down' | 'rating';
  rating?: number;
  feedback?: string;
  createdAt: string;
}

// Problem creation data
export interface ProblemCreateData {
  title: string;
  description: string;
  category: ProblemCategory;
  sector: ProblemSector;
  urgency: ProblemUrgency;
  submitted_by: string;
  tags?: string[];
  location?: string;
  affected_population?: number;
  estimated_cost?: number;
  deadline?: string;
  is_public?: boolean;
}

// Problem update data
export interface ProblemUpdateData {
  title?: string;
  description?: string;
  category?: ProblemCategory;
  sector?: ProblemSector;
  status?: ProblemStatus;
  urgency?: ProblemUrgency;
  assigned_to?: string;
  tags?: string[];
  location?: string;
  affected_population?: number;
  estimated_cost?: number;
  deadline?: string;
  is_public?: boolean;
}

// Solution creation data
export interface SolutionCreateData {
  problem_id: string;
  expert_id: string;
  title: string;
  description: string;
  implementation_plan: string;
  estimated_cost?: number;
  estimated_timeline?: string;
  required_resources?: string[];
}

// Solution update data
export interface SolutionUpdateData {
  title?: string;
  description?: string;
  implementation_plan?: string;
  estimated_cost?: number;
  estimated_timeline?: string;
  required_resources?: string[];
  status?: SolutionStatus;
}

// Problem filters for queries
export interface ProblemFilters {
  status?: ProblemStatus;
  urgency?: ProblemUrgency;
  category?: ProblemCategory;
  sector?: ProblemSector;
  submittedBy?: string;
  assignedTo?: string;
  isPublic?: boolean;
  includeDeleted?: boolean;
  dateFrom?: string;
  dateTo?: string;
  location?: string;
}

// Solution filters for queries
export interface SolutionFilters {
  problemId?: string;
  expertId?: string;
  status?: SolutionStatus;
  minRating?: number;
  includeDeleted?: boolean;
  dateFrom?: string;
  dateTo?: string;
}

// Search parameters
export interface SearchParams {
  query: string;
  category?: ProblemCategory;
  sector?: ProblemSector;
  urgency?: ProblemUrgency;
  status?: ProblemStatus;
  language?: 'ar' | 'en';
  includeDeleted?: boolean;
  limit?: number;
  offset?: number;
}

// Rating data
export interface RatingData {
  userId: string;
  rating: number;
  feedback?: string;
}

