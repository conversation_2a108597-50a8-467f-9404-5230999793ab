/**
 * Type guards for runtime type checking
 */

import { 
  UserData, 
  UserRole, 
  ExpertData, 
  ExpertAvailability,
  Problem,
  ProblemStatus,
  ProblemUrgency,
  ProblemCategory,
  ProblemSector,
  Solution,
  SolutionStatus,
  Vote
} from './index';

// User type guards
export function isUserRole(role: unknown): role is UserRole {
  return typeof role === 'string' && ['expert', 'ministry_user', 'admin'].includes(role);
}

export function isExpertAvailability(status: unknown): status is ExpertAvailability {
  return typeof status === 'string' && ['available', 'busy', 'unavailable'].includes(status);
}

export function isValidUserData(data: unknown): data is UserData {
  if (typeof data !== 'object' || data === null) return false;
  
  const user = data as Record<string, unknown>;
  
  return (
    typeof user.id === 'string' &&
    typeof user.email === 'string' &&
    typeof user.name === 'string' &&
    isUserRole(user.role) &&
    typeof user.is_active === 'boolean' &&
    typeof user.created_at === 'string'
  );
}

export function isValidExpertData(data: unknown): data is ExpertData {
  if (typeof data !== 'object' || data === null) return false;
  
  const expert = data as Record<string, unknown>;
  
  return (
    typeof expert.id === 'string' &&
    typeof expert.user_id === 'string' &&
    Array.isArray(expert.expertise_areas) &&
    typeof expert.experience_years === 'number' &&
    isExpertAvailability(expert.availability) &&
    typeof expert.rating === 'number'
  );
}

// Problem type guards
export function isProblemStatus(status: unknown): status is ProblemStatus {
  return typeof status === 'string' && 
    ['open', 'in_progress', 'resolved', 'closed', 'archived'].includes(status);
}

export function isProblemUrgency(urgency: unknown): urgency is ProblemUrgency {
  return typeof urgency === 'string' && 
    ['low', 'medium', 'high', 'critical'].includes(urgency);
}

export function isProblemCategory(category: unknown): category is ProblemCategory {
  return typeof category === 'string' && [
    'technical', 
    'infrastructure', 
    'policy', 
    'resource', 
    'training', 
    'communication',
    'other'
  ].includes(category);
}

export function isProblemSector(sector: unknown): sector is ProblemSector {
  return typeof sector === 'string' && [
    'health', 
    'education', 
    'transportation', 
    'energy', 
    'water', 
    'agriculture',
    'telecommunications',
    'finance',
    'governance',
    'other'
  ].includes(sector);
}

export function isValidProblem(data: unknown): data is Problem {
  if (typeof data !== 'object' || data === null) return false;
  
  const problem = data as Record<string, unknown>;
  
  return (
    typeof problem.id === 'string' &&
    typeof problem.title === 'string' &&
    typeof problem.description === 'string' &&
    isProblemCategory(problem.category) &&
    isProblemSector(problem.sector) &&
    isProblemStatus(problem.status) &&
    isProblemUrgency(problem.urgency) &&
    typeof problem.submitted_by === 'string' &&
    typeof problem.created_at === 'string'
  );
}

// Solution type guards
export function isSolutionStatus(status: unknown): status is SolutionStatus {
  return typeof status === 'string' && 
    ['draft', 'submitted', 'under_review', 'approved', 'rejected', 'implemented'].includes(status);
}

export function isValidSolution(data: unknown): data is Solution {
  if (typeof data !== 'object' || data === null) return false;
  
  const solution = data as Record<string, unknown>;
  
  return (
    typeof solution.id === 'string' &&
    typeof solution.problem_id === 'string' &&
    typeof solution.expert_id === 'string' &&
    typeof solution.title === 'string' &&
    typeof solution.description === 'string' &&
    isSolutionStatus(solution.status) &&
    typeof solution.created_at === 'string'
  );
}

export function isValidVote(data: unknown): data is Vote {
  if (typeof data !== 'object' || data === null) return false;
  
  const vote = data as Record<string, unknown>;
  
  return (
    typeof vote.userId === 'string' &&
    typeof vote.type === 'string' &&
    ['up', 'down', 'rating'].includes(vote.type) &&
    typeof vote.createdAt === 'string'
  );
}

// Generic type guards
export function isString(value: unknown): value is string {
  return typeof value === 'string';
}

export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value);
}

export function isBoolean(value: unknown): value is boolean {
  return typeof value === 'boolean';
}

export function isArray<T>(value: unknown, itemGuard?: (item: unknown) => item is T): value is T[] {
  if (!Array.isArray(value)) return false;
  if (!itemGuard) return true;
  return value.every(itemGuard);
}

export function isObject(value: unknown): value is Record<string, unknown> {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

// Utility function to safely parse JSON with type checking
export function safeJsonParse<T>(
  json: string, 
  typeGuard: (data: unknown) => data is T
): T | null {
  try {
    const parsed = JSON.parse(json);
    return typeGuard(parsed) ? parsed : null;
  } catch {
    return null;
  }
}

// Utility function to validate API responses
export function validateApiResponse<T>(
  response: unknown,
  dataGuard: (data: unknown) => data is T
): { success: true; data: T } | { success: false; error: string } {
  if (!isObject(response)) {
    return { success: false, error: 'Response is not an object' };
  }

  if (response.error) {
    return { success: false, error: String(response.error) };
  }

  if (!dataGuard(response.data)) {
    return { success: false, error: 'Invalid data format' };
  }

  return { success: true, data: response.data };
}