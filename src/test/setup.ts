// Test setup file for vitest
import { beforeAll, afterAll, vi } from 'vitest'
import '@testing-library/jest-dom'

// Mock environment variables for testing
beforeAll(() => {
  // Set up test environment variables
  vi.stubEnv('VITE_SUPABASE_URL', 'http://localhost:54321')
  vi.stubEnv('VITE_SUPABASE_ANON_KEY', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0')
})

afterAll(() => {
  // Clean up after tests
  vi.unstubAllEnvs()
})