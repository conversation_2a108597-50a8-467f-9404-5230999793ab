import { describe, it, expect, vi, beforeEach, beforeAll } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { <PERSON><PERSON>erRouter } from 'react-router-dom'
import { LoginForm } from '@/components/auth/LoginForm'
import { RegisterForm } from '@/components/auth/RegisterForm'
import { LanguageProvider } from '@/contexts/LanguageContext'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock the auth context
const mockSignIn = vi.fn()
const mockSignUp = vi.fn()

vi.mock('@/components/auth/AuthProvider', () => ({
  useAuthContext: () => ({
    signIn: mockSignIn,
    signUp: mockSignUp,
    loading: false,
    user: null
  })
}))

// Mock the error logger
vi.mock('@/lib/errorLogger', () => ({
  errorLogger: {
    logError: vi.fn(),
    reportError: vi.fn()
  }
}))

// Mock the accessibility hooks to avoid issues
vi.mock('@/hooks/useAccessibility', () => ({
  useTouchTargets: () => ({
    ensureTouchTarget: vi.fn(),
    validateTouchTargets: vi.fn(),
    MIN_TOUCH_SIZE: 44
  }),
  useScreenReader: () => ({
    announce: vi.fn(),
    announceError: vi.fn(),
    announceSuccess: vi.fn(),
    announceNavigation: vi.fn()
  })
}))

// Mock the optimization hooks
vi.mock('@/hooks/useOptimization', () => ({
  useOptimizedCallback: (callback: any) => callback
}))

// Mock the loading state manager
vi.mock('@/components/common/LoadingStateManager', () => ({
  LoadingStateManager: ({ children, isLoading, loadingComponent }: any) => 
    isLoading ? loadingComponent : children,
  useLoadingState: () => ({
    isLoading: false,
    error: null,
    startLoading: vi.fn(),
    stopLoading: vi.fn(),
    setLoadingError: vi.fn()
  })
}))

// Mock the error boundary helpers
vi.mock('@/utils/errorBoundaryHelpers', () => ({
  FormErrorBoundary: ({ children }: any) => children
}))

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <LanguageProvider>
          {children}
        </LanguageProvider>
      </BrowserRouter>
    </QueryClientProvider>
  )
}

describe('Authentication Forms', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('LoginForm', () => {
    it('renders without crashing', () => {
      render(
        <TestWrapper>
          <LoginForm />
        </TestWrapper>
      )

      expect(screen.getByRole('heading', { name: /login/i })).toBeInTheDocument()
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /login/i })).toBeInTheDocument()
    })

    it('handles form submission', async () => {
      mockSignIn.mockResolvedValue({ error: null })

      render(
        <TestWrapper>
          <LoginForm />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /login/i })

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockSignIn).toHaveBeenCalledWith('<EMAIL>', 'password123')
      })
    })

    it('displays error messages', async () => {
      mockSignIn.mockResolvedValue({ 
        error: { message: 'Invalid login credentials' } 
      })

      render(
        <TestWrapper>
          <LoginForm />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /login/i })

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByRole('alert')).toBeInTheDocument()
      })
    })

    it('toggles password visibility', () => {
      render(
        <TestWrapper>
          <LoginForm />
        </TestWrapper>
      )

      const passwordInput = screen.getByLabelText(/password/i)
      const toggleButton = screen.getByRole('button', { name: /show password/i })

      expect(passwordInput).toHaveAttribute('type', 'password')

      fireEvent.click(toggleButton)

      expect(passwordInput).toHaveAttribute('type', 'text')
    })
  })

  describe('RegisterForm', () => {
    it('renders without crashing', () => {
      render(
        <TestWrapper>
          <RegisterForm />
        </TestWrapper>
      )

      expect(screen.getByRole('heading', { name: /register/i })).toBeInTheDocument()
      expect(screen.getByLabelText(/full name/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/^password/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /register/i })).toBeInTheDocument()
    })

    it('validates password confirmation', async () => {
      render(
        <TestWrapper>
          <RegisterForm />
        </TestWrapper>
      )

      const nameInput = screen.getByLabelText(/full name/i)
      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/^password/i)
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i)
      const submitButton = screen.getByRole('button', { name: /register/i })

      fireEvent.change(nameInput, { target: { value: 'Test User' } })
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      fireEvent.change(confirmPasswordInput, { target: { value: 'differentpassword' } })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByRole('alert')).toBeInTheDocument()
        expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument()
      })
    })

    it('handles successful registration', async () => {
      mockSignUp.mockResolvedValue({ error: null })

      render(
        <TestWrapper>
          <RegisterForm />
        </TestWrapper>
      )

      const nameInput = screen.getByLabelText(/full name/i)
      const emailInput = screen.getByLabelText(/email/i)
      const passwordInput = screen.getByLabelText(/^password/i)
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i)
      const submitButton = screen.getByRole('button', { name: /register/i })

      fireEvent.change(nameInput, { target: { value: 'Test User' } })
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(passwordInput, { target: { value: 'password123' } })
      fireEvent.change(confirmPasswordInput, { target: { value: 'password123' } })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockSignUp).toHaveBeenCalledWith(
          '<EMAIL>',
          'password123',
          expect.objectContaining({
            name: 'Test User'
          })
        )
      })
    })
  })
})