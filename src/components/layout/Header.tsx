import { Link, useNavigate } from 'react-router-dom'
import { useAuthContext, useRoleAccess } from '@/components/auth/AuthProvider'
import { useLanguage } from '@/contexts/LanguageContext'
import { useDeviceType } from '@/hooks/use-mobile'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { LanguageSwitcher } from '@/components/ui/LanguageSwitcher'
import { GlobalSearch } from '@/components/search/GlobalSearch'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { 
  User, 
  Settings, 
  LogOut, 
  Shield, 
  Users, 
  BarChart3,
  Menu,
  Home,
  UserCheck,
  FileText,
  Search
} from 'lucide-react'
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from '@/components/ui/sheet'

export function Header() {
  const { user, userData, signOut, loading } = useAuthContext()
  const { isAdmin, canCreateProblems } = useRoleAccess()
  const { t } = useLanguage()
  const { isMobile, isTouchDevice } = useDeviceType()
  const navigate = useNavigate()

  const handleSignOut = async () => {
    await signOut()
    navigate('/')
  }

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin': return t('role.admin')
      case 'expert': return t('role.expert')
      case 'ministry_user': return t('role.ministry_user')
      default: return role
    }
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin': return 'destructive'
      case 'expert': return 'default'
      case 'ministry_user': return 'secondary'
      default: return 'outline'
    }
  }

  const NavigationLinks = ({ mobile = false }: { mobile?: boolean }) => {
    const linkClass = mobile 
      ? "flex items-center gap-2 px-4 py-3 text-sm hover:bg-gray-100 rounded-md cursor-pointer select-none touch-manipulation"
      : "flex items-center gap-1 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors cursor-pointer select-none relative z-10"

    return (
      <>
        <Link to="/" className={linkClass}>
          <Home className="h-4 w-4" />
          {t('nav.home')}
        </Link>
        
        <Link to="/problems" className={linkClass}>
          <FileText className="h-4 w-4" />
          {t('nav.problems')}
        </Link>
        
        <Link to="/experts" className={linkClass}>
          <UserCheck className="h-4 w-4" />
          {t('nav.experts')}
        </Link>

        {canCreateProblems && (
          <Link to="/problems/new" className={linkClass}>
            <FileText className="h-4 w-4" />
            {t('nav.submit_problem')}
          </Link>
        )}

        <Link to="/search" className={linkClass}>
          <Search className="h-4 w-4" />
          {t('nav.search')}
        </Link>

        {isAdmin && (
          <Link to="/admin" className={linkClass}>
            <Shield className="h-4 w-4" />
            {t('nav.admin_panel')}
          </Link>
        )}
      </>
    )
  }

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className={`flex justify-between items-center ${isMobile ? 'h-14' : 'h-16'}`}>
          {/* Logo and Brand */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-2 space-x-reverse touch-manipulation">
              <div className={`bg-blue-600 rounded-lg flex items-center justify-center ${isMobile ? 'w-7 h-7' : 'w-8 h-8'}`}>
                <span className={`text-white font-bold ${isMobile ? 'text-xs' : 'text-sm'}`}>س</span>
              </div>
              <div className={`${isMobile ? 'hidden' : 'hidden sm:block'}`}>
                <h1 className="text-xl font-bold text-gray-900">{t('brand.title')}</h1>
                <p className="text-xs text-gray-600">{t('brand.subtitle')}</p>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation and Search */}
          <div className="hidden md:flex items-center space-x-6 space-x-reverse flex-1 max-w-4xl">
            <nav className="flex items-center space-x-4 space-x-reverse">
              <NavigationLinks />
            </nav>
            
            {/* Integrated Search */}
            <div className="flex-1 max-w-md">
              <GlobalSearch />
            </div>
          </div>

          {/* Language Switcher and User Menu */}
          <div className={`flex items-center ${isMobile ? 'space-x-2' : 'space-x-4'} space-x-reverse`}>
            {!isMobile && <LanguageSwitcher />}
            {loading ? (
              <div className={`bg-gray-200 rounded-full animate-pulse ${isMobile ? 'w-7 h-7' : 'w-8 h-8'}`} />
            ) : user && userData ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className={`relative rounded-full touch-manipulation ${isMobile ? 'h-8 w-8' : 'h-10 w-10'}`}>
                    <Avatar className={isMobile ? 'h-8 w-8' : 'h-10 w-10'}>
                      <AvatarImage src={userData.avatar} alt={userData.name} />
                      <AvatarFallback>{userData.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">{userData.name}</p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {userData.email}
                      </p>
                      <Badge 
                        variant={getRoleBadgeVariant(userData.role)} 
                        className="w-fit text-xs mt-1"
                      >
                        {getRoleLabel(userData.role)}
                      </Badge>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  
                  <DropdownMenuItem asChild>
                    <Link to="/profile" className="flex items-center">
                      <User className="mr-2 h-4 w-4" />
                      {t('nav.profile')}
                    </Link>
                  </DropdownMenuItem>

                  {userData.role === 'expert' && (
                    <DropdownMenuItem asChild>
                      <Link to="/experts/dashboard" className="flex items-center">
                        <UserCheck className="mr-2 h-4 w-4" />
                        لوحة تحكم الخبير
                      </Link>
                    </DropdownMenuItem>
                  )}

                  <DropdownMenuItem asChild>
                    <Link to="/settings" className="flex items-center">
                      <Settings className="mr-2 h-4 w-4" />
                      {t('nav.settings')}
                    </Link>
                  </DropdownMenuItem>

                  {isAdmin && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link to="/admin/users" className="flex items-center">
                          <Users className="mr-2 h-4 w-4" />
                          {t('nav.user_management')}
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link to="/admin/analytics" className="flex items-center">
                          <BarChart3 className="mr-2 h-4 w-4" />
                          {t('nav.analytics')}
                        </Link>
                      </DropdownMenuItem>
                    </>
                  )}

                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleSignOut}>
                    <LogOut className="mr-2 h-4 w-4" />
                    {t('nav.logout')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className={`flex items-center ${isMobile ? 'space-x-1' : 'space-x-2'} space-x-reverse`}>
                <Button asChild variant="ghost" size={isMobile ? 'sm' : 'sm'} className="touch-manipulation">
                  <Link to="/auth/login">{t('nav.login')}</Link>
                </Button>
                <Button asChild size={isMobile ? 'sm' : 'sm'} className="touch-manipulation">
                  <Link to="/auth/register">{t('nav.register')}</Link>
                </Button>
              </div>
            )}

            {/* Mobile Menu */}
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="md:hidden touch-manipulation">
                  <Menu className={`${isMobile ? 'h-4 w-4' : 'h-5 w-5'}`} />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className={`${isMobile ? 'w-full' : 'w-80'}`}>
                <div className="flex flex-col space-y-4 mt-6">
                  <div className="text-lg font-semibold">{t('nav.menu')}</div>
                  
                  {/* Language Switcher in Mobile Menu */}
                  {isMobile && (
                    <div className="border-b pb-4">
                      <LanguageSwitcher />
                    </div>
                  )}
                  
                  <nav className="flex flex-col space-y-2">
                    <NavigationLinks mobile />
                  </nav>
                  
                  {user && userData && (
                    <>
                      <div className="border-t pt-4">
                        <div className="flex items-center space-x-3 space-x-reverse mb-4">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={userData.avatar} alt={userData.name} />
                            <AvatarFallback>{userData.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="text-sm font-medium">{userData.name}</p>
                            <Badge 
                              variant={getRoleBadgeVariant(userData.role)} 
                              className="text-xs"
                            >
                              {getRoleLabel(userData.role)}
                            </Badge>
                          </div>
                        </div>
                        
                        <div className="flex flex-col space-y-2">
                          <Link 
                            to="/profile" 
                            className="flex items-center gap-2 px-4 py-3 text-sm hover:bg-gray-100 rounded-md touch-manipulation"
                          >
                            <User className="h-4 w-4" />
                            {t('nav.profile')}
                          </Link>
                          
                          <Button 
                            variant="ghost" 
                            onClick={handleSignOut}
                            className="justify-start px-4 py-3 text-sm touch-manipulation"
                          >
                            <LogOut className="h-4 w-4 mr-2" />
                            {t('nav.logout')}
                          </Button>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  )
}