/**
 * Syrian Analytics Dashboard
 * 
 * Development dashboard for viewing Syrian identity feature analytics.
 * Shows usage patterns, performance metrics, and adoption rates.
 * Only available in development mode.
 */

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useSyrianAnalyticsSummary, useSyrianAnalyticsDebug } from '@/lib/analytics/hooks';

export function SyrianAnalyticsDashboard() {
  const { getSummary, clearData, isEnabled } = useSyrianAnalyticsSummary();
  const { logSummary, logEvents } = useSyrianAnalyticsDebug();
  const [summary, setSummary] = useState(getSummary());
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Auto-refresh summary data
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      setSummary(getSummary());
    }, 5000); // Refresh every 5 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, getSummary]);

  // Manual refresh
  const handleRefresh = () => {
    setSummary(getSummary());
  };

  // Clear all data
  const handleClearData = () => {
    clearData();
    setSummary(getSummary());
  };

  if (!isEnabled) {
    return (
      <div className="p-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Analytics Not Available</h2>
        <p className="text-muted-foreground">
          Syrian analytics is not enabled. Enable it in development mode to see usage data.
        </p>
      </div>
    );
  }

  return (
    <div className="p-8 space-y-6 max-w-6xl">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Syrian Analytics Dashboard</h1>
          <p className="text-muted-foreground">
            Real-time usage and performance metrics for Syrian identity features
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            {autoRefresh ? 'Pause' : 'Resume'} Auto-refresh
          </Button>
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            Refresh
          </Button>
          <Button variant="outline" size="sm" onClick={logSummary}>
            Log Summary
          </Button>
          <Button variant="outline" size="sm" onClick={logEvents}>
            Log Events
          </Button>
          <Button variant="destructive" size="sm" onClick={handleClearData}>
            Clear Data
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Events</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalEvents}</div>
            <p className="text-xs text-muted-foreground">
              All tracked interactions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Avg Paint Time</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {summary.performanceMetrics.averagePaintTime.toFixed(1)}ms
            </div>
            <p className="text-xs text-muted-foreground">
              Pattern rendering performance
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Renders</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {summary.performanceMetrics.totalRenders}
            </div>
            <p className="text-xs text-muted-foreground">
              Pattern render count
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Error Count</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${summary.errorCount > 0 ? 'text-red-600' : 'text-green-600'}`}>
              {summary.errorCount}
            </div>
            <p className="text-xs text-muted-foreground">
              Syrian feature errors
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Component Usage */}
      <Card>
        <CardHeader>
          <CardTitle>Component Usage</CardTitle>
        </CardHeader>
        <CardContent>
          {Object.keys(summary.componentUsage).length > 0 ? (
            <div className="space-y-4">
              {Object.entries(summary.componentUsage).map(([component, count]) => (
                <div key={component} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 bg-syrian-gold rounded-full"></div>
                    <span className="font-medium">{component}</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span className="text-sm text-muted-foreground">{count} uses</span>
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-syrian-gold h-2 rounded-full"
                        style={{ 
                          width: `${Math.min(100, (count / Math.max(...Object.values(summary.componentUsage))) * 100)}%` 
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground text-center py-8">
              No component usage data yet. Interact with Syrian components to see data.
            </p>
          )}
        </CardContent>
      </Card>

      {/* Pattern Usage */}
      <Card>
        <CardHeader>
          <CardTitle>Pattern Usage</CardTitle>
        </CardHeader>
        <CardContent>
          {Object.keys(summary.patternUsage).length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(summary.patternUsage).map(([pattern, count]) => (
                <div key={pattern} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium capitalize">{pattern.replace(/([A-Z])/g, ' $1').trim()}</h3>
                    <span className="text-sm text-muted-foreground">{count} renders</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-syrian-blue h-2 rounded-full"
                      style={{ 
                        width: `${Math.min(100, (count / Math.max(...Object.values(summary.patternUsage))) * 100)}%` 
                      }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground text-center py-8">
              No pattern usage data yet. Use Syrian patterns to see data.
            </p>
          )}
        </CardContent>
      </Card>

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-syrian-green">
                {summary.performanceMetrics.averagePaintTime.toFixed(1)}ms
              </div>
              <p className="text-sm text-muted-foreground">Average Paint Time</p>
              <p className="text-xs text-muted-foreground mt-1">
                Target: ≤16ms (60fps)
              </p>
            </div>
            <div className="text-center">
              <div className={`text-3xl font-bold ${
                summary.performanceMetrics.maxPaintTime > 16 ? 'text-red-600' : 'text-green-600'
              }`}>
                {summary.performanceMetrics.maxPaintTime.toFixed(1)}ms
              </div>
              <p className="text-sm text-muted-foreground">Max Paint Time</p>
              <p className="text-xs text-muted-foreground mt-1">
                {summary.performanceMetrics.maxPaintTime > 16 ? 'Above target' : 'Within target'}
              </p>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-syrian-blue">
                {summary.performanceMetrics.totalRenders}
              </div>
              <p className="text-sm text-muted-foreground">Total Renders</p>
              <p className="text-xs text-muted-foreground mt-1">
                Pattern render count
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Status */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <h3 className="font-medium">Paint Performance</h3>
                <p className="text-sm text-muted-foreground">
                  Average pattern rendering time
                </p>
              </div>
              <div className={`px-3 py-1 rounded-full text-sm ${
                summary.performanceMetrics.averagePaintTime <= 16 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {summary.performanceMetrics.averagePaintTime <= 16 ? 'Optimal' : 'Needs Optimization'}
              </div>
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <h3 className="font-medium">Error Rate</h3>
                <p className="text-sm text-muted-foreground">
                  Syrian feature error count
                </p>
              </div>
              <div className={`px-3 py-1 rounded-full text-sm ${
                summary.errorCount === 0 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {summary.errorCount === 0 ? 'No Errors' : `${summary.errorCount} Errors`}
              </div>
            </div>

            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div>
                <h3 className="font-medium">Feature Adoption</h3>
                <p className="text-sm text-muted-foreground">
                  Syrian components in use
                </p>
              </div>
              <div className="px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                {Object.keys(summary.componentUsage).length} Components
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Development Info */}
      <Card>
        <CardHeader>
          <CardTitle>Development Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>Analytics Status:</strong> {isEnabled ? 'Enabled' : 'Disabled'}</p>
            <p><strong>Auto-refresh:</strong> {autoRefresh ? 'On (5s)' : 'Off'}</p>
            <p><strong>Data Storage:</strong> Local Storage (development only)</p>
            <p><strong>Privacy Mode:</strong> Enabled (no personal data collected)</p>
            <p className="text-muted-foreground mt-4">
              This dashboard is only available in development mode. 
              Analytics data is stored locally and not sent to any external service.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
