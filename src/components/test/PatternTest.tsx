/**
 * Syrian Pattern System Test Component
 * 
 * This component tests and validates the Syrian pattern background system.
 * Use this to verify pattern rendering, performance, and responsive behavior.
 * Remove this component before production deployment.
 */

import React, { useState } from 'react';
import { 
  PatternBackground, 
  PatternPresets,
  HeroPatternBackground,
  ContentPatternBackground,
  FooterPatternBackground,
  CardPatternBackground,
  type SyrianPatternName 
} from '@/components/ui/pattern-background';
import { 
  syrianPatterns, 
  getAllSyrianPatterns, 
  estimateTotalPatternSize 
} from '@/lib/patterns/syrian-patterns';

export function PatternTest() {
  const [selectedPattern, setSelectedPattern] = useState<SyrianPatternName>('damascusStar');
  const [intensity, setIntensity] = useState<'subtle' | 'moderate' | 'rich'>('moderate');
  const [animated, setAnimated] = useState(false);
  const [interactive, setInteractive] = useState(false);
  const [performanceMonitoring, setPerformanceMonitoring] = useState(false);
  const [layeredPattern, setLayeredPattern] = useState<SyrianPatternName | undefined>();

  const allPatterns = getAllSyrianPatterns();
  const patternNames = Object.keys(syrianPatterns) as SyrianPatternName[];

  return (
    <div className="p-8 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Syrian Pattern System Test</h1>
        <p className="text-muted-foreground">
          Testing performance-optimized Syrian cultural patterns
        </p>
        <p className="text-sm text-muted-foreground mt-1">
          Total Pattern Bundle Size: {estimateTotalPatternSize()}
        </p>
      </div>

      {/* Pattern Controls */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Pattern Controls</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          
          <div className="p-4 border rounded-lg">
            <h3 className="font-medium mb-2">Pattern</h3>
            <select 
              value={selectedPattern}
              onChange={(e) => setSelectedPattern(e.target.value as SyrianPatternName)}
              className="w-full p-2 border rounded"
            >
              {patternNames.map((name) => (
                <option key={name} value={name}>
                  {syrianPatterns[name].name}
                </option>
              ))}
            </select>
          </div>
          
          <div className="p-4 border rounded-lg">
            <h3 className="font-medium mb-2">Intensity</h3>
            <div className="space-y-1">
              {(['subtle', 'moderate', 'rich'] as const).map((level) => (
                <label key={level} className="flex items-center space-x-2">
                  <input
                    type="radio"
                    name="intensity"
                    value={level}
                    checked={intensity === level}
                    onChange={(e) => setIntensity(e.target.value as any)}
                  />
                  <span className="capitalize">{level}</span>
                </label>
              ))}
            </div>
          </div>
          
          <div className="p-4 border rounded-lg">
            <h3 className="font-medium mb-2">Effects</h3>
            <div className="space-y-2">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={animated}
                  onChange={(e) => setAnimated(e.target.checked)}
                />
                <span>Animated</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={interactive}
                  onChange={(e) => setInteractive(e.target.checked)}
                />
                <span>Interactive</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={performanceMonitoring}
                  onChange={(e) => setPerformanceMonitoring(e.target.checked)}
                />
                <span>Performance Monitor</span>
              </label>
            </div>
          </div>
          
          <div className="p-4 border rounded-lg">
            <h3 className="font-medium mb-2">Layered Pattern</h3>
            <select 
              value={layeredPattern || ''}
              onChange={(e) => setLayeredPattern(e.target.value as SyrianPatternName || undefined)}
              className="w-full p-2 border rounded"
            >
              <option value="">None</option>
              {patternNames.map((name) => (
                <option key={name} value={name}>
                  {syrianPatterns[name].name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Pattern Preview */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Pattern Preview</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Current Configuration</h3>
            <PatternBackground
              pattern={selectedPattern}
              intensity={intensity}
              animated={animated}
              interactive={interactive}
              performanceMonitoring={performanceMonitoring}
              layeredPattern={layeredPattern}
              className="h-48 border rounded-lg flex items-center justify-center"
            >
              <div className="text-center p-4 bg-white/80 rounded">
                <h4 className="font-semibold">{syrianPatterns[selectedPattern].name}</h4>
                <p className="text-sm text-muted-foreground">
                  {syrianPatterns[selectedPattern].description}
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Size: {syrianPatterns[selectedPattern].performance.estimatedSize} | 
                  Complexity: {syrianPatterns[selectedPattern].performance.paintComplexity}
                </p>
              </div>
            </PatternBackground>
          </div>
          
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Cultural Significance</h3>
            <div className="p-4 border rounded-lg h-48 overflow-y-auto">
              <p className="text-sm mb-2">
                <strong>Cultural Meaning:</strong>
              </p>
              <p className="text-sm text-muted-foreground">
                {syrianPatterns[selectedPattern].culturalSignificance}
              </p>
              <div className="mt-4">
                <p className="text-sm mb-1"><strong>Performance:</strong></p>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• Size: {syrianPatterns[selectedPattern].performance.estimatedSize}</li>
                  <li>• Paint Complexity: {syrianPatterns[selectedPattern].performance.paintComplexity}</li>
                  <li>• Mobile Optimized: {syrianPatterns[selectedPattern].performance.mobileOptimized ? 'Yes' : 'No'}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* All Patterns Gallery */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Pattern Gallery</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {allPatterns.map((pattern) => (
            <div key={pattern.id} className="space-y-2">
              <h3 className="font-medium">{pattern.name}</h3>
              <PatternBackground
                pattern={pattern.id as SyrianPatternName}
                intensity="moderate"
                className="h-32 border rounded-lg flex items-center justify-center cursor-pointer hover:border-syrian-gold"
                onClick={() => setSelectedPattern(pattern.id as SyrianPatternName)}
              >
                <div className="text-center p-2 bg-white/70 rounded text-xs">
                  <div>{pattern.performance.estimatedSize}</div>
                  <div className="text-muted-foreground">{pattern.performance.paintComplexity}</div>
                </div>
              </PatternBackground>
            </div>
          ))}
        </div>
      </div>

      {/* Preset Patterns */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Pattern Presets</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Hero Section</h3>
            <HeroPatternBackground className="h-32 border rounded-lg flex items-center justify-center">
              <div className="text-center p-4 bg-white/80 rounded">
                <h4 className="font-semibold">Hero Pattern</h4>
                <p className="text-sm text-muted-foreground">Damascus Star - Animated</p>
              </div>
            </HeroPatternBackground>
          </div>
          
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Content Background</h3>
            <ContentPatternBackground className="h-32 border rounded-lg flex items-center justify-center">
              <div className="text-center p-4 bg-white/80 rounded">
                <h4 className="font-semibold">Content Pattern</h4>
                <p className="text-sm text-muted-foreground">Ebla Script - Subtle</p>
              </div>
            </ContentPatternBackground>
          </div>
          
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Footer Section</h3>
            <FooterPatternBackground className="h-32 border rounded-lg flex items-center justify-center">
              <div className="text-center p-4 bg-white/80 rounded">
                <h4 className="font-semibold">Footer Pattern</h4>
                <p className="text-sm text-muted-foreground">Palmyra Columns</p>
              </div>
            </FooterPatternBackground>
          </div>
          
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Card Accent</h3>
            <CardPatternBackground className="h-32 border rounded-lg flex items-center justify-center">
              <div className="text-center p-4 bg-white/80 rounded">
                <h4 className="font-semibold">Card Pattern</h4>
                <p className="text-sm text-muted-foreground">Geometric Weave</p>
              </div>
            </CardPatternBackground>
          </div>
        </div>
      </div>

      {/* Responsive Testing */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Responsive Behavior</h2>
        <div className="space-y-4">
          <div className="p-4 border rounded-lg">
            <h3 className="font-medium mb-2">Resize your browser to test responsive degradation:</h3>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• <strong>Desktop (≥1024px):</strong> All patterns at full opacity</li>
              <li>• <strong>Tablet (768-1023px):</strong> Reduced opacity, geometric weave disabled</li>
              <li>• <strong>Mobile (≤767px):</strong> Minimal patterns only, animations disabled</li>
              <li>• <strong>Small Mobile (≤480px):</strong> Only Damascus star and Ebla script at low opacity</li>
            </ul>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {patternNames.map((patternName) => {
              const pattern = syrianPatterns[patternName];
              return (
                <div key={patternName} className="p-3 border rounded">
                  <h4 className="font-medium text-sm mb-2">{pattern.name}</h4>
                  <div className="text-xs text-muted-foreground space-y-1">
                    <div>Desktop: {pattern.responsive.desktopOpacity}</div>
                    <div>Tablet: {pattern.responsive.tabletOpacity}</div>
                    <div>Mobile: {pattern.responsive.mobileOpacity}</div>
                    <div className={pattern.responsive.disableOnMobile ? 'text-red-600' : 'text-green-600'}>
                      {pattern.responsive.disableOnMobile ? 'Disabled on mobile' : 'Mobile compatible'}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Performance Summary */}
      <div className="p-6 bg-green-50 border border-green-200 rounded-lg">
        <h2 className="text-xl font-semibold text-green-800 mb-2">
          ✅ Pattern System Summary
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-green-700">
          <ul className="space-y-1">
            <li>• Total bundle size: {estimateTotalPatternSize()}</li>
            <li>• All patterns ≤2KB each</li>
            <li>• Automatic responsive degradation</li>
            <li>• Hardware acceleration optimized</li>
          </ul>
          <ul className="space-y-1">
            <li>• WCAG AA accessibility compliant</li>
            <li>• Respects prefers-reduced-motion</li>
            <li>• Zero layout impact</li>
            <li>• Performance monitoring available</li>
          </ul>
        </div>
      </div>

      <div className="text-center text-sm text-muted-foreground">
        <p>This component tests the Syrian pattern background system</p>
        <p>Remove this test component before production deployment</p>
      </div>
    </div>
  );
}
