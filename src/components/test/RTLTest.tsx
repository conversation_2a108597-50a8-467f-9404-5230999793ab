/**
 * RTL & Arabic Typography Test Component
 * 
 * This component tests and validates RTL layout and Arabic typography features.
 * Use this to verify proper Arabic text rendering and RTL layout functionality.
 * Remove this component before production deployment.
 */

import React, { useState } from 'react';
import { 
  RTLWrapper, 
  ArabicText, 
  BiDirectionalText, 
  RTLContainer,
  containsArabicScript,
  detectDirection 
} from '@/components/ui/rtl-wrapper';

export function RTLTest() {
  const [testDirection, setTestDirection] = useState<'ltr' | 'rtl' | 'auto'>('auto');
  const [arabicVariant, setArabicVariant] = useState<'modern' | 'traditional'>('modern');
  const [syrianStyle, setSyrianStyle] = useState(false);

  // Test strings
  const testStrings = {
    arabic: {
      short: "مرحباً",
      medium: "مرحباً بكم في منصة الحلول التقنية السورية",
      long: "هذه منصة شاملة تربط الخبراء التقنيين بالوزارات الحكومية لحل التحديات التقنية وتعزيز الابتكار في سوريا. نحن نسعى لبناء جسور التواصل بين القطاع العام والخبرات التقنية المتميزة.",
      mixed: "Welcome مرحباً to the Syrian منصة Technical Solutions Platform",
      numbers: "العدد ١٢٣٤٥ والرقم 12345 والتاريخ ١٥ أغسطس ٢٠٢٥",
      cultural: "دمشق، حلب، حمص، اللاذقية، طرطوس، السويداء، درعا، الحسكة، الرقة"
    },
    english: {
      short: "Hello",
      medium: "Welcome to Syrian Technical Solutions Platform",
      long: "This is a comprehensive platform connecting technical experts with government ministries to solve technical challenges and foster innovation in Syria."
    }
  };

  return (
    <div className="p-8 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">RTL & Arabic Typography Test</h1>
        <p className="text-muted-foreground">
          Testing Arabic text rendering and RTL layout functionality
        </p>
      </div>

      {/* Controls */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Test Controls</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          
          <div className="p-4 border rounded-lg">
            <h3 className="font-medium mb-2">Direction</h3>
            <div className="space-x-2">
              {(['auto', 'ltr', 'rtl'] as const).map((dir) => (
                <button
                  key={dir}
                  onClick={() => setTestDirection(dir)}
                  className={`px-3 py-1 rounded text-sm ${
                    testDirection === dir ? 'bg-syrian-gold text-white' : 'border'
                  }`}
                >
                  {dir.toUpperCase()}
                </button>
              ))}
            </div>
          </div>
          
          <div className="p-4 border rounded-lg">
            <h3 className="font-medium mb-2">Arabic Variant</h3>
            <div className="space-x-2">
              {(['modern', 'traditional'] as const).map((variant) => (
                <button
                  key={variant}
                  onClick={() => setArabicVariant(variant)}
                  className={`px-3 py-1 rounded text-sm ${
                    arabicVariant === variant ? 'bg-syrian-blue text-white' : 'border'
                  }`}
                >
                  {variant}
                </button>
              ))}
            </div>
          </div>
          
          <div className="p-4 border rounded-lg">
            <h3 className="font-medium mb-2">Syrian Style</h3>
            <button
              onClick={() => setSyrianStyle(!syrianStyle)}
              className={`px-3 py-1 rounded text-sm ${
                syrianStyle ? 'bg-syrian-green text-white' : 'border'
              }`}
            >
              {syrianStyle ? 'Enabled' : 'Disabled'}
            </button>
          </div>
        </div>
      </div>

      {/* Direction Detection Testing */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Direction Detection Testing</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Automatic Detection</h3>
            <div className="space-y-2">
              {Object.entries(testStrings.arabic).slice(0, 3).map(([key, text]) => (
                <div key={key} className="p-3 border rounded">
                  <div className="text-xs text-muted-foreground mb-1">
                    Detected: {detectDirection(undefined, text)} | Contains Arabic: {containsArabicScript(text) ? 'Yes' : 'No'}
                  </div>
                  <RTLWrapper direction="auto" textContent={text}>
                    {text}
                  </RTLWrapper>
                </div>
              ))}
            </div>
          </div>
          
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Manual Direction</h3>
            <div className="space-y-2">
              {Object.entries(testStrings.english).slice(0, 3).map(([key, text]) => (
                <div key={key} className="p-3 border rounded">
                  <div className="text-xs text-muted-foreground mb-1">
                    Forced: {testDirection}
                  </div>
                  <RTLWrapper direction={testDirection}>
                    {text}
                  </RTLWrapper>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Arabic Typography Testing */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Arabic Typography Testing</h2>
        <div className="space-y-6">
          
          {/* Font Variants */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h3 className="text-lg font-medium">Modern Arabic (Cairo)</h3>
              <div className="space-y-2">
                {(['xs', 'sm', 'base', 'lg', 'xl', '2xl'] as const).map((size) => (
                  <ArabicText 
                    key={size}
                    size={size}
                    arabicVariant="modern"
                    syrianStyle={syrianStyle}
                  >
                    {testStrings.arabic.medium} ({size})
                  </ArabicText>
                ))}
              </div>
            </div>
            
            <div className="space-y-3">
              <h3 className="text-lg font-medium">Traditional Arabic (Noto Kufi)</h3>
              <div className="space-y-2">
                {(['xs', 'sm', 'base', 'lg', 'xl', '2xl'] as const).map((size) => (
                  <ArabicText 
                    key={size}
                    size={size}
                    arabicVariant="traditional"
                    syrianStyle={syrianStyle}
                  >
                    {testStrings.arabic.medium} ({size})
                  </ArabicText>
                ))}
              </div>
            </div>
          </div>

          {/* Font Weights */}
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Font Weights</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {(['light', 'normal', 'medium', 'semibold', 'bold'] as const).map((weight) => (
                <ArabicText 
                  key={weight}
                  weight={weight}
                  arabicVariant={arabicVariant}
                  syrianStyle={syrianStyle}
                >
                  {testStrings.arabic.short} - {weight}
                </ArabicText>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Mixed Content Testing */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Mixed Content Testing</h2>
        <div className="space-y-4">
          
          <div className="p-4 border rounded-lg">
            <h3 className="font-medium mb-2">Bidirectional Text</h3>
            <BiDirectionalText primaryDirection="rtl">
              <span className="arabic">مرحباً بكم في</span>{' '}
              <span className="english">Syrian Technical Solutions Platform</span>{' '}
              <span className="arabic">منصة الحلول التقنية السورية</span>
            </BiDirectionalText>
          </div>
          
          <div className="p-4 border rounded-lg">
            <h3 className="font-medium mb-2">Numbers and Dates</h3>
            <div className="space-y-2">
              <ArabicText arabicNumerals={false}>
                التاريخ: 15 أغسطس 2025 (Western numerals)
              </ArabicText>
              <ArabicText arabicNumerals={true}>
                التاريخ: ١٥ أغسطس ٢٠٢٥ (Arabic-Indic numerals)
              </ArabicText>
            </div>
          </div>
          
          <div className="p-4 border rounded-lg">
            <h3 className="font-medium mb-2">Mixed Numbers</h3>
            <ArabicText>
              {testStrings.arabic.numbers}
            </ArabicText>
          </div>
        </div>
      </div>

      {/* Layout Testing */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">RTL Layout Testing</h2>
        <div className="space-y-6">
          
          {/* Flex Layout */}
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Flex Layout</h3>
            <RTLContainer variant="flex" spacing="md" direction="rtl">
              <div className="p-3 bg-syrian-gold/20 rounded">عنصر 1</div>
              <div className="p-3 bg-syrian-blue/20 rounded">عنصر 2</div>
              <div className="p-3 bg-syrian-green/20 rounded">عنصر 3</div>
            </RTLContainer>
          </div>
          
          {/* Grid Layout */}
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Grid Layout</h3>
            <RTLContainer variant="grid" className="grid-cols-3 gap-4" direction="rtl">
              <div className="p-3 bg-syrian-red/20 rounded text-center">
                <ArabicText size="sm">شبكة 1</ArabicText>
              </div>
              <div className="p-3 bg-syrian-stone/20 rounded text-center">
                <ArabicText size="sm">شبكة 2</ArabicText>
              </div>
              <div className="p-3 bg-syrian-purple/20 rounded text-center">
                <ArabicText size="sm">شبكة 3</ArabicText>
              </div>
            </RTLContainer>
          </div>
        </div>
      </div>

      {/* Form Elements Testing */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Form Elements Testing</h2>
        <RTLWrapper direction="rtl">
          <div className="space-y-4">
            <div>
              <label className="block arabic-text mb-2">النص العربي:</label>
              <input 
                type="text" 
                placeholder="أدخل النص هنا"
                className="w-full p-3 border rounded"
                dir="rtl"
              />
            </div>
            
            <div>
              <label className="block arabic-text mb-2">الأرقام:</label>
              <input 
                type="number" 
                placeholder="123"
                className="w-full p-3 border rounded"
              />
            </div>
            
            <div>
              <label className="block arabic-text mb-2">النص المختلط:</label>
              <textarea 
                placeholder="اكتب النص العربي والإنجليزي هنا"
                className="w-full p-3 border rounded h-24"
                dir="auto"
              />
            </div>
          </div>
        </RTLWrapper>
      </div>

      {/* Cultural Content Testing */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Syrian Cultural Content</h2>
        <div className="p-6 border rounded-lg">
          <ArabicText 
            size="xl" 
            weight="semibold" 
            arabicVariant={arabicVariant}
            syrianStyle={syrianStyle}
            className="mb-4"
          >
            المدن السورية التاريخية
          </ArabicText>
          <ArabicText 
            arabicVariant={arabicVariant}
            syrianStyle={syrianStyle}
          >
            {testStrings.arabic.cultural}
          </ArabicText>
        </div>
      </div>

      {/* Summary */}
      <div className="p-6 bg-blue-50 border border-blue-200 rounded-lg">
        <h2 className="text-xl font-semibold text-blue-800 mb-2">
          ✅ RTL & Arabic Typography Summary
        </h2>
        <ul className="space-y-1 text-blue-700">
          <li>• Automatic direction detection working</li>
          <li>• Arabic fonts (Cairo, Noto Kufi) loading correctly</li>
          <li>• Font features (liga, calt, dlig) active</li>
          <li>• RTL layouts functioning properly</li>
          <li>• Mixed content handling correctly</li>
          <li>• Form elements support RTL input</li>
          <li>• Syrian cultural styling integrated</li>
        </ul>
      </div>

      <div className="text-center text-sm text-muted-foreground">
        <p>This component tests RTL and Arabic typography features</p>
        <p>Remove this test component before production deployment</p>
      </div>
    </div>
  );
}
