/**
 * Syrian Colors Test Component
 * 
 * This component displays all Syrian identity colors for testing and validation.
 * Use this to verify colors are properly loaded and accessible.
 * Remove this component before production deployment.
 */

import React from 'react';
import { syrianColors, getSyrianColorCSS } from '@/lib/tokens/syrian-colors';

export function SyrianColorsTest() {
  const colorEntries = Object.entries(syrianColors) as [keyof typeof syrianColors, typeof syrianColors[keyof typeof syrianColors]][];

  return (
    <div className="p-8 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Syrian Identity Colors Test</h1>
        <p className="text-muted-foreground">
          Testing all Syrian cultural colors with WCAG AA compliance
        </p>
      </div>

      {/* CSS Custom Properties Test */}
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold">CSS Custom Properties</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {colorEntries.map(([colorName, colorValue]) => (
            <div key={colorName} className="space-y-3">
              <h3 className="text-lg font-medium capitalize">
                {colorName.replace(/([A-Z])/g, ' $1').trim()}
              </h3>
              <div className="space-y-2">
                {Object.entries(colorValue).filter(([key]) => key !== 'css').map(([variant, value]) => (
                  <div key={variant} className="flex items-center space-x-3">
                    <div 
                      className="w-12 h-12 rounded border border-gray-300"
                      style={{ backgroundColor: `hsl(${value})` }}
                    />
                    <div className="flex-1">
                      <div className="font-mono text-sm">{variant}</div>
                      <div className="text-xs text-muted-foreground">hsl({value})</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Tailwind Classes Test */}
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold">Tailwind CSS Classes</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Background Colors</h3>
            <div className="space-y-2">
              <div className="bg-syrian-gold p-3 rounded text-white">Syrian Gold</div>
              <div className="bg-syrian-red p-3 rounded text-white">Syrian Red</div>
              <div className="bg-syrian-green p-3 rounded text-white">Syrian Green</div>
              <div className="bg-syrian-stone p-3 rounded text-white">Syrian Stone</div>
              <div className="bg-syrian-blue p-3 rounded text-white">Syrian Blue</div>
              <div className="bg-syrian-purple p-3 rounded text-white">Syrian Purple</div>
            </div>
          </div>

          <div className="space-y-3">
            <h3 className="text-lg font-medium">Text Colors</h3>
            <div className="space-y-2">
              <div className="text-syrian-gold text-lg font-semibold">Syrian Gold Text</div>
              <div className="text-syrian-red text-lg font-semibold">Syrian Red Text</div>
              <div className="text-syrian-green text-lg font-semibold">Syrian Green Text</div>
              <div className="text-syrian-stone text-lg font-semibold">Syrian Stone Text</div>
              <div className="text-syrian-blue text-lg font-semibold">Syrian Blue Text</div>
              <div className="text-syrian-purple text-lg font-semibold">Syrian Purple Text</div>
            </div>
          </div>

          <div className="space-y-3">
            <h3 className="text-lg font-medium">Border Colors</h3>
            <div className="space-y-2">
              <div className="border-2 border-syrian-gold p-3 rounded">Gold Border</div>
              <div className="border-2 border-syrian-red p-3 rounded">Red Border</div>
              <div className="border-2 border-syrian-green p-3 rounded">Green Border</div>
              <div className="border-2 border-syrian-stone p-3 rounded">Stone Border</div>
              <div className="border-2 border-syrian-blue p-3 rounded">Blue Border</div>
              <div className="border-2 border-syrian-purple p-3 rounded">Purple Border</div>
            </div>
          </div>
        </div>
      </div>

      {/* Animation Test */}
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold">Syrian Motion Test</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <h3 className="text-lg font-medium">Hover Effects</h3>
            <div className="space-y-2">
              <button className="bg-syrian-gold text-white px-4 py-2 rounded syrian-hover-lift">
                Hover Lift Effect
              </button>
              <button className="bg-syrian-blue text-white px-4 py-2 rounded syrian-hover-glow">
                Hover Glow Effect
              </button>
              <button className="bg-syrian-green text-white px-4 py-2 rounded syrian-focus-ring">
                Focus Ring Effect
              </button>
            </div>
          </div>

          <div className="space-y-3">
            <h3 className="text-lg font-medium">Animations</h3>
            <div className="space-y-2">
              <div className="bg-syrian-stone/10 p-4 rounded animate-syrian-fade-in">
                Fade In Animation
              </div>
              <div className="bg-syrian-gold/10 p-4 rounded animate-syrian-gold-shimmer">
                Gold Shimmer Effect
              </div>
              <div className="bg-syrian-red/10 p-4 rounded animate-syrian-pattern-pulse">
                Pattern Pulse Animation
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Dark Mode Test */}
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold">Dark Mode Compatibility</h2>
        <div className="bg-gray-900 p-6 rounded-lg">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {colorEntries.map(([colorName]) => (
              <div key={colorName} className={`bg-syrian-${colorName.replace(/([A-Z])/g, '-$1').toLowerCase().replace(/^-/, '')} p-3 rounded text-white text-center`}>
                {colorName}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="text-center text-sm text-muted-foreground">
        <p>All colors are WCAG AA compliant and support light/dark themes</p>
        <p>Remove this test component before production deployment</p>
      </div>
    </div>
  );
}
