import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { SimpleErrorBoundary } from '../SimpleErrorBoundary';
import { SimpleFormErrorBoundary } from '../SimpleFormErrorBoundary';

// Mock console.error to avoid noise in tests
const originalError = console.error;
beforeEach(() => {
  console.error = vi.fn();
});

afterEach(() => {
  console.error = originalError;
});

// Different types of errors to test
const ImportError = () => {
  throw new Error('Cannot resolve module');
};

const NetworkError = () => {
  throw new Error('Network request failed');
};

const ChunkLoadError = () => {
  const error = new Error('Loading chunk 1 failed');
  error.name = 'ChunkLoadError';
  throw error;
};

const AsyncError = () => {
  React.useEffect(() => {
    Promise.reject(new Error('Async operation failed'));
  }, []);
  return <div>Async component</div>;
};

const ConditionalError: React.FC<{ shouldError: boolean }> = ({ shouldError }) => {
  if (shouldError) {
    throw new Error('Conditional error');
  }
  return <div>Working component</div>;
};

describe('Error Boundary Scenarios', () => {
  describe('Import/Module Errors', () => {
    it('should handle import errors gracefully', () => {
      render(
        <SimpleErrorBoundary level="component">
          <ImportError />
        </SimpleErrorBoundary>
      );

      expect(screen.getByText(/حدث خطأ في هذا القسم/)).toBeInTheDocument();
      expect(screen.getByText(/إعادة المحاولة/)).toBeInTheDocument();
    });

    it('should show error details for import errors in development', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      render(
        <SimpleErrorBoundary level="component">
          <ImportError />
        </SimpleErrorBoundary>
      );

      // Error details should be visible in development mode
      expect(screen.getByText(/تفاصيل الخطأ \(للمطورين\)/)).toBeInTheDocument();
      expect(screen.getByText(/Cannot resolve module/)).toBeInTheDocument();

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('Network Errors', () => {
    it('should handle network errors', () => {
      render(
        <SimpleErrorBoundary level="component">
          <NetworkError />
        </SimpleErrorBoundary>
      );

      expect(screen.getByText(/حدث خطأ في هذا القسم/)).toBeInTheDocument();
    });
  });

  describe('Chunk Load Errors', () => {
    it('should handle chunk load errors', () => {
      render(
        <SimpleErrorBoundary level="page">
          <ChunkLoadError />
        </SimpleErrorBoundary>
      );

      expect(screen.getByText(/حدث خطأ في التطبيق/)).toBeInTheDocument();
      expect(screen.getByText(/العودة للصفحة الرئيسية/)).toBeInTheDocument();
    });
  });

  describe('Form Error Scenarios', () => {
    it('should handle form validation errors', () => {
      const FormWithError = () => {
        throw new Error('Validation failed');
      };

      render(
        <SimpleFormErrorBoundary formName="LoginForm">
          <FormWithError />
        </SimpleFormErrorBoundary>
      );

      expect(screen.getByText(/خطأ في النموذج \(LoginForm\)/)).toBeInTheDocument();
      expect(screen.getByText(/إعادة تحميل النموذج/)).toBeInTheDocument();
    });

    it('should handle form submission errors', () => {
      const FormWithSubmissionError = () => {
        React.useEffect(() => {
          // Simulate form submission error
          throw new Error('Submission failed');
        }, []);
        return <form>Test form</form>;
      };

      render(
        <SimpleFormErrorBoundary formName="RegisterForm">
          <FormWithSubmissionError />
        </SimpleFormErrorBoundary>
      );

      expect(screen.getByText(/خطأ في النموذج \(RegisterForm\)/)).toBeInTheDocument();
    });
  });

  describe('Nested Error Boundaries', () => {
    it('should handle errors at the correct boundary level', () => {
      render(
        <SimpleErrorBoundary level="page">
          <div>
            <h1>Page Content</h1>
            <SimpleErrorBoundary level="component">
              <div>
                <p>Section content</p>
                <ImportError />
              </div>
            </SimpleErrorBoundary>
            <p>More page content</p>
          </div>
        </SimpleErrorBoundary>
      );

      // Should show component-level error, not page-level
      expect(screen.getByText(/حدث خطأ في هذا القسم/)).toBeInTheDocument();
      expect(screen.queryByText(/حدث خطأ في التطبيق/)).not.toBeInTheDocument();
      
      // Other content should still be visible
      expect(screen.getByText('Page Content')).toBeInTheDocument();
      expect(screen.getByText('More page content')).toBeInTheDocument();
    });

    it('should bubble up to parent boundary when child boundary fails', () => {
      const FailingBoundary = () => {
        // Simulate a boundary that fails itself
        throw new Error('Error boundary failed');
      };

      render(
        <SimpleErrorBoundary level="page">
          <FailingBoundary />
        </SimpleErrorBoundary>
      );

      expect(screen.getByText(/حدث خطأ في التطبيق/)).toBeInTheDocument();
    });
  });

  describe('Recovery Scenarios', () => {
    it('should recover after successful retry', async () => {
      let shouldError = true;
      
      const RecoverableComponent = () => {
        if (shouldError) {
          throw new Error('Temporary error');
        }
        return <div>Component recovered</div>;
      };

      render(
        <SimpleErrorBoundary level="component">
          <RecoverableComponent />
        </SimpleErrorBoundary>
      );

      // Initially shows error
      expect(screen.getByText(/حدث خطأ في هذا القسم/)).toBeInTheDocument();

      // Fix the error condition
      shouldError = false;

      // Click retry
      const retryButton = screen.getByText(/إعادة المحاولة/);
      fireEvent.click(retryButton);

      // Should recover
      await waitFor(() => {
        expect(screen.getByText('Component recovered')).toBeInTheDocument();
      });
    });

    it('should handle multiple retry attempts', async () => {
      let renderCount = 0;
      
      const MultiRetryComponent = () => {
        renderCount++;
        // Always throw error for this test to verify retry behavior
        throw new Error(`Attempt ${renderCount} failed`);
      };

      render(
        <SimpleErrorBoundary level="component" maxRetries={5}>
          <MultiRetryComponent />
        </SimpleErrorBoundary>
      );

      // First error
      expect(screen.getByText(/حدث خطأ في هذا القسم/)).toBeInTheDocument();

      // First retry - should still show error
      fireEvent.click(screen.getByText(/إعادة المحاولة/));
      
      // Should still show error after retry
      await waitFor(() => {
        expect(screen.getByText(/حدث خطأ في هذا القسم/)).toBeInTheDocument();
      });

      // Verify retry count increased
      expect(renderCount).toBeGreaterThan(1);
    });
  });

  describe('Error Isolation', () => {
    it('should isolate errors to specific components', () => {
      render(
        <div>
          <SimpleErrorBoundary level="component">
            <div>Working section 1</div>
          </SimpleErrorBoundary>
          
          <SimpleErrorBoundary level="component">
            <ImportError />
          </SimpleErrorBoundary>
          
          <SimpleErrorBoundary level="component">
            <div>Working section 2</div>
          </SimpleErrorBoundary>
        </div>
      );

      // Working sections should render
      expect(screen.getByText('Working section 1')).toBeInTheDocument();
      expect(screen.getByText('Working section 2')).toBeInTheDocument();
      
      // Error should be contained
      expect(screen.getByText(/حدث خطأ في هذا القسم/)).toBeInTheDocument();
    });

    it('should handle mixed error and success states', () => {
      render(
        <div>
          <SimpleFormErrorBoundary formName="WorkingForm">
            <form>Working form</form>
          </SimpleFormErrorBoundary>
          
          <SimpleFormErrorBoundary formName="FailingForm">
            <ImportError />
          </SimpleFormErrorBoundary>
          
          <SimpleErrorBoundary level="inline">
            <div>Inline content</div>
          </SimpleErrorBoundary>
        </div>
      );

      expect(screen.getByText('Working form')).toBeInTheDocument();
      expect(screen.getByText('Inline content')).toBeInTheDocument();
      expect(screen.getByText(/خطأ في النموذج \(FailingForm\)/)).toBeInTheDocument();
    });
  });

  describe('Performance and Memory', () => {
    it('should clean up timeouts on unmount', () => {
      const { unmount } = render(
        <SimpleErrorBoundary level="component">
          <ImportError />
        </SimpleErrorBoundary>
      );

      // Click retry to start timeout
      fireEvent.click(screen.getByText(/إعادة المحاولة/));

      // Unmount should not cause memory leaks
      expect(() => unmount()).not.toThrow();
    });

    it('should handle rapid error/recovery cycles', () => {
      let errorState = true;
      
      const RapidToggleComponent = () => {
        React.useEffect(() => {
          const interval = setInterval(() => {
            errorState = !errorState;
          }, 10);
          return () => clearInterval(interval);
        }, []);

        if (errorState) {
          throw new Error('Rapid error');
        }
        return <div>Rapid success</div>;
      };

      expect(() => {
        render(
          <SimpleErrorBoundary level="component">
            <RapidToggleComponent />
          </SimpleErrorBoundary>
        );
      }).not.toThrow();
    });
  });
});