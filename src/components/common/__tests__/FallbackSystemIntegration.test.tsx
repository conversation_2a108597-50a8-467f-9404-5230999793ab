import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import {
  ComprehensiveFallback,
  NetworkErrorFallback,
  EmptyProblemsState,
  useRetryMechanism
} from '../ComprehensiveFallbackSystem';
import { useErrorRecovery } from '@/lib/errorRecoveryService';

// Mock the error recovery service
vi.mock('@/lib/errorRecoveryService', () => ({
  useErrorRecovery: vi.fn(() => ({
    handleError: vi.fn(),
    clearHistory: vi.fn(),
    getHealth: vi.fn(() => ({
      totalErrors: 0,
      recentErrors: 0,
      topErrors: [],
      recoverySuccess: 0.85
    }))
  }))
}));

// Mock icons
vi.mock('lucide-react', () => ({
  AlertTriangle: () => <div data-testid="alert-triangle-icon" />,
  RefreshCw: () => <div data-testid="refresh-icon" />,
  Wifi: () => <div data-testid="wifi-icon" />,
  Database: () => <div data-testid="database-icon" />,
  FileX: () => <div data-testid="file-x-icon" />,
  Search: () => <div data-testid="search-icon" />,
  Users: () => <div data-testid="users-icon" />,
  Plus: () => <div data-testid="plus-icon" />,
  CheckCircle: () => <div data-testid="check-circle-icon" />
}));

describe('Fallback System Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Real-world usage scenarios', () => {
    it('handles network error with retry functionality', async () => {
      let attemptCount = 0;
      const mockNetworkOperation = vi.fn().mockImplementation(() => {
        attemptCount++;
        if (attemptCount < 3) {
          throw new Error('Network error');
        }
        return Promise.resolve('Success');
      });

      const TestComponent: React.FC = () => {
        const [error, setError] = React.useState<Error | null>(null);
        const [loading, setLoading] = React.useState(false);
        const [data, setData] = React.useState<string | null>(null);

        const handleRetry = React.useCallback(async () => {
          setLoading(true);
          setError(null);
          try {
            const result = await mockNetworkOperation();
            setData(result);
          } catch (err) {
            setError(err as Error);
          } finally {
            setLoading(false);
          }
        }, []);

        if (loading) {
          return <div>Loading...</div>;
        }

        if (error) {
          return <NetworkErrorFallback onRetry={handleRetry} />;
        }

        if (data) {
          return <div>Success: {data}</div>;
        }

        return <button onClick={handleRetry}>Load Data</button>;
      };

      render(<TestComponent />);

      // Initial state
      expect(screen.getByText('Load Data')).toBeInTheDocument();

      // Trigger first error
      fireEvent.click(screen.getByText('Load Data'));
      await waitFor(() => {
        expect(screen.getByText('مشكلة في الاتصال')).toBeInTheDocument();
      });

      // The component should render the network error fallback
      expect(screen.getByText(/تعذر الاتصال بالخادم/)).toBeInTheDocument();
    });

    it('shows empty state with guidance', () => {
      const mockCreateProblem = vi.fn();
      
      render(<EmptyProblemsState onCreateProblem={mockCreateProblem} />);

      expect(screen.getByText('لا توجد مشاكل تقنية')).toBeInTheDocument();
      expect(screen.getByText(/لم يتم العثور على أي مشاكل تقنية/)).toBeInTheDocument();
      expect(screen.getByText('إضافة مشكلة جديدة')).toBeInTheDocument();

      // Check guidance steps
      expect(screen.getByText('إضافة مشكلة تقنية جديدة')).toBeInTheDocument();
      expect(screen.getByText('تصفح الخبراء المتاحين')).toBeInTheDocument();
      expect(screen.getByText('البحث في المشاكل الموجودة')).toBeInTheDocument();

      // Test action button
      fireEvent.click(screen.getByText('إضافة مشكلة جديدة'));
      expect(mockCreateProblem).toHaveBeenCalledTimes(1);
    });

    it('handles component-level errors gracefully', () => {
      const TestComponentWithError: React.FC<{ shouldError: boolean }> = ({ shouldError }) => {
        if (shouldError) {
          return (
            <ComprehensiveFallback
              title="Component Error"
              description="This component encountered an error"
              level="component"
              onRetry={() => console.log('Retry clicked')}
            />
          );
        }
        return <div>Component working normally</div>;
      };

      const { rerender } = render(<TestComponentWithError shouldError={false} />);
      expect(screen.getByText('Component working normally')).toBeInTheDocument();

      rerender(<TestComponentWithError shouldError={true} />);
      expect(screen.getByText('Component Error')).toBeInTheDocument();
      expect(screen.getByText('This component encountered an error')).toBeInTheDocument();
      expect(screen.getByText('إعادة المحاولة')).toBeInTheDocument();
    });

    it('provides different fallback levels', () => {
      const { rerender } = render(
        <ComprehensiveFallback
          title="Test Error"
          description="Test description"
          level="inline"
        />
      );

      // Inline level should have specific styling
      let container = screen.getByText('Test description').closest('div')?.parentElement;
      expect(container).toHaveClass('border-red-200', 'bg-red-50');

      rerender(
        <ComprehensiveFallback
          title="Test Error"
          description="Test description"
          level="component"
        />
      );

      // Component level should have different styling
      container = screen.getByText('Test description').closest('div')?.parentElement;
      expect(container).toHaveClass('min-h-64');

      rerender(
        <ComprehensiveFallback
          title="Test Error"
          description="Test description"
          level="page"
        />
      );

      // Page level should have full height
      container = screen.getByText('Test description').closest('div')?.parentElement;
      expect(container).toHaveClass('min-h-screen');
    });
  });

  describe('Retry mechanism integration', () => {
    it('integrates with useRetryMechanism hook', async () => {
      const TestRetryComponent: React.FC = () => {
        const [operationResult, setOperationResult] = React.useState<string>('');
        
        const operation = React.useCallback(async () => {
          // Simulate an operation that fails twice then succeeds
          const random = Math.random();
          if (random > 0.3) {
            throw new Error('Operation failed');
          }
          setOperationResult('Operation succeeded!');
        }, []);

        const { retry, retryCount, isRetrying, canRetry } = useRetryMechanism(
          operation,
          { maxRetries: 3, retryDelay: 100, exponentialBackoff: false }
        );

        return (
          <div>
            <div data-testid="result">{operationResult}</div>
            <div data-testid="retry-count">{retryCount}</div>
            <div data-testid="is-retrying">{isRetrying.toString()}</div>
            <div data-testid="can-retry">{canRetry.toString()}</div>
            <button onClick={retry} disabled={!canRetry || isRetrying}>
              {isRetrying ? 'Retrying...' : 'Try Operation'}
            </button>
          </div>
        );
      };

      render(<TestRetryComponent />);

      expect(screen.getByTestId('retry-count')).toHaveTextContent('0');
      expect(screen.getByTestId('can-retry')).toHaveTextContent('true');
      expect(screen.getByText('Try Operation')).toBeInTheDocument();
    });
  });

  describe('Error recovery service integration', () => {
    it('integrates with error recovery service', () => {
      const TestErrorRecoveryComponent: React.FC = () => {
        const { handleError, getHealth } = useErrorRecovery();
        const health = getHealth();

        return (
          <div>
            <div data-testid="total-errors">{health.totalErrors}</div>
            <div data-testid="recovery-success">{health.recoverySuccess}</div>
            <button
              onClick={() => handleError(new Error('Test error'), { component: 'TestComponent' })}
            >
              Trigger Error
            </button>
          </div>
        );
      };

      render(<TestErrorRecoveryComponent />);

      expect(screen.getByTestId('total-errors')).toHaveTextContent('0');
      expect(screen.getByTestId('recovery-success')).toHaveTextContent('0.85');
      expect(screen.getByText('Trigger Error')).toBeInTheDocument();
    });
  });

  describe('Accessibility features', () => {
    it('provides proper ARIA attributes', () => {
      render(
        <ComprehensiveFallback
          title="Accessible Error"
          description="This error message is accessible"
          level="component"
          onRetry={() => {}}
        />
      );

      // Check for proper heading structure
      const heading = screen.getByRole('heading', { level: 2 });
      expect(heading).toHaveTextContent('Accessible Error');

      // Check for retry button
      const retryButton = screen.getByText('إعادة المحاولة');
      expect(retryButton).toBeInTheDocument();
      expect(retryButton.tagName).toBe('BUTTON');
    });

    it('supports keyboard navigation', () => {
      const mockRetry = vi.fn();
      render(
        <ComprehensiveFallback
          title="Keyboard Accessible Error"
          description="This error supports keyboard navigation"
          level="component"
          onRetry={mockRetry}
        />
      );

      const retryButton = screen.getByText('إعادة المحاولة');
      
      // Focus the button
      retryButton.focus();
      expect(document.activeElement).toBe(retryButton);

      // Simulate Enter key press
      fireEvent.keyDown(retryButton, { key: 'Enter', code: 'Enter' });
      // Note: The actual retry mechanism is complex, so we just check the button is focusable
    });
  });
});