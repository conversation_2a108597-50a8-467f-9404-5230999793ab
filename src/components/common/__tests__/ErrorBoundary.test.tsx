import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { SimpleErrorBoundary } from '../SimpleErrorBoundary';
import { SimpleFormErrorBoundary } from '../SimpleFormErrorBoundary';

// Mock console.error to avoid noise in tests
const originalError = console.error;
beforeEach(() => {
  console.error = vi.fn();
});

afterEach(() => {
  console.error = originalError;
});

// Component that throws an error
const ThrowError: React.FC<{ shouldThrow?: boolean }> = ({ shouldThrow = true }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error</div>;
};

describe('SimpleErrorBoundary', () => {
  it('should render children when there is no error', () => {
    render(
      <SimpleErrorBoundary>
        <ThrowError shouldThrow={false} />
      </SimpleErrorBoundary>
    );

    expect(screen.getByText('No error')).toBeInTheDocument();
  });

  it('should render error fallback when child component throws', () => {
    render(
      <SimpleErrorBoundary level="component">
        <ThrowError />
      </SimpleErrorBoundary>
    );

    expect(screen.getByText(/حدث خطأ في هذا القسم/)).toBeInTheDocument();
    expect(screen.getByText(/إعادة المحاولة/)).toBeInTheDocument();
  });

  it('should render page-level error fallback', () => {
    render(
      <SimpleErrorBoundary level="page">
        <ThrowError />
      </SimpleErrorBoundary>
    );

    expect(screen.getByText(/حدث خطأ في التطبيق/)).toBeInTheDocument();
    expect(screen.getByText(/العودة للصفحة الرئيسية/)).toBeInTheDocument();
  });

  it('should render inline error fallback', () => {
    render(
      <SimpleErrorBoundary level="inline">
        <ThrowError />
      </SimpleErrorBoundary>
    );

    expect(screen.getByText(/هذا القسم لا يمكن تحميله/)).toBeInTheDocument();
  });

  it('should call onError callback when error occurs', () => {
    const onError = vi.fn();
    
    render(
      <SimpleErrorBoundary onError={onError}>
        <ThrowError />
      </SimpleErrorBoundary>
    );

    expect(onError).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        componentStack: expect.any(String)
      })
    );
  });

  it('should retry when retry button is clicked', () => {
    const TestComponent = () => {
      const [shouldThrow, setShouldThrow] = React.useState(true);
      
      React.useEffect(() => {
        const timer = setTimeout(() => setShouldThrow(false), 100);
        return () => clearTimeout(timer);
      }, []);

      return <ThrowError shouldThrow={shouldThrow} />;
    };

    render(
      <SimpleErrorBoundary>
        <TestComponent />
      </SimpleErrorBoundary>
    );

    // Error should be displayed initially
    expect(screen.getByText(/حدث خطأ في هذا القسم/)).toBeInTheDocument();

    // Click retry button
    const retryButton = screen.getByText(/إعادة المحاولة/);
    fireEvent.click(retryButton);

    // After retry, component should eventually render without error
    setTimeout(() => {
      expect(screen.queryByText(/حدث خطأ في هذا القسم/)).not.toBeInTheDocument();
    }, 200);
  });

  it('should respect maxRetries limit', () => {
    const onError = vi.fn();
    
    render(
      <SimpleErrorBoundary maxRetries={1} onError={onError}>
        <ThrowError />
      </SimpleErrorBoundary>
    );

    const retryButton = screen.getByText(/إعادة المحاولة/);
    
    // First retry
    fireEvent.click(retryButton);
    
    // Second retry should be ignored due to maxRetries=1
    fireEvent.click(retryButton);

    // Should have been called once (initial error only, retries don't trigger onError again)
    expect(onError).toHaveBeenCalledTimes(1);
  });
});

describe('SimpleFormErrorBoundary', () => {
  it('should render form-specific error message', () => {
    render(
      <SimpleFormErrorBoundary formName="Login">
        <ThrowError />
      </SimpleFormErrorBoundary>
    );

    expect(screen.getByText(/خطأ في النموذج \(Login\)/)).toBeInTheDocument();
    expect(screen.getByText(/إعادة تحميل النموذج/)).toBeInTheDocument();
  });

  it('should show error details in development mode', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    render(
      <SimpleFormErrorBoundary formName="Register">
        <ThrowError />
      </SimpleFormErrorBoundary>
    );

    expect(screen.getByText(/تفاصيل الخطأ \(للمطورين\)/)).toBeInTheDocument();

    process.env.NODE_ENV = originalEnv;
  });

  it('should handle retry in form error boundary', () => {
    render(
      <SimpleFormErrorBoundary formName="Test">
        <ThrowError />
      </SimpleFormErrorBoundary>
    );

    const retryButton = screen.getByText(/إعادة تحميل النموذج/);
    expect(retryButton).toBeInTheDocument();
    
    fireEvent.click(retryButton);
    // Should not throw during retry click
  });
});

describe('Error Boundary Integration', () => {
  it('should work with nested error boundaries', () => {
    render(
      <SimpleErrorBoundary level="page">
        <SimpleFormErrorBoundary formName="Nested">
          <div>
            <ThrowError shouldThrow={false} />
            <SimpleErrorBoundary level="component">
              <ThrowError />
            </SimpleErrorBoundary>
          </div>
        </SimpleFormErrorBoundary>
      </SimpleErrorBoundary>
    );

    // Should show component-level error, not page-level
    expect(screen.getByText(/حدث خطأ في هذا القسم/)).toBeInTheDocument();
    expect(screen.queryByText(/حدث خطأ في التطبيق/)).not.toBeInTheDocument();
  });

  it('should isolate errors to the appropriate boundary level', () => {
    const WorkingComponent = () => <div>This works</div>;
    
    render(
      <div>
        <WorkingComponent />
        <SimpleErrorBoundary level="component">
          <ThrowError />
        </SimpleErrorBoundary>
        <WorkingComponent />
      </div>
    );

    // Working components should still render
    expect(screen.getAllByText('This works')).toHaveLength(2);
    // Error should be contained
    expect(screen.getByText(/حدث خطأ في هذا القسم/)).toBeInTheDocument();
  });
});