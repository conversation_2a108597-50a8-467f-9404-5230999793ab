import React, { Component, ReactNode } from 'react';
import { ErrorFallback } from './ErrorFallback';
import { errorLogger } from '@/lib/errorLogger';

interface ErrorInfo {
  componentStack: string;
  errorBoundary?: string;
}

interface ComponentErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
  retryCount: number;
}

interface ComponentErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<{
    error: Error;
    errorInfo?: ErrorInfo;
    resetError: () => void;
    onReport?: (error: Error, errorInfo?: ErrorInfo) => void;
  }>;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  componentName?: string;
  enableReporting?: boolean;
  maxRetries?: number;
  variant?: 'component' | 'inline';
  isolateError?: boolean; // Whether to prevent error from bubbling up
}

export class ComponentErrorBoundary extends Component<
  ComponentErrorBoundaryProps,
  ComponentErrorBoundaryState
> {
  private retryTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: ComponentErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ComponentErrorBoundaryState> {
    return {
      hasError: true,
      error,
      errorId: `component-error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const enhancedErrorInfo: ErrorInfo = {
      componentStack: errorInfo.componentStack,
      errorBoundary: 'ComponentErrorBoundary',
    };

    this.setState({
      errorInfo: enhancedErrorInfo,
    });

    // Log error details
    this.logError(error, enhancedErrorInfo);

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, enhancedErrorInfo);
    }

    // If isolateError is false, re-throw the error to let parent boundaries handle it
    if (!this.props.isolateError) {
      // Note: In React, we can't actually re-throw from componentDidCatch
      // This is handled by the error boundary hierarchy
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  private logError = async (error: Error, errorInfo: ErrorInfo) => {
    try {
      const errorData = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        componentName: this.props.componentName,
        errorBoundary: 'ComponentErrorBoundary',
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        errorId: this.state.errorId,
        retryCount: this.state.retryCount,
      };

      await errorLogger.logError(errorData);
    } catch (loggingError) {
      console.error('Failed to log component error:', loggingError);
    }
  };

  private resetError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: this.state.retryCount + 1,
    });
  };

  private handleRetry = () => {
    const { maxRetries = 3 } = this.props;
    
    if (this.state.retryCount >= maxRetries) {
      console.warn(`Component ${this.props.componentName} exceeded max retries (${maxRetries})`);
      return;
    }

    // Add a small delay before retrying
    this.retryTimeoutId = setTimeout(() => {
      this.resetError();
    }, 500);
  };

  private handleReport = async (error: Error, errorInfo?: ErrorInfo) => {
    if (!this.props.enableReporting) return;

    try {
      const reportData = {
        error: {
          message: error.message,
          stack: error.stack,
          name: error.name,
        },
        errorInfo,
        context: {
          componentName: this.props.componentName,
          errorId: this.state.errorId,
          retryCount: this.state.retryCount,
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: navigator.userAgent,
        },
      };

      await errorLogger.reportError(reportData);
    } catch (reportingError) {
      console.error('Failed to report component error:', reportingError);
      throw reportingError;
    }
  };

  render() {
    if (this.state.hasError && this.state.error) {
      const { fallback: CustomFallback, variant = 'component' } = this.props;

      if (CustomFallback) {
        return (
          <CustomFallback
            error={this.state.error}
            errorInfo={this.state.errorInfo || undefined}
            resetError={this.handleRetry}
            onReport={this.props.enableReporting ? this.handleReport : undefined}
          />
        );
      }

      return (
        <ErrorFallback
          error={this.state.error}
          errorInfo={this.state.errorInfo || undefined}
          resetError={this.handleRetry}
          onReport={this.props.enableReporting ? this.handleReport : undefined}
          variant={variant}
          showDetails={process.env.NODE_ENV === 'development'}
          showReportButton={this.props.enableReporting}
        />
      );
    }

    return this.props.children;
  }
}

// Hook for component error handling
export function useComponentErrorHandler(componentName?: string) {
  const [error, setError] = React.useState<Error | null>(null);

  const reportError = React.useCallback((error: Error, context?: Record<string, any>) => {
    console.error(`Component error in ${componentName}:`, error);
    
    // Log error with context
    errorLogger.logError({
      message: error.message,
      stack: error.stack,
      componentName,
      context,
      timestamp: new Date().toISOString(),
      source: 'useComponentErrorHandler',
    });

    setError(error);
  }, [componentName]);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  return {
    error,
    reportError,
    clearError,
  };
}

// Higher-order component for wrapping components with error boundaries
export function withComponentErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    componentName?: string;
    enableReporting?: boolean;
    maxRetries?: number;
    variant?: 'component' | 'inline';
    isolateError?: boolean;
    fallback?: React.ComponentType<any>;
  }
) {
  const WrappedComponent = (props: P) => (
    <ComponentErrorBoundary
      componentName={options?.componentName || Component.displayName || Component.name}
      enableReporting={options?.enableReporting ?? true}
      maxRetries={options?.maxRetries}
      variant={options?.variant}
      isolateError={options?.isolateError ?? true}
      fallback={options?.fallback}
    >
      <Component {...props} />
    </ComponentErrorBoundary>
  );

  WrappedComponent.displayName = `withComponentErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

// Async component error boundary for handling async operations
export function AsyncComponentErrorBoundary({ 
  children, 
  fallback,
  onError 
}: {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error) => void;
}) {
  const [asyncError, setAsyncError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));
      setAsyncError(error);
      onError?.(error);
      
      // Log the async error
      errorLogger.logError({
        message: error.message,
        stack: error.stack,
        source: 'AsyncComponentErrorBoundary',
        timestamp: new Date().toISOString(),
        type: 'unhandledRejection',
      });
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [onError]);

  if (asyncError) {
    return fallback || (
      <ErrorFallback
        error={asyncError}
        resetError={() => setAsyncError(null)}
        variant="component"
      />
    );
  }

  return (
    <ComponentErrorBoundary
      componentName="AsyncComponentErrorBoundary"
      enableReporting={true}
      isolateError={true}
    >
      {children}
    </ComponentErrorBoundary>
  );
}
