import { Loader2 } from "lucide-react";

/**
 * Loading fallback component for lazy-loaded routes
 * Provides a consistent loading experience across all route transitions
 */
export const LazyLoadingFallback = () => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="flex flex-col items-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p className="text-sm text-muted-foreground">Loading...</p>
      </div>
    </div>
  );
};

/**
 * Minimal loading fallback for faster perceived performance
 */
export const MinimalLoadingFallback = () => {
  return (
    <div className="flex items-center justify-center p-8">
      <Loader2 className="h-6 w-6 animate-spin text-primary" />
    </div>
  );
};