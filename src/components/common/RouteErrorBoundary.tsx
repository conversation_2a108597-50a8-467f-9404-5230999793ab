import React, { Component, ReactNode } from 'react';
import { Error<PERSON>allback, CriticalErrorFallback } from './ErrorFallback';
import { errorLogger } from '@/lib/errorLogger';

interface ErrorInfo {
  componentStack: string;
  errorBoundary?: string;
}

interface RouteErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
  retryCount: number;
}

interface RouteErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<{
    error: Error;
    errorInfo?: ErrorInfo;
    resetError: () => void;
    onReport: (error: Error, errorInfo?: ErrorInfo) => void;
  }>;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  maxRetries?: number;
  routeName?: string;
  enableReporting?: boolean;
}

export class RouteErrorBoundary extends Component<
  RouteErrorBoundaryProps,
  RouteErrorBoundaryState
> {
  private retryTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: RouteErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<RouteErrorBoundaryState> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `route-error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const enhancedErrorInfo: ErrorInfo = {
      componentStack: errorInfo.componentStack,
      errorBoundary: 'RouteErrorBoundary',
    };

    this.setState({
      errorInfo: enhancedErrorInfo,
    });

    // Log error details
    this.logError(error, enhancedErrorInfo);

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, enhancedErrorInfo);
    }
  }

  componentDidUpdate(prevProps: RouteErrorBoundaryProps) {
    // Reset error state when route changes (children change)
    if (prevProps.children !== this.props.children && this.state.hasError) {
      this.resetError();
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  private logError = async (error: Error, errorInfo: ErrorInfo) => {
    try {
      const errorData = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        routeName: this.props.routeName,
        errorBoundary: 'RouteErrorBoundary',
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        errorId: this.state.errorId,
        retryCount: this.state.retryCount,
      };

      await errorLogger.logError(errorData);
    } catch (loggingError) {
      console.error('Failed to log error:', loggingError);
    }
  };

  private resetError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: this.state.retryCount + 1,
    });
  };

  private handleRetry = () => {
    const { maxRetries = 3 } = this.props;
    
    if (this.state.retryCount >= maxRetries) {
      // Max retries reached, reload the page
      window.location.reload();
      return;
    }

    // Add a small delay before retrying to prevent rapid retry loops
    this.retryTimeoutId = setTimeout(() => {
      this.resetError();
    }, 1000);
  };

  private handleReport = async (error: Error, errorInfo?: ErrorInfo) => {
    if (!this.props.enableReporting) return;

    try {
      const reportData = {
        error: {
          message: error.message,
          stack: error.stack,
          name: error.name,
        },
        errorInfo,
        context: {
          routeName: this.props.routeName,
          errorId: this.state.errorId,
          retryCount: this.state.retryCount,
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: navigator.userAgent,
        },
      };

      await errorLogger.reportError(reportData);
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
      throw reportingError;
    }
  };

  render() {
    if (this.state.hasError && this.state.error) {
      const { fallback: CustomFallback } = this.props;

      // Check if this is a critical error that should use the critical fallback
      const isCriticalError = 
        this.state.error.name === 'ChunkLoadError' ||
        this.state.error.message.includes('Loading chunk') ||
        this.state.error.message.includes('Loading CSS chunk') ||
        this.state.retryCount >= (this.props.maxRetries || 3);

      if (isCriticalError) {
        return <CriticalErrorFallback error={this.state.error} />;
      }

      if (CustomFallback) {
        return (
          <CustomFallback
            error={this.state.error}
            errorInfo={this.state.errorInfo || undefined}
            resetError={this.handleRetry}
            onReport={this.handleReport}
          />
        );
      }

      return (
        <ErrorFallback
          error={this.state.error}
          errorInfo={this.state.errorInfo || undefined}
          resetError={this.handleRetry}
          onReport={this.props.enableReporting ? this.handleReport : undefined}
          variant="page"
          showDetails={process.env.NODE_ENV === 'development'}
          showReportButton={this.props.enableReporting}
        />
      );
    }

    return this.props.children;
  }
}

// Hook for using route error boundary context
export function useRouteErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const reportError = React.useCallback((error: Error, context?: Record<string, any>) => {
    console.error('Route error reported:', error);
    
    // Log error with context
    errorLogger.logError({
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      source: 'useRouteErrorHandler',
    });

    setError(error);
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  return {
    error,
    reportError,
    clearError,
  };
}

// Higher-order component for wrapping routes with error boundaries
export function withRouteErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    routeName?: string;
    enableReporting?: boolean;
    maxRetries?: number;
    fallback?: React.ComponentType<any>;
  }
) {
  const WrappedComponent = (props: P) => (
    <RouteErrorBoundary
      routeName={options?.routeName}
      enableReporting={options?.enableReporting ?? true}
      maxRetries={options?.maxRetries}
      fallback={options?.fallback}
    >
      <Component {...props} />
    </RouteErrorBoundary>
  );

  WrappedComponent.displayName = `withRouteErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}
