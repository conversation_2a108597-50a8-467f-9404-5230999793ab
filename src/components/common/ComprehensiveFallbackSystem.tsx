import React, { useState, useCallback, useEffect } from 'react';
import { 
  AlertTriangle, 
  RefreshCw, 
  Home, 
  Wifi, 
  Database, 
  FileX, 
  Search,
  Users,
  Settings,
  Clock,
  CheckCircle,
  XCircle,
  Info,
  Plus,
  ArrowLeft
} from 'lucide-react';

// Types for fallback system
export interface RetryConfig {
  maxRetries: number;
  retryDelay: number;
  exponentialBackoff: boolean;
}

export interface FallbackAction {
  label: string;
  action: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  icon?: React.ReactNode;
}

export interface FallbackProps {
  title: string;
  description: string;
  level?: 'page' | 'component' | 'inline';
  icon?: React.ReactNode;
  actions?: FallbackAction[];
  error?: Error;
  showDetails?: boolean;
  retryConfig?: RetryConfig;
  onRetry?: () => void | Promise<void>;
  className?: string;
}

// Enhanced retry hook with exponential backoff
export function useRetryMechanism(
  operation: () => void | Promise<void>,
  config: RetryConfig = {
    maxRetries: 3,
    retryDelay: 1000,
    exponentialBackoff: true
  }
) {
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const [lastError, setLastError] = useState<Error | null>(null);

  const retry = useCallback(async () => {
    if (retryCount >= config.maxRetries || isRetrying) {
      return;
    }

    setIsRetrying(true);
    setLastError(null);

    try {
      const delay = config.exponentialBackoff 
        ? config.retryDelay * Math.pow(2, retryCount)
        : config.retryDelay;

      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      await operation();
      setRetryCount(0); // Reset on success
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      setLastError(err);
      setRetryCount(prev => prev + 1);
    } finally {
      setIsRetrying(false);
    }
  }, [operation, retryCount, config, isRetrying]);

  const reset = useCallback(() => {
    setRetryCount(0);
    setIsRetrying(false);
    setLastError(null);
  }, []);

  return {
    retry,
    reset,
    retryCount,
    isRetrying,
    lastError,
    canRetry: retryCount < config.maxRetries && !isRetrying
  };
}

// Base comprehensive fallback component
export const ComprehensiveFallback: React.FC<FallbackProps> = ({
  title,
  description,
  level = 'component',
  icon,
  actions = [],
  error,
  showDetails = false,
  retryConfig,
  onRetry,
  className = ''
}) => {
  const [showErrorDetails, setShowErrorDetails] = useState(false);
  const retryMechanism = useRetryMechanism(
    onRetry || (() => {}), 
    retryConfig
  );

  const isPageLevel = level === 'page';
  const isInline = level === 'inline';

  // Auto-retry for certain error types
  useEffect(() => {
    if (error && onRetry && retryConfig?.maxRetries && retryMechanism.canRetry) {
      // Auto-retry for network errors
      if (error.message.includes('fetch') || error.message.includes('network')) {
        const timer = setTimeout(() => {
          retryMechanism.retry();
        }, 2000);
        return () => clearTimeout(timer);
      }
    }
  }, [error, onRetry, retryConfig, retryMechanism]);

  const handleRetry = useCallback(() => {
    if (onRetry) {
      retryMechanism.retry();
    }
  }, [onRetry, retryMechanism]);

  const renderActions = () => {
    const allActions = [...actions];
    
    if (onRetry && retryMechanism.canRetry) {
      allActions.unshift({
        label: isInline ? '' : 'إعادة المحاولة',
        action: handleRetry,
        variant: 'primary' as const,
        icon: <RefreshCw className={`${isInline ? 'h-3 w-3' : 'h-4 w-4'} ${retryMechanism.isRetrying ? 'animate-spin' : ''}`} />
      });
    }

    return allActions.map((action, index) => (
      <button
        key={index}
        onClick={action.action}
        disabled={retryMechanism.isRetrying}
        className={`
          inline-flex items-center justify-center gap-2 px-4 py-2 rounded-md transition-colors
          ${isInline ? 'p-1 text-xs' : 'text-sm'}
          ${action.variant === 'primary' ? 'bg-blue-600 text-white hover:bg-blue-700' : ''}
          ${action.variant === 'secondary' ? 'border border-gray-300 text-gray-700 hover:bg-gray-50' : ''}
          ${action.variant === 'danger' ? 'bg-red-600 text-white hover:bg-red-700' : ''}
          ${!action.variant ? 'bg-gray-600 text-white hover:bg-gray-700' : ''}
          disabled:opacity-50 disabled:cursor-not-allowed
        `}
      >
        {action.icon}
        {action.label}
      </button>
    ));
  };

  if (isInline) {
    return (
      <div className={`border border-red-200 bg-red-50 p-3 rounded-md text-red-800 ${className}`}>
        <div className="flex items-center gap-2">
          {icon || <AlertTriangle className="h-4 w-4 flex-shrink-0" />}
          <span className="text-sm flex-1">{description}</span>
          <div className="flex gap-1">
            {renderActions()}
          </div>
        </div>
        {retryMechanism.retryCount > 0 && (
          <div className="mt-2 text-xs text-red-600">
            محاولة {retryMechanism.retryCount} من {retryConfig?.maxRetries || 3}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`flex items-center justify-center p-4 ${isPageLevel ? 'min-h-screen' : 'min-h-64'} ${className}`}>
      <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6 max-w-md w-full text-center">
        <div className="mb-4 text-gray-400">
          {icon || <AlertTriangle className="h-12 w-12 mx-auto text-red-600" />}
        </div>
        
        <h2 className="text-xl font-semibold mb-2 text-gray-900">{title}</h2>
        <p className="text-gray-600 mb-4">{description}</p>

        {retryMechanism.retryCount > 0 && (
          <div className="mb-4 text-sm text-gray-500">
            محاولة {retryMechanism.retryCount} من {retryConfig?.maxRetries || 3}
          </div>
        )}

        {retryMechanism.lastError && (
          <div className="mb-4 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
            {retryMechanism.lastError.message}
          </div>
        )}

        {showDetails && error && (
          <div className="mb-4">
            <button
              onClick={() => setShowErrorDetails(!showErrorDetails)}
              className="text-sm text-gray-500 hover:text-gray-700 underline"
            >
              {showErrorDetails ? 'إخفاء التفاصيل' : 'عرض التفاصيل'}
            </button>
            {showErrorDetails && (
              <div className="mt-2 bg-gray-100 p-2 rounded text-xs font-mono text-gray-700 break-all text-left">
                {error.message}
                {error.stack && (
                  <details className="mt-2">
                    <summary className="cursor-pointer">Stack Trace</summary>
                    <pre className="mt-1 text-xs">{error.stack}</pre>
                  </details>
                )}
              </div>
            )}
          </div>
        )}

        <div className="flex flex-col gap-2">
          {renderActions()}
        </div>
      </div>
    </div>
  );
};

// Specific fallback components for different scenarios
export const NetworkErrorFallback: React.FC<{
  onRetry?: () => void;
  level?: 'page' | 'component' | 'inline';
}> = ({ onRetry, level = 'component' }) => (
  <ComprehensiveFallback
    title="مشكلة في الاتصال"
    description="تعذر الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى."
    icon={<Wifi className="h-12 w-12 mx-auto text-orange-500" />}
    level={level}
    onRetry={onRetry}
    retryConfig={{ maxRetries: 5, retryDelay: 2000, exponentialBackoff: true }}
    showDetails={process.env.NODE_ENV === 'development'}
  />
);

export const DataLoadingErrorFallback: React.FC<{
  onRetry?: () => void;
  dataType?: string;
  level?: 'page' | 'component' | 'inline';
}> = ({ onRetry, dataType = 'البيانات', level = 'component' }) => (
  <ComprehensiveFallback
    title="خطأ في تحميل البيانات"
    description={`تعذر تحميل ${dataType}. يرجى المحاولة مرة أخرى.`}
    icon={<Database className="h-12 w-12 mx-auto text-red-500" />}
    level={level}
    onRetry={onRetry}
    retryConfig={{ maxRetries: 3, retryDelay: 1000, exponentialBackoff: false }}
  />
);

export const AuthenticationErrorFallback: React.FC<{
  onRetry?: () => void;
  onLogin?: () => void;
  level?: 'page' | 'component' | 'inline';
}> = ({ onRetry, onLogin, level = 'component' }) => (
  <ComprehensiveFallback
    title="خطأ في المصادقة"
    description="انتهت صلاحية جلسة العمل أو تحتاج إلى تسجيل الدخول مرة أخرى."
    icon={<Users className="h-12 w-12 mx-auto text-yellow-500" />}
    level={level}
    onRetry={onRetry}
    actions={onLogin ? [{
      label: 'تسجيل الدخول',
      action: onLogin,
      variant: 'primary',
      icon: <Users className="h-4 w-4" />
    }] : []}
  />
);

export const PermissionErrorFallback: React.FC<{
  onGoBack?: () => void;
  level?: 'page' | 'component' | 'inline';
}> = ({ onGoBack, level = 'component' }) => (
  <ComprehensiveFallback
    title="غير مصرح لك بالوصول"
    description="ليس لديك الصلاحيات اللازمة للوصول إلى هذا المحتوى."
    icon={<XCircle className="h-12 w-12 mx-auto text-red-500" />}
    level={level}
    actions={onGoBack ? [{
      label: 'العودة',
      action: onGoBack,
      variant: 'secondary',
      icon: <ArrowLeft className="h-4 w-4" />
    }] : []}
  />
);

// Empty state components with user guidance
export const EmptyStateWithGuidance: React.FC<{
  title?: string;
  description?: string;
  actionText?: string;
  onAction?: () => void;
  icon?: React.ReactNode;
  guidanceSteps?: string[];
  level?: 'page' | 'component';
}> = ({
  title = 'لا توجد بيانات',
  description = 'لم يتم العثور على أي بيانات لعرضها.',
  actionText,
  onAction,
  icon,
  guidanceSteps = [],
  level = 'component'
}) => (
  <div className={`flex flex-col items-center justify-center p-8 text-center ${level === 'page' ? 'min-h-screen' : ''}`}>
    <div className="mb-4 text-gray-400">
      {icon || <FileX className="h-12 w-12" />}
    </div>
    <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
    <p className="text-gray-600 mb-4">{description}</p>
    
    {guidanceSteps.length > 0 && (
      <div className="mb-6 text-sm text-gray-600 max-w-md">
        <p className="font-medium mb-2">يمكنك:</p>
        <ul className="text-right space-y-1">
          {guidanceSteps.map((step, index) => (
            <li key={index} className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
              {step}
            </li>
          ))}
        </ul>
      </div>
    )}
    
    {actionText && onAction && (
      <button
        onClick={onAction}
        className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
      >
        <Plus className="h-4 w-4" />
        {actionText}
      </button>
    )}
  </div>
);

// Specific empty states for different sections
export const EmptyProblemsState: React.FC<{
  onCreateProblem?: () => void;
  onBrowseExperts?: () => void;
}> = ({ onCreateProblem, onBrowseExperts }) => (
  <EmptyStateWithGuidance
    title="لا توجد مشاكل تقنية"
    description="لم يتم العثور على أي مشاكل تقنية. ابدأ بإضافة مشكلة جديدة."
    icon={<Search className="h-12 w-12" />}
    actionText="إضافة مشكلة جديدة"
    onAction={onCreateProblem}
    guidanceSteps={[
      'إضافة مشكلة تقنية جديدة',
      'تصفح الخبراء المتاحين',
      'البحث في المشاكل الموجودة'
    ]}
  />
);

export const EmptyExpertsState: React.FC<{
  onCreateProfile?: () => void;
  onBrowseProblems?: () => void;
}> = ({ onCreateProfile, onBrowseProblems }) => (
  <EmptyStateWithGuidance
    title="لا يوجد خبراء"
    description="لم يتم العثور على خبراء في هذا المجال."
    icon={<Users className="h-12 w-12" />}
    actionText="إنشاء ملف خبير"
    onAction={onCreateProfile}
    guidanceSteps={[
      'إنشاء ملف شخصي كخبير',
      'تصفح المشاكل المتاحة',
      'تحديث معايير البحث'
    ]}
  />
);

export const EmptySearchResultsState: React.FC<{
  searchTerm?: string;
  onClearSearch?: () => void;
  onBrowseAll?: () => void;
}> = ({ searchTerm, onClearSearch, onBrowseAll }) => (
  <EmptyStateWithGuidance
    title="لا توجد نتائج"
    description={searchTerm ? `لم يتم العثور على نتائج لـ "${searchTerm}"` : 'لم يتم العثور على نتائج للبحث.'}
    icon={<Search className="h-12 w-12" />}
    guidanceSteps={[
      'جرب كلمات مفتاحية مختلفة',
      'تحقق من الإملاء',
      'استخدم مصطلحات أكثر عمومية',
      'تصفح جميع العناصر المتاحة'
    ]}
  />
);

// Loading error with retry
export const LoadingErrorWithRetry: React.FC<{
  onRetry?: () => void;
  resourceType?: string;
  level?: 'page' | 'component' | 'inline';
}> = ({ onRetry, resourceType = 'المحتوى', level = 'component' }) => (
  <ComprehensiveFallback
    title="خطأ في التحميل"
    description={`تعذر تحميل ${resourceType}. يرجى المحاولة مرة أخرى.`}
    icon={<Clock className="h-12 w-12 mx-auto text-orange-500" />}
    level={level}
    onRetry={onRetry}
    retryConfig={{ maxRetries: 3, retryDelay: 1500, exponentialBackoff: true }}
  />
);

// Configuration error fallback
export const ConfigurationErrorFallback: React.FC<{
  onOpenSettings?: () => void;
  configType?: string;
}> = ({ onOpenSettings, configType = 'التطبيق' }) => (
  <ComprehensiveFallback
    title="خطأ في الإعدادات"
    description={`يوجد خطأ في إعدادات ${configType}. يرجى مراجعة الإعدادات.`}
    icon={<Settings className="h-12 w-12 mx-auto text-yellow-500" />}
    level="component"
    actions={onOpenSettings ? [{
      label: 'فتح الإعدادات',
      action: onOpenSettings,
      variant: 'primary',
      icon: <Settings className="h-4 w-4" />
    }] : []}
  />
);

// Export all components
export default {
  ComprehensiveFallback,
  NetworkErrorFallback,
  DataLoadingErrorFallback,
  AuthenticationErrorFallback,
  PermissionErrorFallback,
  EmptyStateWithGuidance,
  EmptyProblemsState,
  EmptyExpertsState,
  EmptySearchResultsState,
  LoadingErrorWithRetry,
  ConfigurationErrorFallback,
  useRetryMechanism
};