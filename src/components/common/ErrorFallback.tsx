import React from 'react';
import { AlertTriangle, RefreshCw, Home, Bug, ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useLanguage } from '@/contexts/LanguageContext';
import { useDeviceType } from '@/hooks/use-mobile';

interface ErrorInfo {
  componentStack: string;
  errorBoundary?: string;
}

interface ErrorFallbackProps {
  error: Error;
  errorInfo?: ErrorInfo;
  resetError?: () => void;
  onReport?: (error: Error, errorInfo?: ErrorInfo) => void;
  variant?: 'page' | 'component' | 'inline';
  showDetails?: boolean;
  showReportButton?: boolean;
}

export function ErrorFallback({
  error,
  errorInfo,
  resetError,
  onReport,
  variant = 'component',
  showDetails = true,
  showReportButton = true
}: ErrorFallbackProps) {
  const { t, isRTL } = useLanguage();
  const { isMobile } = useDeviceType();
  const [showErrorDetails, setShowErrorDetails] = React.useState(false);
  const [isReporting, setIsReporting] = React.useState(false);

  const handleReport = async () => {
    if (!onReport) return;
    
    setIsReporting(true);
    try {
      await onReport(error, errorInfo);
      // Show success message
    } catch (reportError) {
      console.error('Failed to report error:', reportError);
    } finally {
      setIsReporting(false);
    }
  };

  const handleReload = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  const getErrorTitle = () => {
    switch (variant) {
      case 'page':
        return t('errors.page_error_title', 'Something went wrong with this page');
      case 'component':
        return t('errors.component_error_title', 'Component error occurred');
      case 'inline':
        return t('errors.inline_error_title', 'Error');
      default:
        return t('errors.generic_error_title', 'An error occurred');
    }
  };

  const getErrorDescription = () => {
    switch (variant) {
      case 'page':
        return t('errors.page_error_description', 'We encountered an unexpected error while loading this page. Please try refreshing or go back to the homepage.');
      case 'component':
        return t('errors.component_error_description', 'This component failed to load properly. You can try refreshing the page or continue using other parts of the application.');
      case 'inline':
        return t('errors.inline_error_description', 'This section could not be loaded.');
      default:
        return t('errors.generic_error_description', 'An unexpected error occurred. Please try again.');
    }
  };

  const getErrorActions = () => {
    const actions = [];

    if (resetError) {
      actions.push(
        <Button
          key="retry"
          onClick={resetError}
          variant="default"
          size={isMobile ? "default" : "sm"}
          className="flex items-center gap-2"
        >
          <RefreshCw className="w-4 h-4" />
          {t('errors.retry_button', 'Try Again')}
        </Button>
      );
    }

    if (variant === 'page') {
      actions.push(
        <Button
          key="reload"
          onClick={handleReload}
          variant="outline"
          size={isMobile ? "default" : "sm"}
          className="flex items-center gap-2"
        >
          <RefreshCw className="w-4 h-4" />
          {t('errors.reload_button', 'Reload Page')}
        </Button>
      );

      actions.push(
        <Button
          key="home"
          onClick={handleGoHome}
          variant="outline"
          size={isMobile ? "default" : "sm"}
          className="flex items-center gap-2"
        >
          <Home className="w-4 h-4" />
          {t('errors.home_button', 'Go Home')}
        </Button>
      );
    }

    if (showReportButton && onReport) {
      actions.push(
        <Button
          key="report"
          onClick={handleReport}
          variant="ghost"
          size={isMobile ? "default" : "sm"}
          disabled={isReporting}
          className="flex items-center gap-2"
        >
          <Bug className="w-4 h-4" />
          {isReporting 
            ? t('errors.reporting_button', 'Reporting...') 
            : t('errors.report_button', 'Report Issue')
          }
        </Button>
      );
    }

    return actions;
  };

  if (variant === 'inline') {
    return (
      <Alert className="border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <span>{getErrorDescription()}</span>
          {resetError && (
            <Button
              onClick={resetError}
              variant="ghost"
              size="sm"
              className="h-auto p-1 text-destructive hover:text-destructive"
            >
              <RefreshCw className="w-3 h-3" />
            </Button>
          )}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={`flex items-center justify-center min-h-[400px] p-4 ${variant === 'page' ? 'min-h-screen' : ''}`} dir={isRTL ? 'rtl' : 'ltr'}>
      <Card className={`w-full max-w-lg ${variant === 'page' ? 'max-w-2xl' : ''}`}>
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
            <AlertTriangle className="h-6 w-6 text-destructive" />
          </div>
          <CardTitle className="text-xl font-semibold">
            {getErrorTitle()}
          </CardTitle>
          <CardDescription className="text-center">
            {getErrorDescription()}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Action Buttons */}
          <div className={`flex gap-2 ${isMobile ? 'flex-col' : 'flex-wrap justify-center'}`}>
            {getErrorActions()}
          </div>

          {/* Error Details (Collapsible) */}
          {showDetails && (error.message || errorInfo) && (
            <Collapsible open={showErrorDetails} onOpenChange={setShowErrorDetails}>
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full flex items-center justify-center gap-2 text-muted-foreground"
                >
                  {showErrorDetails ? (
                    <>
                      <ChevronUp className="w-4 h-4" />
                      {t('errors.hide_details', 'Hide Details')}
                    </>
                  ) : (
                    <>
                      <ChevronDown className="w-4 h-4" />
                      {t('errors.show_details', 'Show Details')}
                    </>
                  )}
                </Button>
              </CollapsibleTrigger>
              
              <CollapsibleContent className="space-y-2">
                {error.message && (
                  <div className="rounded-md bg-muted p-3">
                    <h4 className="text-sm font-medium mb-1">
                      {t('errors.error_message', 'Error Message')}:
                    </h4>
                    <code className="text-xs text-muted-foreground break-all">
                      {error.message}
                    </code>
                  </div>
                )}
                
                {errorInfo?.componentStack && (
                  <div className="rounded-md bg-muted p-3">
                    <h4 className="text-sm font-medium mb-1">
                      {t('errors.component_stack', 'Component Stack')}:
                    </h4>
                    <pre className="text-xs text-muted-foreground whitespace-pre-wrap break-all max-h-32 overflow-y-auto">
                      {errorInfo.componentStack}
                    </pre>
                  </div>
                )}
              </CollapsibleContent>
            </Collapsible>
          )}

          {/* Help Text */}
          <div className="text-center text-sm text-muted-foreground">
            {t('errors.help_text', 'If this problem persists, please contact support or try again later.')}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Simplified error fallback for critical errors
export function CriticalErrorFallback({ error }: { error: Error }) {
  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <div className="text-center space-y-4 max-w-md">
        <AlertTriangle className="h-12 w-12 text-destructive mx-auto" />
        <h1 className="text-2xl font-bold">Critical Error</h1>
        <p className="text-muted-foreground">
          The application encountered a critical error and cannot continue.
        </p>
        <div className="bg-muted p-3 rounded-md text-left">
          <code className="text-xs break-all">{error.message}</code>
        </div>
        <Button onClick={() => window.location.reload()} className="w-full">
          <RefreshCw className="w-4 h-4 mr-2" />
          Reload Application
        </Button>
      </div>
    </div>
  );
}
