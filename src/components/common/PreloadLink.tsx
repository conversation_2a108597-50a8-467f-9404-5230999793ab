import { Link, LinkProps } from 'react-router-dom';
import { useRoutePreloader } from './RoutePreloader';

interface PreloadLinkProps extends LinkProps {
  /**
   * Route category to preload on hover
   */
  preloadCategory?: 'auth' | 'main' | 'problems' | 'experts' | 'admin';
  
  /**
   * Custom preload function
   */
  onPreload?: () => void;
  
  /**
   * Delay before preloading (in milliseconds)
   */
  preloadDelay?: number;
}

/**
 * Enhanced Link component that preloads routes on hover
 * Improves perceived performance by loading components before navigation
 */
export const PreloadLink = ({ 
  preloadCategory, 
  onPreload, 
  preloadDelay = 100,
  onMouseEnter,
  onFocus,
  children,
  ...linkProps 
}: PreloadLinkProps) => {
  const preloader = useRoutePreloader();
  
  const handlePreload = () => {
    if (onPreload) {
      onPreload();
      return;
    }
    
    switch (preloadCategory) {
      case 'auth':
        preloader.preloadAuth();
        break;
      case 'main':
        preloader.preloadMain();
        break;
      case 'problems':
        preloader.preloadProblems();
        break;
      case 'experts':
        preloader.preloadExperts();
        break;
      case 'admin':
        preloader.preloadAdmin();
        break;
    }
  };

  const handleMouseEnter = (event: React.MouseEvent<HTMLAnchorElement>) => {
    // Call original onMouseEnter if provided
    onMouseEnter?.(event);
    
    // Preload after delay
    setTimeout(handlePreload, preloadDelay);
  };

  const handleFocus = (event: React.FocusEvent<HTMLAnchorElement>) => {
    // Call original onFocus if provided
    onFocus?.(event);
    
    // Preload on focus for keyboard navigation
    setTimeout(handlePreload, preloadDelay);
  };

  return (
    <Link
      {...linkProps}
      onMouseEnter={handleMouseEnter}
      onFocus={handleFocus}
    >
      {children}
    </Link>
  );
};