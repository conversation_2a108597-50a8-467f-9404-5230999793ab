import React from 'react';
import { AlertTriangle, RefreshCw, Home, Wifi, Database, FileX } from 'lucide-react';

// Base fallback component with minimal dependencies
export const BaseFallback: React.FC<{
  title: string;
  description: string;
  onRetry?: () => void;
  onGoHome?: () => void;
  showDetails?: boolean;
  error?: Error;
  level?: 'page' | 'component' | 'inline';
}> = ({ 
  title, 
  description, 
  onRetry, 
  onGoHome, 
  showDetails = false, 
  error,
  level = 'component'
}) => {
  const [showErrorDetails, setShowErrorDetails] = React.useState(false);
  const isPageLevel = level === 'page';
  const isInline = level === 'inline';

  if (isInline) {
    return (
      <div className="border border-red-200 bg-red-50 p-3 rounded-md text-red-800">
        <div className="flex items-center gap-2">
          <AlertTriangle className="h-4 w-4 flex-shrink-0" />
          <span className="text-sm">{description}</span>
          {onRetry && (
            <button
              onClick={onRetry}
              className="ml-auto p-1 hover:bg-red-100 rounded"
              aria-label="إعادة المحاولة"
            >
              <RefreshCw className="h-3 w-3" />
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-center justify-center p-4 ${isPageLevel ? 'min-h-screen' : 'min-h-64'}`}>
      <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6 max-w-md w-full text-center">
        <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-red-600" />
        <h2 className="text-xl font-semibold mb-2 text-gray-900">{title}</h2>
        <p className="text-gray-600 mb-4">{description}</p>
        
        {showDetails && error && (
          <div className="mb-4">
            <button
              onClick={() => setShowErrorDetails(!showErrorDetails)}
              className="text-sm text-gray-500 hover:text-gray-700 underline"
            >
              {showErrorDetails ? 'إخفاء التفاصيل' : 'عرض التفاصيل'}
            </button>
            {showErrorDetails && (
              <div className="mt-2 bg-gray-100 p-2 rounded text-xs font-mono text-gray-700 break-all text-left">
                {error.message}
              </div>
            )}
          </div>
        )}
        
        <div className="flex flex-col gap-2">
          {onRetry && (
            <button
              onClick={onRetry}
              className="flex items-center justify-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              إعادة المحاولة
            </button>
          )}
          
          {onGoHome && isPageLevel && (
            <button
              onClick={onGoHome}
              className="flex items-center justify-center gap-2 border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors"
            >
              <Home className="w-4 h-4" />
              العودة للصفحة الرئيسية
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

// Network error fallback
export const NetworkErrorFallback: React.FC<{
  onRetry?: () => void;
  level?: 'page' | 'component' | 'inline';
}> = ({ onRetry, level = 'component' }) => (
  <BaseFallback
    title="مشكلة في الاتصال"
    description="تعذر الاتصال بالخادم. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى."
    onRetry={onRetry}
    level={level}
  />
);

// Data loading error fallback
export const DataErrorFallback: React.FC<{
  onRetry?: () => void;
  dataType?: string;
  level?: 'page' | 'component' | 'inline';
}> = ({ onRetry, dataType = 'البيانات', level = 'component' }) => (
  <BaseFallback
    title="خطأ في تحميل البيانات"
    description={`تعذر تحميل ${dataType}. يرجى المحاولة مرة أخرى.`}
    onRetry={onRetry}
    level={level}
  />
);

// Form error fallback
export const FormErrorFallback: React.FC<{
  onRetry?: () => void;
  formName?: string;
  error?: Error;
}> = ({ onRetry, formName = 'النموذج', error }) => (
  <div className="border border-red-200 bg-red-50 p-4 rounded-md">
    <div className="flex items-start gap-3">
      <AlertTriangle className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
      <div className="flex-1">
        <h3 className="text-sm font-medium text-red-800 mb-1">
          خطأ في {formName}
        </h3>
        <p className="text-sm text-red-700 mb-3">
          حدث خطأ أثناء تحميل النموذج. يرجى المحاولة مرة أخرى.
        </p>
        
        {process.env.NODE_ENV === 'development' && error && (
          <details className="mb-3">
            <summary className="cursor-pointer text-xs text-red-600 mb-1">
              تفاصيل الخطأ (للمطورين)
            </summary>
            <div className="bg-red-100 p-2 rounded text-xs font-mono text-red-800 break-all">
              {error.message}
            </div>
          </details>
        )}
        
        {onRetry && (
          <button
            onClick={onRetry}
            className="inline-flex items-center gap-2 bg-red-600 text-white px-3 py-1.5 rounded text-sm hover:bg-red-700 transition-colors"
          >
            <RefreshCw className="w-3 h-3" />
            إعادة تحميل النموذج
          </button>
        )}
      </div>
    </div>
  </div>
);

// Component loading error fallback
export const ComponentErrorFallback: React.FC<{
  onRetry?: () => void;
  componentName?: string;
  error?: Error;
}> = ({ onRetry, componentName = 'المكون', error }) => (
  <BaseFallback
    title={`خطأ في ${componentName}`}
    description="حدث خطأ أثناء تحميل هذا المكون. يرجى المحاولة مرة أخرى."
    onRetry={onRetry}
    showDetails={process.env.NODE_ENV === 'development'}
    error={error}
    level="component"
  />
);

// Page error fallback
export const PageErrorFallback: React.FC<{
  onRetry?: () => void;
  onGoHome?: () => void;
  error?: Error;
}> = ({ onRetry, onGoHome, error }) => (
  <BaseFallback
    title="حدث خطأ في الصفحة"
    description="نعتذر، حدث خطأ غير متوقع أثناء تحميل هذه الصفحة."
    onRetry={onRetry}
    onGoHome={onGoHome}
    showDetails={process.env.NODE_ENV === 'development'}
    error={error}
    level="page"
  />
);

// Empty state fallback
export const EmptyStateFallback: React.FC<{
  title?: string;
  description?: string;
  actionText?: string;
  onAction?: () => void;
  icon?: React.ReactNode;
}> = ({ 
  title = 'لا توجد بيانات',
  description = 'لم يتم العثور على أي بيانات لعرضها.',
  actionText,
  onAction,
  icon
}) => (
  <div className="flex flex-col items-center justify-center p-8 text-center">
    <div className="mb-4 text-gray-400">
      {icon || <FileX className="h-12 w-12" />}
    </div>
    <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
    <p className="text-gray-600 mb-4">{description}</p>
    {actionText && onAction && (
      <button
        onClick={onAction}
        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
      >
        {actionText}
      </button>
    )}
  </div>
);

// Loading error fallback
export const LoadingErrorFallback: React.FC<{
  onRetry?: () => void;
  resourceType?: string;
}> = ({ onRetry, resourceType = 'المحتوى' }) => (
  <div className="flex items-center justify-center p-4">
    <div className="text-center">
      <Database className="h-8 w-8 mx-auto mb-2 text-gray-400" />
      <p className="text-sm text-gray-600 mb-3">
        تعذر تحميل {resourceType}
      </p>
      {onRetry && (
        <button
          onClick={onRetry}
          className="text-sm text-blue-600 hover:text-blue-700 underline"
        >
          إعادة المحاولة
        </button>
      )}
    </div>
  </div>
);

// Critical error fallback (for app-breaking errors)
export const CriticalErrorFallback: React.FC<{
  error?: Error;
}> = ({ error }) => (
  <div className="flex items-center justify-center min-h-screen bg-gray-50 p-4">
    <div className="text-center space-y-4 max-w-md">
      <AlertTriangle className="h-16 w-16 text-red-600 mx-auto" />
      <h1 className="text-2xl font-bold text-gray-900">خطأ حرج</h1>
      <p className="text-gray-600">
        واجه التطبيق خطأ حرج ولا يمكنه المتابعة. يرجى إعادة تحميل الصفحة.
      </p>
      {process.env.NODE_ENV === 'development' && error && (
        <div className="bg-gray-100 p-3 rounded-md text-left">
          <code className="text-xs break-all text-gray-700">{error.message}</code>
        </div>
      )}
      <button 
        onClick={() => window.location.reload()} 
        className="bg-red-600 text-white px-6 py-2 rounded-md hover:bg-red-700 transition-colors"
      >
        <RefreshCw className="w-4 h-4 inline mr-2" />
        إعادة تحميل التطبيق
      </button>
    </div>
  </div>
);

export default {
  BaseFallback,
  NetworkErrorFallback,
  DataErrorFallback,
  FormErrorFallback,
  ComponentErrorFallback,
  PageErrorFallback,
  EmptyStateFallback,
  LoadingErrorFallback,
  CriticalErrorFallback,
};