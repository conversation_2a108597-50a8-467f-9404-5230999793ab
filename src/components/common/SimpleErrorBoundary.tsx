import React, { Component, ReactNode, ErrorInfo } from 'react';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

interface SimpleErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  retryCount: number;
}

interface SimpleErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<{
    error: Error;
    retry: () => void;
    goHome: () => void;
  }>;
  level?: 'page' | 'component' | 'inline';
  maxRetries?: number;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

// Simple fallback component with no external dependencies
const SimpleFallback: React.FC<{
  error: Error;
  retry: () => void;
  goHome: () => void;
  level: 'page' | 'component' | 'inline';
}> = ({ error, retry, goHome, level }) => {
  const isPageLevel = level === 'page';
  const isInline = level === 'inline';

  if (isInline) {
    return (
      <div className="border border-red-200 bg-red-50 p-3 rounded-md text-red-800">
        <div className="flex items-center gap-2">
          <AlertTriangle className="h-4 w-4 flex-shrink-0" />
          <span className="text-sm">هذا القسم لا يمكن تحميله</span>
          <button
            onClick={retry}
            className="ml-auto p-1 hover:bg-red-100 rounded"
            aria-label="إعادة المحاولة"
          >
            <RefreshCw className="h-3 w-3" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-center justify-center p-4 ${isPageLevel ? 'min-h-screen' : 'min-h-64'}`}>
      <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6 max-w-md w-full text-center">
        <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-red-600" />
        <h2 className="text-xl font-semibold mb-2 text-gray-900">
          {isPageLevel ? 'حدث خطأ في التطبيق' : 'حدث خطأ في هذا القسم'}
        </h2>
        <p className="text-gray-600 mb-6">
          {isPageLevel 
            ? 'نعتذر، حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.'
            : 'حدث خطأ في تحميل هذا المحتوى.'
          }
        </p>
        
        {process.env.NODE_ENV === 'development' && (
          <details className="mb-4 text-left">
            <summary className="cursor-pointer text-sm text-gray-500 mb-2">
              تفاصيل الخطأ (للمطورين)
            </summary>
            <div className="bg-gray-100 p-2 rounded text-xs font-mono text-gray-700 break-all">
              {error.message}
            </div>
          </details>
        )}
        
        <div className="flex flex-col gap-2">
          <button
            onClick={retry}
            className="flex items-center justify-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            إعادة المحاولة
          </button>
          
          {isPageLevel && (
            <button
              onClick={goHome}
              className="flex items-center justify-center gap-2 border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 transition-colors"
            >
              <Home className="w-4 h-4" />
              العودة للصفحة الرئيسية
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export class SimpleErrorBoundary extends Component<SimpleErrorBoundaryProps, SimpleErrorBoundaryState> {
  private retryTimeoutId: number | null = null;

  constructor(props: SimpleErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<SimpleErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Simple error logging without external dependencies
    console.error('SimpleErrorBoundary caught an error:', error, errorInfo);
    
    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  handleRetry = () => {
    const { maxRetries = 3 } = this.props;
    
    if (this.state.retryCount >= maxRetries) {
      console.warn('SimpleErrorBoundary exceeded max retries');
      return;
    }

    // Clear error state with a small delay
    this.retryTimeoutId = window.setTimeout(() => {
      this.setState({
        hasError: false,
        error: null,
        retryCount: this.state.retryCount + 1,
      });
    }, 100);
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError && this.state.error) {
      const FallbackComponent = this.props.fallback || SimpleFallback;
      
      return (
        <FallbackComponent
          error={this.state.error}
          retry={this.handleRetry}
          goHome={this.handleGoHome}
          level={this.props.level || 'component'}
        />
      );
    }

    return this.props.children;
  }
}

// Simple HOC wrapper
export const withSimpleErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    level?: 'page' | 'component' | 'inline';
    maxRetries?: number;
  }
) => {
  const WrappedComponent = (props: P) => (
    <SimpleErrorBoundary
      level={options?.level}
      maxRetries={options?.maxRetries}
    >
      <Component {...props} />
    </SimpleErrorBoundary>
  );

  WrappedComponent.displayName = `withSimpleErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

export default SimpleErrorBoundary;