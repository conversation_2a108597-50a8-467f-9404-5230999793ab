import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON>ert<PERSON><PERSON>gle, RefreshCw, Home } from 'lucide-react';
import { 
  BaseFallback, 
  PageErrorFallback, 
  ComponentErrorFallback,
  CriticalErrorFallback 
} from './FallbackComponents';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  level?: 'page' | 'component' | 'route';
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

interface ErrorFallbackProps {
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retry: () => void;
  level: 'page' | 'component' | 'route';
}

// Default error fallback component
const DefaultErrorFallback: React.FC<ErrorFallbackProps> = ({ 
  error, 
  errorInfo, 
  retry, 
  level 
}) => {
  const isRouteLevel = level === 'route';
  const isPageLevel = level === 'page';
  const isComponentLevel = level === 'component';

  const handleGoHome = () => {
    window.location.href = '/';
  };

  if (isRouteLevel || isPageLevel) {
    return (
      <PageErrorFallback
        error={error}
        onRetry={retry}
        onGoHome={handleGoHome}
      />
    );
  }

  if (isComponentLevel) {
    return (
      <ComponentErrorFallback
        error={error}
        onRetry={retry}
      />
    );
  }

  // Inline fallback
  return (
    <BaseFallback
      title="خطأ"
      description="حدث خطأ في تحميل هذا المحتوى"
      onRetry={retry}
      error={error}
      level="inline"
      showDetails={process.env.NODE_ENV === 'development'}
    />
  );
};

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error for monitoring
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Report to error tracking service (e.g., Sentry)
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'exception', {
        description: error.message,
        fatal: false,
      });
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  handleRetry = () => {
    // Clear error state
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });

    // Add small delay to prevent immediate re-error
    this.retryTimeoutId = window.setTimeout(() => {
      // Force re-render by updating key or triggering parent re-render
      this.forceUpdate();
    }, 100);
  };

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      
      return (
        <FallbackComponent
          error={this.state.error}
          errorInfo={this.state.errorInfo}
          retry={this.handleRetry}
          level={this.props.level || 'component'}
        />
      );
    }

    return this.props.children;
  }
}

// Higher-order component for easy wrapping
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

// Hook for triggering errors (useful for testing)
export const useErrorHandler = () => {
  return (error: Error) => {
    throw error;
  };
};

export default ErrorBoundary;