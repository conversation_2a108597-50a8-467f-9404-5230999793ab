import { useState, useEffect, useRef, ReactNode } from 'react'
import { cn } from '@/lib/utils'

interface LoadingStateManagerProps {
  isLoading: boolean
  loadingComponent: ReactNode
  children: ReactNode
  className?: string
  transitionDuration?: number
  minLoadingTime?: number
  fadeTransition?: boolean
  slideTransition?: boolean
  scaleTransition?: boolean
}

export function LoadingStateManager({
  isLoading,
  loadingComponent,
  children,
  className = '',
  transitionDuration = 300,
  minLoadingTime = 500,
  fadeTransition = true,
  slideTransition = false,
  scaleTransition = false
}: LoadingStateManagerProps) {
  const [showLoading, setShowLoading] = useState(isLoading)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const loadingStartTime = useRef<number | null>(null)
  const transitionTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (isLoading) {
      // Starting to load
      loadingStartTime.current = Date.now()
      setShowLoading(true)
      setIsTransitioning(false)
    } else if (showLoading) {
      // Finished loading, but ensure minimum loading time
      const elapsedTime = loadingStartTime.current 
        ? Date.now() - loadingStartTime.current 
        : minLoadingTime

      const remainingTime = Math.max(0, minLoadingTime - elapsedTime)

      if (transitionTimeoutRef.current) {
        clearTimeout(transitionTimeoutRef.current)
      }

      transitionTimeoutRef.current = setTimeout(() => {
        setIsTransitioning(true)
        
        // After transition duration, hide loading
        setTimeout(() => {
          setShowLoading(false)
          setIsTransitioning(false)
          loadingStartTime.current = null
        }, transitionDuration)
      }, remainingTime)
    }

    return () => {
      if (transitionTimeoutRef.current) {
        clearTimeout(transitionTimeoutRef.current)
      }
    }
  }, [isLoading, showLoading, minLoadingTime, transitionDuration])

  const getTransitionClasses = (isExiting: boolean) => {
    const baseClasses = 'loading-transition'
    const classes = [baseClasses]

    if (fadeTransition) {
      classes.push(isExiting ? 'loading-exit-active' : 'loading-enter-active')
    }

    if (slideTransition) {
      classes.push(isExiting ? 'translate-y-[-10px]' : 'translate-y-0')
    }

    if (scaleTransition) {
      classes.push(isExiting ? 'scale-95' : 'scale-100')
    }

    return classes.join(' ')
  }

  return (
    <div className={cn('relative', className)}>
      {showLoading && (
        <div 
          className={cn(
            'absolute inset-0 z-10',
            getTransitionClasses(isTransitioning),
            isTransitioning && 'pointer-events-none'
          )}
          style={{
            transitionDuration: `${transitionDuration}ms`
          }}
        >
          {loadingComponent}
        </div>
      )}
      
      <div 
        className={cn(
          'transition-opacity duration-300',
          showLoading && !isTransitioning ? 'opacity-0' : 'opacity-100'
        )}
        style={{
          transitionDuration: `${transitionDuration}ms`
        }}
      >
        {children}
      </div>
    </div>
  )
}

// Hook for managing loading states with smooth transitions
export function useLoadingState(initialLoading = false) {
  const [isLoading, setIsLoading] = useState(initialLoading)
  const [error, setError] = useState<string | null>(null)

  const startLoading = () => {
    setIsLoading(true)
    setError(null)
  }

  const stopLoading = () => {
    setIsLoading(false)
  }

  const setLoadingError = (errorMessage: string) => {
    setError(errorMessage)
    setIsLoading(false)
  }

  return {
    isLoading,
    error,
    startLoading,
    stopLoading,
    setLoadingError
  }
}

// Higher-order component for adding loading states
interface WithLoadingProps {
  isLoading?: boolean
  loadingComponent?: ReactNode
  className?: string
}

export function withLoading<P extends object>(
  Component: React.ComponentType<P>,
  defaultLoadingComponent?: ReactNode
) {
  return function WithLoadingComponent(props: P & WithLoadingProps) {
    const { isLoading = false, loadingComponent, className, ...componentProps } = props

    if (isLoading) {
      return (
        <div className={className}>
          {loadingComponent || defaultLoadingComponent}
        </div>
      )
    }

    return <Component {...(componentProps as P)} className={className} />
  }
}

// Utility component for content that appears after loading
interface LoadingBoundaryProps {
  isLoading: boolean
  fallback: ReactNode
  children: ReactNode
  className?: string
  delay?: number
}

export function LoadingBoundary({
  isLoading,
  fallback,
  children,
  className = '',
  delay = 0
}: LoadingBoundaryProps) {
  const [showContent, setShowContent] = useState(!isLoading)

  useEffect(() => {
    if (!isLoading) {
      if (delay > 0) {
        const timer = setTimeout(() => setShowContent(true), delay)
        return () => clearTimeout(timer)
      } else {
        setShowContent(true)
      }
    } else {
      setShowContent(false)
    }
  }, [isLoading, delay])

  if (isLoading || !showContent) {
    return <div className={className}>{fallback}</div>
  }

  return <div className={className}>{children}</div>
}