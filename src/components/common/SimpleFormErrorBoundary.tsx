import React, { Component, ReactNode, ErrorInfo } from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface SimpleFormErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  retryCount: number;
}

interface SimpleFormErrorBoundaryProps {
  children: ReactNode;
  formName?: string;
  maxRetries?: number;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

// Simple form error fallback with no external dependencies
const SimpleFormFallback: React.FC<{
  error: Error;
  retry: () => void;
  formName?: string;
}> = ({ error, retry, formName }) => {
  return (
    <div className="border border-red-200 bg-red-50 p-4 rounded-md">
      <div className="flex items-start gap-3">
        <AlertTriangle className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
        <div className="flex-1">
          <h3 className="text-sm font-medium text-red-800 mb-1">
            خطأ في النموذج {formName && `(${formName})`}
          </h3>
          <p className="text-sm text-red-700 mb-3">
            حدث خطأ أثناء تحميل النموذج. يرجى المحاولة مرة أخرى.
          </p>
          
          {process.env.NODE_ENV === 'development' && (
            <details className="mb-3">
              <summary className="cursor-pointer text-xs text-red-600 mb-1">
                تفاصيل الخطأ (للمطورين)
              </summary>
              <div className="bg-red-100 p-2 rounded text-xs font-mono text-red-800 break-all">
                {error.message}
              </div>
            </details>
          )}
          
          <button
            onClick={retry}
            className="inline-flex items-center gap-2 bg-red-600 text-white px-3 py-1.5 rounded text-sm hover:bg-red-700 transition-colors"
          >
            <RefreshCw className="w-3 h-3" />
            إعادة تحميل النموذج
          </button>
        </div>
      </div>
    </div>
  );
};

export class SimpleFormErrorBoundary extends Component<SimpleFormErrorBoundaryProps, SimpleFormErrorBoundaryState> {
  private retryTimeoutId: number | null = null;

  constructor(props: SimpleFormErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<SimpleFormErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Simple error logging
    console.error(`SimpleFormErrorBoundary (${this.props.formName}) caught an error:`, error, errorInfo);
    
    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  handleRetry = () => {
    const { maxRetries = 2 } = this.props;
    
    if (this.state.retryCount >= maxRetries) {
      console.warn(`SimpleFormErrorBoundary (${this.props.formName}) exceeded max retries`);
      return;
    }

    // Clear error state with a small delay
    this.retryTimeoutId = window.setTimeout(() => {
      this.setState({
        hasError: false,
        error: null,
        retryCount: this.state.retryCount + 1,
      });
    }, 100);
  };

  render() {
    if (this.state.hasError && this.state.error) {
      return (
        <SimpleFormFallback
          error={this.state.error}
          retry={this.handleRetry}
          formName={this.props.formName}
        />
      );
    }

    return this.props.children;
  }
}

export default SimpleFormErrorBoundary;