import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { preloadStrategies } from '@/routes/lazyRoutes';

/**
 * Component that handles intelligent route preloading based on current location
 * and user behavior patterns
 */
export const RoutePreloader = () => {
  const location = useLocation();

  useEffect(() => {
    // Preload related routes based on current path
    const currentPath = location.pathname;

    // Preload authentication pages when on landing page
    if (currentPath === '/') {
      // Delay preloading to avoid interfering with initial page load
      setTimeout(() => {
        preloadStrategies.authPages();
      }, 2000);
    }

    // Preload main app pages after authentication
    if (currentPath.startsWith('/auth/')) {
      setTimeout(() => {
        preloadStrategies.mainPages();
      }, 1000);
    }

    // Preload problem-related pages when viewing problems
    if (currentPath.startsWith('/problems')) {
      setTimeout(() => {
        preloadStrategies.problemPages();
      }, 1500);
    }

    // Preload expert-related pages when viewing experts
    if (currentPath.startsWith('/experts')) {
      setTimeout(() => {
        preloadStrategies.expertPages();
      }, 1500);
    }

    // Preload admin pages when accessing admin area
    if (currentPath.startsWith('/admin')) {
      setTimeout(() => {
        preloadStrategies.adminPages();
      }, 1000);
    }
  }, [location.pathname]);

  // This component doesn't render anything
  return null;
};

/**
 * Hook for manual route preloading
 */
export const useRoutePreloader = () => {
  return {
    preloadAuth: preloadStrategies.authPages,
    preloadMain: preloadStrategies.mainPages,
    preloadProblems: preloadStrategies.problemPages,
    preloadExperts: preloadStrategies.expertPages,
    preloadAdmin: preloadStrategies.adminPages,
  };
};