import { ProblemSkeleton as BaseProblemSkeleton, CardSkeleton, ListSkeleton } from '@/components/ui/skeleton-variants'
import { useDeviceType } from '@/hooks/use-mobile'
import { cn } from '@/lib/utils'

interface ProblemDashboardSkeletonProps {
  itemCount?: number
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

export function ProblemDashboardSkeleton({
  itemCount = 6,
  animation = 'pulse',
  className = ''
}: ProblemDashboardSkeletonProps) {
  const { isMobile } = useDeviceType()

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header skeleton */}
      <div className="flex justify-between items-center">
        <div className="space-y-2">
          <div className="h-8 w-48 bg-muted animate-pulse rounded-md" />
          <div className="h-4 w-64 bg-muted animate-pulse rounded-md" />
        </div>
        <div className="h-10 w-32 bg-muted animate-pulse rounded-md" />
      </div>

      {/* Search and filters skeleton */}
      <div className="p-6 border rounded-lg bg-card">
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <div className="w-5 h-5 bg-muted animate-pulse rounded" />
            <div className="h-5 w-32 bg-muted animate-pulse rounded-md" />
          </div>
          
          {/* Search input */}
          <div className="h-10 w-full bg-muted animate-pulse rounded-md" />
          
          {/* Filter row */}
          <div className={`grid grid-cols-1 gap-3 ${isMobile ? '' : 'md:grid-cols-2 lg:grid-cols-5 gap-4'}`}>
            {Array.from({ length: 5 }, (_, i) => (
              <div key={i} className="h-10 bg-muted animate-pulse rounded-md" />
            ))}
          </div>
        </div>
      </div>

      {/* Results count */}
      <div className="flex justify-between items-center">
        <div className="h-4 w-32 bg-muted animate-pulse rounded-md" />
      </div>

      {/* Problem cards grid */}
      <div className={`grid grid-cols-1 ${isMobile ? 'gap-4' : 'lg:grid-cols-2 gap-6'}`}>
        {Array.from({ length: itemCount }, (_, i) => (
          <ProblemCardSkeleton key={i} animation={animation} />
        ))}
      </div>
    </div>
  )
}

interface ProblemCardSkeletonProps {
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

export function ProblemCardSkeleton({
  animation = 'pulse',
  className = ''
}: ProblemCardSkeletonProps) {
  const { isMobile } = useDeviceType()

  return (
    <div className={cn('p-6 border rounded-lg bg-card hover:shadow-md transition-shadow', className)}>
      <div className="space-y-4">
        {/* Header */}
        <div className={`${isMobile ? 'flex flex-col gap-3' : 'flex justify-between items-start gap-4'}`}>
          <div className="flex-1 min-w-0 space-y-3">
            {/* Title */}
            <div className={`h-6 ${isMobile ? 'w-full' : 'w-3/4'} bg-muted animate-${animation} rounded-md`} />
            
            {/* Description */}
            <div className="space-y-2">
              <div className={`h-4 w-full bg-muted animate-${animation} rounded-md`} />
              <div className={`h-4 w-2/3 bg-muted animate-${animation} rounded-md`} />
            </div>
          </div>
          
          {/* Status badges */}
          <div className={`flex gap-2 ${isMobile ? 'flex-row justify-start' : 'flex-col items-end'}`}>
            <div className={`h-6 w-20 bg-muted animate-${animation} rounded-full`} />
            <div className={`h-6 w-16 bg-muted animate-${animation} rounded-full`} />
          </div>
        </div>

        {/* Content */}
        <div className={`${isMobile ? 'space-y-2' : 'space-y-3'}`}>
          {/* Meta information */}
          <div className={`flex flex-wrap gap-2 ${isMobile ? 'text-xs' : 'text-sm gap-4'}`}>
            {Array.from({ length: 3 }, (_, i) => (
              <div key={i} className="flex items-center gap-1">
                <div className="w-3 h-3 bg-muted animate-pulse rounded" />
                <div className={`h-3 ${i === 0 ? 'w-16' : i === 1 ? 'w-20' : 'w-12'} bg-muted animate-${animation} rounded-md`} />
              </div>
            ))}
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-1">
            {Array.from({ length: 3 }, (_, i) => (
              <div key={i} className={`h-6 w-${12 + i * 4} bg-muted animate-${animation} rounded-full`} />
            ))}
          </div>

          {/* Submitter info */}
          <div className="flex justify-between items-center">
            <div className={`h-4 w-32 bg-muted animate-${animation} rounded-md`} />
            <div className={`h-4 w-16 bg-muted animate-${animation} rounded-md`} />
          </div>
        </div>
      </div>
    </div>
  )
}

interface ProblemDetailSkeletonProps {
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

export function ProblemDetailSkeleton({
  animation = 'pulse',
  className = ''
}: ProblemDetailSkeletonProps) {
  return (
    <BaseProblemSkeleton
      variant="detail"
      animation={animation}
      className={className}
    />
  )
}

interface ProblemListSkeletonProps {
  itemCount?: number
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

export function ProblemListSkeleton({
  itemCount = 10,
  animation = 'pulse',
  className = ''
}: ProblemListSkeletonProps) {
  return (
    <ListSkeleton
      items={itemCount}
      itemHeight="lg"
      showDividers={true}
      animation={animation}
      className={className}
    />
  )
}

// Form skeleton for problem submission
interface ProblemFormSkeletonProps {
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

export function ProblemFormSkeleton({
  animation = 'pulse',
  className = ''
}: ProblemFormSkeletonProps) {
  return (
    <div className={cn('space-y-6', className)}>
      {/* Title */}
      <div className="space-y-2">
        <div className={`h-4 w-16 bg-muted animate-${animation} rounded-md`} />
        <div className={`h-10 w-full bg-muted animate-${animation} rounded-md`} />
      </div>

      {/* Description */}
      <div className="space-y-2">
        <div className={`h-4 w-20 bg-muted animate-${animation} rounded-md`} />
        <div className={`h-32 w-full bg-muted animate-${animation} rounded-md`} />
      </div>

      {/* Category and Sector */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <div className={`h-4 w-16 bg-muted animate-${animation} rounded-md`} />
          <div className={`h-10 w-full bg-muted animate-${animation} rounded-md`} />
        </div>
        <div className="space-y-2">
          <div className={`h-4 w-14 bg-muted animate-${animation} rounded-md`} />
          <div className={`h-10 w-full bg-muted animate-${animation} rounded-md`} />
        </div>
      </div>

      {/* Urgency */}
      <div className="space-y-2">
        <div className={`h-4 w-20 bg-muted animate-${animation} rounded-md`} />
        <div className={`h-10 w-full bg-muted animate-${animation} rounded-md`} />
      </div>

      {/* Tags */}
      <div className="space-y-2">
        <div className={`h-4 w-18 bg-muted animate-${animation} rounded-md`} />
        <div className={`h-10 w-full bg-muted animate-${animation} rounded-md`} />
      </div>

      {/* File upload */}
      <div className="space-y-2">
        <div className={`h-4 w-24 bg-muted animate-${animation} rounded-md`} />
        <div className={`h-24 w-full bg-muted animate-${animation} rounded-md border-2 border-dashed`} />
      </div>

      {/* Submit button */}
      <div className="flex justify-end gap-2 pt-4 border-t">
        <div className={`h-10 w-20 bg-muted animate-${animation} rounded-md`} />
        <div className={`h-10 w-24 bg-muted animate-${animation} rounded-md`} />
      </div>
    </div>
  )
}