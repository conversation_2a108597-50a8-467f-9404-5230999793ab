import { memo, useCallback, useMemo } from 'react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useLanguage } from '@/contexts/LanguageContext';
import { useDeviceType } from '@/hooks/use-mobile';
import { useOptimizedCallback } from '@/hooks/useOptimization';
import { GlobalSearch } from '@/components/search/GlobalSearch';
import { 
  Search, 
  SortAsc, 
  SortDesc, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  X
} from 'lucide-react';

interface Filters {
  search: string;
  status: string;
  urgency: string;
  category: string;
  sector: string;
  sortBy: 'created_at' | 'updated_at' | 'title';
  sortOrder: 'asc' | 'desc';
}

interface ProblemFiltersProps {
  filters: Filters;
  onFiltersChange: (filters: Filters) => void;
  categories: string[];
  sectors: string[];
}

const getUrgencyConfig = (t: (key: string) => string) => ({
  low: { label: t('priority.low'), color: 'bg-green-100 text-green-800', icon: '🟢' },
  medium: { label: t('priority.medium'), color: 'bg-yellow-100 text-yellow-800', icon: '🟡' },
  high: { label: t('priority.high'), color: 'bg-orange-100 text-orange-800', icon: '🟠' },
  critical: { label: t('priority.critical'), color: 'bg-red-100 text-red-800', icon: '🔴' }
});

const getStatusConfig = (t: (key: string) => string) => ({
  open: { label: t('status.open'), color: 'bg-blue-100 text-blue-800', icon: <AlertCircle className="w-3 h-3" /> },
  in_progress: { label: t('status.in_progress'), color: 'bg-yellow-100 text-yellow-800', icon: <Clock className="w-3 h-3" /> },
  resolved: { label: t('status.resolved'), color: 'bg-green-100 text-green-800', icon: <CheckCircle className="w-3 h-3" /> },
  closed: { label: t('status.closed'), color: 'bg-gray-100 text-gray-800', icon: <CheckCircle className="w-3 h-3" /> }
});

export const ProblemFilters = memo<ProblemFiltersProps>(({
  filters,
  onFiltersChange,
  categories,
  sectors
}) => {
  const { t, isRTL } = useLanguage();
  const { isMobile } = useDeviceType();

  // Memoize configuration objects to prevent recreation on every render
  const URGENCY_CONFIG = useMemo(() => getUrgencyConfig(t), [t]);
  const STATUS_CONFIG = useMemo(() => getStatusConfig(t), [t]);

  // Optimized filter change handler with shallow comparison
  const handleFilterChange = useOptimizedCallback((key: keyof Filters, value: string) => {
    onFiltersChange({ ...filters, [key]: value });
  }, [filters, onFiltersChange], 'filter-change');

  // Debounced search change handler
  const handleSearchChange = useOptimizedCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFilterChange('search', e.target.value);
  }, [handleFilterChange], 'search-change');

  // Optimized status change handler
  const handleStatusChange = useOptimizedCallback((value: string) => {
    handleFilterChange('status', value);
  }, [handleFilterChange], 'status-change');

  // Optimized urgency change handler
  const handleUrgencyChange = useOptimizedCallback((value: string) => {
    handleFilterChange('urgency', value);
  }, [handleFilterChange], 'urgency-change');

  // Optimized category change handler
  const handleCategoryChange = useOptimizedCallback((value: string) => {
    handleFilterChange('category', value);
  }, [handleFilterChange], 'category-change');

  // Optimized sector change handler
  const handleSectorChange = useOptimizedCallback((value: string) => {
    handleFilterChange('sector', value);
  }, [handleFilterChange], 'sector-change');

  // Optimized sort change handler
  const handleSortChange = useOptimizedCallback((value: string) => {
    const [sortBy, sortOrder] = value.split('-') as [typeof filters.sortBy, typeof filters.sortOrder];
    onFiltersChange({ ...filters, sortBy, sortOrder });
  }, [filters, onFiltersChange], 'sort-change');

  // Optimized clear all filters handler
  const clearAllFilters = useOptimizedCallback(() => {
    onFiltersChange({
      ...filters,
      search: '',
      status: 'all',
      urgency: 'all',
      category: 'all',
      sector: 'all'
    });
  }, [filters, onFiltersChange], 'clear-filters');

  // Optimized remove filter handler
  const removeFilter = useOptimizedCallback((key: keyof Filters) => {
    const value = key === 'search' ? '' : 'all';
    handleFilterChange(key, value);
  }, [handleFilterChange], 'remove-filter');

  // Memoized active filters check
  const hasActiveFilters = useMemo(() => {
    return !!(filters.search || 
             (filters.status && filters.status !== 'all') || 
             (filters.urgency && filters.urgency !== 'all') || 
             (filters.category && filters.category !== 'all') || 
             (filters.sector && filters.sector !== 'all'));
  }, [filters.search, filters.status, filters.urgency, filters.category, filters.sector]);

  return (
    <Card>
      <CardHeader className={isMobile ? 'p-4' : ''}>
        <CardTitle className={`${isMobile ? 'text-base' : 'text-lg'} flex items-center gap-2`}>
          <Search className="w-5 h-5" />
          {t('problems.search_filter')}
        </CardTitle>
      </CardHeader>
      <CardContent className={`${isMobile ? 'space-y-3 p-4 pt-0' : 'space-y-4'}`}>
        {/* Enhanced Search Component */}
        <div className="mb-4">
          <GlobalSearch />
        </div>
        
        {/* Traditional Search Fallback */}
        <div className="relative">
          <Search className={`absolute ${isRTL ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4`} />
          <Input
            placeholder={t('problems.search_placeholder')}
            value={filters.search}
            onChange={handleSearchChange}
            className={`${isRTL ? 'pr-10' : 'pl-10'} ${isMobile ? 'text-base' : ''} touch-manipulation`}
          />
        </div>

        {/* Filter Row */}
        <div className={`grid grid-cols-1 gap-3 ${isMobile ? '' : 'md:grid-cols-2 lg:grid-cols-5 gap-4'}`}>
          <Select value={filters.status} onValueChange={handleStatusChange}>
            <SelectTrigger>
              <SelectValue placeholder={t('problems.status')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('problems.all_statuses')}</SelectItem>
              {Object.entries(STATUS_CONFIG).map(([key, config]) => (
                <SelectItem key={key} value={key}>
                  <div className="flex items-center gap-2">
                    {config.icon}
                    {config.label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filters.urgency} onValueChange={handleUrgencyChange}>
            <SelectTrigger>
              <SelectValue placeholder={t('problems.priority')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('problems.all_priorities')}</SelectItem>
              {Object.entries(URGENCY_CONFIG).map(([key, config]) => (
                <SelectItem key={key} value={key}>
                  <div className="flex items-center gap-2">
                    <span>{config.icon}</span>
                    {config.label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filters.category} onValueChange={handleCategoryChange}>
            <SelectTrigger>
              <SelectValue placeholder={t('problems.category')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('problems.all_categories')}</SelectItem>
              {categories.map(category => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={filters.sector} onValueChange={handleSectorChange}>
            <SelectTrigger>
              <SelectValue placeholder={t('problems.sector')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('problems.all_sectors')}</SelectItem>
              {sectors.map(sector => (
                <SelectItem key={sector} value={sector}>
                  {sector}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select 
            value={`${filters.sortBy}-${filters.sortOrder}`} 
            onValueChange={handleSortChange}
          >
            <SelectTrigger>
              <SelectValue placeholder={t('problems.sort')} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="created_at-desc">
                <div className="flex items-center gap-2">
                  <SortDesc className="w-4 h-4" />
                  {t('problems.newest_first')}
                </div>
              </SelectItem>
              <SelectItem value="created_at-asc">
                <div className="flex items-center gap-2">
                  <SortAsc className="w-4 h-4" />
                  {t('problems.oldest_first')}
                </div>
              </SelectItem>
              <SelectItem value="title-asc">
                <div className="flex items-center gap-2">
                  <SortAsc className="w-4 h-4" />
                  {t('problems.title_az')}
                </div>
              </SelectItem>
              <SelectItem value="title-desc">
                <div className="flex items-center gap-2">
                  <SortDesc className="w-4 h-4" />
                  {t('problems.title_za')}
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Active Filters */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2 pt-2 border-t">
            <span className="text-sm text-gray-600">{t('problems.active_filters')}:</span>
            {filters.search && (
              <Badge variant="outline" className="flex items-center gap-1">
                {t('problems.search_placeholder').split(' ')[0]}: {filters.search}
                <button onClick={() => removeFilter('search')}>
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            )}
            {filters.status && (
              <Badge variant="outline" className="flex items-center gap-1">
                {t('problems.status')}: {STATUS_CONFIG[filters.status as keyof typeof STATUS_CONFIG].label}
                <button onClick={() => removeFilter('status')}>
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            )}
            {filters.urgency && (
              <Badge variant="outline" className="flex items-center gap-1">
                {t('problems.priority')}: {URGENCY_CONFIG[filters.urgency as keyof typeof URGENCY_CONFIG].label}
                <button onClick={() => removeFilter('urgency')}>
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            )}
            {filters.category && (
              <Badge variant="outline" className="flex items-center gap-1">
                {t('problems.category')}: {filters.category}
                <button onClick={() => removeFilter('category')}>
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            )}
            {filters.sector && (
              <Badge variant="outline" className="flex items-center gap-1">
                {t('problems.sector')}: {filters.sector}
                <button onClick={() => removeFilter('sector')}>
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearAllFilters}
              className="text-red-600 hover:text-red-800"
            >
              {t('problems.clear_filters')}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
});

ProblemFilters.displayName = 'ProblemFilters';