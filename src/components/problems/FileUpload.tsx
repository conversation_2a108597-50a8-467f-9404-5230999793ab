import React, { useState, useRef, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { useDeviceType } from '@/hooks/use-mobile';
import { useCamera } from '@/hooks/useCamera';
import { 
  Upload, 
  File, 
  Image, 
  FileText, 
  X, 
  AlertCircle,
  CheckCircle,
  Loader2,
  Camera,
  RotateCcw
} from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface FileUploadProps {
  onFilesChange: (files: UploadedFile[]) => void;
  maxFiles?: number;
  maxFileSize?: number; // in MB
  acceptedTypes?: string[];
  disabled?: boolean;
  allowCamera?: boolean;
}

export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url?: string;
  uploadStatus: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
}

const DEFAULT_ACCEPTED_TYPES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'text/plain'
];

const MAX_FILE_SIZE_MB = 10;
const MAX_FILES = 5;

export function FileUpload({
  onFilesChange,
  maxFiles = MAX_FILES,
  maxFileSize = MAX_FILE_SIZE_MB,
  acceptedTypes = DEFAULT_ACCEPTED_TYPES,
  disabled = false,
  allowCamera = true
}: FileUploadProps) {
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [showCamera, setShowCamera] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const { isMobile, isTouchDevice } = useDeviceType();
  
  const {
    isSupported: cameraSupported,
    isActive: cameraActive,
    error: cameraError,
    videoRef,
    canvasRef,
    startCamera,
    stopCamera,
    capturePhoto,
    switchCamera
  } = useCamera();

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return <Image className="w-5 h-5" />;
    if (type === 'application/pdf') return <FileText className="w-5 h-5 text-red-600" />;
    if (type.includes('word')) return <FileText className="w-5 h-5 text-blue-600" />;
    if (type.includes('powerpoint') || type.includes('presentation')) 
      return <FileText className="w-5 h-5 text-orange-600" />;
    return <File className="w-5 h-5" />;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return 'نوع الملف غير مدعوم';
    }
    
    if (file.size > maxFileSize * 1024 * 1024) {
      return `حجم الملف كبير جداً (الحد الأقصى: ${maxFileSize}MB)`;
    }
    
    return null;
  };

  const uploadFileToSupabase = async (file: File): Promise<string> => {
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
    const filePath = `problem-attachments/${fileName}`;

    const { data, error } = await supabase.storage
      .from('attachments')
      .upload(filePath, file);

    if (error) {
      throw new Error(error.message);
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('attachments')
      .getPublicUrl(filePath);

    return publicUrl;
  };

  const processFiles = useCallback(async (fileList: FileList) => {
    if (disabled) return;

    const newFiles: UploadedFile[] = [];
    const filesToProcess = Array.from(fileList).slice(0, maxFiles - files.length);

    // Validate files first
    for (const file of filesToProcess) {
      const error = validateFile(file);
      const uploadedFile: UploadedFile = {
        id: `${Date.now()}-${Math.random().toString(36).substring(2)}`,
        name: file.name,
        size: file.size,
        type: file.type,
        uploadStatus: error ? 'error' : 'pending',
        error
      };
      newFiles.push(uploadedFile);
    }

    // Update state with new files
    const updatedFiles = [...files, ...newFiles];
    setFiles(updatedFiles);
    onFilesChange(updatedFiles);

    // Upload valid files
    for (const uploadedFile of newFiles) {
      if (uploadedFile.uploadStatus === 'pending') {
        // Update status to uploading
        setFiles(prev => prev.map(f => 
          f.id === uploadedFile.id 
            ? { ...f, uploadStatus: 'uploading' as const }
            : f
        ));

        try {
          const originalFile = filesToProcess.find(f => f.name === uploadedFile.name);
          if (originalFile) {
            const url = await uploadFileToSupabase(originalFile);
            
            // Update with success
            setFiles(prev => {
              const updated = prev.map(f => 
                f.id === uploadedFile.id 
                  ? { ...f, uploadStatus: 'success' as const, url }
                  : f
              );
              onFilesChange(updated);
              return updated;
            });
          }
        } catch (error) {
          // Update with error
          setFiles(prev => {
            const updated = prev.map(f => 
              f.id === uploadedFile.id 
                ? { ...f, uploadStatus: 'error' as const, error: 'فشل في رفع الملف' }
                : f
            );
            onFilesChange(updated);
            return updated;
          });
          
          toast({
            title: "خطأ في رفع الملف",
            description: `فشل في رفع الملف: ${uploadedFile.name}`,
            variant: "destructive",
          });
        }
      }
    }
  }, [files, maxFiles, acceptedTypes, maxFileSize, disabled, onFilesChange, toast]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled && !isTouchDevice) {
      setIsDragOver(true);
    }
  }, [disabled, isTouchDevice]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled || isTouchDevice) return;
    
    const droppedFiles = e.dataTransfer.files;
    if (droppedFiles.length > 0) {
      processFiles(droppedFiles);
    }
  }, [disabled, isTouchDevice, processFiles]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files;
    if (selectedFiles && selectedFiles.length > 0) {
      processFiles(selectedFiles);
    }
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [processFiles]);

  const removeFile = (fileId: string) => {
    const updatedFiles = files.filter(f => f.id !== fileId);
    setFiles(updatedFiles);
    onFilesChange(updatedFiles);
  };

  const retryUpload = async (fileId: string) => {
    const file = files.find(f => f.id === fileId);
    if (!file) return;

    // This would require keeping the original File object
    // For now, we'll just show a message
    toast({
      title: "إعادة المحاولة",
      description: "يرجى حذف الملف وإعادة رفعه",
    });
  };

  const handleCameraCapture = async () => {
    try {
      const photoBlob = await capturePhoto();
      if (photoBlob) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const file = new File([photoBlob], `photo-${timestamp}.jpg`, {
          type: 'image/jpeg'
        });
        
        const fileList = new DataTransfer();
        fileList.items.add(file);
        await processFiles(fileList.files);
        
        setShowCamera(false);
        stopCamera();
        
        toast({
          title: "تم التقاط الصورة",
          description: "تم إضافة الصورة إلى المرفقات بنجاح",
        });
      }
    } catch (error) {
      console.error('Error capturing photo:', error);
      toast({
        title: "خطأ في التقاط الصورة",
        description: "حدث خطأ أثناء التقاط الصورة",
        variant: "destructive",
      });
    }
  };

  const openCamera = async () => {
    setShowCamera(true);
    const success = await startCamera();
    if (!success) {
      setShowCamera(false);
    }
  };

  const closeCamera = () => {
    setShowCamera(false);
    stopCamera();
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <Card 
        className={`border-2 border-dashed transition-colors ${
          isDragOver 
            ? 'border-blue-500 bg-blue-50' 
            : disabled 
              ? 'border-gray-200 bg-gray-50' 
              : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <CardContent className={`text-center ${isMobile ? 'p-4' : 'p-8'}`}>
          <Upload className={`mx-auto mb-4 ${
            isMobile ? 'w-8 h-8' : 'w-12 h-12'
          } ${disabled ? 'text-gray-400' : 'text-gray-500'}`} />
          
          <div className="space-y-2">
            <p className={`font-medium ${
              isMobile ? 'text-base' : 'text-lg'
            } ${disabled ? 'text-gray-400' : 'text-gray-700'}`}>
              {isTouchDevice ? 'انقر لاختيار الملفات' : 'اسحب الملفات هنا أو انقر للتحديد'}
            </p>
            <p className={`text-gray-500 ${isMobile ? 'text-xs' : 'text-sm'}`}>
              الأنواع المدعومة: PDF, DOC, PPT, صور (حتى {maxFileSize}MB لكل ملف)
            </p>
            <p className={`text-gray-500 ${isMobile ? 'text-xs' : 'text-sm'}`}>
              الحد الأقصى: {maxFiles} ملفات
            </p>
          </div>

          <div className={`${isMobile ? 'space-y-2 mt-4' : 'flex gap-2 justify-center mt-4'}`}>
            <Button 
              type="button" 
              variant="outline" 
              onClick={() => fileInputRef.current?.click()}
              disabled={disabled || files.length >= maxFiles}
              className={isMobile ? 'w-full' : ''}
            >
              <Upload className="w-4 h-4 ml-2" />
              اختر الملفات
            </Button>

            {/* Camera Button for Mobile */}
            {isMobile && allowCamera && cameraSupported && (
              <Button
                type="button"
                variant="outline"
                onClick={openCamera}
                disabled={disabled || files.length >= maxFiles}
                className="w-full"
              >
                <Camera className="w-4 h-4 ml-2" />
                التقاط صورة
              </Button>
            )}
          </div>

          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={acceptedTypes.join(',')}
            onChange={handleFileSelect}
            className="hidden"
            disabled={disabled}
            {...(isMobile && { capture: "environment" })}
          />
        </CardContent>
      </Card>

      {/* Desktop Camera Button */}
      {!isMobile && allowCamera && cameraSupported && (
        <div className="flex justify-center">
          <Button
            type="button"
            variant="outline"
            onClick={openCamera}
            disabled={disabled || files.length >= maxFiles}
          >
            <Camera className="w-4 h-4 ml-2" />
            التقاط صورة بالكاميرا
          </Button>
        </div>
      )}

      {/* Camera Dialog */}
      <Dialog open={showCamera} onOpenChange={closeCamera}>
        <DialogContent className={`${isMobile ? 'w-full h-full max-w-none max-h-none m-0 rounded-none' : 'max-w-2xl'}`}>
          <DialogHeader>
            <DialogTitle>التقاط صورة</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            {cameraError && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center">
                  <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
                  <p className="text-red-700 text-sm">{cameraError}</p>
                </div>
              </div>
            )}
            
            <div className="relative bg-black rounded-lg overflow-hidden">
              <video
                ref={videoRef}
                autoPlay
                playsInline
                muted
                className="w-full h-auto max-h-96 object-cover"
              />
              <canvas ref={canvasRef} className="hidden" />
            </div>
            
            <div className={`flex justify-center ${isMobile ? 'flex-col space-y-2' : 'space-x-4 space-x-reverse'}`}>
              <Button
                onClick={handleCameraCapture}
                disabled={!cameraActive}
                className={`bg-blue-600 hover:bg-blue-700 ${isMobile ? 'w-full' : ''}`}
              >
                <Camera className="w-4 h-4 ml-2" />
                التقاط الصورة
              </Button>
              
              <Button
                variant="outline"
                onClick={switchCamera}
                disabled={!cameraActive}
                className={isMobile ? 'w-full' : ''}
              >
                <RotateCcw className="w-4 h-4 ml-2" />
                تبديل الكاميرا
              </Button>
              
              <Button
                variant="outline"
                onClick={closeCamera}
                className={isMobile ? 'w-full' : ''}
              >
                إلغاء
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-2">
          <h4 className={`font-medium text-gray-900 ${isMobile ? 'text-sm' : ''}`}>
            الملفات المرفقة ({files.length})
          </h4>
          <div className="space-y-2">
            {files.map((file) => (
              <Card key={file.id} className={isMobile ? 'p-2' : 'p-3'}>
                <div className={`flex items-center ${isMobile ? 'flex-col space-y-2' : 'justify-between'}`}>
                  <div className={`flex items-center space-x-3 space-x-reverse flex-1 min-w-0 ${isMobile ? 'w-full' : ''}`}>
                    {getFileIcon(file.type)}
                    <div className="flex-1 min-w-0">
                      <p className={`font-medium text-gray-900 truncate ${isMobile ? 'text-xs' : 'text-sm'}`}>
                        {file.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(file.size)}
                      </p>
                    </div>
                  </div>

                  <div className={`flex items-center space-x-2 space-x-reverse ${isMobile ? 'w-full justify-between' : ''}`}>
                    {/* Status Badge */}
                    {file.uploadStatus === 'uploading' && (
                      <Badge variant="secondary" className={`flex items-center gap-1 ${isMobile ? 'text-xs px-1 py-0' : ''}`}>
                        <Loader2 className="w-3 h-3 animate-spin" />
                        {isMobile ? 'رفع...' : 'جاري الرفع...'}
                      </Badge>
                    )}
                    {file.uploadStatus === 'success' && (
                      <Badge variant="default" className={`flex items-center gap-1 bg-green-100 text-green-800 ${isMobile ? 'text-xs px-1 py-0' : ''}`}>
                        <CheckCircle className="w-3 h-3" />
                        تم الرفع
                      </Badge>
                    )}
                    {file.uploadStatus === 'error' && (
                      <Badge variant="destructive" className={`flex items-center gap-1 ${isMobile ? 'text-xs px-1 py-0' : ''}`}>
                        <AlertCircle className="w-3 h-3" />
                        خطأ
                      </Badge>
                    )}

                    <div className={`flex items-center space-x-1 space-x-reverse ${isMobile ? '' : 'space-x-2'}`}>
                      {/* Actions */}
                      {file.uploadStatus === 'error' && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => retryUpload(file.id)}
                          className={`text-blue-600 hover:text-blue-800 ${isMobile ? 'text-xs px-2 py-1 h-auto' : ''}`}
                        >
                          {isMobile ? 'إعادة' : 'إعادة المحاولة'}
                        </Button>
                      )}

                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(file.id)}
                        className={`text-red-600 hover:text-red-800 ${isMobile ? 'p-1 h-auto' : ''}`}
                      >
                        <X className={`${isMobile ? 'w-3 h-3' : 'w-4 h-4'}`} />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Error Message */}
                {file.error && (
                  <div className={`mt-2 text-red-600 flex items-center gap-1 ${isMobile ? 'text-xs' : 'text-sm'}`}>
                    <AlertCircle className="w-4 h-4" />
                    {file.error}
                  </div>
                )}
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}