import { useState, useEffect, useMemo, useCallback, memo } from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useAuthContext } from '@/components/auth/AuthProvider';
import { useLanguage } from '@/contexts/LanguageContext';
import { searchService } from '@/lib/search';
import { SearchFilters as SearchFiltersType } from '@/lib/search';
import { ProblemFilters } from './ProblemFilters';
import { ProblemList } from './ProblemList';
import { Problem } from '@/types';
import { Plus } from 'lucide-react';
import {
  useOptimizedCallback,
  useMemoizedValue,
  useExpensiveComputation,
  useSearchMemo,
  useRenderPerformance
} from '@/hooks/useOptimization';
import { useAriaAnnouncements, useTouchTargets, useKeyboardShortcuts } from '@/hooks/useAccessibility';
import { DataErrorBoundary } from '@/utils/errorBoundaryHelpers';

interface Filters {
  search: string;
  status: string;
  urgency: string;
  category: string;
  sector: string;
  sortBy: 'created_at' | 'updated_at' | 'title';
  sortOrder: 'asc' | 'desc';
}

const ProblemDashboardComponent = memo(function ProblemDashboard() {
  const [problems, setProblems] = useState<Problem[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<Filters>({
    search: '',
    status: 'all',
    urgency: 'all',
    category: 'all',
    sector: 'all',
    sortBy: 'created_at',
    sortOrder: 'desc'
  });

  const { toast } = useToast();
  const { user } = useAuthContext();
  const { t, isRTL } = useLanguage();

  // Accessibility hooks
  const { announce } = useAriaAnnouncements();
  const { validateTouchTargets } = useTouchTargets();

  // Performance monitoring
  const renderPerf = useRenderPerformance('ProblemDashboard');

  // Keyboard shortcuts
  useKeyboardShortcuts({
    'ctrl+k': () => {
      const searchInput = document.querySelector('input[type="search"]') as HTMLInputElement;
      if (searchInput) {
        searchInput.focus();
        announce(t('accessibility.search_focused'), 'polite');
      }
    },
    'ctrl+n': () => {
      if (user) {
        window.location.href = '/problems/new';
        announce(t('accessibility.navigating_new_problem'), 'polite');
      }
    },
    'escape': () => {
      // Clear search and filters
      setFilters(prev => ({
        ...prev,
        search: '',
        status: 'all',
        urgency: 'all',
        category: 'all',
        sector: 'all'
      }));
      announce(t('accessibility.filters_cleared'), 'polite');
    }
  });

  // Optimized filter options with performance monitoring
  const categories = useMemoizedValue(() =>
    [...new Set(problems.map(p => p.category))].filter(Boolean),
    [problems],
    'problem-categories'
  );

  const sectors = useMemoizedValue(() =>
    [...new Set(problems.map(p => p.sector))].filter(Boolean),
    [problems],
    'problem-sectors'
  );

  const fetchProblems = useOptimizedCallback(async () => {
    try {
      setLoading(true);
      
      // Try to use search service for enhanced filtering
      if (filters.search.trim()) {
        try {
          const searchFilters: SearchFiltersType = {
            contentType: ['problem'],
            ...(filters.category && { categories: [filters.category] }),
            ...(filters.sector && { sectors: [filters.sector] }),
            ...(filters.status && { status: [filters.status] }),
            ...(filters.urgency && { urgency: [filters.urgency] })
          };

          const searchResults = await searchService.search({
            text: filters.search,
            filters: searchFilters,
            sortBy: filters.sortBy === 'created_at' ? 'date' : 
                    filters.sortBy === 'updated_at' ? 'date' : 'relevance',
            pagination: { limit: 50, offset: 0 },
            language: 'auto'
          });

          // Convert search results to Problem format
          const searchProblems = searchResults.items
            .filter(item => item.type === 'problem')
            .map(item => ({
              id: item.id,
              title: item.title,
              description: item.description,
              category: item.metadata?.category || 'غير محدد',
              sector: item.metadata?.sector || 'غير محدد',
              urgency: item.metadata?.urgency || 'medium',
              status: item.metadata?.status || 'open',
              submitted_by: item.metadata?.submitted_by || 'user',
              tags: item.tags || [],
              attachments: item.metadata?.attachments || [],
              created_at: item.metadata?.created_at || new Date().toISOString(),
              updated_at: item.metadata?.updated_at || new Date().toISOString(),
              users: {
                name: item.metadata?.submitter_name || 'مستخدم',
                organization: item.metadata?.organization
              },
              solutions: item.metadata?.solutions || []
            }));

          if (searchProblems.length > 0) {
            setProblems(searchProblems);
            setLoading(false);
            return;
          }
        } catch (searchError) {
          console.warn('Search service error, falling back to mock data:', searchError);
          // Show user-friendly error message
          toast({
            title: t('errors.search_failed'),
            description: t('errors.using_fallback_data'),
            variant: 'default',
          });
        }
      }
      
      // Fallback to mock data
      const mockProblems: Problem[] = [
        {
          id: '1',
          title: 'تطوير نظام إدارة المستشفيات الرقمي',
          description: 'نحتاج إلى نظام شامل لإدارة المستشفيات يتضمن إدارة المرضى، الحجوزات، والمخزون الطبي',
          category: 'تطوير البرمجيات',
          sector: 'وزارة الصحة',
          urgency: 'high',
          status: 'open',
          submitted_by: 'user1',
          tags: ['نظم المعلومات الطبية', 'إدارة المستشفيات', 'قواعد البيانات'],
          attachments: [],
          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          users: {
            name: 'د. أحمد الخطيب',
            organization: 'وزارة الصحة'
          },
          solutions: []
        },
        {
          id: '2',
          title: 'أتمتة عمليات التخليص الجمركي',
          description: 'نواجه تحديات في أتمتة عمليات التخليص الجمركي وربطها بالأنظمة المصرفية',
          category: 'أتمتة العمليات',
          sector: 'وزارة المالية',
          urgency: 'medium',
          status: 'in_progress',
          submitted_by: 'user2',
          tags: ['أتمتة', 'جمارك', 'تكامل الأنظمة'],
          attachments: [{ name: 'requirements.pdf', size: '2MB' }],
          created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          users: {
            name: 'م. فاطمة السيد',
            organization: 'وزارة المالية'
          },
          solutions: [{ id: '1' }, { id: '2' }]
        },
        {
          id: '3',
          title: 'منصة التعليم الإلكتروني التفاعلي',
          description: 'تطوير منصة تعليم إلكتروني تفاعلية تدعم الفصول الافتراضية والتقييم الإلكتروني',
          category: 'تطوير المنصات',
          sector: 'وزارة التربية',
          urgency: 'critical',
          status: 'open',
          submitted_by: 'user3',
          tags: ['تعليم إلكتروني', 'فصول افتراضية', 'تقييم'],
          attachments: [],
          created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          users: {
            name: 'م. محمد العلي',
            organization: 'وزارة التربية'
          },
          solutions: []
        },
        {
          id: '4',
          title: 'نظام إدارة الموارد البشرية',
          description: 'نحتاج إلى نظام متكامل لإدارة الموارد البشرية يشمل الرواتب والإجازات والتقييم',
          category: 'أنظمة إدارية',
          sector: 'وزارة الإدارة المحلية',
          urgency: 'low',
          status: 'resolved',
          submitted_by: 'user4',
          tags: ['موارد بشرية', 'رواتب', 'إدارة'],
          attachments: [],
          created_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          users: {
            name: 'د. سارة أحمد',
            organization: 'وزارة الإدارة المحلية'
          },
          solutions: [{ id: '1' }]
        }
      ];
      
      setProblems(mockProblems);
      setLoading(false);
    } catch (error) {
      console.error('Critical error in fetchProblems:', error);
      // Set empty problems array and show error
      setProblems([]);
      setLoading(false);
      toast({
        title: t('errors.critical_error'),
        description: t('errors.contact_support'),
        variant: 'destructive',
      });
    }
  }, [filters, t], 'fetch-problems');

  useEffect(() => {
    fetchProblems();
  }, [fetchProblems]);

  // Optimized search and filter operations with performance monitoring
  const filteredProblems = useSearchMemo(
    problems,
    filters.search,
    (items, searchTerm) => {
      return items
        .filter(problem => {
          // Search filter
          if (searchTerm) {
            const searchLower = searchTerm.toLowerCase();
            const matchesSearch = (
              problem.title.toLowerCase().includes(searchLower) ||
              problem.description.toLowerCase().includes(searchLower) ||
              problem.tags.some(tag => tag.toLowerCase().includes(searchLower))
            );
            if (!matchesSearch) return false;
          }

          // Status filter
          if (filters.status && filters.status !== 'all' && problem.status !== filters.status) {
            return false;
          }

          // Urgency filter
          if (filters.urgency && filters.urgency !== 'all' && problem.urgency !== filters.urgency) {
            return false;
          }

          // Category filter
          if (filters.category && filters.category !== 'all' && problem.category !== filters.category) {
            return false;
          }

          // Sector filter
          if (filters.sector && filters.sector !== 'all' && problem.sector !== filters.sector) {
            return false;
          }

          return true;
        })
        .sort((a, b) => {
          const aValue = a[filters.sortBy];
          const bValue = b[filters.sortBy];

          if (filters.sortOrder === 'asc') {
            return aValue > bValue ? 1 : -1;
          } else {
            return aValue < bValue ? 1 : -1;
          }
        });
    },
    {
      debounceMs: 300,
      minSearchLength: 0,
      debugKey: 'problem-search'
    }
  );

  // Optimized callback for handling filter changes
  const handleFiltersChange = useOptimizedCallback((newFilters: Filters) => {
    setFilters(newFilters);
  }, [], 'handle-filters-change');

  // Check if any filters are active with memoization
  const hasActiveFilters = useMemoizedValue(() => {
    return !!(filters.search || filters.status || filters.urgency || filters.category || filters.sector);
  }, [filters.search, filters.status, filters.urgency, filters.category, filters.sector], 'has-active-filters');

  return (
    <main
      className="space-y-6"
      role="main"
      aria-labelledby="problems-title"
      aria-describedby="problems-subtitle"
    >
      {/* Skip to content link */}
      <a
        href="#problems-content"
        className="skip-to-content sr-only focus:not-sr-only"
        aria-label={t('accessibility.skip_to_content')}
      >
        {t('accessibility.skip_to_content')}
      </a>

      {/* Header */}
      <header className="flex justify-between items-center">
        <div>
          <h1
            id="problems-title"
            className="text-3xl font-bold text-gray-900"
          >
            {t('problems.title')}
          </h1>
          <p
            id="problems-subtitle"
            className="text-gray-600 mt-1"
          >
            {t('problems.subtitle')}
          </p>
        </div>
        {user && (
          <Button
            asChild
            aria-label={t('problems.add_problem')}
            title={t('accessibility.keyboard_shortcut_new', { shortcut: 'Ctrl+N' })}
          >
            <Link to="/problems/new">
              <Plus
                className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`}
                aria-hidden="true"
              />
              {t('problems.add_problem')}
            </Link>
          </Button>
        )}
      </header>

      {/* Filters */}
      <section
        aria-labelledby="filters-title"
        aria-describedby="filters-description"
      >
        <h2 id="filters-title" className="sr-only">
          {t('problems.search_filter')}
        </h2>
        <p id="filters-description" className="sr-only">
          {t('accessibility.filters_description')}
        </p>
        <ProblemFilters
          filters={filters}
          onFiltersChange={handleFiltersChange}
          categories={categories}
          sectors={sectors}
        />
      </section>

      {/* Results */}
      <section
        id="problems-content"
        aria-labelledby="results-title"
        aria-describedby="results-description"
        aria-live="polite"
        aria-busy={loading}
      >
        <h2 id="results-title" className="sr-only">
          {t('problems.results')}
        </h2>
        <p id="results-description" className="sr-only">
          {loading
            ? t('accessibility.loading_problems')
            : t('accessibility.problems_count', { count: filteredProblems.length })
          }
        </p>
        <ProblemList
          problems={filteredProblems}
          loading={loading}
          hasFilters={hasActiveFilters}
        />
      </section>
    </main>
  );
});

// Export wrapped component with error boundary
export const ProblemDashboard = () => (
  <DataErrorBoundary dataType="Problems">
    <ProblemDashboardComponent />
  </DataErrorBoundary>
);