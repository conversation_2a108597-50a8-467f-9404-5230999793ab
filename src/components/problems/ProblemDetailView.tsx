import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { problemOperations, solutionOperations } from '@/lib/database';
import { useAuthContext, useRoleAccess } from '@/components/auth/AuthProvider';
import { 
  ArrowRight,
  Calendar,
  Tag,
  Users,
  FileText,
  Download,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  Send,
  AlertCircle,
  CheckCircle,
  Clock,
  Loader2,
  Edit,
  Trash2
} from 'lucide-react';

interface Problem {
  id: string;
  title: string;
  description: string;
  category: string;
  sector: string;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  submitted_by: string;
  tags: string[];
  attachments: any[];
  created_at: string;
  updated_at: string;
  resolved_at?: string;
  users?: {
    name: string;
    organization?: string;
    avatar?: string;
  };
  solutions?: Solution[];
}

interface Solution {
  id: string;
  problem_id: string;
  expert_id: string;
  content: string;
  attachments: any[];
  status: 'draft' | 'submitted' | 'approved' | 'implemented';
  votes: any[];
  rating: number;
  implementation_notes?: string;
  created_at: string;
  updated_at: string;
  users?: {
    name: string;
    avatar?: string;
    organization?: string;
  };
}

const URGENCY_CONFIG = {
  low: { label: 'منخفضة', color: 'bg-green-100 text-green-800', icon: '🟢' },
  medium: { label: 'متوسطة', color: 'bg-yellow-100 text-yellow-800', icon: '🟡' },
  high: { label: 'عالية', color: 'bg-orange-100 text-orange-800', icon: '🟠' },
  critical: { label: 'حرجة', color: 'bg-red-100 text-red-800', icon: '🔴' }
};

const STATUS_CONFIG = {
  open: { label: 'مفتوحة', color: 'bg-blue-100 text-blue-800', icon: <AlertCircle className="w-3 h-3" /> },
  in_progress: { label: 'قيد المعالجة', color: 'bg-yellow-100 text-yellow-800', icon: <Clock className="w-3 h-3" /> },
  resolved: { label: 'محلولة', color: 'bg-green-100 text-green-800', icon: <CheckCircle className="w-3 h-3" /> },
  closed: { label: 'مغلقة', color: 'bg-gray-100 text-gray-800', icon: <CheckCircle className="w-3 h-3" /> }
};

const SOLUTION_STATUS_CONFIG = {
  draft: { label: 'مسودة', color: 'bg-gray-100 text-gray-800' },
  submitted: { label: 'مرسل', color: 'bg-blue-100 text-blue-800' },
  approved: { label: 'معتمد', color: 'bg-green-100 text-green-800' },
  implemented: { label: 'مطبق', color: 'bg-purple-100 text-purple-800' }
};

interface ProblemDetailViewProps {
  problem: Problem;
}

export function ProblemDetailView({ problem }: ProblemDetailViewProps) {
  const [submittingSolution, setSubmittingSolution] = useState(false);
  const [newSolution, setNewSolution] = useState('');
  
  const { toast } = useToast();
  const { user } = useAuthContext();
  const { canCreateSolutions } = useRoleAccess();

  const fetchProblem = async () => {
    // This function is now handled by the parent component
    // We'll keep it for solution refresh after submission
  };

  const handleSubmitSolution = async () => {
    if (!user || !problem || !newSolution.trim()) return;

    setSubmittingSolution(true);
    try {
      const { data, error } = await solutionOperations.createSolution({
        problem_id: problem.id,
        expert_id: user.id,
        content: newSolution.trim(),
        status: 'submitted'
      });

      if (error) {
        throw error;
      }

      toast({
        title: "تم إرسال الحل بنجاح",
        description: "شكراً لك على مساهمتك! سيتم مراجعة الحل قريباً",
      });

      setNewSolution('');
      fetchProblem(); // Refresh to show new solution
    } catch (error) {
      console.error('Error submitting solution:', error);
      toast({
        title: "خطأ في الإرسال",
        description: "حدث خطأ أثناء إرسال الحل. يرجى المحاولة مرة أخرى",
        variant: "destructive",
      });
    } finally {
      setSubmittingSolution(false);
    }
  };

  const handleVote = async (solutionId: string, voteType: 'up' | 'down') => {
    if (!user) {
      toast({
        title: "يجب تسجيل الدخول",
        description: "يجب تسجيل الدخول للتصويت على الحلول",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await solutionOperations.voteSolution(solutionId, user.id, voteType);
      
      if (error) {
        throw error;
      }

      fetchProblem(); // Refresh to show updated votes
    } catch (error) {
      console.error('Error voting:', error);
      toast({
        title: "خطأ في التصويت",
        description: "حدث خطأ أثناء التصويت",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getVoteCount = (votes: any[], type: 'up' | 'down') => {
    return votes?.filter(vote => vote.type === type).length || 0;
  };

  const hasUserVoted = (votes: any[], userId: string) => {
    return votes?.find(vote => vote.userId === userId);
  };



  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6" dir="rtl">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
        <Link to="/problems" className="hover:text-blue-600">المشاكل</Link>
        <ArrowRight className="w-4 h-4" />
        <span className="text-gray-900">{problem.title}</span>
      </nav>

      {/* Problem Header */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start gap-4">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-3">
                <Badge className={STATUS_CONFIG[problem.status].color}>
                  {STATUS_CONFIG[problem.status].icon}
                  {STATUS_CONFIG[problem.status].label}
                </Badge>
                <Badge variant="outline" className={URGENCY_CONFIG[problem.urgency].color}>
                  {URGENCY_CONFIG[problem.urgency].icon} {URGENCY_CONFIG[problem.urgency].label}
                </Badge>
              </div>
              
              <CardTitle className="text-2xl mb-3">{problem.title}</CardTitle>
              
              <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-4">
                <div className="flex items-center gap-1">
                  <Tag className="w-4 h-4" />
                  {problem.category}
                </div>
                <div className="flex items-center gap-1">
                  <Users className="w-4 h-4" />
                  {problem.sector}
                </div>
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {formatDate(problem.created_at)}
                </div>
                {problem.attachments?.length > 0 && (
                  <div className="flex items-center gap-1">
                    <FileText className="w-4 h-4" />
                    {problem.attachments.length} مرفق
                  </div>
                )}
              </div>

              {/* Submitter Info */}
              <div className="flex items-center gap-3">
                <Avatar className="w-8 h-8">
                  <AvatarImage src={problem.users?.avatar} />
                  <AvatarFallback>{problem.users?.name?.charAt(0) || 'م'}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium text-sm">{problem.users?.name || 'مستخدم'}</p>
                  {problem.users?.organization && (
                    <p className="text-xs text-gray-600">{problem.users.organization}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-2">
              {user?.id === problem.submitted_by && (
                <>
                  <Button variant="outline" size="sm">
                    <Edit className="w-4 h-4 ml-2" />
                    تعديل
                  </Button>
                  <Button variant="outline" size="sm" className="text-red-600 hover:text-red-800">
                    <Trash2 className="w-4 h-4 ml-2" />
                    حذف
                  </Button>
                </>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Description */}
          <div className="prose prose-sm max-w-none mb-6">
            <p className="whitespace-pre-wrap text-gray-700 leading-relaxed">
              {problem.description}
            </p>
          </div>

          {/* Tags */}
          {problem.tags.length > 0 && (
            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-2">الكلمات المفتاحية:</h4>
              <div className="flex flex-wrap gap-2">
                {problem.tags.map((tag, index) => (
                  <Badge key={index} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Attachments */}
          {problem.attachments?.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-3">المرفقات:</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {problem.attachments.map((attachment, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50">
                    <FileText className="w-5 h-5 text-gray-500" />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm truncate">{attachment.name}</p>
                      <p className="text-xs text-gray-500">{attachment.size}</p>
                    </div>
                    <Button variant="ghost" size="sm">
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Solutions Section */}
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-900">
            الحلول المقترحة ({problem.solutions?.length || 0})
          </h2>
        </div>

        {/* Solution Submission Form */}
        {canCreateSolutions && problem.status !== 'closed' && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">اقترح حلاً</CardTitle>
              <CardDescription>
                شارك خبرتك وساعد في حل هذه المشكلة التقنية
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Textarea
                  placeholder="اشرح الحل المقترح بالتفصيل، مع ذكر الخطوات والأدوات المطلوبة..."
                  rows={6}
                  value={newSolution}
                  onChange={(e) => setNewSolution(e.target.value)}
                />
                <div className="flex justify-between items-center">
                  <p className="text-sm text-gray-500">
                    {newSolution.length} حرف (الحد الأدنى: 50 حرف)
                  </p>
                  <Button 
                    onClick={handleSubmitSolution}
                    disabled={submittingSolution || newSolution.length < 50}
                  >
                    {submittingSolution ? (
                      <>
                        <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                        جاري الإرسال...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 ml-2" />
                        إرسال الحل
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Solutions List */}
        {problem.solutions && problem.solutions.length > 0 ? (
          <div className="space-y-4">
            {problem.solutions.map((solution, index) => (
              <Card key={solution.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex items-center gap-3">
                      <Avatar className="w-10 h-10">
                        <AvatarImage src={solution.users?.avatar} />
                        <AvatarFallback>{solution.users?.name?.charAt(0) || 'خ'}</AvatarFallback>
                      </Avatar>
                      <div>
                        <Link 
                          to={`/experts/${solution.expert_id}`}
                          className="font-medium text-blue-600 hover:text-blue-800"
                        >
                          {solution.users?.name || 'خبير'}
                        </Link>
                        {solution.users?.organization && (
                          <p className="text-sm text-gray-600">{solution.users.organization}</p>
                        )}
                        <p className="text-xs text-gray-500">{formatDate(solution.created_at)}</p>
                      </div>
                    </div>
                    
                    <Badge className={SOLUTION_STATUS_CONFIG[solution.status].color}>
                      {SOLUTION_STATUS_CONFIG[solution.status].label}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent>
                  <div className="prose prose-sm max-w-none mb-4">
                    <p className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                      {solution.content}
                    </p>
                  </div>

                  {/* Solution Attachments */}
                  {solution.attachments?.length > 0 && (
                    <div className="mb-4">
                      <h5 className="font-medium text-gray-900 mb-2">المرفقات:</h5>
                      <div className="flex flex-wrap gap-2">
                        {solution.attachments.map((attachment, idx) => (
                          <div key={idx} className="flex items-center gap-2 p-2 border rounded">
                            <FileText className="w-4 h-4 text-gray-500" />
                            <span className="text-sm">{attachment.name}</span>
                            <Button variant="ghost" size="sm">
                              <Download className="w-3 h-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Voting */}
                  <div className="flex items-center gap-4 pt-4 border-t">
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleVote(solution.id, 'up')}
                        className={`${
                          hasUserVoted(solution.votes, user?.id || '')?.type === 'up'
                            ? 'text-green-600 bg-green-50'
                            : 'text-gray-600 hover:text-green-600'
                        }`}
                      >
                        <ThumbsUp className="w-4 h-4 ml-1" />
                        {getVoteCount(solution.votes, 'up')}
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleVote(solution.id, 'down')}
                        className={`${
                          hasUserVoted(solution.votes, user?.id || '')?.type === 'down'
                            ? 'text-red-600 bg-red-50'
                            : 'text-gray-600 hover:text-red-600'
                        }`}
                      >
                        <ThumbsDown className="w-4 h-4 ml-1" />
                        {getVoteCount(solution.votes, 'down')}
                      </Button>
                    </div>

                    <div className="flex items-center gap-1 text-sm text-gray-600">
                      <MessageSquare className="w-4 h-4" />
                      رد
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="text-center py-12">
              <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد حلول بعد</h3>
              <p className="text-gray-600 mb-6">
                كن أول من يقترح حلاً لهذه المشكلة التقنية
              </p>
              {!canCreateSolutions && (
                <p className="text-sm text-gray-500">
                  يجب تسجيل الدخول كخبير لاقتراح الحلول
                </p>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}