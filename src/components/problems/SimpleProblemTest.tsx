import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function SimpleProblemTest() {
  const mockProblems = [
    {
      id: '1',
      title: 'تطوير نظام إدارة المستشفيات الرقمي',
      description: 'نحتاج إلى نظام شامل لإدارة المستشفيات',
      category: 'تطوير البرمجيات',
      sector: 'وزارة الصحة'
    },
    {
      id: '2',
      title: 'أتمتة عمليات التخليص الجمركي',
      description: 'نواجه تحديات في أتمتة عمليات التخليص الجمركي',
      category: 'أتمتة العمليات',
      sector: 'وزارة المالية'
    }
  ];

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">المشاكل التقنية</h1>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {mockProblems.map(problem => (
          <Card key={problem.id}>
            <CardHeader>
              <CardTitle>{problem.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-2">{problem.description}</p>
              <div className="text-sm text-gray-500">
                <span>{problem.category} - {problem.sector}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}