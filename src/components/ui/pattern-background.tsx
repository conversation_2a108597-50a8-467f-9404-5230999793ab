/**
 * Syrian Pattern Background Component
 * 
 * Performance-optimized component for displaying Syrian cultural patterns.
 * Implements automatic responsive degradation and accessibility compliance.
 * 
 * Features:
 * - Automatic pattern selection based on screen size
 * - Performance monitoring and paint time optimization
 * - WCAG AA accessibility compliance
 * - Respects user motion preferences
 * - Zero impact on layout and interactivity
 * - Hardware acceleration when beneficial
 */

import React, { forwardRef, useEffect, useState, useRef } from 'react';
import { cn } from '@/lib/utils';
import { 
  getSyrianPattern, 
  type SyrianPatternName, 
  type SyrianPatternConfig 
} from '@/lib/patterns/syrian-patterns';
import { getDecorativeProps } from '@/lib/a11y/decorative-helpers';

export interface PatternBackgroundProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Pattern to display */
  pattern: SyrianPatternName;
  
  /** Pattern intensity */
  intensity?: 'subtle' | 'moderate' | 'rich';
  
  /** Enable pattern animation */
  animated?: boolean;
  
  /** Make pattern interactive (hover effects) */
  interactive?: boolean;
  
  /** Custom pattern size override */
  patternSize?: string;
  
  /** Custom opacity override */
  opacity?: number;
  
  /** Enable performance monitoring */
  performanceMonitoring?: boolean;
  
  /** Force GPU acceleration */
  forceGPUAcceleration?: boolean;
  
  /** Disable responsive degradation */
  disableResponsive?: boolean;
  
  /** Layer multiple patterns */
  layeredPattern?: SyrianPatternName;
}

/**
 * Hook for performance monitoring
 */
function usePatternPerformance(enabled: boolean, patternName: string) {
  const [paintTime, setPaintTime] = useState<number>(0);
  const [isOptimal, setIsOptimal] = useState<boolean>(true);
  const observerRef = useRef<PerformanceObserver | null>(null);
  
  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return;
    
    // Monitor paint performance
    try {
      observerRef.current = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const paintEntries = entries.filter(entry => 
          entry.entryType === 'paint' || entry.entryType === 'measure'
        );
        
        if (paintEntries.length > 0) {
          const latestPaint = paintEntries[paintEntries.length - 1];
          const duration = latestPaint.duration || 0;
          setPaintTime(duration);
          setIsOptimal(duration <= 16); // 60fps budget
        }
      });
      
      observerRef.current.observe({ entryTypes: ['paint', 'measure'] });
    } catch (error) {
      console.warn('Performance monitoring not supported:', error);
    }
    
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [enabled, patternName]);
  
  return { paintTime, isOptimal };
}

/**
 * Hook for responsive pattern selection
 */
function useResponsivePattern(
  pattern: SyrianPatternName, 
  disableResponsive: boolean = false
): { 
  shouldRender: boolean; 
  adjustedOpacity: number; 
  adjustedSize: string;
} {
  const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  
  useEffect(() => {
    if (disableResponsive || typeof window === 'undefined') return;
    
    const updateScreenSize = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setScreenSize('mobile');
      } else if (width < 1024) {
        setScreenSize('tablet');
      } else {
        setScreenSize('desktop');
      }
    };
    
    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
    
    return () => window.removeEventListener('resize', updateScreenSize);
  }, [disableResponsive]);
  
  const patternConfig = getSyrianPattern(pattern);
  const responsive = patternConfig.responsive;
  
  // Determine if pattern should render
  const shouldRender = disableResponsive || (
    screenSize === 'desktop' || 
    (screenSize === 'tablet' && responsive.tabletOpacity > 0) ||
    (screenSize === 'mobile' && responsive.mobileOpacity > 0)
  );
  
  // Get adjusted opacity
  const adjustedOpacity = disableResponsive ? responsive.desktopOpacity : (
    screenSize === 'mobile' ? responsive.mobileOpacity :
    screenSize === 'tablet' ? responsive.tabletOpacity :
    responsive.desktopOpacity
  );
  
  // Get adjusted size
  const adjustedSize = screenSize === 'mobile' ? '40px' : 
                     screenSize === 'tablet' ? '50px' : '60px';
  
  return { shouldRender, adjustedOpacity, adjustedSize };
}

/**
 * PatternBackground Component
 */
export const PatternBackground = forwardRef<HTMLDivElement, PatternBackgroundProps>(
  ({
    pattern,
    intensity = 'moderate',
    animated = false,
    interactive = false,
    patternSize,
    opacity,
    performanceMonitoring = false,
    forceGPUAcceleration = false,
    disableResponsive = false,
    layeredPattern,
    className,
    style,
    children,
    ...props
  }, ref) => {
    const patternConfig = getSyrianPattern(pattern);
    const { shouldRender, adjustedOpacity, adjustedSize } = useResponsivePattern(pattern, disableResponsive);
    const { paintTime, isOptimal } = usePatternPerformance(performanceMonitoring, pattern);
    
    // Don't render if responsive rules say no
    if (!shouldRender) {
      return (
        <div ref={ref} className={className} style={style} {...props}>
          {children}
        </div>
      );
    }
    
    // Build CSS custom properties
    const cssProperties = {
      '--pattern-size': patternSize || adjustedSize,
      '--pattern-opacity': opacity !== undefined ? opacity : adjustedOpacity,
      ...style,
    } as React.CSSProperties;
    
    // Build class names
    const containerClasses = cn(
      'syrian-pattern-container',
      className
    );
    
    const patternClasses = cn(
      'syrian-pattern',
      `pattern-${pattern}`,
      `pattern-intensity-${intensity}`,
      {
        'pattern-animated': animated,
        'pattern-interactive': interactive,
        'pattern-gpu-accelerated': forceGPUAcceleration,
        'pattern-performance-monitor': performanceMonitoring,
        'pattern-layered': layeredPattern,
        [`${layeredPattern ? `${pattern}-${layeredPattern}` : ''}`]: layeredPattern,
      }
    );
    
    // Accessibility props for decorative patterns
    const accessibilityProps = getDecorativeProps('decorative');
    
    // Performance warning in development
    useEffect(() => {
      if (process.env.NODE_ENV === 'development' && performanceMonitoring && !isOptimal) {
        console.warn(
          `Pattern "${pattern}" paint time (${paintTime.toFixed(2)}ms) exceeds 16ms budget. ` +
          'Consider reducing complexity or disabling on this device.'
        );
      }
    }, [pattern, paintTime, isOptimal, performanceMonitoring]);
    
    return (
      <div
        ref={ref}
        className={containerClasses}
        style={cssProperties}
        {...accessibilityProps}
        {...props}
      >
        {/* Main pattern */}
        <div 
          className={patternClasses}
          style={{
            color: `hsl(var(--syrian-${pattern.includes('gold') ? 'qasioun-gold' : 
                                    pattern.includes('red') ? 'damascus-red' :
                                    pattern.includes('green') ? 'umayyad-green' :
                                    pattern.includes('stone') ? 'palmyra-stone' :
                                    pattern.includes('blue') ? 'ebla-blue' :
                                    'palmyra-stone'}-500))`,
          }}
        />
        
        {/* Layered pattern if specified */}
        {layeredPattern && (
          <div 
            className={cn(
              'syrian-pattern',
              `pattern-${layeredPattern}`,
              'pattern-intensity-subtle'
            )}
            style={{
              opacity: adjustedOpacity * 0.5,
              transform: 'rotate(15deg) scale(0.8)',
              color: `hsl(var(--syrian-${layeredPattern.includes('gold') ? 'qasioun-gold' : 
                                        layeredPattern.includes('red') ? 'damascus-red' :
                                        layeredPattern.includes('green') ? 'umayyad-green' :
                                        layeredPattern.includes('stone') ? 'palmyra-stone' :
                                        layeredPattern.includes('blue') ? 'ebla-blue' :
                                        'palmyra-stone'}-500))`,
            }}
          />
        )}
        
        {/* Content */}
        {children}
        
        {/* Performance monitoring overlay (development only) */}
        {process.env.NODE_ENV === 'development' && performanceMonitoring && (
          <div 
            className="absolute top-2 right-2 text-xs bg-black/80 text-white p-1 rounded"
            style={{ zIndex: 9999 }}
          >
            {pattern}: {paintTime.toFixed(1)}ms {isOptimal ? '✅' : '⚠️'}
          </div>
        )}
      </div>
    );
  }
);

PatternBackground.displayName = 'PatternBackground';

/**
 * Preset pattern combinations for common use cases
 */
export const PatternPresets = {
  // Header patterns
  heroSection: {
    pattern: 'damascusStar' as const,
    intensity: 'subtle' as const,
    animated: true,
  },
  
  // Content area patterns
  contentBackground: {
    pattern: 'eblaScript' as const,
    intensity: 'subtle' as const,
    animated: false,
  },
  
  // Footer patterns
  footerSection: {
    pattern: 'palmyraColumns' as const,
    intensity: 'moderate' as const,
    animated: false,
  },
  
  // Card patterns
  cardAccent: {
    pattern: 'geometricWeave' as const,
    intensity: 'subtle' as const,
    animated: false,
  },
  
  // Layered patterns for special sections
  culturalShowcase: {
    pattern: 'damascusStar' as const,
    layeredPattern: 'eblaScript' as const,
    intensity: 'moderate' as const,
    animated: true,
  },
} as const;

/**
 * Quick preset components
 */
export const HeroPatternBackground = forwardRef<HTMLDivElement, Omit<PatternBackgroundProps, 'pattern'>>(
  (props, ref) => (
    <PatternBackground ref={ref} {...PatternPresets.heroSection} {...props} />
  )
);

export const ContentPatternBackground = forwardRef<HTMLDivElement, Omit<PatternBackgroundProps, 'pattern'>>(
  (props, ref) => (
    <PatternBackground ref={ref} {...PatternPresets.contentBackground} {...props} />
  )
);

export const FooterPatternBackground = forwardRef<HTMLDivElement, Omit<PatternBackgroundProps, 'pattern'>>(
  (props, ref) => (
    <PatternBackground ref={ref} {...PatternPresets.footerSection} {...props} />
  )
);

export const CardPatternBackground = forwardRef<HTMLDivElement, Omit<PatternBackgroundProps, 'pattern'>>(
  (props, ref) => (
    <PatternBackground ref={ref} {...PatternPresets.cardAccent} {...props} />
  )
);

// Export types
export type { PatternBackgroundProps, SyrianPatternName };
