/**
 * Fallback chart components for when chart libraries fail to load
 * These provide basic visual representations without heavy dependencies
 */

interface ChartData {
  name: string;
  value: number;
  [key: string]: any;
}

interface ChartProps {
  data: ChartData[];
  width?: number;
  height?: number;
  className?: string;
}

export function FallbackBarChart({ data, height = 200, className = '' }: ChartProps) {
  const maxValue = Math.max(...data.map(d => d.value));
  
  return (
    <div className={`p-4 ${className}`}>
      <div className="text-sm text-gray-600 mb-4">
        Chart library not available - showing simplified view
      </div>
      <div className="space-y-2" style={{ height }}>
        {data.map((item, index) => (
          <div key={index} className="flex items-center gap-3">
            <div className="w-20 text-sm text-gray-700 truncate">
              {item.name}
            </div>
            <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
              <div 
                className="bg-blue-500 h-6 rounded-full flex items-center justify-end pr-2"
                style={{ width: `${(item.value / maxValue) * 100}%` }}
              >
                <span className="text-xs text-white font-medium">
                  {item.value}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export function FallbackLineChart({ data, height = 200, className = '' }: ChartProps) {
  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(...data.map(d => d.value));
  const range = maxValue - minValue;
  
  return (
    <div className={`p-4 ${className}`}>
      <div className="text-sm text-gray-600 mb-4">
        Chart library not available - showing data points
      </div>
      <div className="relative" style={{ height }}>
        <div className="absolute inset-0 border border-gray-200 rounded">
          <div className="flex items-end justify-between h-full p-2">
            {data.map((item, index) => (
              <div key={index} className="flex flex-col items-center">
                <div 
                  className="w-2 bg-blue-500 rounded-t"
                  style={{ 
                    height: `${range > 0 ? ((item.value - minValue) / range) * 80 : 50}%` 
                  }}
                />
                <div className="text-xs text-gray-600 mt-1 transform -rotate-45 origin-left">
                  {item.name}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export function FallbackPieChart({ data, height = 200, className = '' }: ChartProps) {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  
  return (
    <div className={`p-4 ${className}`}>
      <div className="text-sm text-gray-600 mb-4">
        Chart library not available - showing percentages
      </div>
      <div className="space-y-3" style={{ height }}>
        {data.map((item, index) => {
          const percentage = total > 0 ? ((item.value / total) * 100).toFixed(1) : '0';
          const colors = [
            'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 
            'bg-red-500', 'bg-purple-500', 'bg-indigo-500'
          ];
          
          return (
            <div key={index} className="flex items-center gap-3">
              <div className={`w-4 h-4 rounded ${colors[index % colors.length]}`} />
              <div className="flex-1 flex justify-between">
                <span className="text-sm text-gray-700">{item.name}</span>
                <span className="text-sm font-medium">{percentage}%</span>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}