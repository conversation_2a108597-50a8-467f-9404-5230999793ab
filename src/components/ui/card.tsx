import * as React from "react"

import { cn } from "@/lib/utils"
import { PatternBackground, type SyrianPatternName } from "./pattern-background"
import { getDecorativeProps } from "@/lib/a11y/decorative-helpers"
import { useSyrianComponentTracking } from "@/lib/analytics/hooks"

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Enable Syrian cultural styling */
  syrianStyle?: boolean
  /** Syrian pattern for background */
  syrianPattern?: SyrianPatternName
  /** Syrian styling intensity */
  syrianIntensity?: 'subtle' | 'moderate' | 'rich'
  /** Enable Syrian pattern animation */
  syrianAnimated?: boolean
  /** Make card interactive with Syrian hover effects */
  syrianInteractive?: boolean
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({
    className,
    syrianStyle = false,
    syrianPattern = 'eblaScript',
    syrianIntensity = 'subtle',
    syrianAnimated = false,
    syrianInteractive = false,
    ...props
  }, ref) => {
    // Track Syrian component usage
    useSyrianComponentTracking(
      'Card',
      syrianStyle ? 'syrian' : undefined,
      syrianPattern,
      syrianIntensity,
      syrianStyle
    );

    // Base card classes
    const baseClasses = "rounded-lg border bg-card text-card-foreground shadow-sm"

    // Syrian styling classes
    const syrianClasses = syrianStyle ? cn(
      "border-syrian-gold/20 shadow-syrian-gold/5",
      syrianInteractive && "syrian-hover-lift cursor-pointer",
      "relative overflow-hidden"
    ) : ""

    const cardClasses = cn(baseClasses, syrianClasses, className)

    // If Syrian styling with pattern is enabled
    if (syrianStyle && syrianPattern) {
      return (
        <PatternBackground
          pattern={syrianPattern}
          intensity={syrianIntensity}
          animated={syrianAnimated}
          interactive={syrianInteractive}
          className={cardClasses}
          {...getDecorativeProps('decorative')}
          ref={ref}
          {...props}
        />
      )
    }

    // Standard card rendering
    return (
      <div
        ref={ref}
        className={cardClasses}
        {...props}
      />
    )
  }
)
Card.displayName = "Card"

export interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Enable Syrian cultural styling for header */
  syrianStyle?: boolean
}

const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, syrianStyle = false, ...props }, ref) => {
    const headerClasses = cn(
      "flex flex-col space-y-1.5 p-6",
      syrianStyle && "border-b border-syrian-gold/10",
      className
    )

    return (
      <div
        ref={ref}
        className={headerClasses}
        {...props}
      />
    )
  }
)
CardHeader.displayName = "CardHeader"

export interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  /** Enable Syrian cultural styling for title */
  syrianStyle?: boolean
}

const CardTitle = React.forwardRef<HTMLParagraphElement, CardTitleProps>(
  ({ className, syrianStyle = false, ...props }, ref) => {
    const titleClasses = cn(
      "text-2xl font-semibold leading-none tracking-tight",
      syrianStyle && "text-syrian-gold-dark",
      className
    )

    return (
      <h3
        ref={ref}
        className={titleClasses}
        {...props}
      />
    )
  }
)
CardTitle.displayName = "CardTitle"

export interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {
  /** Enable Syrian cultural styling for description */
  syrianStyle?: boolean
}

const CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(
  ({ className, syrianStyle = false, ...props }, ref) => {
    const descriptionClasses = cn(
      "text-sm text-muted-foreground",
      syrianStyle && "text-syrian-stone-dark",
      className
    )

    return (
      <p
        ref={ref}
        className={descriptionClasses}
        {...props}
      />
    )
  }
)
CardDescription.displayName = "CardDescription"

export interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Enable Syrian cultural styling for content */
  syrianStyle?: boolean
}

const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, syrianStyle = false, ...props }, ref) => {
    const contentClasses = cn(
      "p-6 pt-0",
      syrianStyle && "relative z-10", // Ensure content is above pattern
      className
    )

    return (
      <div
        ref={ref}
        className={contentClasses}
        {...props}
      />
    )
  }
)
CardContent.displayName = "CardContent"

export interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Enable Syrian cultural styling for footer */
  syrianStyle?: boolean
}

const CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className, syrianStyle = false, ...props }, ref) => {
    const footerClasses = cn(
      "flex items-center p-6 pt-0",
      syrianStyle && "border-t border-syrian-gold/10 relative z-10",
      className
    )

    return (
      <div
        ref={ref}
        className={footerClasses}
        {...props}
      />
    )
  }
)
CardFooter.displayName = "CardFooter"

export {
  Card,
  CardHeader,
  CardFooter,
  CardTitle,
  CardDescription,
  CardContent,
  type CardProps,
  type CardHeaderProps,
  type CardTitleProps,
  type CardDescriptionProps,
  type CardContentProps,
  type CardFooterProps
}
