import React, { useState, useRef, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';

interface OptimizedImageProps {
  src: string;
  alt: string;
  className?: string;
  sizes?: string;
  priority?: boolean;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  width?: number;
  height?: number;
  onLoad?: () => void;
  onError?: () => void;
  loading?: 'lazy' | 'eager';
}

interface ImageFormat {
  format: string;
  mimeType: string;
  extension: string;
}

const SUPPORTED_FORMATS: ImageFormat[] = [
  { format: 'avif', mimeType: 'image/avif', extension: '.avif' },
  { format: 'webp', mimeType: 'image/webp', extension: '.webp' },
  { format: 'jpeg', mimeType: 'image/jpeg', extension: '.jpg' },
  { format: 'png', mimeType: 'image/png', extension: '.png' }
];

const DEFAULT_BLUR_DATA_URL = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==';

// Check if browser supports a specific image format
const supportsImageFormat = (format: string): boolean => {
  if (typeof window === 'undefined') return false;
  
  try {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    
    const dataUrl = canvas.toDataURL(`image/${format}`);
    return dataUrl.indexOf(`data:image/${format}`) === 0;
  } catch {
    // Fallback for test environments or unsupported formats
    const basicFormats = ['jpeg', 'jpg', 'png', 'gif'];
    return basicFormats.includes(format.toLowerCase());
  }
};

// Generate optimized source URLs for different formats
const generateOptimizedSources = (src: string, sizes?: string): string[] => {
  const sources: string[] = [];
  
  // Extract base URL and extension
  const lastDotIndex = src.lastIndexOf('.');
  const baseUrl = lastDotIndex > -1 ? src.substring(0, lastDotIndex) : src;
  const originalExtension = lastDotIndex > -1 ? src.substring(lastDotIndex) : '';
  
  // Generate sources for supported formats
  SUPPORTED_FORMATS.forEach(({ format, extension }) => {
    if (supportsImageFormat(format)) {
      // For now, we'll use the original URL with format hints
      // In a real implementation, you'd have a service that generates these
      sources.push(`${baseUrl}${extension}`);
    }
  });
  
  // Always include original as fallback
  if (!sources.includes(src)) {
    sources.push(src);
  }
  
  return sources;
};

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className,
  sizes,
  priority = false,
  placeholder = 'empty',
  blurDataURL,
  width,
  height,
  onLoad,
  onError,
  loading = 'lazy'
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(priority);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || typeof window === 'undefined') {
      setIsInView(true);
      return;
    }

    // Check if IntersectionObserver is available
    if (typeof IntersectionObserver === 'undefined') {
      setIsInView(true);
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin: '50px 0px', // Start loading 50px before the image comes into view
        threshold: 0.01
      }
    );

    observerRef.current = observer;

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [priority]);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setHasError(true);
    onError?.();
  }, [onError]);

  // Generate optimized sources
  const optimizedSources = generateOptimizedSources(src, sizes);
  const shouldShowPlaceholder = !isLoaded && !hasError && placeholder !== 'empty';
  const blurUrl = blurDataURL || DEFAULT_BLUR_DATA_URL;

  return (
    <div 
      ref={imgRef}
      className={cn(
        'relative overflow-hidden',
        className
      )}
      style={{ width, height }}
    >
      {/* Blur placeholder */}
      {shouldShowPlaceholder && placeholder === 'blur' && (
        <img
          src={blurUrl}
          alt=""
          className={cn(
            'absolute inset-0 w-full h-full object-cover transition-opacity duration-300',
            isLoaded ? 'opacity-0' : 'opacity-100'
          )}
          style={{ filter: 'blur(10px)', transform: 'scale(1.1)' }}
          aria-hidden="true"
        />
      )}

      {/* Main image with progressive enhancement */}
      {isInView && (
        <picture>
          {/* Generate source elements for different formats */}
          {optimizedSources.slice(0, -1).map((source, index) => {
            const format = SUPPORTED_FORMATS[index];
            return (
              <source
                key={format.format}
                srcSet={source}
                type={format.mimeType}
                sizes={sizes}
              />
            );
          })}
          
          {/* Fallback img element */}
          <img
            src={optimizedSources[optimizedSources.length - 1]}
            alt={alt}
            className={cn(
              'w-full h-full object-cover transition-opacity duration-300',
              isLoaded ? 'opacity-100' : 'opacity-0',
              hasError && 'opacity-50'
            )}
            loading={priority ? 'eager' : loading}
            onLoad={handleLoad}
            onError={handleError}
            sizes={sizes}
            width={width}
            height={height}
          />
        </picture>
      )}

      {/* Error fallback */}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-400">
          <div className="text-center">
            <svg
              className="mx-auto h-8 w-8 mb-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <p className="text-xs">Image failed to load</p>
          </div>
        </div>
      )}

      {/* Loading skeleton */}
      {!isLoaded && !hasError && placeholder === 'empty' && isInView && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse" />
      )}
    </div>
  );
};

export default OptimizedImage;