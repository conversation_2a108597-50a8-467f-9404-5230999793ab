import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { Badge } from '../badge'
import { Button } from '../button'
import { Input } from '../input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../select'
import { Skeleton } from '../skeleton'

describe('UI Components Rendering', () => {
  describe('Badge Component', () => {
    it('renders with default variant', () => {
      render(<Badge>Test Badge</Badge>)
      const badge = screen.getByText('Test Badge')
      expect(badge).toBeInTheDocument()
      expect(badge).toHaveClass('inline-flex', 'items-center', 'rounded-full')
    })

    it('renders with different variants', () => {
      const { rerender } = render(<Badge variant="default">Default</Badge>)
      expect(screen.getByText('Default')).toHaveClass('bg-primary')

      rerender(<Badge variant="secondary">Secondary</Badge>)
      expect(screen.getByText('Secondary')).toHaveClass('bg-secondary')

      rerender(<Badge variant="destructive">Destructive</Badge>)
      expect(screen.getByText('Destructive')).toHaveClass('bg-destructive')

      rerender(<Badge variant="outline">Outline</Badge>)
      expect(screen.getByText('Outline')).toHaveClass('text-foreground')
    })

    it('accepts custom className', () => {
      render(<Badge className="custom-class">Custom Badge</Badge>)
      const badge = screen.getByText('Custom Badge')
      expect(badge).toHaveClass('custom-class')
    })
  })

  describe('Button Component', () => {
    it('renders with default variant and size', () => {
      render(<Button>Test Button</Button>)
      const button = screen.getByRole('button', { name: 'Test Button' })
      expect(button).toBeInTheDocument()
      expect(button).toHaveClass('inline-flex', 'items-center', 'justify-center')
    })

    it('renders with different variants', () => {
      const { rerender } = render(<Button variant="default">Default</Button>)
      expect(screen.getByRole('button')).toHaveClass('bg-primary')

      rerender(<Button variant="destructive">Destructive</Button>)
      expect(screen.getByRole('button')).toHaveClass('bg-destructive')

      rerender(<Button variant="outline">Outline</Button>)
      expect(screen.getByRole('button')).toHaveClass('border', 'border-input')

      rerender(<Button variant="secondary">Secondary</Button>)
      expect(screen.getByRole('button')).toHaveClass('bg-secondary')

      rerender(<Button variant="ghost">Ghost</Button>)
      expect(screen.getByRole('button')).toHaveClass('hover:bg-accent')

      rerender(<Button variant="link">Link</Button>)
      expect(screen.getByRole('button')).toHaveClass('text-primary', 'underline-offset-4')
    })

    it('renders with different sizes', () => {
      const { rerender } = render(<Button size="default">Default</Button>)
      expect(screen.getByRole('button')).toHaveClass('h-10', 'px-4', 'py-2')

      rerender(<Button size="sm">Small</Button>)
      expect(screen.getByRole('button')).toHaveClass('h-9', 'px-3')

      rerender(<Button size="lg">Large</Button>)
      expect(screen.getByRole('button')).toHaveClass('h-11', 'px-8')

      rerender(<Button size="icon">Icon</Button>)
      expect(screen.getByRole('button')).toHaveClass('h-10', 'w-10')
    })

    it('handles disabled state', () => {
      render(<Button disabled>Disabled Button</Button>)
      const button = screen.getByRole('button')
      expect(button).toBeDisabled()
      expect(button).toHaveClass('disabled:pointer-events-none', 'disabled:opacity-50')
    })
  })

  describe('Input Component', () => {
    it('renders with default props', () => {
      render(<Input placeholder="Test input" />)
      const input = screen.getByPlaceholderText('Test input')
      expect(input).toBeInTheDocument()
      expect(input).toHaveClass('flex', 'h-10', 'w-full', 'rounded-md')
    })

    it('handles different input types', () => {
      const { rerender } = render(<Input type="text" placeholder="Text input" />)
      expect(screen.getByPlaceholderText('Text input')).toHaveAttribute('type', 'text')

      rerender(<Input type="email" placeholder="Email input" />)
      expect(screen.getByPlaceholderText('Email input')).toHaveAttribute('type', 'email')

      rerender(<Input type="password" placeholder="Password input" />)
      expect(screen.getByPlaceholderText('Password input')).toHaveAttribute('type', 'password')
    })

    it('handles disabled state', () => {
      render(<Input disabled placeholder="Disabled input" />)
      const input = screen.getByPlaceholderText('Disabled input')
      expect(input).toBeDisabled()
      expect(input).toHaveClass('disabled:cursor-not-allowed', 'disabled:opacity-50')
    })
  })

  describe('Skeleton Component', () => {
    it('renders with default animation', () => {
      render(<Skeleton className="h-4 w-20" />)
      const skeleton = document.querySelector('.animate-pulse')
      expect(skeleton).toBeInTheDocument()
      expect(skeleton).toHaveClass('rounded-md', 'bg-muted')
    })

    it('accepts custom className', () => {
      render(<Skeleton className="h-8 w-32 custom-skeleton" />)
      const skeleton = document.querySelector('.custom-skeleton')
      expect(skeleton).toBeInTheDocument()
      expect(skeleton).toHaveClass('h-8', 'w-32')
    })
  })

  describe('Select Component', () => {
    it('renders select trigger', () => {
      render(
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Select an option" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="option1">Option 1</SelectItem>
            <SelectItem value="option2">Option 2</SelectItem>
          </SelectContent>
        </Select>
      )
      
      const trigger = screen.getByRole('combobox')
      expect(trigger).toBeInTheDocument()
      expect(trigger).toHaveClass('flex', 'h-10', 'w-full', 'items-center')
    })

    it('shows placeholder text', () => {
      render(
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Choose option" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="test">Test</SelectItem>
          </SelectContent>
        </Select>
      )
      
      expect(screen.getByText('Choose option')).toBeInTheDocument()
    })
  })
})