import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, vi } from 'vitest'
import { Button } from '../button'
import { Input } from '../input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../select'
import { Switch } from '../switch'
import { Checkbox } from '../checkbox'

describe('Interactive UI Elements', () => {
  describe('Button Interactions', () => {
    it('handles click events correctly', async () => {
      const handleClick = vi.fn()
      const user = userEvent.setup()
      
      render(<Button onClick={handleClick}>Click me</Button>)
      
      const button = screen.getByRole('button', { name: 'Click me' })
      await user.click(button)
      
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it('prevents clicks when disabled', async () => {
      const handleClick = vi.fn()
      const user = userEvent.setup()
      
      render(<Button disabled onClick={handleClick}>Disabled But<PERSON></Button>)
      
      const button = screen.getByRole('button')
      await user.click(button)
      
      expect(handleClick).not.toHaveBeenCalled()
      expect(button).toBeDisabled()
    })

    it('handles keyboard navigation', async () => {
      const handleClick = vi.fn()
      const user = userEvent.setup()
      
      render(<Button onClick={handleClick}>Keyboard Button</Button>)
      
      const button = screen.getByRole('button')
      button.focus()
      await user.keyboard('{Enter}')
      
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it('supports different button types', () => {
      const { rerender } = render(<Button type="button">Button</Button>)
      expect(screen.getByRole('button')).toHaveAttribute('type', 'button')

      rerender(<Button type="submit">Submit</Button>)
      expect(screen.getByRole('button')).toHaveAttribute('type', 'submit')

      rerender(<Button type="reset">Reset</Button>)
      expect(screen.getByRole('button')).toHaveAttribute('type', 'reset')
    })
  })

  describe('Input Interactions', () => {
    it('handles text input correctly', async () => {
      const handleChange = vi.fn()
      const user = userEvent.setup()
      
      render(<Input onChange={handleChange} placeholder="Enter text" />)
      
      const input = screen.getByPlaceholderText('Enter text')
      await user.type(input, 'Hello World')
      
      expect(input).toHaveValue('Hello World')
      expect(handleChange).toHaveBeenCalled()
    })

    it('handles focus and blur events', async () => {
      const handleFocus = vi.fn()
      const handleBlur = vi.fn()
      const user = userEvent.setup()
      
      render(
        <Input 
          onFocus={handleFocus} 
          onBlur={handleBlur} 
          placeholder="Focus test" 
        />
      )
      
      const input = screen.getByPlaceholderText('Focus test')
      
      await user.click(input)
      expect(handleFocus).toHaveBeenCalledTimes(1)
      
      await user.tab()
      expect(handleBlur).toHaveBeenCalledTimes(1)
    })

    it('validates input constraints', async () => {
      const user = userEvent.setup()
      
      render(
        <Input 
          type="email" 
          required 
          placeholder="Email input" 
        />
      )
      
      const input = screen.getByPlaceholderText('Email input')
      await user.type(input, 'invalid-email')
      
      expect(input).toHaveValue('invalid-email')
      expect(input).toBeInvalid()
      
      await user.clear(input)
      await user.type(input, '<EMAIL>')
      expect(input).toBeValid()
    })

    it('handles disabled state correctly', async () => {
      const handleChange = vi.fn()
      const user = userEvent.setup()
      
      render(<Input disabled onChange={handleChange} placeholder="Disabled input" />)
      
      const input = screen.getByPlaceholderText('Disabled input')
      expect(input).toBeDisabled()
      
      await user.type(input, 'test')
      expect(input).toHaveValue('')
      expect(handleChange).not.toHaveBeenCalled()
    })
  })

  describe('Select Interactions', () => {
    it('renders select trigger correctly', () => {
      render(
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Select option" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="option1">Option 1</SelectItem>
            <SelectItem value="option2">Option 2</SelectItem>
          </SelectContent>
        </Select>
      )
      
      const trigger = screen.getByRole('combobox')
      expect(trigger).toBeInTheDocument()
      expect(trigger).toHaveAttribute('aria-expanded', 'false')
      expect(screen.getByText('Select option')).toBeInTheDocument()
    })

    it('has proper accessibility attributes', () => {
      render(
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Choose an option" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="test">Test Option</SelectItem>
          </SelectContent>
        </Select>
      )
      
      const trigger = screen.getByRole('combobox')
      expect(trigger).toHaveAttribute('aria-autocomplete', 'none')
      expect(trigger).toHaveAttribute('data-state', 'closed')
    })
  })

  describe('Switch Interactions', () => {
    it('toggles state correctly', async () => {
      const handleChange = vi.fn()
      const user = userEvent.setup()
      
      render(<Switch onCheckedChange={handleChange} />)
      
      const switchElement = screen.getByRole('switch')
      expect(switchElement).not.toBeChecked()
      
      await user.click(switchElement)
      expect(handleChange).toHaveBeenCalledWith(true)
      
      await user.click(switchElement)
      expect(handleChange).toHaveBeenCalledWith(false)
    })

    it('handles disabled state', async () => {
      const handleChange = vi.fn()
      const user = userEvent.setup()
      
      render(<Switch disabled onCheckedChange={handleChange} />)
      
      const switchElement = screen.getByRole('switch')
      expect(switchElement).toBeDisabled()
      
      await user.click(switchElement)
      expect(handleChange).not.toHaveBeenCalled()
    })
  })

  describe('Checkbox Interactions', () => {
    it('toggles checked state correctly', async () => {
      const handleChange = vi.fn()
      const user = userEvent.setup()
      
      render(<Checkbox onCheckedChange={handleChange} />)
      
      const checkbox = screen.getByRole('checkbox')
      expect(checkbox).not.toBeChecked()
      
      await user.click(checkbox)
      expect(handleChange).toHaveBeenCalledWith(true)
      
      await user.click(checkbox)
      expect(handleChange).toHaveBeenCalledWith(false)
    })

    it('handles indeterminate state', () => {
      render(<Checkbox checked="indeterminate" />)
      
      const checkbox = screen.getByRole('checkbox')
      expect(checkbox).toHaveAttribute('data-state', 'indeterminate')
    })

    it('supports keyboard interaction', async () => {
      const handleChange = vi.fn()
      const user = userEvent.setup()
      
      render(<Checkbox onCheckedChange={handleChange} />)
      
      const checkbox = screen.getByRole('checkbox')
      checkbox.focus()
      
      await user.keyboard(' ')
      expect(handleChange).toHaveBeenCalledWith(true)
    })
  })

  describe('Form Integration', () => {
    it('works correctly in form context', async () => {
      const handleSubmit = vi.fn((e) => e.preventDefault())
      const user = userEvent.setup()
      
      render(
        <form onSubmit={handleSubmit}>
          <Input name="username" placeholder="Username" required />
          <Input name="email" type="email" placeholder="Email" required />
          <Button type="submit">Submit</Button>
        </form>
      )
      
      const usernameInput = screen.getByPlaceholderText('Username')
      const emailInput = screen.getByPlaceholderText('Email')
      const submitButton = screen.getByRole('button', { name: 'Submit' })
      
      await user.type(usernameInput, 'testuser')
      await user.type(emailInput, '<EMAIL>')
      await user.click(submitButton)
      
      expect(handleSubmit).toHaveBeenCalledTimes(1)
    })

    it('prevents submission with invalid data', async () => {
      const handleSubmit = vi.fn((e) => e.preventDefault())
      const user = userEvent.setup()
      
      render(
        <form onSubmit={handleSubmit}>
          <Input name="email" type="email" placeholder="Email" required />
          <Button type="submit">Submit</Button>
        </form>
      )
      
      const emailInput = screen.getByPlaceholderText('Email')
      const submitButton = screen.getByRole('button', { name: 'Submit' })
      
      await user.type(emailInput, 'invalid-email')
      await user.click(submitButton)
      
      // Form should not submit with invalid email
      expect(emailInput).toBeInvalid()
    })
  })

  describe('Accessibility Features', () => {
    it('maintains proper ARIA attributes', () => {
      render(
        <div>
          <Button aria-label="Close dialog">×</Button>
          <Input aria-describedby="help-text" placeholder="Input with help" />
          <div id="help-text">This is help text</div>
        </div>
      )
      
      const button = screen.getByRole('button')
      const input = screen.getByRole('textbox')
      
      expect(button).toHaveAttribute('aria-label', 'Close dialog')
      expect(input).toHaveAttribute('aria-describedby', 'help-text')
    })

    it('supports screen reader announcements', () => {
      render(
        <div>
          <Button aria-live="polite">Status Button</Button>
          <div role="status" aria-live="polite">Status message</div>
        </div>
      )
      
      const button = screen.getByRole('button')
      const status = screen.getByRole('status')
      
      expect(button).toHaveAttribute('aria-live', 'polite')
      expect(status).toHaveAttribute('aria-live', 'polite')
    })
  })
})