import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { Badge } from '../badge'
import { ExpertMatchBadge } from '../../experts/ExpertMatchBadge'
import { <PERSON>rowserRouter } from 'react-router-dom'

// Mock the expert matching API
vi.mock('../../../lib/expertMatching', () => ({
  problemExpertMatchesAPI: {
    getPendingMatchesForExpert: vi.fn().mockResolvedValue([])
  }
}))

// Mock the useAuth hook
vi.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: { id: 'test-user' },
    isAuthenticated: true
  })
}))

describe('Status Indicators and Badges', () => {
  describe('Badge Status Variants', () => {
    it('renders status badges with correct colors', () => {
      const { rerender } = render(<Badge variant="default">Active</Badge>)
      expect(screen.getByText('Active')).toHaveClass('bg-primary')

      rerender(<Badge variant="secondary">Pending</Badge>)
      expect(screen.getByText('Pending')).toHaveClass('bg-secondary')

      rerender(<Badge variant="destructive">Error</Badge>)
      expect(screen.getByText('Error')).toHaveClass('bg-destructive')

      rerender(<Badge variant="outline">Draft</Badge>)
      expect(screen.getByText('Draft')).toHaveClass('text-foreground')
    })

    it('renders urgency badges correctly', () => {
      const getUrgencyVariant = (urgency: string) => {
        switch (urgency) {
          case 'critical': return 'destructive'
          case 'high': return 'destructive'
          case 'medium': return 'secondary'
          default: return 'outline'
        }
      }

      const { rerender } = render(
        <Badge variant={getUrgencyVariant('critical')}>Critical</Badge>
      )
      expect(screen.getByText('Critical')).toHaveClass('bg-destructive')

      rerender(<Badge variant={getUrgencyVariant('high')}>High</Badge>)
      expect(screen.getByText('High')).toHaveClass('bg-destructive')

      rerender(<Badge variant={getUrgencyVariant('medium')}>Medium</Badge>)
      expect(screen.getByText('Medium')).toHaveClass('bg-secondary')

      rerender(<Badge variant={getUrgencyVariant('low')}>Low</Badge>)
      expect(screen.getByText('Low')).toHaveClass('text-foreground')
    })

    it('renders match score badges correctly', () => {
      const getScoreBadgeVariant = (score: number) => {
        if (score >= 0.8) return 'default'
        if (score >= 0.6) return 'secondary'
        return 'outline'
      }

      const { rerender } = render(
        <Badge variant={getScoreBadgeVariant(0.9)}>90%</Badge>
      )
      expect(screen.getByText('90%')).toHaveClass('bg-primary')

      rerender(<Badge variant={getScoreBadgeVariant(0.7)}>70%</Badge>)
      expect(screen.getByText('70%')).toHaveClass('bg-secondary')

      rerender(<Badge variant={getScoreBadgeVariant(0.4)}>40%</Badge>)
      expect(screen.getByText('40%')).toHaveClass('text-foreground')
    })
  })

  describe('Expert Match Badge', () => {
    it('renders loading state correctly', () => {
      render(
        <BrowserRouter>
          <ExpertMatchBadge expertId="test-expert" />
        </BrowserRouter>
      )
      
      // Should show loading state initially
      const button = screen.getByRole('button')
      expect(button).toBeInTheDocument()
      expect(button).toBeDisabled()
    })

    it('renders with custom className', () => {
      render(
        <BrowserRouter>
          <ExpertMatchBadge expertId="test-expert" className="custom-badge" />
        </BrowserRouter>
      )
      
      const button = screen.getByRole('button')
      expect(button).toHaveClass('custom-badge')
    })
  })

  describe('Status Color Functions', () => {
    it('returns correct colors for different statuses', () => {
      const getStatusColor = (status: string) => {
        switch (status) {
          case 'good': return 'bg-green-100 text-green-800'
          case 'needs-improvement': return 'bg-yellow-100 text-yellow-800'
          case 'poor': return 'bg-red-100 text-red-800'
          default: return 'bg-gray-100 text-gray-800'
        }
      }

      expect(getStatusColor('good')).toBe('bg-green-100 text-green-800')
      expect(getStatusColor('needs-improvement')).toBe('bg-yellow-100 text-yellow-800')
      expect(getStatusColor('poor')).toBe('bg-red-100 text-red-800')
      expect(getStatusColor('unknown')).toBe('bg-gray-100 text-gray-800')
    })

    it('handles urgency colors correctly', () => {
      const getUrgencyColor = (urgency: string) => {
        switch (urgency) {
          case 'critical': return 'text-red-600 bg-red-50 border-red-200'
          case 'high': return 'text-orange-600 bg-orange-50 border-orange-200'
          case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
          default: return 'text-blue-600 bg-blue-50 border-blue-200'
        }
      }

      expect(getUrgencyColor('critical')).toBe('text-red-600 bg-red-50 border-red-200')
      expect(getUrgencyColor('high')).toBe('text-orange-600 bg-orange-50 border-orange-200')
      expect(getUrgencyColor('medium')).toBe('text-yellow-600 bg-yellow-50 border-yellow-200')
      expect(getUrgencyColor('low')).toBe('text-blue-600 bg-blue-50 border-blue-200')
    })
  })

  describe('Badge Accessibility', () => {
    it('maintains proper semantic structure', () => {
      render(<Badge role="status" aria-label="Status indicator">Active</Badge>)
      const badge = screen.getByRole('status')
      expect(badge).toHaveAttribute('aria-label', 'Status indicator')
      expect(badge).toHaveTextContent('Active')
    })

    it('supports screen reader announcements', () => {
      render(
        <Badge aria-live="polite" aria-atomic="true">
          Status Updated
        </Badge>
      )
      const badge = screen.getByText('Status Updated')
      expect(badge).toHaveAttribute('aria-live', 'polite')
      expect(badge).toHaveAttribute('aria-atomic', 'true')
    })
  })

  describe('Interactive Badge Behavior', () => {
    it('handles click events when used as button', () => {
      const handleClick = vi.fn()
      render(
        <button onClick={handleClick}>
          <Badge>Clickable Badge</Badge>
        </button>
      )
      
      const button = screen.getByRole('button')
      button.click()
      expect(handleClick).toHaveBeenCalledTimes(1)
    })

    it('maintains focus styles', () => {
      render(
        <button className="focus:outline-none focus:ring-2 focus:ring-ring">
          <Badge>Focusable Badge</Badge>
        </button>
      )
      
      const button = screen.getByRole('button')
      expect(button).toHaveClass('focus:outline-none', 'focus:ring-2', 'focus:ring-ring')
    })
  })
})