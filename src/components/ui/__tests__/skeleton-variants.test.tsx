import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import {
  EnhancedSkeleton,
  CardSkeleton,
  ListSkeleton,
  FormSkeleton,
  ProblemSkeleton,
  ExpertSkeleton,
  SearchResultsSkeleton
} from '../skeleton-variants'

describe('Skeleton Components', () => {
  describe('EnhancedSkeleton', () => {
    it('renders with default pulse animation', () => {
      render(<EnhancedSkeleton data-testid="skeleton" />)
      const skeleton = screen.getByTestId('skeleton')
      expect(skeleton).toHaveClass('animate-pulse')
    })

    it('renders with wave animation', () => {
      render(<EnhancedSkeleton animation="wave" data-testid="skeleton" />)
      const skeleton = screen.getByTestId('skeleton')
      expect(skeleton).toHaveClass('animate-shimmer')
    })

    it('renders with no animation', () => {
      render(<EnhancedSkeleton animation="none" data-testid="skeleton" />)
      const skeleton = screen.getByTestId('skeleton')
      expect(skeleton).not.toHaveClass('animate-pulse')
      expect(skeleton).not.toHaveClass('animate-shimmer')
    })

    it('applies custom className', () => {
      render(<EnhancedSkeleton className="custom-class" data-testid="skeleton" />)
      const skeleton = screen.getByTestId('skeleton')
      expect(skeleton).toHaveClass('custom-class')
    })
  })

  describe('CardSkeleton', () => {
    it('renders with default configuration', () => {
      render(<CardSkeleton data-testid="card-skeleton" />)
      const cardSkeleton = screen.getByTestId('card-skeleton')
      expect(cardSkeleton).toBeInTheDocument()
      expect(cardSkeleton).toHaveClass('p-6', 'border', 'rounded-lg', 'bg-card')
    })

    it('renders with avatar when specified', () => {
      const { container } = render(<CardSkeleton avatar={true} />)
      const avatarSkeleton = container.querySelector('.w-12.h-12.rounded-full')
      expect(avatarSkeleton).toBeInTheDocument()
    })

    it('renders specified number of rows', () => {
      const { container } = render(<CardSkeleton rows={5} />)
      // Look for the content rows in the second space-y-2 div (content section)
      const contentSections = container.querySelectorAll('.space-y-2')
      const contentRows = contentSections[1]?.querySelectorAll('div') || []
      expect(contentRows).toHaveLength(5)
    })

    it('shows actions when specified', () => {
      const { container } = render(<CardSkeleton showActions={true} />)
      const actionsSection = container.querySelector('.flex.items-center.justify-between.pt-2')
      expect(actionsSection).toBeInTheDocument()
    })

    it('shows metadata when specified', () => {
      const { container } = render(<CardSkeleton showMetadata={true} />)
      const metadataSection = container.querySelector('.flex.items-center.gap-4.pt-2.border-t')
      expect(metadataSection).toBeInTheDocument()
    })
  })

  describe('ListSkeleton', () => {
    it('renders with default configuration', () => {
      const { container } = render(<ListSkeleton />)
      const listItems = container.querySelectorAll('.flex.items-center.gap-4')
      expect(listItems).toHaveLength(5) // default items count
    })

    it('renders specified number of items', () => {
      const { container } = render(<ListSkeleton items={3} />)
      const listItems = container.querySelectorAll('.flex.items-center.gap-4')
      expect(listItems).toHaveLength(3)
    })

    it('shows dividers when specified', () => {
      const { container } = render(<ListSkeleton showDividers={true} />)
      const dividers = container.querySelectorAll('.border-b.border-border')
      expect(dividers.length).toBeGreaterThan(0)
    })

    it('applies correct item height', () => {
      const { container } = render(<ListSkeleton itemHeight="lg" />)
      const listItems = container.querySelectorAll('.h-20')
      expect(listItems.length).toBeGreaterThan(0)
    })
  })

  describe('FormSkeleton', () => {
    it('renders with default configuration', () => {
      const { container } = render(<FormSkeleton />)
      const formFields = container.querySelectorAll('.space-y-2')
      expect(formFields).toHaveLength(4) // default fields count
    })

    it('renders specified number of fields', () => {
      const { container } = render(<FormSkeleton fields={6} />)
      const formFields = container.querySelectorAll('.space-y-2')
      expect(formFields).toHaveLength(6)
    })

    it('shows submit button when specified', () => {
      const { container } = render(<FormSkeleton showSubmit={true} />)
      const submitSection = container.querySelector('.flex.justify-end.gap-2.pt-4.border-t')
      expect(submitSection).toBeInTheDocument()
    })

    it('renders different field types correctly', () => {
      const { container } = render(
        <FormSkeleton 
          fields={3} 
          fieldTypes={['input', 'textarea', 'select']} 
        />
      )
      
      // Check for textarea (h-24)
      const textarea = container.querySelector('.h-24')
      expect(textarea).toBeInTheDocument()
      
      // Check for regular inputs (h-10)
      const inputs = container.querySelectorAll('.h-10')
      expect(inputs.length).toBeGreaterThan(0)
    })
  })

  describe('ProblemSkeleton', () => {
    it('renders card variant by default', () => {
      const { container } = render(<ProblemSkeleton />)
      const cardSkeleton = container.querySelector('.p-6.border.rounded-lg.bg-card')
      expect(cardSkeleton).toBeInTheDocument()
    })

    it('renders list variant', () => {
      const { container } = render(<ProblemSkeleton variant="list" />)
      const listSkeleton = container.querySelector('.space-y-2')
      expect(listSkeleton).toBeInTheDocument()
    })

    it('renders detail variant', () => {
      const { container } = render(<ProblemSkeleton variant="detail" />)
      const detailSkeleton = container.querySelector('.space-y-6')
      expect(detailSkeleton).toBeInTheDocument()
    })
  })

  describe('ExpertSkeleton', () => {
    it('renders card variant by default', () => {
      const { container } = render(<ExpertSkeleton />)
      const cardSkeleton = container.querySelector('.p-6.border.rounded-lg.bg-card')
      expect(cardSkeleton).toBeInTheDocument()
    })

    it('renders list variant', () => {
      const { container } = render(<ExpertSkeleton variant="list" />)
      const listSkeleton = container.querySelector('.flex.items-center.gap-4')
      expect(listSkeleton).toBeInTheDocument()
    })

    it('renders profile variant', () => {
      const { container } = render(<ExpertSkeleton variant="profile" />)
      const profileSkeleton = container.querySelector('.space-y-6')
      expect(profileSkeleton).toBeInTheDocument()
    })

    it('includes avatar in card variant', () => {
      const { container } = render(<ExpertSkeleton variant="card" />)
      const avatar = container.querySelector('.w-16.h-16.rounded-full')
      expect(avatar).toBeInTheDocument()
    })
  })

  describe('SearchResultsSkeleton', () => {
    it('renders with default configuration', () => {
      const { container } = render(<SearchResultsSkeleton />)
      const searchSkeleton = container.querySelector('.space-y-6')
      expect(searchSkeleton).toBeInTheDocument()
    })

    it('renders specified number of results', () => {
      const { container } = render(<SearchResultsSkeleton results={3} />)
      const resultCards = container.querySelectorAll('.p-6.border.rounded-lg.bg-card')
      expect(resultCards).toHaveLength(3)
    })

    it('shows tabs when specified', () => {
      const { container } = render(<SearchResultsSkeleton showTabs={true} />)
      const tabsSection = container.querySelector('.flex.gap-2')
      expect(tabsSection).toBeInTheDocument()
    })

    it('hides tabs when specified', () => {
      const { container } = render(<SearchResultsSkeleton showTabs={false} />)
      // Look specifically for the tabs grid container
      const tabsGrid = container.querySelector('.grid.grid-cols-2')
      expect(tabsGrid).toBeNull()
    })
  })

  describe('Animation Classes', () => {
    it('applies pulse animation class correctly', () => {
      const { container } = render(<CardSkeleton animation="pulse" />)
      const pulseElements = container.querySelectorAll('.animate-pulse')
      expect(pulseElements.length).toBeGreaterThan(0)
    })

    it('applies wave animation class correctly', () => {
      const { container } = render(<CardSkeleton animation="wave" />)
      const waveElements = container.querySelectorAll('.animate-shimmer')
      expect(waveElements.length).toBeGreaterThan(0)
    })

    it('applies no animation when specified', () => {
      const { container } = render(<CardSkeleton animation="none" />)
      const animatedElements = container.querySelectorAll('.animate-pulse, .animate-shimmer')
      expect(animatedElements).toHaveLength(0)
    })
  })

  describe('Accessibility', () => {
    it('skeleton elements have appropriate ARIA attributes', () => {
      render(<CardSkeleton data-testid="card-skeleton" />)
      const cardSkeleton = screen.getByTestId('card-skeleton')
      
      // Skeleton should be presentational and not interfere with screen readers
      expect(cardSkeleton).toBeInTheDocument()
    })

    it('maintains proper semantic structure', () => {
      const { container } = render(<ListSkeleton />)
      
      // Check that the structure is maintained for screen readers
      const listContainer = container.firstChild
      expect(listContainer).toHaveClass('space-y-0')
    })
  })

  describe('Responsive Behavior', () => {
    it('renders with proper structure for responsive design', () => {
      const { container } = render(<SearchResultsSkeleton />)
      
      // Check that the component has a proper structure that can be styled responsively
      const mainContainer = container.querySelector('.space-y-6')
      expect(mainContainer).toBeInTheDocument()
      
      // Check for flex layouts that are commonly responsive
      const flexElements = container.querySelectorAll('.flex')
      expect(flexElements.length).toBeGreaterThan(0)
    })
  })

  describe('Custom Styling', () => {
    it('accepts and applies custom className', () => {
      render(<CardSkeleton className="custom-skeleton-class" data-testid="custom-skeleton" />)
      const skeleton = screen.getByTestId('custom-skeleton')
      expect(skeleton).toHaveClass('custom-skeleton-class')
    })

    it('merges custom classes with default classes', () => {
      render(<CardSkeleton className="custom-class" data-testid="merged-skeleton" />)
      const skeleton = screen.getByTestId('merged-skeleton')
      expect(skeleton).toHaveClass('custom-class')
      expect(skeleton).toHaveClass('p-6') // default class should still be present
    })
  })
})