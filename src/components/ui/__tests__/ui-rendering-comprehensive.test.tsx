import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { Badge } from '../badge'
import { Button } from '../button'
import { Input } from '../input'
import { Skeleton } from '../skeleton'
import { ProblemSkeleton, ExpertSkeleton, SearchResultsSkeleton } from '../skeleton-variants'

describe('UI Component Rendering - Comprehensive', () => {
  describe('Badge Component Variants', () => {
    it('renders all badge variants without errors', () => {
      const variants = ['default', 'secondary', 'destructive', 'outline'] as const
      
      variants.forEach((variant) => {
        const { unmount } = render(<Badge variant={variant}>{variant} Badge</Badge>)
        const badge = screen.getByText(`${variant} Badge`)
        expect(badge).toBeInTheDocument()
        expect(badge).toHaveClass('inline-flex', 'items-center', 'rounded-full')
        unmount()
      })
    })

    it('handles status indicators correctly', () => {
      const statusBadges = [
        { status: 'active', variant: 'default' as const, color: 'bg-primary' },
        { status: 'pending', variant: 'secondary' as const, color: 'bg-secondary' },
        { status: 'error', variant: 'destructive' as const, color: 'bg-destructive' },
        { status: 'draft', variant: 'outline' as const, color: 'text-foreground' }
      ]

      statusBadges.forEach(({ status, variant, color }) => {
        const { unmount } = render(<Badge variant={variant}>{status}</Badge>)
        const badge = screen.getByText(status)
        expect(badge).toHaveClass(color)
        unmount()
      })
    })

    it('renders urgency badges with correct styling', () => {
      const urgencyLevels = [
        { level: 'critical', variant: 'destructive' as const },
        { level: 'high', variant: 'destructive' as const },
        { level: 'medium', variant: 'secondary' as const },
        { level: 'low', variant: 'outline' as const }
      ]

      urgencyLevels.forEach(({ level, variant }) => {
        const { unmount } = render(<Badge variant={variant}>{level}</Badge>)
        const badge = screen.getByText(level)
        expect(badge).toBeInTheDocument()
        unmount()
      })
    })
  })

  describe('Button Component Variants', () => {
    it('renders all button variants without errors', () => {
      const variants = ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link'] as const
      
      variants.forEach((variant) => {
        const { unmount } = render(<Button variant={variant}>{variant} Button</Button>)
        const button = screen.getByRole('button', { name: `${variant} Button` })
        expect(button).toBeInTheDocument()
        expect(button).toHaveClass('inline-flex', 'items-center', 'justify-center')
        unmount()
      })
    })

    it('renders all button sizes without errors', () => {
      const sizes = ['default', 'sm', 'lg', 'icon'] as const
      
      sizes.forEach((size) => {
        const { unmount } = render(<Button size={size}>Button</Button>)
        const button = screen.getByRole('button')
        expect(button).toBeInTheDocument()
        unmount()
      })
    })
  })

  describe('Input Component Types', () => {
    it('renders different input types without errors', () => {
      const inputTypes = ['text', 'email', 'password', 'number', 'tel', 'url'] as const
      
      inputTypes.forEach((type) => {
        const { unmount } = render(<Input type={type} placeholder={`${type} input`} />)
        const input = screen.getByPlaceholderText(`${type} input`)
        expect(input).toBeInTheDocument()
        expect(input).toHaveAttribute('type', type)
        unmount()
      })
    })

    it('handles input states correctly', () => {
      const { rerender } = render(<Input placeholder="Normal input" />)
      expect(screen.getByPlaceholderText('Normal input')).not.toBeDisabled()

      rerender(<Input disabled placeholder="Disabled input" />)
      expect(screen.getByPlaceholderText('Disabled input')).toBeDisabled()

      rerender(<Input required placeholder="Required input" />)
      expect(screen.getByPlaceholderText('Required input')).toBeRequired()
    })
  })

  describe('Skeleton Components', () => {
    it('renders basic skeleton without errors', () => {
      render(<Skeleton className="h-4 w-20" />)
      const skeleton = document.querySelector('.animate-pulse')
      expect(skeleton).toBeInTheDocument()
      expect(skeleton).toHaveClass('rounded-md', 'bg-muted')
    })

    it('renders problem skeleton variants without errors', () => {
      const variants = ['card', 'list', 'detail'] as const
      
      variants.forEach((variant) => {
        const { unmount } = render(<ProblemSkeleton variant={variant} />)
        // Check that the skeleton renders without throwing errors
        expect(document.body).toBeInTheDocument()
        unmount()
      })
    })

    it('renders expert skeleton variants without errors', () => {
      const variants = ['card', 'list', 'profile'] as const
      
      variants.forEach((variant) => {
        const { unmount } = render(<ExpertSkeleton variant={variant} />)
        // Check that the skeleton renders without throwing errors
        expect(document.body).toBeInTheDocument()
        unmount()
      })
    })

    it('renders search results skeleton without errors', () => {
      render(<SearchResultsSkeleton results={3} />)
      // Check that the skeleton renders without throwing errors
      expect(document.body).toBeInTheDocument()
    })
  })

  describe('Component Prop Validation', () => {
    it('handles missing props gracefully', () => {
      // These should render without errors even with minimal props
      expect(() => render(<Badge>Test</Badge>)).not.toThrow()
      expect(() => render(<Button>Test</Button>)).not.toThrow()
      expect(() => render(<Input />)).not.toThrow()
      expect(() => render(<Skeleton />)).not.toThrow()
    })

    it('handles custom className props correctly', () => {
      const customClass = 'custom-test-class'
      
      render(<Badge className={customClass}>Badge</Badge>)
      expect(screen.getByText('Badge')).toHaveClass(customClass)

      render(<Button className={customClass}>Button</Button>)
      expect(screen.getByRole('button')).toHaveClass(customClass)

      render(<Input className={customClass} placeholder="Input" />)
      expect(screen.getByPlaceholderText('Input')).toHaveClass(customClass)
    })
  })

  describe('Accessibility Compliance', () => {
    it('maintains proper semantic structure', () => {
      render(
        <div>
          <Button>Action Button</Button>
          <Input placeholder="Text input" />
          <Badge>Status Badge</Badge>
        </div>
      )

      expect(screen.getByRole('button')).toBeInTheDocument()
      expect(screen.getByRole('textbox')).toBeInTheDocument()
      expect(screen.getByText('Status Badge')).toBeInTheDocument()
    })

    it('supports ARIA attributes', () => {
      render(
        <div>
          <Button aria-label="Close dialog" aria-pressed="false">×</Button>
          <Input aria-describedby="help-text" placeholder="Input with help" />
          <Badge role="status" aria-live="polite">Live status</Badge>
          <div id="help-text">Help text</div>
        </div>
      )

      const button = screen.getByRole('button')
      const input = screen.getByRole('textbox')
      const badge = screen.getByRole('status')

      expect(button).toHaveAttribute('aria-label', 'Close dialog')
      expect(button).toHaveAttribute('aria-pressed', 'false')
      expect(input).toHaveAttribute('aria-describedby', 'help-text')
      expect(badge).toHaveAttribute('aria-live', 'polite')
    })
  })

  describe('Performance and Rendering', () => {
    it('renders multiple components efficiently', () => {
      const startTime = performance.now()
      
      render(
        <div>
          {Array.from({ length: 50 }, (_, i) => (
            <div key={i}>
              <Badge variant="default">Badge {i}</Badge>
              <Button size="sm">Button {i}</Button>
              <Skeleton className="h-4 w-20" />
            </div>
          ))}
        </div>
      )
      
      const endTime = performance.now()
      const renderTime = endTime - startTime
      
      // Should render 50 components in reasonable time (less than 100ms)
      expect(renderTime).toBeLessThan(100)
      
      // Verify all components rendered
      expect(screen.getAllByText(/Badge \d+/)).toHaveLength(50)
      expect(screen.getAllByText(/Button \d+/)).toHaveLength(50)
    })

    it('handles rapid re-renders without errors', () => {
      const { rerender } = render(<Badge variant="default">Initial</Badge>)
      
      // Rapidly change variants
      const variants = ['secondary', 'destructive', 'outline', 'default'] as const
      variants.forEach((variant, index) => {
        rerender(<Badge variant={variant}>Update {index}</Badge>)
        expect(screen.getByText(`Update ${index}`)).toBeInTheDocument()
      })
    })
  })

  describe('Error Boundaries and Edge Cases', () => {
    it('handles undefined/null props gracefully', () => {
      // These should not throw errors
      expect(() => render(<Badge variant={undefined as any}>Test</Badge>)).not.toThrow()
      expect(() => render(<Button size={null as any}>Test</Button>)).not.toThrow()
      expect(() => render(<Input type={undefined as any} />)).not.toThrow()
    })

    it('handles empty content gracefully', () => {
      render(<Badge></Badge>)
      render(<Button></Button>)
      
      // Components should render even with empty content
      expect(document.body).toBeInTheDocument()
    })

    it('handles very long content appropriately', () => {
      const longBadgeText = 'Badge' + 'A'.repeat(995)
      const longButtonText = 'Button' + 'B'.repeat(994)
      
      render(
        <div>
          <Badge>{longBadgeText}</Badge>
          <Button>{longButtonText}</Button>
        </div>
      )
      
      expect(screen.getByText(longBadgeText)).toBeInTheDocument()
      expect(screen.getByText(longButtonText)).toBeInTheDocument()
    })
  })
})