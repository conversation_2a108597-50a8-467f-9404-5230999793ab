import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import '@testing-library/jest-dom';
import OptimizedImage from '../OptimizedImage';

// Mock IntersectionObserver completely
beforeEach(() => {
  // Mock IntersectionObserver to be undefined to test fallback
  global.IntersectionObserver = undefined as any;
});

describe('OptimizedImage - Simple Tests', () => {
  const defaultProps = {
    src: '/test-image.jpg',
    alt: 'Test image',
  };

  it('renders with priority flag (no lazy loading)', () => {
    render(<OptimizedImage {...defaultProps} priority />);
    
    const img = screen.getByRole('img', { name: 'Test image' });
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('src', '/test-image.jpg');
    expect(img).toHaveAttribute('alt', 'Test image');
    expect(img).toHaveAttribute('loading', 'eager');
  });

  it('renders with custom className', () => {
    render(<OptimizedImage {...defaultProps} className="custom-class" priority />);
    
    const container = document.querySelector('.custom-class');
    expect(container).toBeInTheDocument();
    expect(container).toHaveClass('relative', 'overflow-hidden', 'custom-class');
  });

  it('renders with width and height', () => {
    render(<OptimizedImage {...defaultProps} width={300} height={200} priority />);
    
    const img = screen.getByRole('img', { name: 'Test image' });
    expect(img).toHaveAttribute('width', '300');
    expect(img).toHaveAttribute('height', '200');
  });

  it('renders with sizes attribute', () => {
    const sizes = '(max-width: 768px) 100vw, 50vw';
    render(<OptimizedImage {...defaultProps} sizes={sizes} priority />);
    
    const img = screen.getByRole('img', { name: 'Test image' });
    expect(img).toHaveAttribute('sizes', sizes);
  });

  it('renders picture element with sources', () => {
    render(<OptimizedImage {...defaultProps} priority />);
    
    const picture = document.querySelector('picture');
    expect(picture).toBeInTheDocument();
    
    const sources = document.querySelectorAll('source');
    expect(sources.length).toBeGreaterThanOrEqual(0);
  });

  it('shows loading skeleton when placeholder is empty', () => {
    render(<OptimizedImage {...defaultProps} placeholder="empty" priority />);
    
    const skeleton = document.querySelector('.animate-pulse');
    expect(skeleton).toBeInTheDocument();
  });

  it('shows blur placeholder when specified', () => {
    render(<OptimizedImage {...defaultProps} placeholder="blur" priority />);
    
    // Should have multiple images (blur + main)
    const images = screen.getAllByRole('img');
    expect(images.length).toBeGreaterThan(1);
    
    // Find blur image by aria-hidden
    const blurImage = images.find(img => img.getAttribute('aria-hidden') === 'true');
    expect(blurImage).toBeInTheDocument();
  });

  it('uses custom blur data URL', () => {
    const customBlurURL = 'data:image/jpeg;base64,custom';
    render(
      <OptimizedImage 
        {...defaultProps} 
        placeholder="blur" 
        blurDataURL={customBlurURL} 
        priority
      />
    );
    
    const images = screen.getAllByRole('img');
    const blurImage = images.find(img => img.getAttribute('aria-hidden') === 'true');
    expect(blurImage).toHaveAttribute('src', customBlurURL);
  });

  it('falls back to eager loading when IntersectionObserver is not available', () => {
    // This test verifies the fallback behavior when IntersectionObserver is undefined
    render(<OptimizedImage {...defaultProps} />);
    
    // Should render the image immediately since IntersectionObserver is mocked as undefined
    const img = screen.getByRole('img', { name: 'Test image' });
    expect(img).toBeInTheDocument();
  });
});