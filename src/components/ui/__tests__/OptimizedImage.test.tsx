import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import '@testing-library/jest-dom';
import OptimizedImage from '../OptimizedImage';

// Mock IntersectionObserver
const mockObserve = vi.fn();
const mockUnobserve = vi.fn();
const mockDisconnect = vi.fn();

const mockIntersectionObserver = vi.fn().mockImplementation((callback) => ({
  observe: mockObserve,
  unobserve: mockUnobserve,
  disconnect: mockDisconnect,
  callback
}));

// Mock HTMLCanvasElement.toDataURL
const mockToDataURL = vi.fn();

beforeEach(() => {
  // Reset mocks
  vi.clearAllMocks();
  mockObserve.mockClear();
  mockUnobserve.mockClear();
  mockDisconnect.mockClear();
  
  // Mock IntersectionObserver
  global.IntersectionObserver = mockIntersectionObserver;
  
  // Mock canvas toDataURL
  HTMLCanvasElement.prototype.toDataURL = mockToDataURL;
  mockToDataURL.mockReturnValue('data:image/webp;base64,test');
  
  // Mock document.createElement for canvas
  const originalCreateElement = document.createElement;
  vi.spyOn(document, 'createElement').mockImplementation((tagName) => {
    if (tagName === 'canvas') {
      const canvas = originalCreateElement.call(document, 'canvas') as HTMLCanvasElement;
      canvas.getContext = vi.fn().mockReturnValue({
        fillStyle: '',
        fillRect: vi.fn(),
      });
      return canvas;
    }
    return originalCreateElement.call(document, tagName);
  });
});

afterEach(() => {
  vi.restoreAllMocks();
});

describe('OptimizedImage', () => {
  const defaultProps = {
    src: '/test-image.jpg',
    alt: 'Test image',
  };

  it('renders with basic props', () => {
    render(<OptimizedImage {...defaultProps} priority />);
    
    // Should render the image
    const img = screen.getByRole('img', { name: 'Test image' });
    expect(img).toBeInTheDocument();
  });

  it('shows loading skeleton when image is not loaded', () => {
    render(<OptimizedImage {...defaultProps} placeholder="empty" priority />);
    
    // Should show loading skeleton
    const skeleton = document.querySelector('.animate-pulse');
    expect(skeleton).toBeInTheDocument();
  });

  it('shows blur placeholder when specified', () => {
    render(<OptimizedImage {...defaultProps} placeholder="blur" priority />);
    
    // Should show blur placeholder - it should be the first img element
    const images = screen.getAllByRole('img');
    expect(images.length).toBeGreaterThan(1); // Should have blur + main image
    
    // The blur image should have aria-hidden
    const blurImage = images.find(img => img.getAttribute('aria-hidden') === 'true');
    expect(blurImage).toBeInTheDocument();
  });

  it('handles image load event', async () => {
    const onLoad = vi.fn();
    render(<OptimizedImage {...defaultProps} onLoad={onLoad} priority />);
    
    const img = screen.getByRole('img', { name: 'Test image' });
    
    // Simulate image load
    fireEvent.load(img);
    
    await waitFor(() => {
      expect(onLoad).toHaveBeenCalledTimes(1);
    });
  });

  it('handles image error event', async () => {
    const onError = vi.fn();
    render(<OptimizedImage {...defaultProps} onError={onError} priority />);
    
    const img = screen.getByRole('img', { name: 'Test image' });
    
    // Simulate image error
    fireEvent.error(img);
    
    await waitFor(() => {
      expect(onError).toHaveBeenCalledTimes(1);
    });
    
    // Should show error fallback
    expect(screen.getByText('Image failed to load')).toBeInTheDocument();
  });

  it('sets up intersection observer for lazy loading', () => {
    render(<OptimizedImage {...defaultProps} />);
    
    expect(mockIntersectionObserver).toHaveBeenCalledWith(
      expect.any(Function),
      expect.objectContaining({
        rootMargin: '50px 0px',
        threshold: 0.01
      })
    );
    expect(mockObserve).toHaveBeenCalled();
  });

  it('skips intersection observer when priority is true', () => {
    render(<OptimizedImage {...defaultProps} priority />);
    
    // Should not set up intersection observer for priority images
    expect(mockObserve).not.toHaveBeenCalled();
  });

  it('generates picture element with multiple sources', () => {
    render(<OptimizedImage {...defaultProps} priority />);
    
    const picture = document.querySelector('picture');
    expect(picture).toBeInTheDocument();
    
    // Should have source elements for different formats
    const sources = document.querySelectorAll('source');
    expect(sources.length).toBeGreaterThan(0);
  });

  it('applies custom className', () => {
    render(<OptimizedImage {...defaultProps} className="custom-class" priority />);
    
    // The className should be applied to the container div
    const container = document.querySelector('.custom-class');
    expect(container).toBeInTheDocument();
    expect(container).toHaveClass('relative', 'overflow-hidden', 'custom-class');
  });

  it('sets loading attribute based on priority', () => {
    const { rerender } = render(<OptimizedImage {...defaultProps} priority />);
    
    let img = screen.getByRole('img', { name: 'Test image' });
    expect(img).toHaveAttribute('loading', 'eager');
    
    rerender(<OptimizedImage {...defaultProps} />);
    img = screen.getByRole('img', { name: 'Test image' });
    expect(img).toHaveAttribute('loading', 'lazy');
  });

  it('handles custom blur data URL', () => {
    const customBlurURL = 'data:image/jpeg;base64,custom';
    render(
      <OptimizedImage 
        {...defaultProps} 
        placeholder="blur" 
        blurDataURL={customBlurURL} 
        priority
      />
    );
    
    // Find the blur image by aria-hidden attribute
    const images = screen.getAllByRole('img');
    const blurImage = images.find(img => img.getAttribute('aria-hidden') === 'true');
    expect(blurImage).toHaveAttribute('src', customBlurURL);
  });

  it('sets width and height attributes', () => {
    render(<OptimizedImage {...defaultProps} width={300} height={200} priority />);
    
    const img = screen.getByRole('img', { name: 'Test image' });
    expect(img).toHaveAttribute('width', '300');
    expect(img).toHaveAttribute('height', '200');
  });

  it('handles sizes attribute', () => {
    const sizes = '(max-width: 768px) 100vw, 50vw';
    render(<OptimizedImage {...defaultProps} sizes={sizes} priority />);
    
    const img = screen.getByRole('img', { name: 'Test image' });
    expect(img).toHaveAttribute('sizes', sizes);
  });

  it('simulates intersection observer callback', async () => {
    let intersectionCallback: (entries: IntersectionObserverEntry[]) => void;
    
    mockIntersectionObserver.mockImplementation((callback) => {
      intersectionCallback = callback;
      return {
        observe: vi.fn(),
        unobserve: vi.fn(),
        disconnect: vi.fn(),
      };
    });

    render(<OptimizedImage {...defaultProps} />);
    
    // Initially, image should not be in view
    expect(screen.queryByRole('img', { name: 'Test image' })).not.toBeInTheDocument();
    
    // Simulate intersection
    const mockEntry = {
      isIntersecting: true,
      target: document.createElement('div'),
    } as IntersectionObserverEntry;
    
    intersectionCallback!([mockEntry]);
    
    await waitFor(() => {
      expect(screen.getByRole('img', { name: 'Test image' })).toBeInTheDocument();
    });
  });

  it('handles format support detection', () => {
    // Mock toDataURL to return different results for different formats
    mockToDataURL
      .mockReturnValueOnce('data:image/avif;base64,test') // AVIF supported
      .mockReturnValueOnce('data:image/webp;base64,test') // WebP supported
      .mockReturnValueOnce('data:image/jpeg;base64,test'); // JPEG supported

    render(<OptimizedImage {...defaultProps} priority />);
    
    // Should create canvas elements for format detection
    expect(document.createElement).toHaveBeenCalledWith('canvas');
  });
});