/**
 * RTL Wrapper Component
 * 
 * Provides comprehensive RTL (Right-to-Left) layout support for Arabic content.
 * Handles direction switching, typography optimization, and cultural styling.
 * 
 * Features:
 * - Automatic direction detection
 * - Arabic typography optimization
 * - Syrian cultural styling integration
 * - Accessibility compliance
 * - Responsive behavior
 */

import React, { forwardRef, useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

// RTL detection utilities
const RTL_LANGUAGES = ['ar', 'he', 'fa', 'ur', 'ku', 'ps'] as const;
const ARABIC_SCRIPT_REGEX = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;

export interface RTLWrapperProps extends React.HTMLAttributes<HTMLDivElement> {
  /** Content language (auto-detected if not provided) */
  language?: string;
  
  /** Force direction (overrides auto-detection) */
  direction?: 'ltr' | 'rtl' | 'auto';
  
  /** Enable Syrian cultural styling */
  syrianStyle?: boolean;
  
  /** Typography variant for Arabic text */
  arabicVariant?: 'modern' | 'traditional';
  
  /** Text content for auto-detection */
  textContent?: string;
  
  /** Enable mixed content support (Arabic + English) */
  mixedContent?: boolean;
  
  /** Disable automatic direction detection */
  disableAutoDetection?: boolean;
}

/**
 * Detect if text contains Arabic script
 */
function containsArabicScript(text: string): boolean {
  return ARABIC_SCRIPT_REGEX.test(text);
}

/**
 * Detect text direction based on content and language
 */
function detectDirection(
  language?: string,
  textContent?: string,
  forceDirection?: 'ltr' | 'rtl' | 'auto'
): 'ltr' | 'rtl' {
  if (forceDirection && forceDirection !== 'auto') {
    return forceDirection;
  }
  
  // Check language code
  if (language && RTL_LANGUAGES.includes(language as any)) {
    return 'rtl';
  }
  
  // Check text content for Arabic script
  if (textContent && containsArabicScript(textContent)) {
    return 'rtl';
  }
  
  // Check document language
  if (typeof document !== 'undefined') {
    const docLang = document.documentElement.lang;
    if (docLang && RTL_LANGUAGES.includes(docLang as any)) {
      return 'rtl';
    }
  }
  
  return 'ltr';
}

/**
 * RTL Wrapper Component
 */
export const RTLWrapper = forwardRef<HTMLDivElement, RTLWrapperProps>(
  ({
    children,
    className,
    language,
    direction = 'auto',
    syrianStyle = false,
    arabicVariant = 'modern',
    textContent,
    mixedContent = false,
    disableAutoDetection = false,
    ...props
  }, ref) => {
    const [detectedDirection, setDetectedDirection] = useState<'ltr' | 'rtl'>('ltr');
    const [isClient, setIsClient] = useState(false);
    
    useEffect(() => {
      setIsClient(true);
      
      if (!disableAutoDetection) {
        // Get text content from children if not provided
        const contentText = textContent || (typeof children === 'string' ? children : '');
        const detected = detectDirection(language, contentText, direction);
        setDetectedDirection(detected);
      }
    }, [language, textContent, children, direction, disableAutoDetection]);
    
    // Use provided direction or detected direction
    const finalDirection = direction !== 'auto' ? direction : detectedDirection;
    const isRTL = finalDirection === 'rtl';
    
    // Build class names
    const wrapperClasses = cn(
      // Base RTL/LTR classes
      isRTL ? 'rtl' : 'ltr',
      
      // Arabic typography classes
      isRTL && {
        'arabic-text': arabicVariant === 'modern',
        'arabic-text-traditional': arabicVariant === 'traditional',
        'mixed-content': mixedContent,
      },
      
      // Syrian cultural styling
      syrianStyle && isRTL && 'syrian-cultural-text',
      
      // Custom classes
      className
    );
    
    // Accessibility attributes
    const accessibilityProps = {
      dir: finalDirection,
      lang: language || (isRTL ? 'ar' : undefined),
      'data-direction': finalDirection,
      'data-arabic-variant': isRTL ? arabicVariant : undefined,
    };
    
    return (
      <div
        ref={ref}
        className={wrapperClasses}
        {...accessibilityProps}
        {...props}
      >
        {children}
      </div>
    );
  }
);

RTLWrapper.displayName = 'RTLWrapper';

/**
 * Arabic Text Component
 * Specialized component for Arabic text with proper typography
 */
export interface ArabicTextProps extends Omit<RTLWrapperProps, 'direction'> {
  /** Text size variant */
  size?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl';
  
  /** Font weight */
  weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold';
  
  /** Enable Arabic-Indic numerals */
  arabicNumerals?: boolean;
}

export const ArabicText = forwardRef<HTMLDivElement, ArabicTextProps>(
  ({
    children,
    className,
    size = 'base',
    weight = 'normal',
    arabicNumerals = false,
    syrianStyle = false,
    arabicVariant = 'modern',
    ...props
  }, ref) => {
    const textClasses = cn(
      // Size classes
      `arabic-text-${size}`,
      
      // Weight classes
      `arabic-font-${weight}`,
      
      // Numeral classes
      arabicNumerals && 'arabic-indic-numerals',
      
      // Custom classes
      className
    );
    
    return (
      <RTLWrapper
        ref={ref}
        direction="rtl"
        syrianStyle={syrianStyle}
        arabicVariant={arabicVariant}
        className={textClasses}
        {...props}
      >
        {children}
      </RTLWrapper>
    );
  }
);

ArabicText.displayName = 'ArabicText';

/**
 * Bidirectional Text Component
 * For content that mixes Arabic and English text
 */
export interface BiDirectionalTextProps extends RTLWrapperProps {
  /** Primary text direction */
  primaryDirection?: 'ltr' | 'rtl';
}

export const BiDirectionalText = forwardRef<HTMLDivElement, BiDirectionalTextProps>(
  ({
    children,
    className,
    primaryDirection = 'rtl',
    ...props
  }, ref) => {
    return (
      <RTLWrapper
        ref={ref}
        direction={primaryDirection}
        mixedContent={true}
        className={cn('mixed-content', className)}
        {...props}
      >
        {children}
      </RTLWrapper>
    );
  }
);

BiDirectionalText.displayName = 'BiDirectionalText';

/**
 * RTL-aware Container Component
 * Provides RTL-aware layout utilities
 */
export interface RTLContainerProps extends RTLWrapperProps {
  /** Container type */
  variant?: 'flex' | 'grid' | 'block';
  
  /** Spacing between items */
  spacing?: 'none' | 'sm' | 'md' | 'lg';
}

export const RTLContainer = forwardRef<HTMLDivElement, RTLContainerProps>(
  ({
    children,
    className,
    variant = 'block',
    spacing = 'md',
    direction = 'auto',
    ...props
  }, ref) => {
    const [detectedDirection, setDetectedDirection] = useState<'ltr' | 'rtl'>('ltr');
    
    useEffect(() => {
      if (direction === 'auto') {
        const detected = detectDirection();
        setDetectedDirection(detected);
      }
    }, [direction]);
    
    const finalDirection = direction !== 'auto' ? direction : detectedDirection;
    const isRTL = finalDirection === 'rtl';
    
    const containerClasses = cn(
      // Base container classes
      variant === 'flex' && 'flex',
      variant === 'grid' && 'grid',
      
      // RTL-aware layout classes
      variant === 'flex' && isRTL && 'rtl-flex-row-reverse',
      variant === 'grid' && isRTL && 'rtl-grid-flow-col-reverse',
      
      // Spacing classes
      spacing === 'sm' && (isRTL ? 'rtl-space-x-2' : 'space-x-2'),
      spacing === 'md' && (isRTL ? 'rtl-space-x-4' : 'space-x-4'),
      spacing === 'lg' && (isRTL ? 'rtl-space-x-6' : 'space-x-6'),
      
      // Custom classes
      className
    );
    
    return (
      <RTLWrapper
        ref={ref}
        direction={finalDirection}
        className={containerClasses}
        {...props}
      >
        {children}
      </RTLWrapper>
    );
  }
);

RTLContainer.displayName = 'RTLContainer';

// Export utilities
export {
  containsArabicScript,
  detectDirection,
  RTL_LANGUAGES,
  ARABIC_SCRIPT_REGEX,
};
