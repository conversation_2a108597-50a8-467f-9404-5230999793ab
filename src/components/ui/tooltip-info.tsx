import { useState } from 'react'
import { HelpCircle } from 'lucide-react'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

interface TooltipInfoProps {
  content: string
  side?: 'top' | 'right' | 'bottom' | 'left'
  className?: string
}

export function TooltipInfo({ content, side = 'top', className = '' }: TooltipInfoProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            type="button"
            className={`inline-flex items-center justify-center w-4 h-4 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded-full ${className}`}
            aria-label="معلومات إضافية"
          >
            <HelpCircle className="w-3 h-3" />
          </button>
        </TooltipTrigger>
        <TooltipContent side={side} className="max-w-xs text-sm">
          <p>{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

// Predefined tooltips for common technical terms
export const TECHNICAL_TOOLTIPS = {
  'تطوير البرمجيات': 'عملية إنشاء وتصميم وبرمجة واختبار التطبيقات والأنظمة الحاسوبية',
  'الذكاء الاصطناعي': 'تقنية تمكن الآلات من محاكاة الذكاء البشري في التعلم واتخاذ القرارات',
  'أمن المعلومات': 'حماية البيانات والأنظمة من التهديدات والوصول غير المصرح به',
  'إنترنت الأشياء': 'شبكة من الأجهزة المترابطة التي يمكنها جمع وتبادل البيانات',
  'الحوسبة السحابية': 'توفير الخدمات الحاسوبية عبر الإنترنت بدلاً من الخوادم المحلية',
  'البلوك تشين': 'تقنية دفتر الأستاذ الموزع التي تضمن أمان وشفافية المعاملات',
  'تعلم الآلة': 'فرع من الذكاء الاصطناعي يمكن الأنظمة من التعلم والتحسن تلقائياً',
  'الواقع المعزز': 'تقنية تدمج العناصر الرقمية مع العالم الحقيقي',
  'الواقع الافتراضي': 'بيئة محاكاة ثلاثية الأبعاد يمكن للمستخدم التفاعل معها',
  'البيانات الضخمة': 'مجموعات البيانات الكبيرة والمعقدة التي تتطلب أدوات خاصة للتحليل'
}

interface SmartTooltipProps {
  text: string
  children: React.ReactNode
  className?: string
}

export function SmartTooltip({ text, children, className = '' }: SmartTooltipProps) {
  const tooltipContent = TECHNICAL_TOOLTIPS[text as keyof typeof TECHNICAL_TOOLTIPS]
  
  if (!tooltipContent) {
    return <>{children}</>
  }
  
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <span className={`cursor-help border-b border-dotted border-gray-400 ${className}`}>
            {children}
          </span>
        </TooltipTrigger>
        <TooltipContent className="max-w-xs text-sm">
          <p>{tooltipContent}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}