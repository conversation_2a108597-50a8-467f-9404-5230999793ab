import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"
import { PatternBackground, type SyrianPatternName } from "./pattern-background"
import { getDecorativeProps } from "@/lib/a11y/decorative-helpers"
import { useSyrianComponentTracking } from "@/lib/analytics/hooks"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        // Syrian Identity Variants
        syrian: "bg-syrian-gold text-syrian-gold-dark hover:bg-syrian-gold/90 border border-syrian-gold/30 syrian-hover-lift syrian-focus-ring",
        "syrian-outline": "border border-syrian-gold bg-background text-syrian-gold hover:bg-syrian-gold/10 hover:text-syrian-gold-dark syrian-hover-lift syrian-focus-ring",
        "syrian-ghost": "text-syrian-gold hover:bg-syrian-gold/10 hover:text-syrian-gold-dark syrian-hover-lift syrian-focus-ring",
        "syrian-secondary": "bg-syrian-stone text-syrian-stone-dark hover:bg-syrian-stone/90 border border-syrian-stone/30 syrian-hover-lift syrian-focus-ring",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  /** Enable Syrian cultural styling */
  syrianStyle?: boolean
  /** Syrian pattern for background (when syrianStyle is true) */
  syrianPattern?: SyrianPatternName
  /** Syrian styling intensity */
  syrianIntensity?: 'subtle' | 'moderate' | 'rich'
  /** Enable Syrian pattern animation */
  syrianAnimated?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant,
    size,
    asChild = false,
    syrianStyle = false,
    syrianPattern,
    syrianIntensity = 'moderate',
    syrianAnimated = false,
    ...props
  }, ref) => {
    const Comp = asChild ? Slot : "button"

    // Determine if we should use Syrian styling
    const useSyrianStyling = syrianStyle || variant?.startsWith('syrian')

    // Auto-select Syrian variant if syrianStyle is true but no Syrian variant specified
    const finalVariant = syrianStyle && !variant?.startsWith('syrian') ? 'syrian' : variant

    // Track Syrian component usage
    useSyrianComponentTracking(
      'Button',
      useSyrianStyling ? (finalVariant || 'syrian') : undefined,
      syrianPattern,
      syrianIntensity,
      useSyrianStyling
    )

    // Build the button classes
    const buttonClasses = cn(
      buttonVariants({ variant: finalVariant, size }),
      useSyrianStyling && "relative overflow-hidden",
      className
    )

    // If Syrian styling is enabled and pattern is specified, wrap with pattern
    if (useSyrianStyling && syrianPattern) {
      return (
        <PatternBackground
          pattern={syrianPattern}
          intensity={syrianIntensity}
          animated={syrianAnimated}
          className="inline-flex rounded-md"
          {...getDecorativeProps('decorative')}
        >
          <Comp
            className={buttonClasses}
            ref={ref}
            {...props}
          />
        </PatternBackground>
      )
    }

    // Standard button rendering
    return (
      <Comp
        className={buttonClasses}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
