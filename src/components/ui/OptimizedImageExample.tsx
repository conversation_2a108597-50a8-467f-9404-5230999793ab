import React from 'react';
import OptimizedImage from './OptimizedImage';

/**
 * Example usage of the OptimizedImage component
 */
export const OptimizedImageExample: React.FC = () => {
  return (
    <div className="space-y-8 p-8">
      <h2 className="text-2xl font-bold">OptimizedImage Component Examples</h2>
      
      {/* Basic usage */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Basic Usage</h3>
        <OptimizedImage
          src="/placeholder.svg"
          alt="Basic example"
          className="w-64 h-48 rounded-lg"
        />
      </div>

      {/* Priority image (no lazy loading) */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Priority Image (Above the fold)</h3>
        <OptimizedImage
          src="/placeholder.svg"
          alt="Priority image"
          className="w-64 h-48 rounded-lg"
          priority
        />
      </div>

      {/* With blur placeholder */}
      <div>
        <h3 className="text-lg font-semibold mb-2">With Blur Placeholder</h3>
        <OptimizedImage
          src="/placeholder.svg"
          alt="Image with blur placeholder"
          className="w-64 h-48 rounded-lg"
          placeholder="blur"
        />
      </div>

      {/* With custom dimensions */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Custom Dimensions</h3>
        <OptimizedImage
          src="/placeholder.svg"
          alt="Custom dimensions"
          width={300}
          height={200}
          className="rounded-lg"
        />
      </div>

      {/* Responsive with sizes */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Responsive with Sizes</h3>
        <OptimizedImage
          src="/placeholder.svg"
          alt="Responsive image"
          className="w-full max-w-md h-48 rounded-lg"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </div>

      {/* Grid of lazy-loaded images */}
      <div>
        <h3 className="text-lg font-semibold mb-2">Grid of Lazy-loaded Images</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {Array.from({ length: 6 }, (_, i) => (
            <OptimizedImage
              key={i}
              src="/placeholder.svg"
              alt={`Grid image ${i + 1}`}
              className="w-full h-32 rounded-lg"
              placeholder="empty"
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default OptimizedImageExample;