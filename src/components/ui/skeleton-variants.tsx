import { cn } from "@/lib/utils"
import { Skeleton } from "@/components/ui/skeleton"

// Base skeleton component with enhanced animation options
interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  animation?: 'pulse' | 'wave' | 'none'
}

function EnhancedSkeleton({
  className,
  animation = 'pulse',
  ...props
}: SkeletonProps) {
  return (
    <div
      className={cn(
        "rounded-md bg-muted",
        animation === 'pulse' && "animate-pulse",
        animation === 'wave' && "animate-shimmer bg-gradient-to-r from-muted via-muted/50 to-muted bg-[length:200%_100%]",
        animation === 'none' && "",
        className
      )}
      {...props}
    />
  )
}

// Card skeleton variants
interface CardSkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  rows?: number
  avatar?: boolean
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
  showActions?: boolean
  showMetadata?: boolean
}

function CardSkeleton({
  rows = 3,
  avatar = false,
  animation = 'pulse',
  className = '',
  showActions = false,
  showMetadata = true,
  ...props
}: CardSkeletonProps) {
  return (
    <div className={cn("p-6 border rounded-lg bg-card", className)} {...props}>
      <div className="space-y-4">
        {/* Header with optional avatar */}
        <div className="flex items-start gap-4">
          {avatar && (
            <EnhancedSkeleton 
              animation={animation} 
              className="w-12 h-12 rounded-full flex-shrink-0" 
            />
          )}
          <div className="flex-1 space-y-2">
            <EnhancedSkeleton animation={animation} className="h-6 w-3/4" />
            <EnhancedSkeleton animation={animation} className="h-4 w-1/2" />
          </div>
        </div>

        {/* Content rows */}
        <div className="space-y-2">
          {Array.from({ length: rows }, (_, i) => (
            <EnhancedSkeleton 
              key={i} 
              animation={animation} 
              className={cn(
                "h-4",
                i === rows - 1 ? "w-2/3" : "w-full"
              )} 
            />
          ))}
        </div>

        {/* Metadata */}
        {showMetadata && (
          <div className="flex items-center gap-4 pt-2 border-t">
            <EnhancedSkeleton animation={animation} className="h-4 w-20" />
            <EnhancedSkeleton animation={animation} className="h-4 w-16" />
            <EnhancedSkeleton animation={animation} className="h-4 w-24" />
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex items-center justify-between pt-2">
            <div className="flex gap-2">
              <EnhancedSkeleton animation={animation} className="h-6 w-16 rounded-full" />
              <EnhancedSkeleton animation={animation} className="h-6 w-20 rounded-full" />
            </div>
            <EnhancedSkeleton animation={animation} className="h-8 w-24 rounded-md" />
          </div>
        )}
      </div>
    </div>
  )
}

// List skeleton variants
interface ListSkeletonProps {
  items?: number
  itemHeight?: 'sm' | 'md' | 'lg'
  showDividers?: boolean
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

function ListSkeleton({
  items = 5,
  itemHeight = 'md',
  showDividers = true,
  animation = 'pulse',
  className = ''
}: ListSkeletonProps) {
  const heights = {
    sm: 'h-12',
    md: 'h-16',
    lg: 'h-20'
  }

  return (
    <div className={cn("space-y-0", className)}>
      {Array.from({ length: items }, (_, i) => (
        <div key={i}>
          <div className={cn("flex items-center gap-4 p-4", heights[itemHeight])}>
            <EnhancedSkeleton 
              animation={animation} 
              className="w-10 h-10 rounded-full flex-shrink-0" 
            />
            <div className="flex-1 space-y-2">
              <EnhancedSkeleton animation={animation} className="h-4 w-3/4" />
              <EnhancedSkeleton animation={animation} className="h-3 w-1/2" />
            </div>
            <EnhancedSkeleton animation={animation} className="h-6 w-16 rounded-full" />
          </div>
          {showDividers && i < items - 1 && (
            <div className="border-b border-border" />
          )}
        </div>
      ))}
    </div>
  )
}

// Form skeleton variants
interface FormSkeletonProps {
  fields?: number
  showSubmit?: boolean
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
  fieldTypes?: ('input' | 'textarea' | 'select' | 'checkbox')[]
}

function FormSkeleton({
  fields = 4,
  showSubmit = true,
  animation = 'pulse',
  className = '',
  fieldTypes = []
}: FormSkeletonProps) {
  const getFieldSkeleton = (type: string, index: number) => {
    switch (type) {
      case 'textarea':
        return (
          <div key={index} className="space-y-2">
            <EnhancedSkeleton animation={animation} className="h-4 w-24" />
            <EnhancedSkeleton animation={animation} className="h-24 w-full rounded-md" />
          </div>
        )
      case 'select':
        return (
          <div key={index} className="space-y-2">
            <EnhancedSkeleton animation={animation} className="h-4 w-20" />
            <EnhancedSkeleton animation={animation} className="h-10 w-full rounded-md" />
          </div>
        )
      case 'checkbox':
        return (
          <div key={index} className="flex items-center gap-2">
            <EnhancedSkeleton animation={animation} className="w-4 h-4 rounded" />
            <EnhancedSkeleton animation={animation} className="h-4 w-32" />
          </div>
        )
      default:
        return (
          <div key={index} className="space-y-2">
            <EnhancedSkeleton animation={animation} className="h-4 w-20" />
            <EnhancedSkeleton animation={animation} className="h-10 w-full rounded-md" />
          </div>
        )
    }
  }

  return (
    <div className={cn("space-y-6", className)}>
      {Array.from({ length: fields }, (_, i) => {
        const fieldType = fieldTypes[i] || 'input'
        return getFieldSkeleton(fieldType, i)
      })}
      
      {showSubmit && (
        <div className="flex justify-end gap-2 pt-4 border-t">
          <EnhancedSkeleton animation={animation} className="h-10 w-20 rounded-md" />
          <EnhancedSkeleton animation={animation} className="h-10 w-24 rounded-md" />
        </div>
      )}
    </div>
  )
}

// Problem-specific skeleton
interface ProblemSkeletonProps {
  variant?: 'card' | 'list' | 'detail'
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

function ProblemSkeleton({
  variant = 'card',
  animation = 'pulse',
  className = ''
}: ProblemSkeletonProps) {
  if (variant === 'detail') {
    return (
      <div className={cn("space-y-6", className)}>
        {/* Header */}
        <div className="space-y-4">
          <EnhancedSkeleton animation={animation} className="h-8 w-3/4" />
          <div className="flex items-center gap-4">
            <EnhancedSkeleton animation={animation} className="h-6 w-20 rounded-full" />
            <EnhancedSkeleton animation={animation} className="h-6 w-16 rounded-full" />
            <EnhancedSkeleton animation={animation} className="h-6 w-24 rounded-full" />
          </div>
        </div>

        {/* Content */}
        <div className="space-y-4">
          <EnhancedSkeleton animation={animation} className="h-4 w-full" />
          <EnhancedSkeleton animation={animation} className="h-4 w-full" />
          <EnhancedSkeleton animation={animation} className="h-4 w-2/3" />
        </div>

        {/* Metadata */}
        <div className="grid grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
          <div className="space-y-2">
            <EnhancedSkeleton animation={animation} className="h-4 w-16" />
            <EnhancedSkeleton animation={animation} className="h-4 w-24" />
          </div>
          <div className="space-y-2">
            <EnhancedSkeleton animation={animation} className="h-4 w-20" />
            <EnhancedSkeleton animation={animation} className="h-4 w-28" />
          </div>
        </div>

        {/* Solutions section */}
        <div className="space-y-4">
          <EnhancedSkeleton animation={animation} className="h-6 w-32" />
          <div className="space-y-3">
            {Array.from({ length: 2 }, (_, i) => (
              <div key={i} className="p-4 border rounded-lg">
                <div className="flex items-start gap-3">
                  <EnhancedSkeleton animation={animation} className="w-8 h-8 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <EnhancedSkeleton animation={animation} className="h-4 w-2/3" />
                    <EnhancedSkeleton animation={animation} className="h-3 w-1/2" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (variant === 'list') {
    return (
      <div className={cn("space-y-2", className)}>
        <div className="flex items-center gap-4 p-4">
          <div className="flex-1 space-y-2">
            <EnhancedSkeleton animation={animation} className="h-5 w-3/4" />
            <EnhancedSkeleton animation={animation} className="h-4 w-1/2" />
          </div>
          <div className="flex gap-2">
            <EnhancedSkeleton animation={animation} className="h-6 w-16 rounded-full" />
            <EnhancedSkeleton animation={animation} className="h-6 w-20 rounded-full" />
          </div>
        </div>
      </div>
    )
  }

  // Default card variant
  return (
    <CardSkeleton
      rows={2}
      animation={animation}
      className={className}
      showActions={true}
      showMetadata={true}
    />
  )
}

// Expert-specific skeleton
interface ExpertSkeletonProps {
  variant?: 'card' | 'list' | 'profile'
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

function ExpertSkeleton({
  variant = 'card',
  animation = 'pulse',
  className = ''
}: ExpertSkeletonProps) {
  if (variant === 'profile') {
    return (
      <div className={cn("space-y-6", className)}>
        {/* Header */}
        <div className="text-center space-y-4">
          <EnhancedSkeleton animation={animation} className="w-24 h-24 rounded-full mx-auto" />
          <div className="space-y-2">
            <EnhancedSkeleton animation={animation} className="h-6 w-48 mx-auto" />
            <EnhancedSkeleton animation={animation} className="h-4 w-32 mx-auto" />
          </div>
          <div className="flex justify-center gap-2">
            <EnhancedSkeleton animation={animation} className="h-6 w-16 rounded-full" />
            <EnhancedSkeleton animation={animation} className="h-6 w-20 rounded-full" />
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4">
          {Array.from({ length: 3 }, (_, i) => (
            <div key={i} className="text-center space-y-2">
              <EnhancedSkeleton animation={animation} className="h-8 w-12 mx-auto" />
              <EnhancedSkeleton animation={animation} className="h-4 w-16 mx-auto" />
            </div>
          ))}
        </div>

        {/* Sections */}
        <div className="space-y-6">
          {Array.from({ length: 3 }, (_, i) => (
            <div key={i} className="space-y-3">
              <EnhancedSkeleton animation={animation} className="h-5 w-32" />
              <div className="space-y-2">
                <EnhancedSkeleton animation={animation} className="h-4 w-full" />
                <EnhancedSkeleton animation={animation} className="h-4 w-3/4" />
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (variant === 'list') {
    return (
      <div className={cn("flex items-center gap-4 p-4", className)}>
        <EnhancedSkeleton animation={animation} className="w-12 h-12 rounded-full flex-shrink-0" />
        <div className="flex-1 space-y-2">
          <EnhancedSkeleton animation={animation} className="h-4 w-2/3" />
          <EnhancedSkeleton animation={animation} className="h-3 w-1/2" />
        </div>
        <div className="flex items-center gap-2">
          <EnhancedSkeleton animation={animation} className="h-4 w-8" />
          <EnhancedSkeleton animation={animation} className="h-6 w-16 rounded-full" />
        </div>
      </div>
    )
  }

  // Default card variant
  return (
    <div className={cn("p-6 border rounded-lg bg-card", className)}>
      <div className="space-y-4">
        {/* Header with avatar */}
        <div className="text-center space-y-3">
          <EnhancedSkeleton animation={animation} className="w-16 h-16 rounded-full mx-auto" />
          <div className="space-y-2">
            <EnhancedSkeleton animation={animation} className="h-5 w-32 mx-auto" />
            <EnhancedSkeleton animation={animation} className="h-4 w-24 mx-auto" />
          </div>
        </div>

        {/* Stats */}
        <div className="flex justify-between text-sm">
          <EnhancedSkeleton animation={animation} className="h-4 w-16" />
          <EnhancedSkeleton animation={animation} className="h-4 w-12" />
        </div>

        {/* Skills */}
        <div className="space-y-2">
          <EnhancedSkeleton animation={animation} className="h-4 w-20" />
          <div className="flex flex-wrap gap-1">
            {Array.from({ length: 3 }, (_, i) => (
              <EnhancedSkeleton 
                key={i} 
                animation={animation} 
                className="h-6 w-16 rounded-full" 
              />
            ))}
          </div>
        </div>

        {/* Action */}
        <div className="flex justify-between items-center pt-2 border-t">
          <EnhancedSkeleton animation={animation} className="h-4 w-16" />
          <EnhancedSkeleton animation={animation} className="h-8 w-24 rounded-md" />
        </div>
      </div>
    </div>
  )
}

// Search results skeleton
interface SearchResultsSkeletonProps {
  results?: number
  showTabs?: boolean
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

function SearchResultsSkeleton({
  results = 5,
  showTabs = true,
  animation = 'pulse',
  className = ''
}: SearchResultsSkeletonProps) {
  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <EnhancedSkeleton animation={animation} className="h-6 w-24" />
          <EnhancedSkeleton animation={animation} className="h-5 w-16 rounded-full" />
        </div>
        <EnhancedSkeleton animation={animation} className="h-10 w-48 rounded-md" />
      </div>

      {/* Tabs */}
      {showTabs && (
        <div className="flex gap-2">
          {Array.from({ length: 5 }, (_, i) => (
            <EnhancedSkeleton 
              key={i} 
              animation={animation} 
              className="h-10 w-24 rounded-md" 
            />
          ))}
        </div>
      )}

      {/* Results */}
      <div className="space-y-4">
        {Array.from({ length: results }, (_, i) => (
          <div key={i} className="p-6 border rounded-lg bg-card">
            <div className="space-y-4">
              {/* Result header */}
              <div className="flex items-start justify-between">
                <div className="flex-1 space-y-3">
                  <div className="flex items-center gap-2">
                    <EnhancedSkeleton animation={animation} className="w-4 h-4" />
                    <EnhancedSkeleton animation={animation} className="h-5 w-16 rounded-full" />
                  </div>
                  <EnhancedSkeleton animation={animation} className="h-6 w-3/4" />
                  <div className="space-y-2">
                    <EnhancedSkeleton animation={animation} className="h-4 w-full" />
                    <EnhancedSkeleton animation={animation} className="h-4 w-2/3" />
                  </div>
                </div>
                <EnhancedSkeleton animation={animation} className="h-8 w-16" />
              </div>

              {/* Tags */}
              <div className="flex gap-2">
                {Array.from({ length: 3 }, (_, j) => (
                  <EnhancedSkeleton 
                    key={j} 
                    animation={animation} 
                    className="h-6 w-16 rounded-full" 
                  />
                ))}
              </div>

              {/* Metadata */}
              <div className="flex items-center gap-4 text-sm">
                <EnhancedSkeleton animation={animation} className="h-4 w-20" />
                <EnhancedSkeleton animation={animation} className="h-4 w-16" />
                <EnhancedSkeleton animation={animation} className="h-4 w-24" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <EnhancedSkeleton animation={animation} className="h-4 w-32" />
        <div className="flex items-center gap-2">
          <EnhancedSkeleton animation={animation} className="h-8 w-16 rounded-md" />
          <EnhancedSkeleton animation={animation} className="h-8 w-8 rounded-md" />
          <EnhancedSkeleton animation={animation} className="h-8 w-8 rounded-md" />
          <EnhancedSkeleton animation={animation} className="h-8 w-8 rounded-md" />
          <EnhancedSkeleton animation={animation} className="h-8 w-16 rounded-md" />
        </div>
      </div>
    </div>
  )
}

export {
  EnhancedSkeleton,
  CardSkeleton,
  ListSkeleton,
  FormSkeleton,
  ProblemSkeleton,
  ExpertSkeleton,
  SearchResultsSkeleton
}

// Additional exports for backward compatibility
export { FormSkeleton as FormSkeletonComponent }