/**
 * Expert Matches Component
 * Displays expert matches for problems and allows expert responses
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { MessageSquare, AlertCircle } from 'lucide-react';

interface ExpertMatchesProps {
  expertId?: string;
  problemId?: string;
  showPendingOnly?: boolean;
  onMatchResponse?: (match: any) => void;
}

export const ExpertMatches: React.FC<ExpertMatchesProps> = ({
  expertId,
  problemId,
  showPendingOnly = false,
  onMatchResponse
}) => {
  const [matches, setMatches] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadMatches();
  }, [expertId, problemId, showPendingOnly]);

  const loadMatches = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Temporary mock data for development
      const mockData: any[] = [];
      setMatches(mockData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load matches');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <AlertCircle className="h-8 w-8 mx-auto mb-2" />
            <p>{error}</p>
            <Button variant="outline" onClick={loadMatches} className="mt-2">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (matches.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            <MessageSquare className="h-8 w-8 mx-auto mb-2" />
            <p>
              {showPendingOnly 
                ? 'No pending matches found' 
                : 'No matches found'
              }
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Expert Matches</CardTitle>
          <CardDescription>
            Expert matching functionality is being developed
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600">
            This component will display expert matches when the matching system is fully implemented.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};