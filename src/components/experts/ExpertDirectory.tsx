import { useState, useEffect, use<PERSON>emo, use<PERSON><PERSON>back, memo } from 'react'
import { Search, Filter, MapPin, Award, ChevronRight, Star, Clock, Users, ExternalLink, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useToast } from '@/hooks/use-toast'
import { useDeviceType } from '@/hooks/use-mobile'
import { expertOperations } from '@/lib/database'
import { GlobalSearch } from '@/components/search/GlobalSearch'
import { searchService } from '@/lib/search'
import { SearchFilters as SearchFiltersType } from '@/lib/search'
import { ExpertDirectorySkeleton } from './ExpertSkeleton'
import { DataErrorBoundary } from '@/utils/errorBoundaryHelpers'
import { Link } from 'react-router-dom'

interface Expert {
  id: string
  user_id: string
  expertise_areas: Array<{
    category: string
    skills: string[]
    proficiencyLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert'
    yearsOfExperience: number
  }>
  experience_years: number
  availability: 'available' | 'busy' | 'unavailable'
  rating: number
  total_contributions: number
  success_rate: number
  response_time_hours: number
  portfolio: Array<{
    title: string
    description: string
    technologies: string[]
    url?: string
    completedAt: string
  }>
  certifications: Array<{
    name: string
    issuer: string
    issuedAt: string
    expiresAt?: string
    credentialId?: string
    url?: string
  }>
  users: {
    id: string
    name: string
    email: string
    avatar?: string
    bio?: string
    location: string
    organization?: string
    position?: string
  }
}

interface ExpertFilters {
  searchQuery: string
  selectedCategories: string[]
  selectedSkills: string[]
  availability: string[]
  minRating: number
  maxResponseTime: number
  location: string
  minExperience: number
  sortBy: 'rating' | 'contributions' | 'response_time' | 'experience'
  sortOrder: 'asc' | 'desc'
}

const availabilityLabels = {
  available: 'متاح',
  busy: 'مشغول',
  unavailable: 'غير متاح'
}

const availabilityColors = {
  available: 'bg-green-500',
  busy: 'bg-yellow-500',
  unavailable: 'bg-red-500'
}

const proficiencyLabels = {
  beginner: 'مبتدئ',
  intermediate: 'متوسط',
  advanced: 'متقدم',
  expert: 'خبير'
}

const sortOptions = [
  { value: 'rating', label: 'التقييم' },
  { value: 'contributions', label: 'عدد المساهمات' },
  { value: 'response_time', label: 'وقت الاستجابة' },
  { value: 'experience', label: 'سنوات الخبرة' }
]

const ExpertDirectoryComponent = memo(function ExpertDirectory() {
  const { toast } = useToast()
  const { isMobile } = useDeviceType()
  const [experts, setExperts] = useState<Expert[]>([])
  const [loading, setLoading] = useState(true)
  const [filters, setFilters] = useState<ExpertFilters>({
    searchQuery: '',
    selectedCategories: [],
    selectedSkills: [],
    availability: ['available'],
    minRating: 0,
    maxResponseTime: 168,
    location: '',
    minExperience: 0,
    sortBy: 'rating',
    sortOrder: 'desc'
  })

  // Extract unique categories and skills from experts
  const { categories, skills, locations } = useMemo(() => {
    const categoriesSet = new Set<string>()
    const skillsSet = new Set<string>()
    const locationsSet = new Set<string>()

    experts.forEach(expert => {
      expert.expertise_areas.forEach(area => {
        categoriesSet.add(area.category)
        area.skills.forEach(skill => skillsSet.add(skill))
      })
      if (expert.users.location) {
        locationsSet.add(expert.users.location)
      }
    })

    return {
      categories: Array.from(categoriesSet).sort(),
      skills: Array.from(skillsSet).sort(),
      locations: Array.from(locationsSet).sort()
    }
  }, [experts])

  // Filter and sort experts
  const filteredExperts = useMemo(() => {
    let filtered = experts.filter(expert => {
      // Search query
      if (filters.searchQuery) {
        const query = filters.searchQuery.toLowerCase()
        const matchesName = expert.users.name.toLowerCase().includes(query)
        const matchesBio = expert.users.bio?.toLowerCase().includes(query)
        const matchesOrg = expert.users.organization?.toLowerCase().includes(query)
        const matchesPosition = expert.users.position?.toLowerCase().includes(query)
        const matchesSkills = expert.expertise_areas.some(area =>
          area.skills.some(skill => skill.toLowerCase().includes(query))
        )
        const matchesCategories = expert.expertise_areas.some(area =>
          area.category.toLowerCase().includes(query)
        )

        if (!matchesName && !matchesBio && !matchesOrg && !matchesPosition && !matchesSkills && !matchesCategories) {
          return false
        }
      }

      // Categories filter
      if (filters.selectedCategories.length > 0) {
        const hasMatchingCategory = expert.expertise_areas.some(area =>
          filters.selectedCategories.includes(area.category)
        )
        if (!hasMatchingCategory) return false
      }

      // Skills filter
      if (filters.selectedSkills.length > 0) {
        const hasMatchingSkill = expert.expertise_areas.some(area =>
          area.skills.some(skill => filters.selectedSkills.includes(skill))
        )
        if (!hasMatchingSkill) return false
      }

      // Availability filter
      if (filters.availability.length > 0 && !filters.availability.includes(expert.availability)) {
        return false
      }

      // Rating filter
      if (expert.rating < filters.minRating) return false

      // Response time filter
      if (expert.response_time_hours > filters.maxResponseTime) return false

      // Location filter
      if (filters.location && !expert.users.location.toLowerCase().includes(filters.location.toLowerCase())) {
        return false
      }

      // Experience filter
      if (expert.experience_years < filters.minExperience) return false

      return true
    })

    // Sort experts
    filtered.sort((a, b) => {
      let aValue: number
      let bValue: number

      switch (filters.sortBy) {
        case 'rating':
          aValue = a.rating
          bValue = b.rating
          break
        case 'contributions':
          aValue = a.total_contributions
          bValue = b.total_contributions
          break
        case 'response_time':
          aValue = a.response_time_hours
          bValue = b.response_time_hours
          break
        case 'experience':
          aValue = a.experience_years
          bValue = b.experience_years
          break
        default:
          aValue = a.rating
          bValue = b.rating
      }

      return filters.sortOrder === 'desc' ? bValue - aValue : aValue - bValue
    })

    return filtered
  }, [experts, filters])

  useEffect(() => {
    loadExperts()
  }, [])

  const loadExperts = async () => {
    try {
      setLoading(true)

      // Try to use search service for enhanced expert discovery
      if (filters.searchQuery.trim()) {
        try {
          const searchFilters: SearchFiltersType = {
            contentType: ['expert'],
            ...(filters.selectedCategories.length > 0 && { categories: filters.selectedCategories }),
            ...(filters.selectedSkills.length > 0 && { skills: filters.selectedSkills }),
            ...(filters.availability.length > 0 && { availability: filters.availability }),
            ...(filters.location && { location: filters.location }),
            ...(filters.minRating > 0 && { rating: { min: filters.minRating } }),
            ...(filters.minExperience > 0 && { experience: { min: filters.minExperience } })
          };

          const searchResults = await searchService.search({
            text: filters.searchQuery,
            filters: searchFilters,
            sortBy: filters.sortBy === 'rating' ? 'rating' :
              filters.sortBy === 'contributions' ? 'popularity' :
                filters.sortBy === 'experience' ? 'date' : 'relevance',
            pagination: { limit: 100, offset: 0 },
            language: 'auto'
          });

          // Convert search results to Expert format
          const searchExperts = searchResults.items
            .filter(item => item.type === 'expert')
            .map(item => ({
              id: item.id,
              user_id: item.id,
              expertise_areas: item.metadata?.expertise_areas || [],
              experience_years: item.metadata?.experience_years || 0,
              availability: item.metadata?.availability || 'available',
              rating: item.metadata?.rating || 0,
              total_contributions: item.metadata?.total_contributions || 0,
              success_rate: item.metadata?.success_rate || 0,
              response_time_hours: item.metadata?.response_time_hours || 24,
              portfolio: item.metadata?.portfolio || [],
              certifications: item.metadata?.certifications || [],
              users: {
                id: item.id,
                name: item.title,
                email: item.metadata?.email || '',
                avatar: item.metadata?.avatar,
                bio: item.description,
                location: item.metadata?.location || '',
                organization: item.metadata?.organization,
                position: item.metadata?.position
              }
            }));

          if (searchExperts.length > 0) {
            setExperts(searchExperts);
            setLoading(false);
            return;
          }
        } catch (searchError) {
          console.warn('Search service not available, falling back to database:', searchError);
        }
      }

      // Fallback to database query
      const { data, error } = await expertOperations.getAllExperts()

      if (error) {
        throw new Error(error.message)
      }

      setExperts(data || [])
    } catch (error) {
      console.error('Error loading experts:', error)
      toast({
        title: 'خطأ في تحميل البيانات',
        description: 'حدث خطأ أثناء تحميل قائمة الخبراء',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const updateFilter = useCallback((key: keyof ExpertFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }, [])

  const toggleCategoryFilter = useCallback((category: string) => {
    setFilters(prev => ({
      ...prev,
      selectedCategories: prev.selectedCategories.includes(category)
        ? prev.selectedCategories.filter(c => c !== category)
        : [...prev.selectedCategories, category]
    }))
  }, [])

  const toggleSkillFilter = useCallback((skill: string) => {
    setFilters(prev => ({
      ...prev,
      selectedSkills: prev.selectedSkills.includes(skill)
        ? prev.selectedSkills.filter(s => s !== skill)
        : [...prev.selectedSkills, skill]
    }))
  }, [])

  const toggleAvailabilityFilter = useCallback((availability: string) => {
    setFilters(prev => ({
      ...prev,
      availability: prev.availability.includes(availability)
        ? prev.availability.filter(a => a !== availability)
        : [...prev.availability, availability]
    }))
  }, [])

  const clearFilters = useCallback(() => {
    setFilters({
      searchQuery: '',
      selectedCategories: [],
      selectedSkills: [],
      availability: ['available'],
      minRating: 0,
      maxResponseTime: 168,
      location: '',
      minExperience: 0,
      sortBy: 'rating',
      sortOrder: 'desc'
    })
  }, [])

  const getExpertInitials = useCallback((name: string) => {
    const parts = name.split(' ')
    return parts.length >= 2 ? `${parts[0][0]}${parts[1][0]}` : name[0]
  }, [])

  if (loading) {
    return (
      <ExpertDirectorySkeleton
        cardCount={isMobile ? 4 : 6}
        showFilters={true}
        animation="shimmer"
      />
    )
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Enhanced Search Integration */}
      <Card>
        <CardContent className={isMobile ? 'p-4' : 'p-6'}>
          <div className={`${isMobile ? 'flex flex-col gap-3' : 'flex flex-col lg:flex-row gap-4'}`}>
            {/* Enhanced Search Component */}
            <div className="flex-1 mb-4">
              <GlobalSearch />
            </div>

            {/* Traditional Search Input */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  type="text"
                  placeholder="ابحث عن خبير بالاسم، المهارات، أو التخصص..."
                  value={filters.searchQuery}
                  onChange={(e) => updateFilter('searchQuery', e.target.value)}
                  className={`pr-10 ${isMobile ? 'text-base' : ''} touch-manipulation`}
                />
              </div>
            </div>

            {/* Sort Options */}
            <div className={`flex gap-2 ${isMobile ? 'flex-col' : ''}`}>
              <Select value={filters.sortBy} onValueChange={(value) => updateFilter('sortBy', value)}>
                <SelectTrigger className={isMobile ? 'w-full' : 'w-40'}>
                  <SelectValue placeholder="ترتيب حسب" />
                </SelectTrigger>
                <SelectContent>
                  {sortOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="icon"
                onClick={() => updateFilter('sortOrder', filters.sortOrder === 'desc' ? 'asc' : 'desc')}
                className={`${isMobile ? 'w-full' : ''} touch-manipulation`}
              >
                {filters.sortOrder === 'desc' ? '↓' : '↑'}
              </Button>

              {/* Advanced Filters Sheet */}
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="outline" className={`${isMobile ? 'w-full' : ''} touch-manipulation`}>
                    <Filter className="w-4 h-4 ml-2" />
                    فلترة متقدمة
                    {(filters.selectedCategories.length + filters.selectedSkills.length +
                      (filters.availability.length !== 1 ? 1 : 0) +
                      (filters.minRating > 0 ? 1 : 0) +
                      (filters.maxResponseTime < 168 ? 1 : 0) +
                      (filters.location ? 1 : 0) +
                      (filters.minExperience > 0 ? 1 : 0)) > 0 && (
                        <Badge variant="secondary" className="mr-2">
                          {filters.selectedCategories.length + filters.selectedSkills.length +
                            (filters.availability.length !== 1 ? 1 : 0) +
                            (filters.minRating > 0 ? 1 : 0) +
                            (filters.maxResponseTime < 168 ? 1 : 0) +
                            (filters.location ? 1 : 0) +
                            (filters.minExperience > 0 ? 1 : 0)}
                        </Badge>
                      )}
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className={isMobile ? 'w-full' : 'w-96'}>
                  <SheetHeader>
                    <SheetTitle>فلترة متقدمة</SheetTitle>
                    <SheetDescription>
                      استخدم الفلاتر التالية للعثور على الخبراء المناسبين
                    </SheetDescription>
                  </SheetHeader>

                  <div className="space-y-6 mt-6">
                    {/* Categories Filter */}
                    <div>
                      <h4 className="font-medium mb-3">مجالات الخبرة</h4>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {categories.map(category => (
                          <div key={category} className="flex items-center space-x-2 space-x-reverse">
                            <Checkbox
                              id={`category-${category}`}
                              checked={filters.selectedCategories.includes(category)}
                              onCheckedChange={() => toggleCategoryFilter(category)}
                            />
                            <label htmlFor={`category-${category}`} className="text-sm">
                              {category}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    {/* Skills Filter */}
                    <div>
                      <h4 className="font-medium mb-3">المهارات التقنية</h4>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {skills.slice(0, 20).map(skill => (
                          <div key={skill} className="flex items-center space-x-2 space-x-reverse">
                            <Checkbox
                              id={`skill-${skill}`}
                              checked={filters.selectedSkills.includes(skill)}
                              onCheckedChange={() => toggleSkillFilter(skill)}
                            />
                            <label htmlFor={`skill-${skill}`} className="text-sm">
                              {skill}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    {/* Availability Filter */}
                    <div>
                      <h4 className="font-medium mb-3">حالة التوفر</h4>
                      <div className="space-y-2">
                        {Object.entries(availabilityLabels).map(([value, label]) => (
                          <div key={value} className="flex items-center space-x-2 space-x-reverse">
                            <Checkbox
                              id={`availability-${value}`}
                              checked={filters.availability.includes(value)}
                              onCheckedChange={() => toggleAvailabilityFilter(value)}
                            />
                            <label htmlFor={`availability-${value}`} className="text-sm">
                              {label}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    {/* Rating Filter */}
                    <div>
                      <h4 className="font-medium mb-3">الحد الأدنى للتقييم</h4>
                      <div className="px-2">
                        <Slider
                          value={[filters.minRating]}
                          onValueChange={([value]) => updateFilter('minRating', value)}
                          max={5}
                          min={0}
                          step={0.5}
                          className="w-full"
                        />
                        <div className="flex justify-between text-sm text-gray-500 mt-1">
                          <span>0</span>
                          <span className="font-medium">{filters.minRating}</span>
                          <span>5</span>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* Response Time Filter */}
                    <div>
                      <h4 className="font-medium mb-3">الحد الأقصى لوقت الاستجابة (ساعة)</h4>
                      <div className="px-2">
                        <Slider
                          value={[filters.maxResponseTime]}
                          onValueChange={([value]) => updateFilter('maxResponseTime', value)}
                          max={168}
                          min={1}
                          step={1}
                          className="w-full"
                        />
                        <div className="flex justify-between text-sm text-gray-500 mt-1">
                          <span>1</span>
                          <span className="font-medium">{filters.maxResponseTime}</span>
                          <span>168</span>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* Experience Filter */}
                    <div>
                      <h4 className="font-medium mb-3">الحد الأدنى لسنوات الخبرة</h4>
                      <div className="px-2">
                        <Slider
                          value={[filters.minExperience]}
                          onValueChange={([value]) => updateFilter('minExperience', value)}
                          max={30}
                          min={0}
                          step={1}
                          className="w-full"
                        />
                        <div className="flex justify-between text-sm text-gray-500 mt-1">
                          <span>0</span>
                          <span className="font-medium">{filters.minExperience}</span>
                          <span>30+</span>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* Location Filter */}
                    <div>
                      <h4 className="font-medium mb-3">الموقع</h4>
                      <Input
                        placeholder="ابحث بالموقع..."
                        value={filters.location}
                        onChange={(e) => updateFilter('location', e.target.value)}
                      />
                    </div>

                    {/* Clear Filters */}
                    <Button variant="outline" onClick={clearFilters} className="w-full">
                      مسح جميع الفلاتر
                    </Button>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">
          الخبراء المتاحون ({filteredExperts.length})
        </h2>
        {filters.selectedCategories.length > 0 || filters.selectedSkills.length > 0 ? (
          <div className="flex flex-wrap gap-2">
            {filters.selectedCategories.map(category => (
              <Badge key={category} variant="secondary" className="flex items-center gap-1">
                {category}
                <X className="w-3 h-3 cursor-pointer" onClick={() => toggleCategoryFilter(category)} />
              </Badge>
            ))}
            {filters.selectedSkills.map(skill => (
              <Badge key={skill} variant="outline" className="flex items-center gap-1">
                {skill}
                <X className="w-3 h-3 cursor-pointer" onClick={() => toggleSkillFilter(skill)} />
              </Badge>
            ))}
          </div>
        ) : null}
      </div>

      {/* Experts Grid */}
      {filteredExperts.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              لا توجد نتائج
            </h3>
            <p className="text-gray-600 mb-4">
              لم يتم العثور على خبراء يطابقون معايير البحث الحالية
            </p>
            <Button variant="outline" onClick={clearFilters}>
              مسح الفلاتر
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className={`grid grid-cols-1 gap-4 ${isMobile ? '' : 'md:grid-cols-2 lg:grid-cols-3 gap-6'}`}>
          {filteredExperts.map((expert) => (
            <Card key={expert.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="text-center">
                <div className="relative mx-auto mb-4">
                  <Avatar className="w-20 h-20">
                    <AvatarImage src={expert.users.avatar} alt={expert.users.name} />
                    <AvatarFallback className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white text-lg font-bold">
                      {getExpertInitials(expert.users.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full border-2 border-white ${availabilityColors[expert.availability]}`}></div>
                </div>
                <CardTitle className="text-lg">{expert.users.name}</CardTitle>
                <CardDescription className="text-blue-600">
                  {expert.users.position || 'خبير تقني'}
                  {expert.users.organization && ` - ${expert.users.organization}`}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center text-sm text-gray-600">
                  <MapPin className="w-4 h-4 ml-2" />
                  {expert.users.location}
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">الخبرة: {expert.experience_years} سنة</span>
                  <div className="flex items-center text-yellow-600">
                    <Star className="w-4 h-4 ml-1" />
                    {expert.rating.toFixed(1)}
                  </div>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 flex items-center">
                    <Clock className="w-4 h-4 ml-1" />
                    {expert.response_time_hours}ساعة
                  </span>
                  <span className="text-green-600">
                    {expert.total_contributions} مساهمة
                  </span>
                </div>

                {/* Expertise Areas */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">مجالات الخبرة:</h4>
                  <div className="flex flex-wrap gap-1">
                    {expert.expertise_areas.slice(0, 2).map((area, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {area.category}
                      </Badge>
                    ))}
                    {expert.expertise_areas.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{expert.expertise_areas.length - 2}
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Top Skills */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">المهارات الرئيسية:</h4>
                  <div className="flex flex-wrap gap-1">
                    {expert.expertise_areas
                      .flatMap(area => area.skills)
                      .slice(0, 3)
                      .map((skill, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                  </div>
                </div>

                {expert.users.bio && (
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {expert.users.bio}
                  </p>
                )}

                <div className="flex justify-between items-center pt-4 border-t">
                  <div className="flex items-center text-sm">
                    <div className={`w-2 h-2 rounded-full ${availabilityColors[expert.availability]} ml-2`}></div>
                    <span className="text-gray-600">
                      {availabilityLabels[expert.availability]}
                    </span>
                  </div>
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700" asChild>
                    <Link to={`/experts/${expert.user_id}`}>
                      عرض الملف الشخصي
                      <ChevronRight className="w-4 h-4 mr-2" />
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
})

// Export wrapped component with error boundary
export const ExpertDirectory = () => (
  <DataErrorBoundary dataType="Experts">
    <ExpertDirectoryComponent />
  </DataErrorBoundary>
)