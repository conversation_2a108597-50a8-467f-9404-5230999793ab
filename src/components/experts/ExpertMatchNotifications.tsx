/**
 * Expert Match Notifications Component
 * Shows real-time notifications for new expert matches
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { 
  Bell, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Star,
  AlertCircle,
  Eye,
  X
} from 'lucide-react';
import { problemExpertMatchesAPI, expertMatchingUtils } from '../../lib/expertMatching';
import type { ProblemExpertMatch } from '../../types/user';
import { formatDistanceToNow } from 'date-fns';
import { Link } from 'react-router-dom';

interface ExpertMatchNotificationsProps {
  expertId: string;
  onMatchResponse?: (match: ProblemExpertMatch) => void;
  maxNotifications?: number;
}

export const ExpertMatchNotifications: React.FC<ExpertMatchNotificationsProps> = ({
  expertId,
  onMatchResponse,
  maxNotifications = 5
}) => {
  const [notifications, setNotifications] = useState<ProblemExpertMatch[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dismissedIds, setDismissedIds] = useState<Set<string>>(new Set());
  const [respondingTo, setRespondingTo] = useState<string | null>(null);

  useEffect(() => {
    loadNotifications();
    
    // Set up polling for new matches every 30 seconds
    const interval = setInterval(loadNotifications, 30000);
    
    return () => clearInterval(interval);
  }, [expertId]);

  const loadNotifications = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const matches = await problemExpertMatchesAPI.getPendingMatchesForExpert(expertId);
      
      // Filter out dismissed notifications and limit count
      const filteredMatches = matches
        .filter(match => !dismissedIds.has(match.id))
        .slice(0, maxNotifications);
      
      setNotifications(filteredMatches);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load notifications');
    } finally {
      setLoading(false);
    }
  };

  const handleMatchResponse = async (
    matchId: string, 
    response: 'accepted' | 'declined',
    interestLevel?: number
  ) => {
    try {
      setRespondingTo(matchId);
      
      const updatedMatch = await problemExpertMatchesAPI.respondToMatch(matchId, {
        expert_response_status: response,
        expert_interest_level: interestLevel
      });
      
      // Remove from notifications
      setNotifications(prev => prev.filter(n => n.id !== matchId));
      
      onMatchResponse?.(updatedMatch);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to respond to match');
    } finally {
      setRespondingTo(null);
    }
  };

  const dismissNotification = (matchId: string) => {
    setDismissedIds(prev => new Set([...prev, matchId]));
    setNotifications(prev => prev.filter(n => n.id !== matchId));
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default:
        return 'text-blue-600 bg-blue-50 border-blue-200';
    }
  };

  const getMatchScoreColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-blue-600';
    if (score >= 0.4) return 'text-yellow-600';
    return 'text-gray-600';
  };

  if (loading && notifications.length === 0) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-sm text-gray-600">Loading notifications...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (notifications.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Bell className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <p className="text-sm text-gray-500">No new match notifications</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-3">
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <span className="text-sm text-red-600">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {notifications.map((match) => (
        <Card key={match.id} className="border-l-4 border-l-blue-500 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-full">
                  <Star className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-base">New Problem Match</CardTitle>
                  <CardDescription className="flex items-center gap-2">
                    <span>{match.problems?.title}</span>
                    <Badge 
                      variant="outline" 
                      className={getUrgencyColor(match.problems?.urgency || 'medium')}
                    >
                      {match.problems?.urgency}
                    </Badge>
                  </CardDescription>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <div className={`text-sm font-semibold ${getMatchScoreColor(match.match_score)}`}>
                  {expertMatchingUtils.formatMatchScore(match.match_score)}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => dismissNotification(match.id)}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* Problem Details */}
            <div className="text-sm text-gray-600">
              <p className="line-clamp-2">{match.problems?.description}</p>
              <div className="flex items-center gap-4 mt-2 text-xs">
                <span>Category: {match.problems?.category}</span>
                <span>Sector: {match.problems?.sector}</span>
                <span>
                  Created {formatDistanceToNow(new Date(match.problems?.created_at || ''), { addSuffix: true })}
                </span>
              </div>
            </div>

            {/* Match Reasons */}
            {match.match_reasons && match.match_reasons.length > 0 && (
              <div>
                <h5 className="text-xs font-medium text-gray-700 mb-1">Why this matches you:</h5>
                <div className="flex flex-wrap gap-1">
                  {match.match_reasons.map((reason, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {reason}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Time Remaining */}
            <div className="flex items-center gap-2 text-xs text-gray-500">
              <Clock className="h-3 w-3" />
              <span>
                Expires {formatDistanceToNow(new Date(match.expires_at), { addSuffix: true })}
              </span>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 pt-2">
              <Button
                size="sm"
                onClick={() => handleMatchResponse(match.id, 'accepted', 5)}
                disabled={respondingTo === match.id}
                className="flex-1"
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                Accept
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleMatchResponse(match.id, 'declined')}
                disabled={respondingTo === match.id}
                className="flex-1"
              >
                <XCircle className="h-3 w-3 mr-1" />
                Decline
              </Button>
              <Button
                size="sm"
                variant="ghost"
                asChild
                className="px-3"
              >
                <Link to={`/problems/${match.problem_id}`}>
                  <Eye className="h-3 w-3" />
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}

      {notifications.length >= maxNotifications && (
        <Card>
          <CardContent className="p-4 text-center">
            <p className="text-sm text-gray-500">
              Showing {maxNotifications} most recent notifications
            </p>
            <Button variant="link" size="sm" asChild>
              <Link to="/experts/dashboard?tab=matches">
                View all matches
              </Link>
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};