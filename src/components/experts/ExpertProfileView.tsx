import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { expertOperations, solutionOperations } from '@/lib/database';
import { useAuthContext } from '@/components/auth/AuthProvider';
import { 
  ArrowRight,
  Calendar,
  MapPin,
  Building,
  Phone,
  Mail,
  Star,
  Award,
  Clock,
  CheckCircle,
  FileText,
  Users,
  TrendingUp,
  Loader2,
  AlertCircle,
  MessageSquare,
  ThumbsUp,
  ExternalLink,
  Edit
} from 'lucide-react';

interface ExpertProfile {
  id: string;
  user_id: string;
  expertise_areas: any[];
  experience_years: number;
  availability: 'available' | 'busy' | 'unavailable';
  rating: number;
  total_contributions: number;
  success_rate: number;
  response_time_hours: number;
  portfolio: any[];
  certifications: any[];
  created_at: string;
  updated_at: string;
  users: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    bio?: string;
    location: string;
    phone_number?: string;
    organization?: string;
    position?: string;
    languages: string[];
  };
}

interface Solution {
  id: string;
  problem_id: string;
  content: string;
  status: 'draft' | 'submitted' | 'approved' | 'implemented';
  rating: number;
  created_at: string;
  problems?: {
    id: string;
    title: string;
    category: string;
    sector: string;
    urgency: string;
    status: string;
  };
}

interface ExpertProfileViewProps {
  expert: ExpertProfile;
}

const AVAILABILITY_CONFIG = {
  available: { label: 'متاح', color: 'bg-green-100 text-green-800', icon: '🟢' },
  busy: { label: 'مشغول', color: 'bg-yellow-100 text-yellow-800', icon: '🟡' },
  unavailable: { label: 'غير متاح', color: 'bg-red-100 text-red-800', icon: '🔴' }
};

const SOLUTION_STATUS_CONFIG = {
  draft: { label: 'مسودة', color: 'bg-gray-100 text-gray-800' },
  submitted: { label: 'مرسل', color: 'bg-blue-100 text-blue-800' },
  approved: { label: 'معتمد', color: 'bg-green-100 text-green-800' },
  implemented: { label: 'مطبق', color: 'bg-purple-100 text-purple-800' }
};

export function ExpertProfileView({ expert }: ExpertProfileViewProps) {
  const [solutions, setSolutions] = useState<Solution[]>([]);
  const [solutionsLoading, setSolutionsLoading] = useState(false);
  
  const { toast } = useToast();
  const { user } = useAuthContext();

  const fetchSolutions = async () => {
    setSolutionsLoading(true);
    try {
      const { data, error } = await solutionOperations.getSolutionsForExpert(expert.user_id);
      
      if (error) {
        throw error;
      }
      
      setSolutions(data || []);
    } catch (error) {
      console.error('Error fetching solutions:', error);
      toast({
        title: "خطأ في تحميل الحلول",
        description: "حدث خطأ أثناء تحميل حلول الخبير",
        variant: "destructive",
      });
    } finally {
      setSolutionsLoading(false);
    }
  };

  useEffect(() => {
    fetchSolutions();
  }, [expert.user_id]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStarRating = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push(<Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />);
    }
    
    if (hasHalfStar) {
      stars.push(<Star key="half" className="w-4 h-4 fill-yellow-200 text-yellow-400" />);
    }
    
    const remainingStars = 5 - Math.ceil(rating);
    for (let i = 0; i < remainingStars; i++) {
      stars.push(<Star key={`empty-${i}`} className="w-4 h-4 text-gray-300" />);
    }
    
    return stars;
  };



  const isOwnProfile = user?.id === expert.user_id;

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6" dir="rtl">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
        <Link to="/experts" className="hover:text-blue-600">الخبراء</Link>
        <ArrowRight className="w-4 h-4" />
        <span className="text-gray-900">{expert.users?.name}</span>
      </nav>

      {/* Expert Header */}
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row gap-6">
            {/* Avatar and Basic Info */}
            <div className="flex items-start gap-4">
              <Avatar className="w-24 h-24">
                <AvatarImage src={expert.users?.avatar} />
                <AvatarFallback className="text-2xl">
                  {expert.users?.name?.charAt(0) || 'خ'}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-2xl font-bold text-gray-900">
                    {expert.users.name}
                  </h1>
                  <Badge className={AVAILABILITY_CONFIG[expert.availability].color}>
                    {AVAILABILITY_CONFIG[expert.availability].icon} {AVAILABILITY_CONFIG[expert.availability].label}
                  </Badge>
                </div>
                
                {expert.users.position && expert.users.organization && (
                  <p className="text-lg text-gray-700 mb-2">
                    {expert.users.position} في {expert.users.organization}
                  </p>
                )}
                
                <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                  <div className="flex items-center gap-1">
                    <MapPin className="w-4 h-4" />
                    {expert.users.location}
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    انضم في {formatDate(expert.created_at)}
                  </div>
                </div>

                {/* Rating */}
                <div className="flex items-center gap-2 mb-3">
                  <div className="flex items-center gap-1">
                    {getStarRating(expert.rating)}
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    {expert.rating.toFixed(1)} ({expert.total_contributions} مساهمة)
                  </span>
                </div>

                {/* Bio */}
                {expert.users.bio && (
                  <p className="text-gray-700 leading-relaxed mb-4">
                    {expert.users.bio}
                  </p>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="flex flex-col gap-2 md:items-end">
              {isOwnProfile ? (
                <Button asChild>
                  <Link to="/experts/profile/create">
                    <Edit className="w-4 h-4 ml-2" />
                    تعديل الملف
                  </Link>
                </Button>
              ) : (
                <div className="flex flex-col gap-2">
                  <Button>
                    <MessageSquare className="w-4 h-4 ml-2" />
                    إرسال رسالة
                  </Button>
                  <Button variant="outline">
                    <Mail className="w-4 h-4 ml-2" />
                    التواصل
                  </Button>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{expert.total_contributions}</p>
                <p className="text-sm text-gray-600">مساهمة</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{expert.success_rate.toFixed(0)}%</p>
                <p className="text-sm text-gray-600">معدل النجاح</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Clock className="w-5 h-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{expert.response_time_hours}</p>
                <p className="text-sm text-gray-600">ساعة متوسط الرد</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{expert.experience_years}</p>
                <p className="text-sm text-gray-600">سنة خبرة</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Information Tabs */}
      <Tabs defaultValue="expertise" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="expertise">الخبرات</TabsTrigger>
          <TabsTrigger value="solutions">الحلول</TabsTrigger>
          <TabsTrigger value="portfolio">الأعمال</TabsTrigger>
          <TabsTrigger value="contact">التواصل</TabsTrigger>
        </TabsList>

        {/* Expertise Tab */}
        <TabsContent value="expertise" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>مجالات الخبرة</CardTitle>
              <CardDescription>
                التخصصات والمهارات التقنية للخبير
              </CardDescription>
            </CardHeader>
            <CardContent>
              {expert.expertise_areas && expert.expertise_areas.length > 0 ? (
                <div className="space-y-4">
                  {expert.expertise_areas.map((area: any, index: number) => (
                    <div key={index} className="border rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2">{area.category}</h4>
                      <div className="flex flex-wrap gap-2 mb-2">
                        {area.skills?.map((skill: string, skillIndex: number) => (
                          <Badge key={skillIndex} variant="secondary">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span>المستوى: {area.proficiencyLevel}</span>
                        <span>الخبرة: {area.yearsOfExperience} سنة</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-600 text-center py-8">
                  لم يتم إضافة مجالات خبرة بعد
                </p>
              )}
            </CardContent>
          </Card>

          {/* Certifications */}
          {expert.certifications && expert.certifications.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>الشهادات والمؤهلات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {expert.certifications.map((cert: any, index: number) => (
                    <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                      <Award className="w-5 h-5 text-yellow-600" />
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">{cert.name}</p>
                        <p className="text-sm text-gray-600">{cert.issuer}</p>
                        {cert.date && (
                          <p className="text-xs text-gray-500">{formatDate(cert.date)}</p>
                        )}
                      </div>
                      {cert.url && (
                        <Button variant="ghost" size="sm" asChild>
                          <a href={cert.url} target="_blank" rel="noopener noreferrer">
                            <ExternalLink className="w-4 h-4" />
                          </a>
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Solutions Tab */}
        <TabsContent value="solutions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>الحلول المقدمة ({solutions.length})</CardTitle>
              <CardDescription>
                الحلول التي قدمها الخبير للمشاكل التقنية
              </CardDescription>
            </CardHeader>
            <CardContent>
              {solutionsLoading ? (
                <div className="flex justify-center py-8">
                  <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
                </div>
              ) : solutions.length > 0 ? (
                <div className="space-y-4">
                  {solutions.map((solution) => (
                    <div key={solution.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex-1">
                          <Link 
                            to={`/problems/${solution.problem_id}`}
                            className="font-medium text-blue-600 hover:text-blue-800"
                          >
                            {solution.problems?.title}
                          </Link>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {solution.problems?.category}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {solution.problems?.sector}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={SOLUTION_STATUS_CONFIG[solution.status].color}>
                            {SOLUTION_STATUS_CONFIG[solution.status].label}
                          </Badge>
                          {solution.rating > 0 && (
                            <div className="flex items-center gap-1">
                              <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                              <span className="text-xs text-gray-600">{solution.rating.toFixed(1)}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <p className="text-gray-700 text-sm mb-3 line-clamp-2">
                        {solution.content}
                      </p>
                      
                      <div className="flex justify-between items-center text-xs text-gray-500">
                        <span>{formatDate(solution.created_at)}</span>
                        <Link 
                          to={`/problems/${solution.problem_id}`}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          عرض التفاصيل
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-600 text-center py-8">
                  لم يقدم الخبير أي حلول بعد
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Portfolio Tab */}
        <TabsContent value="portfolio" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>الأعمال والمشاريع</CardTitle>
              <CardDescription>
                نماذج من أعمال ومشاريع الخبير
              </CardDescription>
            </CardHeader>
            <CardContent>
              {expert.portfolio && expert.portfolio.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {expert.portfolio.map((project: any, index: number) => (
                    <div key={index} className="border rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2">{project.title}</h4>
                      <p className="text-gray-700 text-sm mb-3">{project.description}</p>
                      
                      {project.technologies && (
                        <div className="flex flex-wrap gap-1 mb-3">
                          {project.technologies.map((tech: string, techIndex: number) => (
                            <Badge key={techIndex} variant="outline" className="text-xs">
                              {tech}
                            </Badge>
                          ))}
                        </div>
                      )}
                      
                      <div className="flex justify-between items-center text-xs text-gray-500">
                        {project.date && <span>{formatDate(project.date)}</span>}
                        {project.url && (
                          <Button variant="ghost" size="sm" asChild>
                            <a href={project.url} target="_blank" rel="noopener noreferrer">
                              <ExternalLink className="w-3 h-3 ml-1" />
                              عرض المشروع
                            </a>
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-600 text-center py-8">
                  لم يتم إضافة أعمال للمعرض بعد
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Contact Tab */}
        <TabsContent value="contact" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>معلومات التواصل</CardTitle>
              <CardDescription>
                طرق التواصل مع الخبير
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <Mail className="w-5 h-5 text-gray-500" />
                  <div>
                    <p className="font-medium text-gray-900">البريد الإلكتروني</p>
                    <p className="text-gray-600">{expert.users?.email}</p>
                  </div>
                </div>

                {expert.users?.phone_number && (
                  <div className="flex items-center gap-3 p-3 border rounded-lg">
                    <Phone className="w-5 h-5 text-gray-500" />
                    <div>
                      <p className="font-medium text-gray-900">رقم الهاتف</p>
                      <p className="text-gray-600">{expert.users.phone_number}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <MapPin className="w-5 h-5 text-gray-500" />
                  <div>
                    <p className="font-medium text-gray-900">الموقع</p>
                    <p className="text-gray-600">{expert.users?.location}</p>
                  </div>
                </div>

                {expert.users?.organization && (
                  <div className="flex items-center gap-3 p-3 border rounded-lg">
                    <Building className="w-5 h-5 text-gray-500" />
                    <div>
                      <p className="font-medium text-gray-900">المؤسسة</p>
                      <p className="text-gray-600">{expert.users.organization}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <Users className="w-5 h-5 text-gray-500" />
                  <div>
                    <p className="font-medium text-gray-900">اللغات</p>
                    <div className="flex gap-2 mt-1">
                      {expert.users?.languages?.map((lang, index) => (
                        <Badge key={index} variant="secondary">
                          {lang === 'ar' ? 'العربية' : lang === 'en' ? 'الإنجليزية' : lang}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <Clock className="w-5 h-5 text-gray-500" />
                  <div>
                    <p className="font-medium text-gray-900">متوسط وقت الاستجابة</p>
                    <p className="text-gray-600">{expert.response_time_hours} ساعة</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}