/**
 * Expert Workload Dashboard Component
 * Displays expert workload statistics and trends
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { 
  Calendar, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  BarChart3,
  Users,
  DollarSign
} from 'lucide-react';
import { expertWorkloadAPI } from '../../lib/expertMatching';
import type { ExpertWorkload } from '../../types/user';
import { format, subMonths, startOfMonth } from 'date-fns';

interface ExpertWorkloadDashboardProps {
  expertId?: string;
  showAllExperts?: boolean;
}

export const ExpertWorkloadDashboard: React.FC<ExpertWorkloadDashboardProps> = ({
  expertId,
  showAllExperts = false
}) => {
  const [workloadData, setWorkloadData] = useState<ExpertWorkload[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedMonth, setSelectedMonth] = useState<string>(
    format(new Date(), 'yyyy-MM-01')
  );

  // Generate month options for the last 12 months
  const monthOptions = Array.from({ length: 12 }, (_, i) => {
    const date = startOfMonth(subMonths(new Date(), i));
    return {
      value: format(date, 'yyyy-MM-01'),
      label: format(date, 'MMMM yyyy')
    };
  });

  useEffect(() => {
    loadWorkloadData();
  }, [expertId, selectedMonth, showAllExperts]);

  const loadWorkloadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let data: ExpertWorkload[] = [];
      
      if (showAllExperts) {
        data = await expertWorkloadAPI.getAllExpertsWorkload(selectedMonth);
      } else if (expertId) {
        data = await expertWorkloadAPI.getExpertWorkload(expertId, selectedMonth);
      }
      
      setWorkloadData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load workload data');
    } finally {
      setLoading(false);
    }
  };

  const calculateTotals = () => {
    return workloadData.reduce(
      (totals, item) => ({
        totalAssigned: totals.totalAssigned + item.problems_assigned,
        totalCompleted: totals.totalCompleted + item.problems_completed,
        totalCompensation: totals.totalCompensation + item.total_compensation,
        avgResponseTime: totals.avgResponseTime + item.average_response_time_hours
      }),
      { totalAssigned: 0, totalCompleted: 0, totalCompensation: 0, avgResponseTime: 0 }
    );
  };

  const totals = calculateTotals();
  const avgResponseTime = workloadData.length > 0 ? totals.avgResponseTime / workloadData.length : 0;
  const completionRate = totals.totalAssigned > 0 ? (totals.totalCompleted / totals.totalAssigned) * 100 : 0;

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <AlertCircle className="h-8 w-8 mx-auto mb-2" />
            <p>{error}</p>
            <Button variant="outline" onClick={loadWorkloadData} className="mt-2">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Month Selection */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">
            {showAllExperts ? 'Expert Workload Overview' : 'My Workload'}
          </h2>
          <p className="text-gray-600">
            {showAllExperts 
              ? 'Track workload across all experts' 
              : 'Monitor your problem assignments and performance'
            }
          </p>
        </div>
        <Select value={selectedMonth} onValueChange={setSelectedMonth}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {monthOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Summary Cards */}
      {showAllExperts && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Assigned</p>
                  <p className="text-2xl font-bold">{totals.totalAssigned}</p>
                </div>
                <Calendar className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Completed</p>
                  <p className="text-2xl font-bold">{totals.totalCompleted}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Completion Rate</p>
                  <p className="text-2xl font-bold">{completionRate.toFixed(1)}%</p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Response Time</p>
                  <p className="text-2xl font-bold">{avgResponseTime.toFixed(1)}h</p>
                </div>
                <Clock className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Individual Expert Workload Cards */}
      <div className="space-y-4">
        {workloadData.length === 0 ? (
          <Card>
            <CardContent className="p-6">
              <div className="text-center text-gray-500">
                <BarChart3 className="h-8 w-8 mx-auto mb-2" />
                <p>No workload data found for the selected period</p>
              </div>
            </CardContent>
          </Card>
        ) : (
          workloadData.map((workload) => {
            const expertCompletionRate = workload.problems_assigned > 0 
              ? (workload.problems_completed / workload.problems_assigned) * 100 
              : 0;

            return (
              <Card key={workload.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {showAllExperts && (
                          <Users className="h-5 w-5" />
                        )}
                        {showAllExperts 
                          ? workload.experts?.users?.name || 'Expert'
                          : 'Workload Summary'
                        }
                      </CardTitle>
                      <CardDescription>
                        {format(new Date(workload.month_year), 'MMMM yyyy')}
                      </CardDescription>
                    </div>
                    <Badge 
                      variant={expertCompletionRate >= 80 ? 'default' : 
                               expertCompletionRate >= 60 ? 'secondary' : 'destructive'}
                    >
                      {expertCompletionRate.toFixed(1)}% completion
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Statistics Grid */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <Calendar className="h-6 w-6 text-blue-500 mx-auto mb-1" />
                      <p className="text-2xl font-bold text-blue-700">
                        {workload.problems_assigned}
                      </p>
                      <p className="text-sm text-blue-600">Assigned</p>
                    </div>
                    
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <CheckCircle className="h-6 w-6 text-green-500 mx-auto mb-1" />
                      <p className="text-2xl font-bold text-green-700">
                        {workload.problems_completed}
                      </p>
                      <p className="text-sm text-green-600">Completed</p>
                    </div>
                    
                    <div className="text-center p-3 bg-orange-50 rounded-lg">
                      <Clock className="h-6 w-6 text-orange-500 mx-auto mb-1" />
                      <p className="text-2xl font-bold text-orange-700">
                        {workload.average_response_time_hours.toFixed(1)}h
                      </p>
                      <p className="text-sm text-orange-600">Avg Response</p>
                    </div>
                    
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <DollarSign className="h-6 w-6 text-purple-500 mx-auto mb-1" />
                      <p className="text-2xl font-bold text-purple-700">
                        ${workload.total_compensation.toFixed(0)}
                      </p>
                      <p className="text-sm text-purple-600">Compensation</p>
                    </div>
                  </div>

                  {/* Performance Indicators */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Completion Rate</span>
                      <span className="font-medium">{expertCompletionRate.toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          expertCompletionRate >= 80 ? 'bg-green-500' :
                          expertCompletionRate >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${Math.min(expertCompletionRate, 100)}%` }}
                      />
                    </div>
                  </div>

                  {/* Response Time Indicator */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Response Time</span>
                      <span className="font-medium">
                        {workload.average_response_time_hours <= 24 ? 'Excellent' :
                         workload.average_response_time_hours <= 48 ? 'Good' :
                         workload.average_response_time_hours <= 72 ? 'Fair' : 'Needs Improvement'}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          workload.average_response_time_hours <= 24 ? 'bg-green-500' :
                          workload.average_response_time_hours <= 48 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ 
                          width: `${Math.max(10, Math.min(100, 100 - (workload.average_response_time_hours / 72) * 100))}%` 
                        }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>
    </div>
  );
};