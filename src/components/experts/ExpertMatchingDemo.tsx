/**
 * Expert Matching Demo Component
 * Demonstrates the expert matching system functionality
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { 
  Users, 
  Target, 
  TrendingUp, 
  Play,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { matchingAlgorithmAPI } from '../../lib/expertMatching';
import type { ExpertMatchResult } from '../../types/user';

interface ExpertMatchingDemoProps {
  problemId?: string;
}

export const ExpertMatchingDemo: React.FC<ExpertMatchingDemoProps> = ({
  problemId
}) => {
  const [matches, setMatches] = useState<ExpertMatchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [autoAssigning, setAutoAssigning] = useState(false);
  const [assignmentResult, setAssignmentResult] = useState<number | null>(null);

  const findMatches = async () => {
    if (!problemId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const results = await matchingAlgorithmAPI.findMatchingExperts(problemId, 10);
      setMatches(results);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to find matches');
    } finally {
      setLoading(false);
    }
  };

  const autoAssignExperts = async () => {
    if (!problemId) return;
    
    try {
      setAutoAssigning(true);
      setError(null);
      
      const assignedCount = await matchingAlgorithmAPI.autoAssignExperts(problemId, 3);
      setAssignmentResult(assignedCount);
      
      // Refresh matches after assignment
      await findMatches();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to auto-assign experts');
    } finally {
      setAutoAssigning(false);
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-blue-600';
    if (score >= 0.4) return 'text-yellow-600';
    return 'text-gray-600';
  };

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 0.8) return 'default';
    if (score >= 0.6) return 'secondary';
    if (score >= 0.4) return 'outline';
    return 'destructive';
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Expert Matching System Demo
          </CardTitle>
          <CardDescription>
            Test the intelligent expert-problem matching algorithm
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            </div>
          )}

          {assignmentResult !== null && (
            <div className="bg-green-50 border border-green-200 rounded-md p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <p className="text-green-600 text-sm">
                  Successfully auto-assigned {assignmentResult} expert{assignmentResult !== 1 ? 's' : ''} to this problem
                </p>
              </div>
            </div>
          )}

          <div className="flex gap-2">
            <Button 
              onClick={findMatches} 
              disabled={loading || !problemId}
              className="flex items-center gap-2"
            >
              {loading ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Play className="h-4 w-4" />
              )}
              Find Matching Experts
            </Button>
            
            <Button 
              onClick={autoAssignExperts} 
              disabled={autoAssigning || !problemId}
              variant="outline"
              className="flex items-center gap-2"
            >
              {autoAssigning ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Users className="h-4 w-4" />
              )}
              Auto-Assign Experts
            </Button>
          </div>

          {!problemId && (
            <div className="text-center text-gray-500 py-8">
              <Target className="h-8 w-8 mx-auto mb-2" />
              <p>Select a problem to test the matching system</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Matching Results */}
      {matches.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Matching Results ({matches.length} experts found)
            </CardTitle>
            <CardDescription>
              Experts ranked by match score with the selected problem
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {matches.map((match, index) => (
                <div key={match.expert_id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-medium text-blue-600">
                        #{index + 1}
                      </div>
                      <div>
                        <h4 className="font-medium">
                          Expert ID: {match.expert_id.slice(0, 8)}...
                        </h4>
                        <p className="text-sm text-gray-500">
                          {match.expert?.users?.name || 'Expert Name'}
                        </p>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <Badge 
                        variant={getScoreBadgeVariant(match.match_score)}
                        className="text-lg px-3 py-1"
                      >
                        {Math.round(match.match_score * 100)}%
                      </Badge>
                      <p className="text-xs text-gray-500 mt-1">match score</p>
                    </div>
                  </div>

                  {/* Match Reasons */}
                  <div className="space-y-2">
                    <h5 className="text-sm font-medium text-gray-700">Match Reasons:</h5>
                    <div className="flex flex-wrap gap-1">
                      {match.match_reasons.map((reason, reasonIndex) => (
                        <Badge 
                          key={reasonIndex} 
                          variant="outline" 
                          className="text-xs"
                        >
                          {reason}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Expert Details (if available) */}
                  {match.expert && (
                    <>
                      <Separator className="my-3" />
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Experience:</span>
                          <p className="font-medium">{match.expert.experience_years} years</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Rating:</span>
                          <p className="font-medium">{match.expert.rating.toFixed(1)}/5.0</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Success Rate:</span>
                          <p className="font-medium">{match.expert.success_rate.toFixed(0)}%</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Response Time:</span>
                          <p className="font-medium">{match.expert.response_time_hours}h</p>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Algorithm Information */}
      <Card>
        <CardHeader>
          <CardTitle>How the Matching Algorithm Works</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="font-medium">Matching Factors:</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>Problem category alignment (30%)</span>
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Sector expertise match (25%)</span>
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span>Expert's expertise areas (20%)</span>
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span>Current availability (15%)</span>
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span>Response time history (10%)</span>
                </li>
              </ul>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-medium">Score Interpretation:</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <Badge variant="default" className="text-xs">80-100%</Badge>
                  <span>Excellent match</span>
                </li>
                <li className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-xs">60-79%</Badge>
                  <span>Good match</span>
                </li>
                <li className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">40-59%</Badge>
                  <span>Fair match</span>
                </li>
                <li className="flex items-center gap-2">
                  <Badge variant="destructive" className="text-xs">20-39%</Badge>
                  <span>Poor match</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-xs text-gray-500">Below 20%</span>
                  <span>Not recommended</span>
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};