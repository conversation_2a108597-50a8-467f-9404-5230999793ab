/**
 * Expert Matching Preferences Component
 * Allows experts to configure their matching preferences
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Switch } from '../ui/switch';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { Clock, DollarSign, Calendar, Settings, Save, Plus, X } from 'lucide-react';
import { expertMatchingPreferencesAPI, expertMatchingUtils } from '../../lib/expertMatching';
import type { 
  ExpertMatchingPreferences, 
  ExpertMatchingPreferencesUpdateData,
  AvailabilitySchedule,
  DaySchedule 
} from '../../types/user';

interface ExpertMatchingPreferencesProps {
  expertId: string;
  onPreferencesUpdate?: (preferences: ExpertMatchingPreferences) => void;
}

const DAYS_OF_WEEK = [
  { key: 'monday', label: 'Monday' },
  { key: 'tuesday', label: 'Tuesday' },
  { key: 'wednesday', label: 'Wednesday' },
  { key: 'thursday', label: 'Thursday' },
  { key: 'friday', label: 'Friday' },
  { key: 'saturday', label: 'Saturday' },
  { key: 'sunday', label: 'Sunday' }
] as const;

const COMMON_CATEGORIES = [
  'Software Development',
  'Data Analysis',
  'Cybersecurity',
  'Cloud Computing',
  'Mobile Development',
  'Web Development',
  'AI/Machine Learning',
  'DevOps',
  'Database Management',
  'Network Administration'
];

const COMMON_SECTORS = [
  'Healthcare',
  'Education',
  'Finance',
  'Government',
  'Transportation',
  'Energy',
  'Agriculture',
  'Manufacturing',
  'Telecommunications',
  'Defense'
];

export const ExpertMatchingPreferences: React.FC<ExpertMatchingPreferencesProps> = ({
  expertId,
  onPreferencesUpdate
}) => {
  const [preferences, setPreferences] = useState<ExpertMatchingPreferences | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Form state
  const [formData, setFormData] = useState<ExpertMatchingPreferencesUpdateData>({
    preferred_problem_categories: [],
    preferred_sectors: [],
    max_problems_per_month: 5,
    min_compensation: 0,
    availability_schedule: expertMatchingUtils.getDefaultAvailabilitySchedule(),
    response_time_preference: 24,
    is_active: true
  });

  // Category and sector input states
  const [newCategory, setNewCategory] = useState('');
  const [newSector, setNewSector] = useState('');

  useEffect(() => {
    loadPreferences();
  }, [expertId]);

  const loadPreferences = async () => {
    try {
      setLoading(true);
      const data = await expertMatchingPreferencesAPI.getPreferences(expertId);
      
      if (data) {
        setPreferences(data);
        setFormData({
          preferred_problem_categories: data.preferred_problem_categories,
          preferred_sectors: data.preferred_sectors,
          max_problems_per_month: data.max_problems_per_month,
          min_compensation: data.min_compensation,
          availability_schedule: data.availability_schedule,
          response_time_preference: data.response_time_preference,
          is_active: data.is_active
        });
      } else {
        // Set default values for new preferences
        setFormData({
          preferred_problem_categories: [],
          preferred_sectors: [],
          max_problems_per_month: 5,
          min_compensation: 0,
          availability_schedule: expertMatchingUtils.getDefaultAvailabilitySchedule(),
          response_time_preference: 24,
          is_active: true
        });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load preferences');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      
      const updatedPreferences = await expertMatchingPreferencesAPI.upsertPreferences(
        expertId,
        formData
      );
      
      setPreferences(updatedPreferences);
      onPreferencesUpdate?.(updatedPreferences);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save preferences');
    } finally {
      setSaving(false);
    }
  };

  const addCategory = (category: string) => {
    if (category && !formData.preferred_problem_categories?.includes(category)) {
      setFormData(prev => ({
        ...prev,
        preferred_problem_categories: [...(prev.preferred_problem_categories || []), category]
      }));
    }
    setNewCategory('');
  };

  const removeCategory = (category: string) => {
    setFormData(prev => ({
      ...prev,
      preferred_problem_categories: prev.preferred_problem_categories?.filter(c => c !== category) || []
    }));
  };

  const addSector = (sector: string) => {
    if (sector && !formData.preferred_sectors?.includes(sector)) {
      setFormData(prev => ({
        ...prev,
        preferred_sectors: [...(prev.preferred_sectors || []), sector]
      }));
    }
    setNewSector('');
  };

  const removeSector = (sector: string) => {
    setFormData(prev => ({
      ...prev,
      preferred_sectors: prev.preferred_sectors?.filter(s => s !== sector) || []
    }));
  };

  const updateDaySchedule = (day: keyof AvailabilitySchedule, schedule: DaySchedule) => {
    setFormData(prev => ({
      ...prev,
      availability_schedule: {
        ...prev.availability_schedule!,
        [day]: schedule
      }
    }));
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Matching Preferences
          </CardTitle>
          <CardDescription>
            Configure your preferences for automatic problem matching
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Active Status */}
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="is-active">Enable Automatic Matching</Label>
              <p className="text-sm text-gray-500">
                Receive automatic problem matches based on your preferences
              </p>
            </div>
            <Switch
              id="is-active"
              checked={formData.is_active}
              onCheckedChange={(checked) => 
                setFormData(prev => ({ ...prev, is_active: checked }))
              }
            />
          </div>

          <Separator />

          {/* Problem Categories */}
          <div className="space-y-3">
            <Label>Preferred Problem Categories</Label>
            <div className="flex flex-wrap gap-2">
              {formData.preferred_problem_categories?.map((category) => (
                <Badge key={category} variant="secondary" className="flex items-center gap-1">
                  {category}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeCategory(category)}
                  />
                </Badge>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                placeholder="Add category..."
                value={newCategory}
                onChange={(e) => setNewCategory(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addCategory(newCategory)}
              />
              <Button 
                type="button" 
                variant="outline" 
                size="sm"
                onClick={() => addCategory(newCategory)}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {COMMON_CATEGORIES.map((category) => (
                <Button
                  key={category}
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-auto p-1 text-xs"
                  onClick={() => addCategory(category)}
                  disabled={formData.preferred_problem_categories?.includes(category)}
                >
                  + {category}
                </Button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Preferred Sectors */}
          <div className="space-y-3">
            <Label>Preferred Sectors</Label>
            <div className="flex flex-wrap gap-2">
              {formData.preferred_sectors?.map((sector) => (
                <Badge key={sector} variant="secondary" className="flex items-center gap-1">
                  {sector}
                  <X 
                    className="h-3 w-3 cursor-pointer" 
                    onClick={() => removeSector(sector)}
                  />
                </Badge>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                placeholder="Add sector..."
                value={newSector}
                onChange={(e) => setNewSector(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addSector(newSector)}
              />
              <Button 
                type="button" 
                variant="outline" 
                size="sm"
                onClick={() => addSector(newSector)}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {COMMON_SECTORS.map((sector) => (
                <Button
                  key={sector}
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-auto p-1 text-xs"
                  onClick={() => addSector(sector)}
                  disabled={formData.preferred_sectors?.includes(sector)}
                >
                  + {sector}
                </Button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Workload Preferences */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="max-problems">Max Problems per Month</Label>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <Input
                  id="max-problems"
                  type="number"
                  min="1"
                  max="50"
                  value={formData.max_problems_per_month}
                  onChange={(e) => 
                    setFormData(prev => ({ 
                      ...prev, 
                      max_problems_per_month: parseInt(e.target.value) || 5 
                    }))
                  }
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="min-compensation">Minimum Compensation (USD)</Label>
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-gray-500" />
                <Input
                  id="min-compensation"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.min_compensation}
                  onChange={(e) => 
                    setFormData(prev => ({ 
                      ...prev, 
                      min_compensation: parseFloat(e.target.value) || 0 
                    }))
                  }
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="response-time">Preferred Response Time (hours)</Label>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <Input
                id="response-time"
                type="number"
                min="1"
                max="168"
                value={formData.response_time_preference}
                onChange={(e) => 
                  setFormData(prev => ({ 
                    ...prev, 
                    response_time_preference: parseInt(e.target.value) || 24 
                  }))
                }
              />
            </div>
            <p className="text-sm text-gray-500">
              How quickly you typically respond to problem matches
            </p>
          </div>

          <Separator />

          {/* Availability Schedule */}
          <div className="space-y-4">
            <Label>Availability Schedule</Label>
            <div className="space-y-3">
              {DAYS_OF_WEEK.map(({ key, label }) => {
                const daySchedule = formData.availability_schedule?.[key];
                return (
                  <div key={key} className="flex items-center gap-4 p-3 border rounded-lg">
                    <div className="w-20">
                      <Label className="text-sm font-medium">{label}</Label>
                    </div>
                    <Switch
                      checked={daySchedule?.available || false}
                      onCheckedChange={(checked) => 
                        updateDaySchedule(key, {
                          ...daySchedule!,
                          available: checked
                        })
                      }
                    />
                    {daySchedule?.available && (
                      <div className="flex items-center gap-2">
                        <Input
                          type="time"
                          value={daySchedule.start}
                          onChange={(e) => 
                            updateDaySchedule(key, {
                              ...daySchedule,
                              start: e.target.value
                            })
                          }
                          className="w-24"
                        />
                        <span className="text-sm text-gray-500">to</span>
                        <Input
                          type="time"
                          value={daySchedule.end}
                          onChange={(e) => 
                            updateDaySchedule(key, {
                              ...daySchedule,
                              end: e.target.value
                            })
                          }
                          className="w-24"
                        />
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button onClick={handleSave} disabled={saving}>
              <Save className="h-4 w-4 mr-2" />
              {saving ? 'Saving...' : 'Save Preferences'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};