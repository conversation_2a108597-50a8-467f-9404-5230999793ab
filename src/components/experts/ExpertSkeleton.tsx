import { ExpertSkeleton as BaseExpertSkeleton, CardSkeleton, ListSkeleton } from '@/components/ui/skeleton-variants'
import { useDeviceType } from '@/hooks/use-mobile'
import { cn } from '@/lib/utils'

interface ExpertDirectorySkeletonProps {
  itemCount?: number
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

export function ExpertDirectorySkeleton({
  itemCount = 9,
  animation = 'pulse',
  className = ''
}: ExpertDirectorySkeletonProps) {
  const { isMobile } = useDeviceType()

  return (
    <div className={cn('space-y-6', className)} dir="rtl">
      {/* Search and filters skeleton */}
      <div className="p-6 border rounded-lg bg-card">
        <div className={`${isMobile ? 'flex flex-col gap-3' : 'flex flex-col lg:flex-row gap-4'}`}>
          {/* Search inputs */}
          <div className="flex-1 space-y-4">
            <div className={`h-10 w-full bg-muted animate-${animation} rounded-md`} />
            <div className={`h-10 w-full bg-muted animate-${animation} rounded-md`} />
          </div>
          
          {/* Sort and filter controls */}
          <div className={`flex gap-2 ${isMobile ? 'flex-col' : ''}`}>
            <div className={`${isMobile ? 'w-full' : 'w-40'} h-10 bg-muted animate-${animation} rounded-md`} />
            <div className={`${isMobile ? 'w-full' : 'w-10'} h-10 bg-muted animate-${animation} rounded-md`} />
            <div className={`${isMobile ? 'w-full' : 'w-32'} h-10 bg-muted animate-${animation} rounded-md`} />
          </div>
        </div>
      </div>

      {/* Results header */}
      <div className="flex justify-between items-center">
        <div className={`h-6 w-48 bg-muted animate-${animation} rounded-md`} />
      </div>

      {/* Expert cards grid */}
      <div className={`grid grid-cols-1 gap-4 ${isMobile ? '' : 'md:grid-cols-2 lg:grid-cols-3 gap-6'}`}>
        {Array.from({ length: itemCount }, (_, i) => (
          <ExpertCardSkeleton key={i} animation={animation} />
        ))}
      </div>
    </div>
  )
}

interface ExpertCardSkeletonProps {
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

export function ExpertCardSkeleton({
  animation = 'pulse',
  className = ''
}: ExpertCardSkeletonProps) {
  return (
    <div className={cn('p-6 border rounded-lg bg-card hover:shadow-lg transition-shadow', className)}>
      <div className="space-y-4">
        {/* Header with avatar */}
        <div className="text-center space-y-4">
          <div className="relative mx-auto">
            <div className={`w-20 h-20 bg-muted animate-${animation} rounded-full mx-auto`} />
            <div className={`absolute -bottom-1 -right-1 w-6 h-6 bg-muted animate-${animation} rounded-full border-2 border-white`} />
          </div>
          <div className="space-y-2">
            <div className={`h-5 w-32 bg-muted animate-${animation} rounded-md mx-auto`} />
            <div className={`h-4 w-40 bg-muted animate-${animation} rounded-md mx-auto`} />
          </div>
        </div>

        {/* Location */}
        <div className="flex items-center justify-center gap-2">
          <div className="w-4 h-4 bg-muted animate-pulse rounded" />
          <div className={`h-4 w-24 bg-muted animate-${animation} rounded-md`} />
        </div>
        
        {/* Stats */}
        <div className="flex justify-between text-sm">
          <div className={`h-4 w-20 bg-muted animate-${animation} rounded-md`} />
          <div className="flex items-center gap-1">
            <div className="w-4 h-4 bg-muted animate-pulse rounded" />
            <div className={`h-4 w-8 bg-muted animate-${animation} rounded-md`} />
          </div>
        </div>

        <div className="flex justify-between text-sm">
          <div className="flex items-center gap-1">
            <div className="w-4 h-4 bg-muted animate-pulse rounded" />
            <div className={`h-4 w-12 bg-muted animate-${animation} rounded-md`} />
          </div>
          <div className={`h-4 w-16 bg-muted animate-${animation} rounded-md`} />
        </div>

        {/* Expertise Areas */}
        <div className="space-y-2">
          <div className={`h-4 w-24 bg-muted animate-${animation} rounded-md`} />
          <div className="flex flex-wrap gap-1">
            {Array.from({ length: 2 }, (_, i) => (
              <div key={i} className={`h-6 w-${16 + i * 4} bg-muted animate-${animation} rounded-full`} />
            ))}
            <div className={`h-6 w-8 bg-muted animate-${animation} rounded-full`} />
          </div>
        </div>

        {/* Top Skills */}
        <div className="space-y-2">
          <div className={`h-4 w-28 bg-muted animate-${animation} rounded-md`} />
          <div className="flex flex-wrap gap-1">
            {Array.from({ length: 3 }, (_, i) => (
              <div key={i} className={`h-6 w-${12 + i * 2} bg-muted animate-${animation} rounded-full`} />
            ))}
          </div>
        </div>

        {/* Bio */}
        <div className="space-y-2">
          <div className={`h-3 w-full bg-muted animate-${animation} rounded-md`} />
          <div className={`h-3 w-2/3 bg-muted animate-${animation} rounded-md`} />
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 bg-muted animate-${animation} rounded-full`} />
            <div className={`h-4 w-16 bg-muted animate-${animation} rounded-md`} />
          </div>
          <div className={`h-8 w-32 bg-muted animate-${animation} rounded-md`} />
        </div>
      </div>
    </div>
  )
}

interface ExpertProfileSkeletonProps {
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

export function ExpertProfileSkeleton({
  animation = 'pulse',
  className = ''
}: ExpertProfileSkeletonProps) {
  return (
    <BaseExpertSkeleton
      variant="profile"
      animation={animation}
      className={className}
    />
  )
}

interface ExpertListSkeletonProps {
  itemCount?: number
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

export function ExpertListSkeleton({
  itemCount = 10,
  animation = 'pulse',
  className = ''
}: ExpertListSkeletonProps) {
  return (
    <div className={cn('space-y-0', className)}>
      {Array.from({ length: itemCount }, (_, i) => (
        <div key={i}>
          <div className="flex items-center gap-4 p-4 h-20">
            <div className={`w-12 h-12 bg-muted animate-${animation} rounded-full flex-shrink-0`} />
            <div className="flex-1 space-y-2">
              <div className={`h-4 w-2/3 bg-muted animate-${animation} rounded-md`} />
              <div className={`h-3 w-1/2 bg-muted animate-${animation} rounded-md`} />
            </div>
            <div className="flex items-center gap-2">
              <div className={`h-4 w-8 bg-muted animate-${animation} rounded-md`} />
              <div className={`h-6 w-16 bg-muted animate-${animation} rounded-full`} />
            </div>
          </div>
          {i < itemCount - 1 && <div className="border-b border-border" />}
        </div>
      ))}
    </div>
  )
}

// Form skeleton for expert profile creation/editing
interface ExpertFormSkeletonProps {
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

export function ExpertFormSkeleton({
  animation = 'pulse',
  className = ''
}: ExpertFormSkeletonProps) {
  return (
    <div className={cn('space-y-6', className)}>
      {/* Personal Information */}
      <div className="space-y-4">
        <div className={`h-6 w-32 bg-muted animate-${animation} rounded-md`} />
        
        {/* Avatar upload */}
        <div className="flex items-center gap-4">
          <div className={`w-20 h-20 bg-muted animate-${animation} rounded-full`} />
          <div className={`h-10 w-32 bg-muted animate-${animation} rounded-md`} />
        </div>

        {/* Basic fields */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Array.from({ length: 4 }, (_, i) => (
            <div key={i} className="space-y-2">
              <div className={`h-4 w-20 bg-muted animate-${animation} rounded-md`} />
              <div className={`h-10 w-full bg-muted animate-${animation} rounded-md`} />
            </div>
          ))}
        </div>

        {/* Bio */}
        <div className="space-y-2">
          <div className={`h-4 w-16 bg-muted animate-${animation} rounded-md`} />
          <div className={`h-24 w-full bg-muted animate-${animation} rounded-md`} />
        </div>
      </div>

      {/* Expertise Areas */}
      <div className="space-y-4">
        <div className={`h-6 w-28 bg-muted animate-${animation} rounded-md`} />
        
        {Array.from({ length: 2 }, (_, i) => (
          <div key={i} className="p-4 border rounded-lg space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className={`h-4 w-16 bg-muted animate-${animation} rounded-md`} />
                <div className={`h-10 w-full bg-muted animate-${animation} rounded-md`} />
              </div>
              <div className="space-y-2">
                <div className={`h-4 w-24 bg-muted animate-${animation} rounded-md`} />
                <div className={`h-10 w-full bg-muted animate-${animation} rounded-md`} />
              </div>
            </div>
            
            <div className="space-y-2">
              <div className={`h-4 w-20 bg-muted animate-${animation} rounded-md`} />
              <div className={`h-10 w-full bg-muted animate-${animation} rounded-md`} />
            </div>
          </div>
        ))}
        
        <div className={`h-10 w-40 bg-muted animate-${animation} rounded-md`} />
      </div>

      {/* Portfolio */}
      <div className="space-y-4">
        <div className={`h-6 w-24 bg-muted animate-${animation} rounded-md`} />
        
        {Array.from({ length: 1 }, (_, i) => (
          <div key={i} className="p-4 border rounded-lg space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Array.from({ length: 4 }, (_, j) => (
                <div key={j} className="space-y-2">
                  <div className={`h-4 w-16 bg-muted animate-${animation} rounded-md`} />
                  <div className={`h-10 w-full bg-muted animate-${animation} rounded-md`} />
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Submit buttons */}
      <div className="flex justify-end gap-2 pt-4 border-t">
        <div className={`h-10 w-20 bg-muted animate-${animation} rounded-md`} />
        <div className={`h-10 w-24 bg-muted animate-${animation} rounded-md`} />
      </div>
    </div>
  )
}