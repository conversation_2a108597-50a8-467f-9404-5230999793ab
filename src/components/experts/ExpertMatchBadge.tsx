/**
 * Expert Match Badge Component
 * Shows a notification badge for pending expert matches in the header
 */

import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { Bell, Zap } from 'lucide-react';
import { problemExpertMatchesAPI } from '../../lib/expertMatching';
import { ExpertMatchNotifications } from './ExpertMatchNotifications';
import { Link } from 'react-router-dom';

interface ExpertMatchBadgeProps {
  expertId: string;
  className?: string;
}

export const ExpertMatchBadge: React.FC<ExpertMatchBadgeProps> = ({
  expertId,
  className = ''
}) => {
  const [pendingCount, setPendingCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    loadPendingCount();
    
    // Poll for updates every 60 seconds
    const interval = setInterval(loadPendingCount, 60000);
    
    return () => clearInterval(interval);
  }, [expertId]);

  const loadPendingCount = async () => {
    try {
      const matches = await problemExpertMatchesAPI.getPendingMatchesForExpert(expertId);
      setPendingCount(matches.length);
    } catch (error) {
      console.error('Failed to load pending matches:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleMatchResponse = () => {
    // Reload count when expert responds to a match
    loadPendingCount();
  };

  if (loading) {
    return (
      <Button variant="ghost" size="sm" className={className} disabled>
        <Bell className="h-4 w-4" />
      </Button>
    );
  }

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button 
          variant="ghost" 
          size="sm" 
          className={`relative ${className}`}
          onClick={() => setIsOpen(!isOpen)}
        >
          <Zap className="h-4 w-4" />
          {pendingCount > 0 && (
            <Badge 
              className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs bg-red-500 hover:bg-red-600"
            >
              {pendingCount > 9 ? '9+' : pendingCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-96 p-0" align="end">
        <div className="p-4 border-b">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold">Expert Matches</h3>
            {pendingCount > 0 && (
              <Badge variant="secondary">
                {pendingCount} pending
              </Badge>
            )}
          </div>
          <p className="text-sm text-gray-600 mt-1">
            Problems matched to your expertise
          </p>
        </div>
        
        <div className="max-h-96 overflow-y-auto p-4">
          <ExpertMatchNotifications
            expertId={expertId}
            onMatchResponse={handleMatchResponse}
            maxNotifications={3}
          />
        </div>
        
        {pendingCount > 3 && (
          <div className="p-4 border-t bg-gray-50">
            <Button variant="outline" size="sm" className="w-full" asChild>
              <Link to="/experts/dashboard?tab=matches">
                View All Matches ({pendingCount})
              </Link>
            </Button>
          </div>
        )}
      </PopoverContent>
    </Popover>
  );
};