import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { 
  User, 
  Award, 
  TrendingUp, 
  Clock, 
  Star, 
  MessageSquare, 
  CheckCircle, 
  AlertCircle,
  Edit,
  Eye,
  Calendar,
  BarChart3,
  Target,
  Users,
  FileText,
  Settings,
  Zap,
  Bell
} from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'
import { useToast } from '@/hooks/use-toast'
import { expertOperations, problemOperations, solutionOperations } from '@/lib/database'
import { problemExpertMatchesAPI, expertWorkloadAPI } from '@/lib/expertMatching'
import { ExpertMatches } from './ExpertMatches'
import { ExpertWorkloadDashboard } from './ExpertWorkloadDashboard'
import { Link } from 'react-router-dom'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'

interface ExpertStats {
  totalContributions: number
  solvedProblems: number
  averageRating: number
  responseTime: number
  successRate: number
  thisMonthContributions: number
  pendingSolutions: number
  activeProblemAssignments: number
  pendingMatches: number
  matchAcceptanceRate: number
}

interface RecentActivity {
  id: string
  type: 'solution_submitted' | 'problem_assigned' | 'solution_approved' | 'rating_received'
  title: string
  description: string
  date: string
  status?: string
  rating?: number
}

interface ExpertProfile {
  id: string
  user_id: string
  expertise_areas: any[]
  experience_years: number
  availability: string
  rating: number
  total_contributions: number
  success_rate: number
  response_time_hours: number
  users: {
    name: string
    email: string
    avatar?: string
    bio?: string
    location: string
    organization?: string
    position?: string
  }
}

const availabilityLabels = {
  available: 'متاح',
  busy: 'مشغول',
  unavailable: 'غير متاح'
}

const availabilityColors = {
  available: 'bg-green-100 text-green-800',
  busy: 'bg-yellow-100 text-yellow-800',
  unavailable: 'bg-red-100 text-red-800'
}

export function ExpertDashboard() {
  const { user } = useAuth()
  const { toast } = useToast()
  const [profile, setProfile] = useState<ExpertProfile | null>(null)
  const [stats, setStats] = useState<ExpertStats | null>(null)
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [pendingMatchesCount, setPendingMatchesCount] = useState(0)

  useEffect(() => {
    if (user) {
      loadDashboardData()
    }
  }, [user])

  const loadDashboardData = async () => {
    if (!user) return

    try {
      setLoading(true)
      
      // Load expert profile
      const { data: expertData, error: expertError } = await expertOperations.getExpertProfile(user.id)
      if (expertError) {
        throw new Error(expertError.message)
      }
      setProfile(expertData)

      // Load pending matches
      const pendingMatches = await problemExpertMatchesAPI.getPendingMatchesForExpert(expertData.id)
      setPendingMatchesCount(pendingMatches.length)

      // Load statistics (enhanced with matching data)
      const mockStats: ExpertStats = {
        totalContributions: expertData?.total_contributions || 0,
        solvedProblems: Math.floor((expertData?.total_contributions || 0) * 0.8),
        averageRating: expertData?.rating || 0,
        responseTime: expertData?.response_time_hours || 24,
        successRate: expertData?.success_rate || 0,
        thisMonthContributions: Math.floor((expertData?.total_contributions || 0) * 0.3),
        pendingSolutions: Math.floor(Math.random() * 5),
        activeProblemAssignments: Math.floor(Math.random() * 3),
        pendingMatches: pendingMatches.length,
        matchAcceptanceRate: 85 // Mock data - would be calculated from actual responses
      }
      setStats(mockStats)

      // Load recent activity (mock data for now)
      const mockActivity: RecentActivity[] = [
        {
          id: '1',
          type: 'solution_submitted',
          title: 'تم تقديم حل جديد',
          description: 'قمت بتقديم حل لمشكلة "تطوير نظام إدارة المستشفيات"',
          date: new Date().toISOString(),
          status: 'pending'
        },
        {
          id: '2',
          type: 'rating_received',
          title: 'تم تقييم حلك',
          description: 'حصلت على تقييم 5 نجوم لحلك في مشكلة "أمان قواعد البيانات"',
          date: new Date(Date.now() - 86400000).toISOString(),
          rating: 5
        },
        {
          id: '3',
          type: 'problem_assigned',
          title: 'تم تعيين مشكلة جديدة',
          description: 'تم تعيينك لحل مشكلة "تحسين أداء التطبيق"',
          date: new Date(Date.now() - 172800000).toISOString()
        }
      ]
      setRecentActivity(mockActivity)

    } catch (error) {
      console.error('Error loading dashboard data:', error)
      toast({
        title: 'خطأ في تحميل البيانات',
        description: 'حدث خطأ أثناء تحميل بيانات لوحة التحكم',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'solution_submitted':
        return <FileText className="w-4 h-4" />
      case 'problem_assigned':
        return <Target className="w-4 h-4" />
      case 'solution_approved':
        return <CheckCircle className="w-4 h-4" />
      case 'rating_received':
        return <Star className="w-4 h-4" />
      default:
        return <MessageSquare className="w-4 h-4" />
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'solution_submitted':
        return 'text-blue-600'
      case 'problem_assigned':
        return 'text-orange-600'
      case 'solution_approved':
        return 'text-green-600'
      case 'rating_received':
        return 'text-yellow-600'
      default:
        return 'text-gray-600'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل لوحة التحكم...</p>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <User className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            لم يتم العثور على ملف الخبير
          </h3>
          <p className="text-gray-600 mb-4">
            يبدو أنك لم تقم بإنشاء ملف شخصي كخبير بعد
          </p>
          <Button asChild>
            <Link to="/experts/profile/create">
              إنشاء ملف الخبير
            </Link>
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">لوحة تحكم الخبير</h1>
          <p className="text-gray-600 mt-1">مرحباً {profile.users.name}، إليك ملخص نشاطك</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" asChild>
            <Link to="/experts/profile/edit">
              <Edit className="w-4 h-4 ml-2" />
              تحديث الملف الشخصي
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link to={`/experts/${profile.user_id}`}>
              <Eye className="w-4 h-4 ml-2" />
              عرض الملف العام
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي المساهمات</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.totalContributions || 0}</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <Award className="w-6 h-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4">
              <div className="flex items-center text-sm text-green-600">
                <TrendingUp className="w-4 h-4 ml-1" />
                +{stats?.thisMonthContributions || 0} هذا الشهر
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">التقييم المتوسط</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.averageRating.toFixed(1) || '0.0'}</p>
              </div>
              <div className="bg-yellow-100 p-3 rounded-full">
                <Star className="w-6 h-6 text-yellow-600" />
              </div>
            </div>
            <div className="mt-4">
              <Progress value={(stats?.averageRating || 0) * 20} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">وقت الاستجابة</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.responseTime || 0}ساعة</p>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <Clock className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4">
              <div className="text-sm text-gray-600">
                معدل الاستجابة السريعة
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">المطابقات المعلقة</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.pendingMatches || 0}</p>
              </div>
              <div className="bg-orange-100 p-3 rounded-full">
                <Bell className="w-6 h-6 text-orange-600" />
              </div>
            </div>
            <div className="mt-4">
              <div className="text-sm text-gray-600">
                {stats?.pendingMatches ? 'يتطلب الرد' : 'لا توجد مطابقات معلقة'}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">نظرة عامة</TabsTrigger>
          <TabsTrigger value="matches" className="relative">
            المطابقات
            {pendingMatchesCount > 0 && (
              <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs bg-red-500">
                {pendingMatchesCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="solutions">حلولي</TabsTrigger>
          <TabsTrigger value="assignments">المهام المعينة</TabsTrigger>
          <TabsTrigger value="analytics">التحليلات</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Profile Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  ملخص الملف الشخصي
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">حالة التوفر:</span>
                  <Badge className={availabilityColors[profile.availability as keyof typeof availabilityColors]}>
                    {availabilityLabels[profile.availability as keyof typeof availabilityLabels]}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">سنوات الخبرة:</span>
                  <span className="font-medium">{profile.experience_years} سنة</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">مجالات الخبرة:</span>
                  <span className="font-medium">{profile.expertise_areas.length} مجال</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">الموقع:</span>
                  <span className="font-medium">{profile.users.location}</span>
                </div>
                {profile.users.organization && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">المؤسسة:</span>
                    <span className="font-medium">{profile.users.organization}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  النشاط الأخير
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">لا يوجد نشاط حديث</p>
                  ) : (
                    recentActivity.map((activity) => (
                      <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg bg-gray-50">
                        <div className={`p-2 rounded-full bg-white ${getActivityColor(activity.type)}`}>
                          {getActivityIcon(activity.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-sm">{activity.title}</p>
                          <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                          <p className="text-xs text-gray-500 mt-2">
                            {format(new Date(activity.date), 'dd MMMM yyyy، HH:mm', { locale: ar })}
                          </p>
                        </div>
                        {activity.rating && (
                          <div className="flex items-center text-yellow-600">
                            <Star className="w-4 h-4 ml-1" />
                            {activity.rating}
                          </div>
                        )}
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>إجراءات سريعة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Button 
                  variant={pendingMatchesCount > 0 ? "default" : "outline"} 
                  className="h-20 flex-col relative" 
                  asChild
                >
                  <Link to="#matches" onClick={() => document.querySelector('[value="matches"]')?.click()}>
                    <Zap className="w-6 h-6 mb-2" />
                    المطابقات الذكية
                    {pendingMatchesCount > 0 && (
                      <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs bg-red-500">
                        {pendingMatchesCount}
                      </Badge>
                    )}
                  </Link>
                </Button>
                <Button variant="outline" className="h-20 flex-col" asChild>
                  <Link to="/problems">
                    <Target className="w-6 h-6 mb-2" />
                    تصفح المشاكل
                  </Link>
                </Button>
                <Button variant="outline" className="h-20 flex-col" asChild>
                  <Link to="/experts/profile/edit">
                    <Settings className="w-6 h-6 mb-2" />
                    تحديث الملف الشخصي
                  </Link>
                </Button>
                <Button variant="outline" className="h-20 flex-col" asChild>
                  <Link to="/experts">
                    <Users className="w-6 h-6 mb-2" />
                    شبكة الخبراء
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="matches" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="w-5 h-5" />
                    المطابقات المعلقة
                    {pendingMatchesCount > 0 && (
                      <Badge className="bg-red-500">{pendingMatchesCount}</Badge>
                    )}
                  </CardTitle>
                  <CardDescription>
                    المشاكل التي تم مطابقتها معك بناءً على خبرتك
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {profile && (
                    <ExpertMatches 
                      expertId={profile.id} 
                      showPendingOnly={true}
                      onMatchResponse={() => {
                        // Reload dashboard data when expert responds
                        loadDashboardData()
                      }}
                    />
                  )}
                </CardContent>
              </Card>
            </div>
            
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>إحصائيات المطابقة</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">المطابقات المعلقة:</span>
                    <Badge variant={pendingMatchesCount > 0 ? "destructive" : "secondary"}>
                      {pendingMatchesCount}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">معدل القبول:</span>
                    <span className="font-medium">{stats?.matchAcceptanceRate || 0}%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">وقت الاستجابة:</span>
                    <span className="font-medium">{stats?.responseTime || 0}ساعة</span>
                  </div>
                  <Separator />
                  <Button variant="outline" className="w-full" asChild>
                    <Link to="/experts/preferences">
                      <Settings className="w-4 h-4 mr-2" />
                      إعدادات المطابقة
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="solutions">
          <Card>
            <CardHeader>
              <CardTitle>حلولي المقدمة</CardTitle>
              <CardDescription>
                جميع الحلول التي قمت بتقديمها للمشاكل التقنية
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  لا توجد حلول مقدمة بعد
                </h3>
                <p className="text-gray-600 mb-4">
                  ابدأ بتصفح المشاكل المتاحة وقدم حلولك التقنية
                </p>
                <Button asChild>
                  <Link to="/problems">
                    تصفح المشاكل
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="assignments">
          <Card>
            <CardHeader>
              <CardTitle>المهام المعينة لي</CardTitle>
              <CardDescription>
                المشاكل التي تم تعيينك لحلها
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <Target className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  لا توجد مهام معينة حالياً
                </h3>
                <p className="text-gray-600 mb-4">
                  سيتم إشعارك عند تعيين مشاكل جديدة لك بناءً على خبرتك
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          {profile && (
            <ExpertWorkloadDashboard expertId={profile.id} />
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}