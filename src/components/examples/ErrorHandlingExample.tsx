import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ComponentErrorBoundary, 
  withComponentErrorBoundary 
} from '@/components/common/ComponentErrorBoundary';
import { 
  SectionErrorBoundary, 
  FormErrorBoundary, 
  DataErrorBoundary 
} from '@/utils/errorBoundaryHelpers';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { AlertTriangle, Bug, RefreshCw, Zap } from 'lucide-react';

// Component that throws an error for demonstration
function ErrorThrowingComponent({ errorType }: { errorType: string }) {
  const throwError = () => {
    switch (errorType) {
      case 'render':
        throw new Error('Render error: Component failed to render properly');
      case 'async':
        setTimeout(() => {
          throw new Error('Async error: Background operation failed');
        }, 1000);
        break;
      case 'api':
        throw new Error('API error: Failed to fetch data from server');
      default:
        throw new Error('Unknown error occurred');
    }
  };

  React.useEffect(() => {
    if (errorType === 'async') {
      throwError();
    }
  }, [errorType]);

  if (errorType === 'render') {
    throwError();
  }

  if (errorType === 'api') {
    throwError();
  }

  return (
    <div className="p-4 bg-green-50 border border-green-200 rounded-md">
      <p className="text-green-800">✅ Component rendered successfully!</p>
    </div>
  );
}

// Component with error handling hook
function ComponentWithErrorHook() {
  const [count, setCount] = useState(0);
  const { handleAsyncError, handleComponentError, hasError, clearError } = useErrorHandler({
    componentName: 'ComponentWithErrorHook',
    enableReporting: true,
  });

  const simulateAsyncError = async () => {
    await handleAsyncError(
      async () => {
        // Simulate API call that fails
        await new Promise(resolve => setTimeout(resolve, 1000));
        throw new Error('Simulated async operation failed');
      },
      {
        operationName: 'simulateAsyncError',
        showSuccessToast: false,
      }
    );
  };

  const simulateComponentError = () => {
    try {
      // Simulate component error
      throw new Error('Simulated component error');
    } catch (error) {
      handleComponentError(error as Error, { count, action: 'button-click' });
    }
  };

  if (hasError) {
    return (
      <Alert className="border-destructive/50">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between">
          <span>Component encountered an error</span>
          <Button onClick={clearError} variant="outline" size="sm">
            <RefreshCw className="w-3 h-3 mr-1" />
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <span>Count: {count}</span>
        <Button onClick={() => setCount(c => c + 1)} variant="outline" size="sm">
          Increment
        </Button>
      </div>
      
      <div className="flex gap-2">
        <Button onClick={simulateAsyncError} variant="destructive" size="sm">
          <Bug className="w-3 h-3 mr-1" />
          Trigger Async Error
        </Button>
        <Button onClick={simulateComponentError} variant="destructive" size="sm">
          <Bug className="w-3 h-3 mr-1" />
          Trigger Component Error
        </Button>
      </div>
    </div>
  );
}

// Enhanced component with error boundary
const SafeComponentWithErrorHook = withComponentErrorBoundary(ComponentWithErrorHook, {
  componentName: 'SafeComponentWithErrorHook',
  enableReporting: true,
  variant: 'component',
  isolateError: true,
});

export function ErrorHandlingExample() {
  const [selectedErrorType, setSelectedErrorType] = useState<string | null>(null);
  const [showErrorComponent, setShowErrorComponent] = useState(false);

  const errorTypes = [
    { id: 'render', label: 'Render Error', description: 'Error during component rendering' },
    { id: 'async', label: 'Async Error', description: 'Error in background operation' },
    { id: 'api', label: 'API Error', description: 'Error during API call' },
  ];

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            Error Handling & Boundaries Demo
          </CardTitle>
          <CardDescription>
            Comprehensive demonstration of error handling patterns and error boundaries
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Error Type Selection */}
          <div>
            <h3 className="text-lg font-semibold mb-3">1. Error Boundary Testing</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Select an error type to test how error boundaries handle different scenarios:
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
              {errorTypes.map((type) => (
                <Button
                  key={type.id}
                  onClick={() => {
                    setSelectedErrorType(type.id);
                    setShowErrorComponent(true);
                  }}
                  variant={selectedErrorType === type.id ? "default" : "outline"}
                  className="h-auto p-3 flex flex-col items-start"
                >
                  <span className="font-medium">{type.label}</span>
                  <span className="text-xs text-muted-foreground">{type.description}</span>
                </Button>
              ))}
            </div>

            <div className="flex gap-2 mb-4">
              <Button
                onClick={() => setShowErrorComponent(false)}
                variant="ghost"
                size="sm"
              >
                Reset
              </Button>
              {selectedErrorType && (
                <Badge variant="secondary">
                  Testing: {errorTypes.find(t => t.id === selectedErrorType)?.label}
                </Badge>
              )}
            </div>

            {/* Error Component Display */}
            {showErrorComponent && selectedErrorType && (
              <ComponentErrorBoundary
                componentName="ErrorThrowingComponent"
                enableReporting={true}
                variant="component"
                isolateError={true}
              >
                <ErrorThrowingComponent errorType={selectedErrorType} />
              </ComponentErrorBoundary>
            )}
          </div>

          <Separator />

          {/* Section Error Boundary */}
          <div>
            <h3 className="text-lg font-semibold mb-3">2. Section Error Boundary</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Error boundaries can isolate errors to specific sections:
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <SectionErrorBoundary sectionName="safe-section">
                <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                  <h4 className="font-medium text-green-800">Safe Section</h4>
                  <p className="text-sm text-green-600">This section works normally</p>
                </div>
              </SectionErrorBoundary>

              <SectionErrorBoundary 
                sectionName="error-section"
                fallback={
                  <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                    <h4 className="font-medium text-yellow-800">Section Unavailable</h4>
                    <p className="text-sm text-yellow-600">This section encountered an error</p>
                  </div>
                }
              >
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                  <h4 className="font-medium text-blue-800">Another Safe Section</h4>
                  <p className="text-sm text-blue-600">This section also works normally</p>
                </div>
              </SectionErrorBoundary>
            </div>
          </div>

          <Separator />

          {/* Form Error Boundary */}
          <div>
            <h3 className="text-lg font-semibold mb-3">3. Form Error Boundary</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Forms can be protected with specialized error boundaries:
            </p>
            
            <FormErrorBoundary formName="demo-form">
              <div className="space-y-4 p-4 border rounded-md">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Name</label>
                    <input 
                      type="text" 
                      className="w-full p-2 border rounded-md" 
                      placeholder="Enter your name"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Email</label>
                    <input 
                      type="email" 
                      className="w-full p-2 border rounded-md" 
                      placeholder="Enter your email"
                    />
                  </div>
                </div>
                <Button type="submit" size="sm">Submit Form</Button>
              </div>
            </FormErrorBoundary>
          </div>

          <Separator />

          {/* Data Error Boundary */}
          <div>
            <h3 className="text-lg font-semibold mb-3">4. Data Error Boundary</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Data display components can gracefully handle errors:
            </p>
            
            <DataErrorBoundary dataType="user-stats">
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-md">
                  <div className="text-2xl font-bold">1,234</div>
                  <div className="text-sm text-muted-foreground">Total Users</div>
                </div>
                <div className="text-center p-4 border rounded-md">
                  <div className="text-2xl font-bold">567</div>
                  <div className="text-sm text-muted-foreground">Active Sessions</div>
                </div>
                <div className="text-center p-4 border rounded-md">
                  <div className="text-2xl font-bold">89%</div>
                  <div className="text-sm text-muted-foreground">Uptime</div>
                </div>
              </div>
            </DataErrorBoundary>
          </div>

          <Separator />

          {/* Error Handler Hook */}
          <div>
            <h3 className="text-lg font-semibold mb-3">5. Error Handler Hook</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Components can use the error handler hook for custom error handling:
            </p>
            
            <SafeComponentWithErrorHook />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
