import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ComprehensiveFallback,
  NetworkErrorFallback,
  DataLoadingErrorFallback,
  AuthenticationErrorFallback,
  PermissionErrorFallback,
  EmptyStateWithGuidance,
  EmptyProblemsState,
  EmptyExpertsState,
  EmptySearchResultsState,
  LoadingErrorWithRetry,
  ConfigurationErrorFallback,
  useRetryMechanism
} from '../common/ComprehensiveFallbackSystem';
import { useErrorRecovery } from '@/lib/errorRecoveryService';
import { AlertTriangle, CheckCircle, RefreshCw, Settings } from 'lucide-react';

/**
 * Comprehensive demo of the fallback system
 * This component demonstrates all fallback scenarios and their usage
 */
export const FallbackSystemDemo: React.FC = () => {
  const [activeDemo, setActiveDemo] = useState<string>('network');
  const [demoState, setDemoState] = useState<'normal' | 'error' | 'empty' | 'loading'>('normal');
  const [retryCount, setRetryCount] = useState(0);
  const { handleError, getHealth } = useErrorRecovery();

  // Simulate different error scenarios
  const simulateError = useCallback(async (errorType: string) => {
    setDemoState('error');
    
    let error: Error;
    switch (errorType) {
      case 'network':
        error = new Error('Failed to fetch data from server');
        break;
      case 'auth':
        error = new Error('401 Unauthorized - Session expired');
        break;
      case 'permission':
        error = new Error('403 Forbidden - Access denied');
        break;
      case 'module':
        error = new Error('Cannot resolve module "./NonExistentComponent"');
        break;
      case 'database':
        error = new Error('Database connection timeout');
        break;
      case 'chunk':
        error = new Error('Loading chunk 1 failed');
        break;
      default:
        error = new Error('Unknown error occurred');
    }

    const strategy = await handleError(error, {
      component: 'FallbackSystemDemo',
      operation: `simulate-${errorType}-error`
    });

    console.log('Recovery strategy:', strategy);
  }, [handleError]);

  const simulateRetry = useCallback(() => {
    setRetryCount(prev => prev + 1);
    // Simulate success after 2 retries
    if (retryCount >= 1) {
      setDemoState('normal');
      setRetryCount(0);
    }
  }, [retryCount]);

  const resetDemo = useCallback(() => {
    setDemoState('normal');
    setRetryCount(0);
  }, []);

  // Demo component for retry mechanism
  const RetryMechanismDemo: React.FC = () => {
    const mockOperation = useCallback(async () => {
      if (Math.random() > 0.7) {
        throw new Error('Random operation failure');
      }
      console.log('Operation succeeded!');
    }, []);

    const { retry, retryCount, isRetrying, canRetry, lastError } = useRetryMechanism(
      mockOperation,
      { maxRetries: 3, retryDelay: 1000, exponentialBackoff: true }
    );

    return (
      <Card>
        <CardHeader>
          <CardTitle>Retry Mechanism Demo</CardTitle>
          <CardDescription>
            Test the automatic retry mechanism with exponential backoff
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-4">
            <Button onClick={retry} disabled={!canRetry || isRetrying}>
              {isRetrying ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Retrying...
                </>
              ) : (
                'Test Operation'
              )}
            </Button>
            <Badge variant={canRetry ? 'default' : 'destructive'}>
              Attempts: {retryCount}/3
            </Badge>
          </div>
          
          {lastError && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Last error: {lastError.message}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    );
  };

  const renderFallbackDemo = () => {
    switch (activeDemo) {
      case 'network':
        return demoState === 'error' ? (
          <NetworkErrorFallback onRetry={simulateRetry} />
        ) : (
          <div className="p-8 text-center">
            <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
            <h3 className="text-lg font-semibold mb-2">Network Connection OK</h3>
            <p className="text-gray-600">All network operations are working normally.</p>
          </div>
        );

      case 'data':
        return demoState === 'error' ? (
          <DataLoadingErrorFallback 
            onRetry={simulateRetry} 
            dataType="المشاكل التقنية" 
          />
        ) : demoState === 'empty' ? (
          <EmptyProblemsState 
            onCreateProblem={() => console.log('Create problem clicked')}
          />
        ) : (
          <div className="p-8 text-center">
            <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
            <h3 className="text-lg font-semibold mb-2">Data Loaded Successfully</h3>
            <p className="text-gray-600">All data has been loaded without errors.</p>
          </div>
        );

      case 'auth':
        return demoState === 'error' ? (
          <AuthenticationErrorFallback 
            onRetry={simulateRetry}
            onLogin={() => console.log('Login clicked')}
          />
        ) : (
          <div className="p-8 text-center">
            <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
            <h3 className="text-lg font-semibold mb-2">Authenticated</h3>
            <p className="text-gray-600">User is successfully authenticated.</p>
          </div>
        );

      case 'permission':
        return demoState === 'error' ? (
          <PermissionErrorFallback 
            onGoBack={() => console.log('Go back clicked')}
          />
        ) : (
          <div className="p-8 text-center">
            <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
            <h3 className="text-lg font-semibold mb-2">Access Granted</h3>
            <p className="text-gray-600">You have permission to access this resource.</p>
          </div>
        );

      case 'empty-problems':
        return (
          <EmptyProblemsState 
            onCreateProblem={() => console.log('Create problem clicked')}
            onBrowseExperts={() => console.log('Browse experts clicked')}
          />
        );

      case 'empty-experts':
        return (
          <EmptyExpertsState 
            onCreateProfile={() => console.log('Create profile clicked')}
            onBrowseProblems={() => console.log('Browse problems clicked')}
          />
        );

      case 'empty-search':
        return (
          <EmptySearchResultsState 
            searchTerm="React TypeScript"
            onClearSearch={() => console.log('Clear search clicked')}
            onBrowseAll={() => console.log('Browse all clicked')}
          />
        );

      case 'loading':
        return demoState === 'error' ? (
          <LoadingErrorWithRetry 
            onRetry={simulateRetry}
            resourceType="المكونات"
          />
        ) : (
          <div className="p-8 text-center">
            <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
            <h3 className="text-lg font-semibold mb-2">Loading Complete</h3>
            <p className="text-gray-600">All components loaded successfully.</p>
          </div>
        );

      case 'config':
        return demoState === 'error' ? (
          <ConfigurationErrorFallback 
            onOpenSettings={() => console.log('Open settings clicked')}
            configType="قاعدة البيانات"
          />
        ) : (
          <div className="p-8 text-center">
            <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
            <h3 className="text-lg font-semibold mb-2">Configuration Valid</h3>
            <p className="text-gray-600">All system configurations are correct.</p>
          </div>
        );

      case 'custom':
        return demoState === 'error' ? (
          <ComprehensiveFallback
            title="Custom Error Scenario"
            description="This is a custom error scenario with multiple actions and retry mechanism."
            level="component"
            onRetry={simulateRetry}
            retryConfig={{
              maxRetries: 3,
              retryDelay: 1000,
              exponentialBackoff: true
            }}
            actions={[
              {
                label: 'Contact Support',
                action: () => console.log('Contact support clicked'),
                variant: 'secondary',
                icon: <Settings className="w-4 h-4" />
              },
              {
                label: 'Report Issue',
                action: () => console.log('Report issue clicked'),
                variant: 'danger'
              }
            ]}
            showDetails={true}
            error={new Error('Custom error with detailed information and stack trace')}
          />
        ) : (
          <div className="p-8 text-center">
            <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-500" />
            <h3 className="text-lg font-semibold mb-2">Custom Component OK</h3>
            <p className="text-gray-600">Custom component is working normally.</p>
          </div>
        );

      default:
        return null;
    }
  };

  const systemHealth = getHealth();

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2">Comprehensive Fallback System Demo</h1>
        <p className="text-gray-600">
          Interactive demonstration of all fallback components and error recovery mechanisms
        </p>
      </div>

      {/* System Health Overview */}
      <Card>
        <CardHeader>
          <CardTitle>System Health</CardTitle>
          <CardDescription>Current error recovery system status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{systemHealth.totalErrors}</div>
              <div className="text-sm text-gray-600">Total Errors</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{systemHealth.recentErrors}</div>
              <div className="text-sm text-gray-600">Recent Errors</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {Math.round(systemHealth.recoverySuccess * 100)}%
              </div>
              <div className="text-sm text-gray-600">Recovery Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{retryCount}</div>
              <div className="text-sm text-gray-600">Demo Retries</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Retry Mechanism Demo */}
      <RetryMechanismDemo />

      {/* Fallback Scenarios */}
      <Card>
        <CardHeader>
          <CardTitle>Fallback Scenarios</CardTitle>
          <CardDescription>
            Test different error scenarios and their corresponding fallback components
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeDemo} onValueChange={setActiveDemo}>
            <TabsList className="grid w-full grid-cols-4 lg:grid-cols-8">
              <TabsTrigger value="network">Network</TabsTrigger>
              <TabsTrigger value="data">Data</TabsTrigger>
              <TabsTrigger value="auth">Auth</TabsTrigger>
              <TabsTrigger value="permission">Permission</TabsTrigger>
              <TabsTrigger value="empty-problems">Empty Problems</TabsTrigger>
              <TabsTrigger value="empty-experts">Empty Experts</TabsTrigger>
              <TabsTrigger value="empty-search">Empty Search</TabsTrigger>
              <TabsTrigger value="loading">Loading</TabsTrigger>
              <TabsTrigger value="config">Config</TabsTrigger>
              <TabsTrigger value="custom">Custom</TabsTrigger>
            </TabsList>

            <div className="mt-6">
              {/* Control buttons */}
              <div className="flex gap-2 mb-4">
                <Button 
                  onClick={() => simulateError(activeDemo)}
                  variant="destructive"
                  disabled={demoState === 'error'}
                >
                  Simulate Error
                </Button>
                <Button 
                  onClick={() => setDemoState('empty')}
                  variant="outline"
                  disabled={!['data', 'empty-problems', 'empty-experts', 'empty-search'].includes(activeDemo)}
                >
                  Show Empty State
                </Button>
                <Button 
                  onClick={resetDemo}
                  variant="outline"
                >
                  Reset Demo
                </Button>
              </div>

              {/* Demo content */}
              <div className="border rounded-lg min-h-64">
                {renderFallbackDemo()}
              </div>
            </div>
          </Tabs>
        </CardContent>
      </Card>

      {/* Usage Examples */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Examples</CardTitle>
          <CardDescription>Code examples for implementing fallback components</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="basic">
            <TabsList>
              <TabsTrigger value="basic">Basic Usage</TabsTrigger>
              <TabsTrigger value="advanced">Advanced Usage</TabsTrigger>
              <TabsTrigger value="integration">Error Boundary Integration</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="mt-4">
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`// Basic network error fallback
<NetworkErrorFallback 
  onRetry={() => refetch()} 
  level="component" 
/>

// Empty state with guidance
<EmptyProblemsState 
  onCreateProblem={() => navigate('/problems/new')}
  onBrowseExperts={() => navigate('/experts')}
/>

// Custom fallback with actions
<ComprehensiveFallback
  title="Custom Error"
  description="Something went wrong"
  onRetry={handleRetry}
  actions={[
    {
      label: 'Contact Support',
      action: openSupport,
      variant: 'secondary'
    }
  ]}
/>`}
              </pre>
            </TabsContent>

            <TabsContent value="advanced" className="mt-4">
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`// Advanced retry mechanism
const { retry, retryCount, isRetrying, canRetry } = useRetryMechanism(
  async () => {
    const response = await fetch('/api/data');
    if (!response.ok) throw new Error('Failed to fetch');
    return response.json();
  },
  {
    maxRetries: 3,
    retryDelay: 1000,
    exponentialBackoff: true
  }
);

// Error recovery service
const { handleError } = useErrorRecovery();

try {
  await riskyOperation();
} catch (error) {
  const strategy = await handleError(error, {
    component: 'MyComponent',
    operation: 'data-fetch'
  });
  
  if (strategy?.type === 'retry') {
    retry();
  }
}`}
              </pre>
            </TabsContent>

            <TabsContent value="integration" className="mt-4">
              <pre className="bg-gray-100 p-4 rounded-md text-sm overflow-x-auto">
{`// Error boundary with fallback system
import { createErrorRecoveryBoundary } from '@/lib/errorRecoveryService';
import { ComponentErrorFallback } from '@/components/common/ComprehensiveFallbackSystem';

const MyComponentBoundary = createErrorRecoveryBoundary(
  'MyComponent',
  ComponentErrorFallback
);

// Usage
<MyComponentBoundary>
  <MyComponent />
</MyComponentBoundary>

// Or with SimpleErrorBoundary
<SimpleErrorBoundary
  level="component"
  maxRetries={3}
  fallbackComponent={NetworkErrorFallback}
>
  <NetworkDependentComponent />
</SimpleErrorBoundary>`}
              </pre>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default FallbackSystemDemo;