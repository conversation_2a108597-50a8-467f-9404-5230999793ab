import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useDeviceType, useOrientation, useSwipeGestures, useTouchSupport } from '@/hooks/use-mobile';
import { useTouchTargets } from '@/hooks/useAccessibility';
import { cn } from '@/lib/utils';

interface MobileOptimizedWrapperProps {
  children: React.ReactNode;
  className?: string;
  enableSwipeGestures?: boolean;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  optimizeTouchTargets?: boolean;
  enablePullToRefresh?: boolean;
  onPullToRefresh?: () => Promise<void>;
  swipeThreshold?: number;
}

export function MobileOptimizedWrapper({
  children,
  className = '',
  enableSwipeGestures = false,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  optimizeTouchTargets = true,
  enablePullToRefresh = false,
  onPullToRefresh,
  swipeThreshold = 50
}: MobileOptimizedWrapperProps) {
  const { isMobile, isTablet, isTouchDevice } = useDeviceType();
  const orientation = useOrientation();
  const hasTouch = useTouchSupport();
  const { validateTouchTargets } = useTouchTargets();
  
  const containerRef = useRef<HTMLDivElement>(null);
  const [isPulling, setIsPulling] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  const pullStartY = useRef<number>(0);
  const pullThreshold = 80;

  // Swipe gesture handlers
  const { handleTouchStart, handleTouchMove, handleTouchEnd } = useSwipeGestures(
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    swipeThreshold
  );

  // Pull to refresh handlers
  const handlePullStart = useCallback((e: TouchEvent) => {
    if (!enablePullToRefresh || !onPullToRefresh) return;
    
    const touch = e.touches[0];
    pullStartY.current = touch.clientY;
    
    // Only start pull if at top of container
    if (containerRef.current && containerRef.current.scrollTop === 0) {
      setIsPulling(true);
    }
  }, [enablePullToRefresh, onPullToRefresh]);

  const handlePullMove = useCallback((e: TouchEvent) => {
    if (!isPulling || !enablePullToRefresh) return;
    
    const touch = e.touches[0];
    const distance = Math.max(0, touch.clientY - pullStartY.current);
    
    if (distance > 0) {
      e.preventDefault();
      setPullDistance(Math.min(distance, pullThreshold * 1.5));
    }
  }, [isPulling, enablePullToRefresh, pullThreshold]);

  const handlePullEnd = useCallback(async () => {
    if (!isPulling || !enablePullToRefresh || !onPullToRefresh) return;
    
    setIsPulling(false);
    
    if (pullDistance >= pullThreshold) {
      setIsRefreshing(true);
      try {
        await onPullToRefresh();
      } finally {
        setIsRefreshing(false);
      }
    }
    
    setPullDistance(0);
  }, [isPulling, enablePullToRefresh, onPullToRefresh, pullDistance, pullThreshold]);

  // Combined touch handlers
  const handleTouchStartCombined = useCallback((e: TouchEvent) => {
    if (enableSwipeGestures) {
      handleTouchStart(e);
    }
    if (enablePullToRefresh) {
      handlePullStart(e);
    }
  }, [enableSwipeGestures, enablePullToRefresh, handleTouchStart, handlePullStart]);

  const handleTouchMoveCombined = useCallback((e: TouchEvent) => {
    if (enableSwipeGestures) {
      handleTouchMove(e);
    }
    if (enablePullToRefresh) {
      handlePullMove(e);
    }
  }, [enableSwipeGestures, enablePullToRefresh, handleTouchMove, handlePullMove]);

  const handleTouchEndCombined = useCallback(() => {
    if (enableSwipeGestures) {
      handleTouchEnd();
    }
    if (enablePullToRefresh) {
      handlePullEnd();
    }
  }, [enableSwipeGestures, enablePullToRefresh, handleTouchEnd, handlePullEnd]);

  // Set up touch event listeners
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !isTouchDevice) return;

    container.addEventListener('touchstart', handleTouchStartCombined, { passive: false });
    container.addEventListener('touchmove', handleTouchMoveCombined, { passive: false });
    container.addEventListener('touchend', handleTouchEndCombined);

    return () => {
      container.removeEventListener('touchstart', handleTouchStartCombined);
      container.removeEventListener('touchmove', handleTouchMoveCombined);
      container.removeEventListener('touchend', handleTouchEndCombined);
    };
  }, [isTouchDevice, handleTouchStartCombined, handleTouchMoveCombined, handleTouchEndCombined]);

  // Optimize touch targets
  useEffect(() => {
    if (optimizeTouchTargets && isTouchDevice && containerRef.current) {
      validateTouchTargets(containerRef.current);
    }
  }, [optimizeTouchTargets, isTouchDevice, validateTouchTargets, children]);

  // Apply mobile-specific styles
  const mobileStyles = {
    // Improve scrolling performance on mobile
    WebkitOverflowScrolling: 'touch',
    // Prevent zoom on double tap
    touchAction: enableSwipeGestures ? 'pan-y' : 'manipulation',
    // Improve text rendering on mobile
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
    // Transform for pull to refresh
    transform: isPulling ? `translateY(${pullDistance * 0.5}px)` : 'none',
    transition: isPulling ? 'none' : 'transform 0.3s ease-out'
  };

  const containerClasses = cn(
    'mobile-optimized-wrapper',
    {
      'touch-device': isTouchDevice,
      'mobile': isMobile,
      'tablet': isTablet,
      'portrait': orientation === 'portrait',
      'landscape': orientation === 'landscape',
      'pulling': isPulling,
      'refreshing': isRefreshing
    },
    className
  );

  return (
    <div
      ref={containerRef}
      className={containerClasses}
      style={mobileStyles}
      data-mobile-optimized="true"
    >
      {/* Pull to refresh indicator */}
      {enablePullToRefresh && (isPulling || isRefreshing) && (
        <div 
          className="pull-to-refresh-indicator"
          style={{
            position: 'absolute',
            top: -40,
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 1000,
            opacity: pullDistance / pullThreshold,
            transition: isRefreshing ? 'opacity 0.3s ease' : 'none'
          }}
        >
          <div className="flex items-center justify-center w-8 h-8 bg-primary rounded-full">
            {isRefreshing ? (
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              <div className="w-4 h-4 border-2 border-white rounded-full" />
            )}
          </div>
        </div>
      )}
      
      {children}
    </div>
  );
}

// Hook for using mobile optimization features
export function useMobileOptimization() {
  const { isMobile, isTablet, isTouchDevice } = useDeviceType();
  const orientation = useOrientation();
  const hasTouch = useTouchSupport();

  const getMobileBreakpoint = useCallback(() => {
    if (isMobile) return 'mobile';
    if (isTablet) return 'tablet';
    return 'desktop';
  }, [isMobile, isTablet]);

  const getOptimalColumns = useCallback((maxColumns: number = 4) => {
    if (isMobile) return 1;
    if (isTablet) return orientation === 'portrait' ? 2 : 3;
    return maxColumns;
  }, [isMobile, isTablet, orientation]);

  const getOptimalSpacing = useCallback(() => {
    if (isMobile) return 'compact';
    if (isTablet) return 'normal';
    return 'comfortable';
  }, [isMobile, isTablet]);

  const shouldUseBottomSheet = useCallback(() => {
    return isMobile;
  }, [isMobile]);

  const shouldUseStickyHeader = useCallback(() => {
    return isMobile || isTablet;
  }, [isMobile, isTablet]);

  return {
    isMobile,
    isTablet,
    isTouchDevice,
    hasTouch,
    orientation,
    getMobileBreakpoint,
    getOptimalColumns,
    getOptimalSpacing,
    shouldUseBottomSheet,
    shouldUseStickyHeader
  };
}

// CSS classes for mobile optimization
export const mobileOptimizationClasses = {
  touchTarget: 'min-h-[44px] min-w-[44px] touch-manipulation',
  mobileSpacing: 'mobile:p-4 tablet:p-6 desktop:p-8',
  responsiveText: 'mobile:text-sm tablet:text-base desktop:text-lg',
  responsiveGrid: 'grid mobile:grid-cols-1 tablet:grid-cols-2 desktop:grid-cols-3',
  mobileFirst: 'flex flex-col mobile:gap-2 tablet:gap-4 desktop:gap-6',
  touchFriendly: 'select-none touch-manipulation active:scale-95 transition-transform',
  mobileHidden: 'mobile:hidden',
  mobileOnly: 'hidden mobile:block',
  tabletUp: 'hidden tablet:block'
};
