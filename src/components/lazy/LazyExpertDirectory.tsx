import { lazy, Suspense } from 'react';
import { ExpertDirectorySkeleton } from '@/components/experts/ExpertSkeleton';

// Lazy load the ExpertDirectory component
const ExpertDirectory = lazy(() => 
  import('@/components/experts/ExpertDirectory').then(module => ({
    default: module.ExpertDirectory
  }))
);

export function LazyExpertDirectory(props: any) {
  return (
    <Suspense fallback={<ExpertDirectorySkeleton />}>
      <ExpertDirectory {...props} />
    </Suspense>
  );
}