import { lazy, Suspense } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';

// Loading skeleton for charts
const ChartSkeleton = () => (
  <Card>
    <CardHeader>
      <CardTitle>
        <div className="h-6 bg-gray-200 rounded animate-pulse w-32" />
      </CardTitle>
    </CardHeader>
    <CardContent>
      <div className="h-64 bg-gray-200 rounded animate-pulse" />
    </CardContent>
  </Card>
);

// Lazy load chart components that use heavy libraries
export const LazyBarChart = lazy(() => 
  import('@/components/charts/BarChart').catch(() => 
    import('@/components/ui/fallback-chart').then(module => ({
      default: module.FallbackBarChart
    }))
  )
);

export const LazyLineChart = lazy(() => 
  import('@/components/charts/LineChart').catch(() => 
    import('@/components/ui/fallback-chart').then(module => ({
      default: module.FallbackLineC<PERSON>
    }))
  )
);

export const Lazy<PERSON><PERSON><PERSON>hart = lazy(() => 
  import('@/components/charts/PieChart').catch(() => 
    import('@/components/ui/fallback-chart').then(module => ({
      default: module.FallbackPieChart
    }))
  )
);

// Wrapper components with Suspense
export function LazyBarChartWrapper(props: any) {
  return (
    <Suspense fallback={<ChartSkeleton />}>
      <LazyBarChart {...props} />
    </Suspense>
  );
}

export function LazyLineChartWrapper(props: any) {
  return (
    <Suspense fallback={<ChartSkeleton />}>
      <LazyLineChart {...props} />
    </Suspense>
  );
}

export function LazyPieChartWrapper(props: any) {
  return (
    <Suspense fallback={<ChartSkeleton />}>
      <LazyPieChart {...props} />
    </Suspense>
  );
}