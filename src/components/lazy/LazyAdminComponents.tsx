import { lazy, Suspense } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';

// Loading skeleton for admin components
const AdminSkeleton = () => (
  <div className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {[...Array(4)].map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
          </CardHeader>
          <CardContent>
            <div className="h-8 bg-gray-200 rounded animate-pulse" />
          </CardContent>
        </Card>
      ))}
    </div>
    <Card>
      <CardHeader>
        <CardTitle>
          <div className="h-6 bg-gray-200 rounded animate-pulse w-48" />
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-200 rounded animate-pulse" />
          ))}
        </div>
      </CardContent>
    </Card>
  </div>
);

// Lazy load admin components
export const LazyAdminStats = lazy(() => 
  import('@/components/admin/AdminStats').then(module => ({
    default: module.AdminStats
  }))
);

export const LazyContentModeration = lazy(() => 
  import('@/components/admin/ContentModeration').then(module => ({
    default: module.ContentModeration
  }))
);

export const LazyUserManagement = lazy(() => 
  import('@/components/admin/UserManagement').then(module => ({
    default: module.UserManagement
  }))
);

export const LazySystemSettings = lazy(() => 
  import('@/components/admin/SystemSettings').then(module => ({
    default: module.SystemSettings
  }))
);

export const LazyAnalyticsDashboard = lazy(() => 
  import('@/components/admin/AnalyticsDashboard').then(module => ({
    default: module.AnalyticsDashboard
  }))
);

// Wrapper components with Suspense
export function LazyAdminStatsWrapper(props: any) {
  return (
    <Suspense fallback={<AdminSkeleton />}>
      <LazyAdminStats {...props} />
    </Suspense>
  );
}

export function LazyContentModerationWrapper(props: any) {
  return (
    <Suspense fallback={<AdminSkeleton />}>
      <LazyContentModeration {...props} />
    </Suspense>
  );
}

export function LazyUserManagementWrapper(props: any) {
  return (
    <Suspense fallback={<AdminSkeleton />}>
      <LazyUserManagement {...props} />
    </Suspense>
  );
}

export function LazySystemSettingsWrapper(props: any) {
  return (
    <Suspense fallback={<AdminSkeleton />}>
      <LazySystemSettings {...props} />
    </Suspense>
  );
}

export function LazyAnalyticsDashboardWrapper(props: any) {
  return (
    <Suspense fallback={<AdminSkeleton />}>
      <LazyAnalyticsDashboard {...props} />
    </Suspense>
  );
}