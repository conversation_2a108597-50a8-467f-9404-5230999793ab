import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { useAuthContext } from '@/components/auth/AuthProvider'
import { solutionOperations } from '@/lib/database'
import { Star, ThumbsUp, ThumbsDown, MessageSquare, Send, Loader2 } from 'lucide-react'

interface SolutionRatingProps {
  solutionId: string
  currentRating: number
  votes: any[]
  onRatingUpdate?: () => void
  disabled?: boolean
}

interface RatingData {
  rating: number
  feedback?: string
}

export function SolutionRating({ 
  solutionId, 
  currentRating, 
  votes, 
  onRatingUpdate,
  disabled = false 
}: SolutionRatingProps) {
  const { user } = useAuthContext()
  const { toast } = useToast()
  const [isRating, setIsRating] = useState(false)
  const [selectedRating, setSelectedRating] = useState(0)
  const [feedback, setFeedback] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  const userVote = votes?.find(vote => vote.userId === user?.id)
  const upVotes = votes?.filter(vote => vote.type === 'up').length || 0
  const downVotes = votes?.filter(vote => vote.type === 'down').length || 0

  const handleVote = async (voteType: 'up' | 'down') => {
    if (!user || disabled) {
      toast({
        title: 'يجب تسجيل الدخول',
        description: 'يجب تسجيل الدخول للتصويت على الحلول',
        variant: 'destructive'
      })
      return
    }

    try {
      const { error } = await solutionOperations.voteSolution(solutionId, user.id, voteType)
      
      if (error) {
        throw new Error(error.message)
      }

      toast({
        title: 'تم التصويت بنجاح',
        description: `تم ${voteType === 'up' ? 'تأييد' : 'رفض'} الحل`
      })

      onRatingUpdate?.()
    } catch (error) {
      console.error('Error voting:', error)
      toast({
        title: 'خطأ في التصويت',
        description: 'حدث خطأ أثناء التصويت',
        variant: 'destructive'
      })
    }
  }

  const handleRatingSubmit = async () => {
    if (!user || !selectedRating || disabled) return

    setIsSubmitting(true)
    try {
      // This would need to be implemented in the database operations
      const { error } = await solutionOperations.rateSolution?.(solutionId, {
        userId: user.id,
        rating: selectedRating,
        feedback: feedback.trim() || undefined
      })

      if (error) {
        throw new Error(error.message)
      }

      toast({
        title: 'تم التقييم بنجاح',
        description: 'شكراً لك على تقييم الحل'
      })

      setIsRating(false)
      setSelectedRating(0)
      setFeedback('')
      onRatingUpdate?.()
    } catch (error) {
      console.error('Error rating solution:', error)
      toast({
        title: 'خطأ في التقييم',
        description: 'حدث خطأ أثناء تقييم الحل',
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const StarRating = ({ rating, onRatingChange, readonly = false }: {
    rating: number
    onRatingChange?: (rating: number) => void
    readonly?: boolean
  }) => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => !readonly && onRatingChange?.(star)}
            disabled={readonly || disabled}
            className={`p-1 transition-colors ${
              readonly || disabled 
                ? 'cursor-default' 
                : 'hover:scale-110 cursor-pointer'
            }`}
          >
            <Star
              className={`w-5 h-5 ${
                star <= rating
                  ? 'fill-yellow-400 text-yellow-400'
                  : 'text-gray-300'
              }`}
            />
          </button>
        ))}
        <span className="text-sm text-gray-600 mr-2">
          ({rating.toFixed(1)})
        </span>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Current Rating Display */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <StarRating rating={currentRating} readonly />
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleVote('up')}
              disabled={disabled}
              className={`${
                userVote?.type === 'up'
                  ? 'text-green-600 bg-green-50'
                  : 'text-gray-600 hover:text-green-600'
              }`}
            >
              <ThumbsUp className="w-4 h-4 ml-1" />
              {upVotes}
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleVote('down')}
              disabled={disabled}
              className={`${
                userVote?.type === 'down'
                  ? 'text-red-600 bg-red-50'
                  : 'text-gray-600 hover:text-red-600'
              }`}
            >
              <ThumbsDown className="w-4 h-4 ml-1" />
              {downVotes}
            </Button>
          </div>
        </div>

        {user && !disabled && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsRating(!isRating)}
          >
            <Star className="w-4 h-4 ml-2" />
            قيم الحل
          </Button>
        )}
      </div>

      {/* Rating Form */}
      {isRating && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">تقييم الحل</CardTitle>
            <CardDescription>
              قيم جودة الحل وفعاليته في حل المشكلة
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                التقييم *
              </label>
              <StarRating 
                rating={selectedRating} 
                onRatingChange={setSelectedRating}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تعليق (اختياري)
              </label>
              <Textarea
                placeholder="اكتب تعليقك على الحل..."
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                rows={3}
              />
            </div>

            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setIsRating(false)
                  setSelectedRating(0)
                  setFeedback('')
                }}
                disabled={isSubmitting}
              >
                إلغاء
              </Button>
              <Button
                onClick={handleRatingSubmit}
                disabled={!selectedRating || isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                    جاري الإرسال...
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4 ml-2" />
                    إرسال التقييم
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* User's Previous Vote/Rating */}
      {userVote && (
        <div className="text-sm text-gray-600">
          <Badge variant="outline" className="flex items-center gap-1 w-fit">
            {userVote.type === 'up' ? (
              <>
                <ThumbsUp className="w-3 h-3" />
                أيدت هذا الحل
              </>
            ) : (
              <>
                <ThumbsDown className="w-3 h-3" />
                رفضت هذا الحل
              </>
            )}
          </Badge>
        </div>
      )}
    </div>
  )
}