import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { useAuthContext, useRoleAccess } from '@/components/auth/AuthProvider'
import { solutionOperations } from '@/lib/database'
import { 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  FileText, 
  Settings, 
  PlayCircle,
  Loader2,
  MessageSquare,
  User,
  Calendar
} from 'lucide-react'

interface SolutionStatusTrackerProps {
  solutionId: string
  currentStatus: 'draft' | 'submitted' | 'approved' | 'implemented'
  implementationNotes?: string
  onStatusUpdate?: () => void
  expertId: string
  problemSubmitterId: string
}

const STATUS_FLOW = [
  {
    key: 'draft',
    label: 'مسودة',
    description: 'الحل قيد الكتابة',
    icon: <FileText className="w-5 h-5" />,
    color: 'bg-gray-100 text-gray-800',
    allowedNextStates: ['submitted']
  },
  {
    key: 'submitted',
    label: 'مرسل',
    description: 'تم إرسال الحل للمراجعة',
    icon: <Clock className="w-5 h-5" />,
    color: 'bg-blue-100 text-blue-800',
    allowedNextStates: ['approved', 'draft']
  },
  {
    key: 'approved',
    label: 'معتمد',
    description: 'تم اعتماد الحل من قبل الإدارة',
    icon: <CheckCircle className="w-5 h-5" />,
    color: 'bg-green-100 text-green-800',
    allowedNextStates: ['implemented']
  },
  {
    key: 'implemented',
    label: 'مطبق',
    description: 'تم تطبيق الحل بنجاح',
    icon: <PlayCircle className="w-5 h-5" />,
    color: 'bg-purple-100 text-purple-800',
    allowedNextStates: []
  }
]

export function SolutionStatusTracker({
  solutionId,
  currentStatus,
  implementationNotes,
  onStatusUpdate,
  expertId,
  problemSubmitterId
}: SolutionStatusTrackerProps) {
  const { user } = useAuthContext()
  const { isAdmin } = useRoleAccess()
  const { toast } = useToast()
  const [isUpdating, setIsUpdating] = useState(false)
  const [newImplementationNotes, setNewImplementationNotes] = useState(implementationNotes || '')
  const [showNotesEditor, setShowNotesEditor] = useState(false)

  const currentStatusIndex = STATUS_FLOW.findIndex(status => status.key === currentStatus)
  const currentStatusConfig = STATUS_FLOW[currentStatusIndex]

  const canUpdateStatus = (targetStatus: string) => {
    // Admin can always update status
    if (isAdmin) return true
    
    // Expert can only update their own solutions from draft to submitted
    if (user?.id === expertId && currentStatus === 'draft' && targetStatus === 'submitted') {
      return true
    }
    
    // Problem submitter can approve solutions
    if (user?.id === problemSubmitterId && currentStatus === 'submitted' && targetStatus === 'approved') {
      return true
    }
    
    // Problem submitter can mark as implemented
    if (user?.id === problemSubmitterId && currentStatus === 'approved' && targetStatus === 'implemented') {
      return true
    }
    
    return false
  }

  const handleStatusUpdate = async (newStatus: string) => {
    if (!canUpdateStatus(newStatus)) {
      toast({
        title: 'غير مصرح',
        description: 'ليس لديك صلاحية لتحديث حالة هذا الحل',
        variant: 'destructive'
      })
      return
    }

    setIsUpdating(true)
    try {
      const updateData: any = {
        status: newStatus,
        updated_at: new Date().toISOString()
      }

      // Add implementation notes if moving to implemented status
      if (newStatus === 'implemented' && newImplementationNotes.trim()) {
        updateData.implementation_notes = newImplementationNotes.trim()
      }

      const { error } = await solutionOperations.updateSolution(solutionId, updateData)

      if (error) {
        throw new Error(error.message)
      }

      toast({
        title: 'تم التحديث بنجاح',
        description: `تم تحديث حالة الحل إلى: ${STATUS_FLOW.find(s => s.key === newStatus)?.label}`
      })

      setShowNotesEditor(false)
      onStatusUpdate?.()
    } catch (error) {
      console.error('Error updating solution status:', error)
      toast({
        title: 'خطأ في التحديث',
        description: 'حدث خطأ أثناء تحديث حالة الحل',
        variant: 'destructive'
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const getStatusActions = () => {
    if (!currentStatusConfig) return []

    return currentStatusConfig.allowedNextStates
      .filter(status => canUpdateStatus(status))
      .map(status => {
        const statusConfig = STATUS_FLOW.find(s => s.key === status)
        if (!statusConfig) return null

        return {
          key: status,
          label: statusConfig.label,
          description: statusConfig.description,
          color: statusConfig.color
        }
      })
      .filter(Boolean)
  }

  const statusActions = getStatusActions()

  return (
    <div className="space-y-4">
      {/* Current Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-full bg-gray-100">
                {currentStatusConfig?.icon}
              </div>
              <div>
                <CardTitle className="text-lg">حالة الحل</CardTitle>
                <CardDescription>
                  تتبع مراحل معالجة الحل
                </CardDescription>
              </div>
            </div>
            
            <Badge className={currentStatusConfig?.color}>
              {currentStatusConfig?.label}
            </Badge>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Status Timeline */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              {STATUS_FLOW.map((status, index) => (
                <div key={status.key} className="flex items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                    index <= currentStatusIndex
                      ? 'bg-blue-600 border-blue-600 text-white'
                      : 'bg-gray-100 border-gray-300 text-gray-400'
                  }`}>
                    {status.icon}
                  </div>
                  
                  {index < STATUS_FLOW.length - 1 && (
                    <div className={`w-16 h-0.5 mx-2 ${
                      index < currentStatusIndex
                        ? 'bg-blue-600'
                        : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              ))}
            </div>
            
            <div className="flex justify-between text-sm">
              {STATUS_FLOW.map((status) => (
                <div key={status.key} className="text-center max-w-20">
                  <p className="font-medium">{status.label}</p>
                  <p className="text-gray-500 text-xs">{status.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Current Status Description */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-700">
              {currentStatusConfig?.description}
            </p>
          </div>

          {/* Implementation Notes */}
          {implementationNotes && (
            <div className="mt-4 p-4 bg-green-50 rounded-lg">
              <h4 className="font-medium text-green-900 mb-2 flex items-center gap-2">
                <MessageSquare className="w-4 h-4" />
                ملاحظات التطبيق
              </h4>
              <p className="text-sm text-green-800">
                {implementationNotes}
              </p>
            </div>
          )}

          {/* Status Actions */}
          {statusActions.length > 0 && (
            <div className="mt-6 space-y-3">
              <h4 className="font-medium text-gray-900">الإجراءات المتاحة:</h4>
              <div className="flex flex-wrap gap-2">
                {statusActions.map((action) => (
                  <Button
                    key={action.key}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (action.key === 'implemented') {
                        setShowNotesEditor(true)
                      } else {
                        handleStatusUpdate(action.key)
                      }
                    }}
                    disabled={isUpdating}
                  >
                    {isUpdating ? (
                      <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                    ) : (
                      <Settings className="w-4 h-4 ml-2" />
                    )}
                    تحديث إلى: {action.label}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Implementation Notes Editor */}
          {showNotesEditor && (
            <div className="mt-6 p-4 border rounded-lg space-y-4">
              <h4 className="font-medium text-gray-900">ملاحظات التطبيق</h4>
              <Textarea
                placeholder="اكتب ملاحظات حول تطبيق الحل..."
                value={newImplementationNotes}
                onChange={(e) => setNewImplementationNotes(e.target.value)}
                rows={4}
              />
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowNotesEditor(false)
                    setNewImplementationNotes(implementationNotes || '')
                  }}
                  disabled={isUpdating}
                >
                  إلغاء
                </Button>
                <Button
                  onClick={() => handleStatusUpdate('implemented')}
                  disabled={isUpdating}
                >
                  {isUpdating ? (
                    <>
                      <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                      جاري التحديث...
                    </>
                  ) : (
                    'تأكيد التطبيق'
                  )}
                </Button>
              </div>
            </div>
          )}

          {/* Permissions Info */}
          <div className="mt-6 text-xs text-gray-500 space-y-1">
            <p>• الخبير يمكنه إرسال الحل من المسودة</p>
            <p>• صاحب المشكلة يمكنه اعتماد الحل وتأكيد التطبيق</p>
            <p>• المدير يمكنه تحديث أي حالة</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}