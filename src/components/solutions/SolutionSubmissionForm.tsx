import React, { useState, useEffect, memo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAuthContext } from '@/components/auth/AuthProvider';
import { useDeviceType } from '@/hooks/use-mobile';
import { solutionOperations, problemOperations } from '@/lib/database';
import { FileUpload, UploadedFile } from '@/components/problems/FileUpload';
import { Loader2, Send, Save, AlertCircle, CheckCircle, FileText, Clock, Target } from 'lucide-react';
import { LoadingStateManager, useLoadingState } from '@/components/common/LoadingStateManager';
import { FormErrorBoundary } from '@/utils/errorBoundaryHelpers';
import { useOptimizedCallback } from '@/hooks/useOptimization';
import { useAriaAnnouncements } from '@/hooks/useAccessibility';
import { FormSkeleton } from '@/components/ui/skeleton-variants';

interface SolutionFormData {
  title: string;
  content: string;
  implementation_plan: string;
  estimated_cost?: number;
  estimated_timeline: string;
  required_resources: string[];
  attachments: UploadedFile[];
  status: 'draft' | 'submitted';
}

interface Problem {
  id: string;
  title: string;
  description: string;
  category: string;
  sector: string;
  urgency: string;
  status: string;
  submitted_by: string;
  users?: {
    name: string;
    organization?: string;
  };
}

const TIMELINE_OPTIONS = [
  '1-3 أيام',
  '1 أسبوع',
  '2-3 أسابيع',
  '1 شهر',
  '2-3 أشهر',
  '6 أشهر',
  'أكثر من 6 أشهر'
];

const COMMON_RESOURCES = [
  'فريق تطوير',
  'خادم/استضافة',
  'قاعدة بيانات',
  'أدوات تطوير',
  'تراخيص برمجيات',
  'أجهزة إضافية',
  'تدريب المستخدمين',
  'دعم فني',
  'اختبار وجودة',
  'توثيق تقني'
];

const SolutionSubmissionFormComponent = memo(function SolutionSubmissionForm() {
  const navigate = useNavigate();
  const { problemId } = useParams<{ problemId: string }>();
  const { toast } = useToast();
  const { user } = useAuthContext();
  const { isMobile } = useDeviceType();
  const { isLoading, error: loadingError, startLoading, stopLoading, setLoadingError } = useLoadingState();
  const { announce } = useAriaAnnouncements();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [problem, setProblem] = useState<Problem | null>(null);
  const [newResource, setNewResource] = useState('');

  const [formData, setFormData] = useState<SolutionFormData>({
    title: '',
    content: '',
    implementation_plan: '',
    estimated_cost: undefined,
    estimated_timeline: '',
    required_resources: [],
    attachments: [],
    status: 'draft'
  });

  const [errors, setErrors] = useState<Partial<SolutionFormData>>({});

  useEffect(() => {
    if (problemId) {
      loadProblem();
    }
  }, [problemId]);

  const loadProblem = async () => {
    if (!problemId) return;

    startLoading();
    try {
      const { data, error } = await problemOperations.getProblem(problemId);
      
      if (error) {
        throw new Error(error.message);
      }

      if (!data) {
        throw new Error('المشكلة غير موجودة');
      }

      setProblem(data);
      
      // Auto-generate solution title based on problem
      setFormData(prev => ({
        ...prev,
        title: `حل مقترح لمشكلة: ${data.title}`
      }));

    } catch (error: any) {
      console.error('Error loading problem:', error);
      setLoadingError(error.message);
      toast({
        title: "خطأ في تحميل المشكلة",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      stopLoading();
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<SolutionFormData> = {};

    // Title validation
    if (!formData.title.trim()) {
      newErrors.title = 'عنوان الحل مطلوب';
    } else if (formData.title.length < 10) {
      newErrors.title = 'عنوان الحل يجب أن يكون 10 أحرف على الأقل';
    } else if (formData.title.length > 200) {
      newErrors.title = 'عنوان الحل طويل جداً (الحد الأقصى: 200 حرف)';
    }

    // Content validation
    if (!formData.content.trim()) {
      newErrors.content = 'محتوى الحل مطلوب';
    } else if (formData.content.length < 100) {
      newErrors.content = 'محتوى الحل يجب أن يكون 100 حرف على الأقل';
    } else if (formData.content.length > 10000) {
      newErrors.content = 'محتوى الحل طويل جداً (الحد الأقصى: 10000 حرف)';
    }

    // Implementation plan validation
    if (!formData.implementation_plan.trim()) {
      newErrors.implementation_plan = 'خطة التنفيذ مطلوبة';
    } else if (formData.implementation_plan.length < 50) {
      newErrors.implementation_plan = 'خطة التنفيذ يجب أن تكون 50 حرف على الأقل';
    }

    // Timeline validation
    if (!formData.estimated_timeline) {
      newErrors.estimated_timeline = 'الجدول الزمني المتوقع مطلوب';
    }

    // Cost validation
    if (formData.estimated_cost !== undefined && formData.estimated_cost < 0) {
      newErrors.estimated_cost = 'التكلفة المتوقعة يجب أن تكون رقم موجب';
    }

    // Resources validation
    if (formData.required_resources.length === 0) {
      newErrors.required_resources = 'يجب إضافة مورد واحد على الأقل';
    }

    // Attachments validation
    const failedUploads = formData.attachments.filter(file => file.uploadStatus === 'error');
    if (failedUploads.length > 0) {
      newErrors.attachments = `فشل في رفع ${failedUploads.length} ملف. يرجى إعادة المحاولة أو حذف الملفات الفاشلة.`;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = useOptimizedCallback(async (e: React.FormEvent, submitStatus: 'draft' | 'submitted') => {
    e.preventDefault();

    if (!user) {
      toast({
        title: "خطأ في المصادقة",
        description: "يجب تسجيل الدخول أولاً",
        variant: "destructive",
      });
      return;
    }

    if (!problemId || !problem) {
      toast({
        title: "خطأ في البيانات",
        description: "معرف المشكلة غير صحيح",
        variant: "destructive",
      });
      return;
    }

    // Only validate for submitted solutions, allow drafts with minimal validation
    if (submitStatus === 'submitted' && !validateForm()) {
      const errorMessage = "يرجى تصحيح الأخطاء المذكورة";
      toast({
        title: "خطأ في البيانات",
        description: errorMessage,
        variant: "destructive",
      });
      announce(`خطأ: ${errorMessage}`, 'assertive');
      return;
    }

    // Check for pending uploads
    const pendingUploads = formData.attachments.filter(file => 
      file.uploadStatus === 'uploading' || file.uploadStatus === 'pending'
    );
    
    if (pendingUploads.length > 0) {
      toast({
        title: "انتظار رفع الملفات",
        description: "يرجى انتظار انتهاء رفع جميع الملفات قبل الإرسال",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    const actionText = submitStatus === 'draft' ? 'حفظ المسودة' : 'إرسال الحل';
    announce(`جاري ${actionText}...`, 'polite');

    try {
      // Prepare attachments data
      const attachments = formData.attachments
        .filter(file => file.uploadStatus === 'success')
        .map(file => ({
          id: file.id,
          name: file.name,
          size: file.size,
          type: file.type,
          url: file.url
        }));

      const solutionData = {
        problem_id: problemId,
        expert_id: user.id,
        title: formData.title.trim(),
        content: formData.content.trim(),
        implementation_plan: formData.implementation_plan.trim(),
        estimated_cost: formData.estimated_cost,
        estimated_timeline: formData.estimated_timeline,
        required_resources: formData.required_resources,
        attachments,
        status: submitStatus,
        rating: 0.0,
        votes: []
      };

      const { data, error } = await solutionOperations.createSolution(solutionData);

      if (error) {
        throw error;
      }

      const successMessage = submitStatus === 'draft' 
        ? "تم حفظ المسودة بنجاح! يمكنك العودة لتعديلها لاحقاً"
        : "تم إرسال الحل بنجاح! سيتم مراجعة الحل وإشعار صاحب المشكلة";

      toast({
        title: submitStatus === 'draft' ? "تم حفظ المسودة" : "تم إرسال الحل بنجاح",
        description: successMessage,
      });

      announce(successMessage, 'polite');

      // Navigate to problem details or solution management
      if (submitStatus === 'submitted') {
        navigate(`/problems/${problemId}`);
      } else {
        navigate('/expert/solutions');
      }

    } catch (error: any) {
      console.error('Error creating solution:', error);
      
      let errorMessage = `حدث خطأ أثناء ${actionText}. يرجى المحاولة مرة أخرى`;
      
      if (error?.message?.includes('duplicate')) {
        errorMessage = "لقد قمت بتقديم حل لهذه المشكلة مسبقاً";
      } else if (error?.message?.includes('permission')) {
        errorMessage = "ليس لديك صلاحية لتقديم حلول";
      } else if (error?.code === 'PGRST116') {
        errorMessage = "البيانات المدخلة غير صحيحة";
      }
      
      toast({
        title: "خطأ في الإرسال",
        description: errorMessage,
        variant: "destructive",
      });
      announce(`خطأ: ${errorMessage}`, 'assertive');
    } finally {
      setIsSubmitting(false);
    }
  }, [user, validateForm, formData, problemId, problem, toast, navigate, announce]);

  const addResource = () => {
    const resource = newResource.trim();
    if (resource && !formData.required_resources.includes(resource)) {
      setFormData(prev => ({
        ...prev,
        required_resources: [...prev.required_resources, resource]
      }));
      setNewResource('');
    }
  };

  const removeResource = (resourceToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      required_resources: prev.required_resources.filter(resource => resource !== resourceToRemove)
    }));
  };

  const addCommonResource = (resource: string) => {
    if (!formData.required_resources.includes(resource)) {
      setFormData(prev => ({
        ...prev,
        required_resources: [...prev.required_resources, resource]
      }));
    }
  };

  if (loadingError) {
    return (
      <div className={`mx-auto ${isMobile ? 'p-4' : 'max-w-4xl p-6'}`} dir="rtl">
        <Card>
          <CardContent className="p-12 text-center">
            <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              خطأ في تحميل المشكلة
            </h3>
            <p className="text-gray-600 mb-4">
              {loadingError}
            </p>
            <Button onClick={() => navigate('/problems')}>
              العودة إلى المشاكل
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <LoadingStateManager
      isLoading={isLoading}
      loadingComponent={
        <div className={`mx-auto ${isMobile ? 'p-4' : 'max-w-4xl p-6'}`} dir="rtl">
          <Card>
            <CardHeader className={isMobile ? 'p-4' : ''}>
              <CardTitle className={`${isMobile ? 'text-xl' : 'text-2xl'}`}>تقديم حل تقني</CardTitle>
              <CardDescription className={isMobile ? 'text-sm' : ''}>
                جاري تحميل بيانات المشكلة...
              </CardDescription>
            </CardHeader>
            <CardContent className={isMobile ? 'p-4' : ''}>
              <FormSkeleton
                fields={8}
                showSubmit={true}
                fieldTypes={['input', 'textarea', 'textarea', 'select', 'input']}
              />
            </CardContent>
          </Card>
        </div>
      }
      fadeTransition={true}
      transitionDuration={300}
    >
      <div className={`mx-auto ${isMobile ? 'p-4' : 'max-w-4xl p-6'}`} dir="rtl">
        <Card>
          <CardHeader className={isMobile ? 'p-4' : ''}>
            <CardTitle className={`${isMobile ? 'text-xl' : 'text-2xl'}`}>تقديم حل تقني</CardTitle>
            <CardDescription className={isMobile ? 'text-sm' : ''}>
              قدم حلاً تقنياً شاملاً للمشكلة المطروحة
            </CardDescription>
          </CardHeader>
          <CardContent className={isMobile ? 'p-4' : ''}>
            {/* Problem Summary */}
            {problem && (
              <Card className="mb-6 bg-blue-50 border-blue-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg text-blue-900 flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    المشكلة المطلوب حلها
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <h3 className="font-semibold text-blue-900">{problem.title}</h3>
                  <p className="text-blue-800 text-sm line-clamp-3">{problem.description}</p>
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="secondary">{problem.category}</Badge>
                    <Badge variant="outline">{problem.sector}</Badge>
                    <Badge className={
                      problem.urgency === 'critical' ? 'bg-red-100 text-red-800' :
                      problem.urgency === 'high' ? 'bg-orange-100 text-orange-800' :
                      problem.urgency === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }>
                      {problem.urgency === 'critical' ? 'حرجة' :
                       problem.urgency === 'high' ? 'عالية' :
                       problem.urgency === 'medium' ? 'متوسطة' : 'منخفضة'}
                    </Badge>
                  </div>
                  {problem.users && (
                    <p className="text-sm text-blue-700">
                      مقدم من: {problem.users.name}
                      {problem.users.organization && ` - ${problem.users.organization}`}
                    </p>
                  )}
                </CardContent>
              </Card>
            )}

            <form className={`${isMobile ? 'space-y-4' : 'space-y-6'}`}>
              {/* Solution Title */}
              <div className="space-y-2">
                <Label htmlFor="title">عنوان الحل *</Label>
                <Input
                  id="title"
                  placeholder="مثال: تطوير نظام إدارة متكامل باستخدام تقنيات حديثة"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  className={errors.title ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                  aria-invalid={!!errors.title}
                  aria-describedby={errors.title ? 'title-error' : 'title-help'}
                />
                {errors.title && (
                  <p id="title-error" className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {errors.title}
                  </p>
                )}
                <p id="title-help" className={`text-sm ${
                  formData.title.length < 10 
                    ? 'text-orange-600' 
                    : formData.title.length > 180 
                      ? 'text-yellow-600' 
                      : 'text-gray-500'
                }`}>
                  {formData.title.length}/200 حرف (الحد الأدنى: 10 أحرف)
                </p>
              </div>

              {/* Solution Content */}
              <div className="space-y-2">
                <Label htmlFor="content">محتوى الحل التفصيلي *</Label>
                <Textarea
                  id="content"
                  placeholder="اشرح الحل بالتفصيل: ما هي التقنيات المستخدمة؟ كيف سيتم تطبيق الحل؟ ما هي المزايا والفوائد؟ ما هي التحديات المتوقعة وكيفية التعامل معها؟"
                  rows={10}
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  className={errors.content ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                  aria-invalid={!!errors.content}
                  aria-describedby={errors.content ? 'content-error' : 'content-help'}
                />
                {errors.content && (
                  <p id="content-error" className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {errors.content}
                  </p>
                )}
                <p id="content-help" className={`text-sm ${
                  formData.content.length < 100 
                    ? 'text-orange-600' 
                    : formData.content.length > 9000 
                      ? 'text-yellow-600' 
                      : 'text-gray-500'
                }`}>
                  {formData.content.length}/10000 حرف (الحد الأدنى: 100 حرف)
                </p>
              </div>

              {/* Implementation Plan */}
              <div className="space-y-2">
                <Label htmlFor="implementation_plan">خطة التنفيذ *</Label>
                <Textarea
                  id="implementation_plan"
                  placeholder="اشرح خطة التنفيذ خطوة بخطوة: المراحل، الجدول الزمني، المسؤوليات، نقاط التحقق والمراجعة"
                  rows={6}
                  value={formData.implementation_plan}
                  onChange={(e) => setFormData(prev => ({ ...prev, implementation_plan: e.target.value }))}
                  className={errors.implementation_plan ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
                  aria-invalid={!!errors.implementation_plan}
                  aria-describedby={errors.implementation_plan ? 'plan-error' : 'plan-help'}
                />
                {errors.implementation_plan && (
                  <p id="plan-error" className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {errors.implementation_plan}
                  </p>
                )}
                <p id="plan-help" className={`text-sm ${
                  formData.implementation_plan.length < 50 ? 'text-orange-600' : 'text-gray-500'
                }`}>
                  {formData.implementation_plan.length} حرف (الحد الأدنى: 50 حرف)
                </p>
              </div>

              {/* Timeline and Cost */}
              <div className={`grid grid-cols-1 ${isMobile ? 'gap-4' : 'md:grid-cols-2 gap-4'}`}>
                <div className="space-y-2">
                  <Label htmlFor="timeline">الجدول الزمني المتوقع *</Label>
                  <Select 
                    value={formData.estimated_timeline} 
                    onValueChange={(value) => setFormData(prev => ({ ...prev, estimated_timeline: value }))}
                  >
                    <SelectTrigger className={errors.estimated_timeline ? 'border-red-500' : ''}>
                      <SelectValue placeholder="اختر الجدول الزمني" />
                    </SelectTrigger>
                    <SelectContent>
                      {TIMELINE_OPTIONS.map(timeline => (
                        <SelectItem key={timeline} value={timeline}>
                          <div className="flex items-center gap-2">
                            <Clock className="w-4 h-4" />
                            {timeline}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.estimated_timeline && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      {errors.estimated_timeline}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="cost">التكلفة المتوقعة (ل.س) (اختياري)</Label>
                  <Input
                    id="cost"
                    type="number"
                    min="0"
                    placeholder="مثال: 500000"
                    value={formData.estimated_cost || ''}
                    onChange={(e) => setFormData(prev => ({ 
                      ...prev, 
                      estimated_cost: e.target.value ? parseInt(e.target.value) : undefined 
                    }))}
                    className={errors.estimated_cost ? 'border-red-500' : ''}
                  />
                  {errors.estimated_cost && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="w-4 h-4" />
                      {errors.estimated_cost}
                    </p>
                  )}
                </div>
              </div>

              {/* Required Resources */}
              <div className="space-y-2">
                <Label htmlFor="resources">الموارد المطلوبة *</Label>
                
                {/* Common Resources */}
                <div className="mb-3">
                  <p className="text-sm text-gray-600 mb-2">الموارد الشائعة:</p>
                  <div className="flex flex-wrap gap-2">
                    {COMMON_RESOURCES.map(resource => (
                      <Button
                        key={resource}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addCommonResource(resource)}
                        disabled={formData.required_resources.includes(resource)}
                        className="text-xs"
                      >
                        {resource}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Custom Resource Input */}
                <div className="flex gap-2">
                  <Input
                    placeholder="أضف مورد مطلوب..."
                    value={newResource}
                    onChange={(e) => setNewResource(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        addResource();
                      }
                    }}
                    className="flex-1"
                  />
                  <Button 
                    type="button" 
                    onClick={addResource} 
                    variant="outline"
                    disabled={!newResource.trim()}
                  >
                    <Target className="w-4 h-4 ml-2" />
                    إضافة
                  </Button>
                </div>
                
                {errors.required_resources && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {errors.required_resources}
                  </p>
                )}
                
                {formData.required_resources.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-2">
                    {formData.required_resources.map((resource, index) => (
                      <Badge key={index} variant="secondary" className="flex items-center gap-1">
                        {resource}
                        <button
                          type="button"
                          onClick={() => removeResource(resource)}
                          className="ml-1 hover:text-red-600"
                          aria-label={`حذف المورد: ${resource}`}
                        >
                          ×
                        </button>
                      </Badge>
                    ))}
                  </div>
                )}
                <p className="text-sm text-gray-500">
                  {formData.required_resources.length} مورد مضاف
                </p>
              </div>

              {/* File Upload */}
              <div className="space-y-2">
                <Label>المرفقات (اختياري)</Label>
                <FileUpload
                  onFilesChange={(files) => setFormData(prev => ({ ...prev, attachments: files }))}
                  disabled={isSubmitting}
                  maxFiles={10}
                  acceptedTypes={[
                    'application/pdf',
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'image/jpeg',
                    'image/png',
                    'image/gif',
                    'text/plain',
                    'application/zip'
                  ]}
                />
                {errors.attachments && (
                  <p className="text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {errors.attachments}
                  </p>
                )}
                {formData.attachments.length > 0 && (
                  <p className="text-sm text-gray-500">
                    {formData.attachments.filter(f => f.uploadStatus === 'success').length} من {formData.attachments.length} ملف تم رفعه بنجاح
                  </p>
                )}
              </div>

              {/* Submit Buttons */}
              <div className={`${isMobile ? 'flex flex-col space-y-3 pt-4' : 'flex justify-between items-center pt-6'} border-t`}>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => navigate(-1)}
                  disabled={isSubmitting}
                  className={`${isMobile ? 'w-full order-3' : ''} touch-manipulation`}
                >
                  إلغاء
                </Button>
                
                <div className={`flex gap-3 ${isMobile ? 'w-full order-1' : ''}`}>
                  <Button 
                    type="button"
                    variant="outline"
                    onClick={(e) => handleSubmit(e, 'draft')}
                    disabled={isSubmitting}
                    className={`${isMobile ? 'flex-1' : ''} touch-manipulation`}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                        {isMobile ? 'حفظ...' : 'جاري الحفظ...'}
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 ml-2" />
                        حفظ كمسودة
                      </>
                    )}
                  </Button>
                  
                  <Button 
                    type="button"
                    onClick={(e) => handleSubmit(e, 'submitted')}
                    disabled={isSubmitting}
                    className={`bg-blue-600 hover:bg-blue-700 touch-manipulation ${isMobile ? 'flex-1' : ''}`}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="w-4 h-4 ml-2 animate-spin" />
                        {isMobile ? 'إرسال...' : 'جاري الإرسال...'}
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 ml-2" />
                        إرسال الحل
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </LoadingStateManager>
  );
});

// Export wrapped component with error boundary
export function SolutionSubmissionForm() {
  return (
    <FormErrorBoundary formName="SolutionSubmission">
      <SolutionSubmissionFormComponent />
    </FormErrorBoundary>
  );
}