import React, { useState, memo, useCallback } from 'react'
import { useAuthContext } from './AuthProvider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Loader2, Eye, EyeOff } from 'lucide-react'
import { Link } from 'react-router-dom'
import { LoadingStateManager, useLoadingState } from '@/components/common/LoadingStateManager'
import { FormErrorBoundary } from '@/utils/errorBoundaryHelpers'
import { useOptimizedCallback } from '@/hooks/useOptimization'
import { useTouchTargets, useScreenReader } from '@/hooks/useAccessibility'
import { FormSkeleton } from '@/components/ui/skeleton-variants'
import { useLanguage } from '@/contexts/LanguageContext'

interface RegisterFormProps {
  onSuccess?: () => void
  redirectTo?: string
}

// Memoized RegisterForm component for better performance
const RegisterFormComponent = memo(function RegisterForm({ onSuccess, redirectTo }: RegisterFormProps) {
  const { signUp, loading } = useAuthContext()
  const { isLoading, error: loadingError, startLoading, stopLoading, setLoadingError } = useLoadingState()
  const { ensureTouchTarget } = useTouchTargets()
  const { announce } = useScreenReader()
  const { t } = useLanguage()

  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    name: '',
    role: 'ministry_user' as 'expert' | 'ministry_user',
    location: 'Damascus, Syria',
    organization: '',
    position: '',
    phoneNumber: '',
    bio: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const validateForm = useCallback(() => {
    if (formData.password !== formData.confirmPassword) {
      setError(t('auth.passwords_dont_match', 'Passwords do not match'))
      return false
    }
    
    if (formData.password.length < 6) {
      setError(t('auth.password_min_length', 'Password must be at least 6 characters'))
      return false
    }

    if (!formData.name.trim()) {
      setError(t('auth.name_required', 'Name is required'))
      return false
    }

    if (!formData.location.trim()) {
      setError(t('auth.location_required', 'Location is required'))
      return false
    }

    return true
  }, [formData, t])

  // Optimized submit handler with proper loading states and accessibility
  const handleSubmit = useOptimizedCallback(async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setSuccess(null)

    if (!validateForm()) {
      return
    }

    startLoading()
    setIsSubmitting(true)
    announce(t('auth.creating_account', 'Creating account...'), 'polite')

    try {
      const { error } = await signUp(formData.email, formData.password, {
        name: formData.name,
        role: formData.role,
        location: formData.location,
        organization: formData.organization || null,
        position: formData.position || null,
        phone_number: formData.phoneNumber || null,
        bio: formData.bio || null
      })
      
      if (error) {
        let errorMessage = t('auth.registration_failed', 'Registration failed. Please try again.')

        if (error.message.includes('User already registered')) {
          errorMessage = t('auth.email_already_registered', 'This email is already registered')
        } else if (error.message.includes('Password should be at least 6 characters')) {
          errorMessage = t('auth.password_min_length', 'Password must be at least 6 characters')
        } else if (error.message.includes('Invalid email')) {
          errorMessage = t('auth.invalid_email', 'Invalid email address')
        }

        setError(errorMessage)
        setLoadingError(errorMessage)
        announce(`${t('common.error', 'Error')}: ${errorMessage}`, 'assertive')
      } else {
        const successMessage = t('auth.registration_success', 'Account created successfully! Please check your email to confirm your account.')
        setSuccess(successMessage)
        announce(successMessage, 'polite')
        stopLoading()
        onSuccess?.()
        if (redirectTo) {
          setTimeout(() => {
            window.location.href = redirectTo
          }, 2000)
        }
      }
    } catch (err) {
      const errorMessage = t('errors.unknown_error', 'An unexpected error occurred. Please try again.')
      setError(errorMessage)
      setLoadingError(errorMessage)
      announce(`${t('common.error', 'Error')}: ${errorMessage}`, 'assertive')
    } finally {
      setIsSubmitting(false)
      if (isLoading) stopLoading()
    }
  }, [signUp, formData, onSuccess, redirectTo, validateForm, startLoading, stopLoading, setLoadingError, announce, isLoading, t])

  // Optimized change handlers
  const handleChange = useOptimizedCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
    // Clear error when user starts typing
    if (error) setError(null)
  }, [error])

  const handleSelectChange = useOptimizedCallback((name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    if (error) setError(null)
  }, [error])

  // Toggle password visibility functions
  const togglePasswordVisibility = useCallback(() => {
    setShowPassword(prev => !prev)
  }, [])

  const toggleConfirmPasswordVisibility = useCallback(() => {
    setShowConfirmPassword(prev => !prev)
  }, [])

  return (
    <LoadingStateManager
      isLoading={isLoading || loading}
      loadingComponent={
        <Card className="w-full max-w-2xl mx-auto">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">{t('nav.register', 'Register')}</CardTitle>
            <CardDescription>
              {t('auth.register_description', 'Join the technical solutions platform and contribute to solving technical challenges')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <FormSkeleton
              fields={8}
              showSubmit={true}
            />
          </CardContent>
        </Card>
      }
      fadeTransition={true}
      transitionDuration={300}
    >
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">{t('nav.register', 'Register')}</CardTitle>
          <CardDescription>
            {t('auth.register_description', 'Join the technical solutions platform and contribute to solving technical challenges')}
          </CardDescription>
        </CardHeader>
        <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4" noValidate>
          {(error || loadingError) && (
            <Alert variant="destructive" id="register-error" role="alert">
              <AlertDescription>{error || loadingError}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t('auth.full_name', 'Full Name')} *</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder={t('auth.full_name_placeholder', 'Ahmad Mohammad')}
                required
                disabled={isSubmitting}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">{t('auth.email', 'Email')} *</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder={t('auth.email_placeholder', '<EMAIL>')}
                required
                disabled={isSubmitting}
                dir="ltr"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="password">{t('auth.password', 'Password')} *</Label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={handleChange}
                  placeholder={t('auth.password_min_chars', 'At least 6 characters')}
                  required
                  disabled={isSubmitting}
                  dir="ltr"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={togglePasswordVisibility}
                  disabled={isSubmitting}
                  aria-label={showPassword ? t('auth.hide_password', 'Hide password') : t('auth.show_password', 'Show password')}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">{t('auth.confirm_password', 'Confirm Password')} *</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  placeholder={t('auth.confirm_password_placeholder', 'Re-enter your password')}
                  required
                  disabled={isSubmitting}
                  dir="ltr"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute left-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={toggleConfirmPasswordVisibility}
                  disabled={isSubmitting}
                  aria-label={showConfirmPassword ? t('auth.hide_password', 'Hide password') : t('auth.show_password', 'Show password')}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="role">نوع الحساب *</Label>
              <Select 
                value={formData.role} 
                onValueChange={(value) => handleSelectChange('role', value)}
                disabled={isSubmitting}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر نوع الحساب" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ministry_user">موظف وزارة</SelectItem>
                  <SelectItem value="expert">خبير تقني</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">الموقع *</Label>
              <Input
                id="location"
                name="location"
                value={formData.location}
                onChange={handleChange}
                placeholder="دمشق، سوريا"
                required
                disabled={isSubmitting}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="organization">المؤسسة</Label>
              <Input
                id="organization"
                name="organization"
                value={formData.organization}
                onChange={handleChange}
                placeholder="وزارة الاتصالات والتقانة"
                disabled={isSubmitting}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="position">المنصب</Label>
              <Input
                id="position"
                name="position"
                value={formData.position}
                onChange={handleChange}
                placeholder="مهندس نظم"
                disabled={isSubmitting}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="phoneNumber">رقم الهاتف</Label>
            <Input
              id="phoneNumber"
              name="phoneNumber"
              value={formData.phoneNumber}
              onChange={handleChange}
              placeholder="+963 11 123 4567"
              disabled={isSubmitting}
              dir="ltr"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="bio">نبذة شخصية</Label>
            <Textarea
              id="bio"
              name="bio"
              value={formData.bio}
              onChange={handleChange}
              placeholder="اكتب نبذة مختصرة عن خبرتك وتخصصك..."
              rows={3}
              disabled={isSubmitting}
            />
          </div>

          <Button 
            type="submit" 
            className="w-full" 
            disabled={isSubmitting || loading}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {t('auth.creating_account', 'Creating account...')}
              </>
            ) : (
              t('nav.register', 'Register')
            )}
          </Button>

          <div className="text-center text-sm text-gray-600">
            {t('auth.have_account', 'Already have an account?')}{' '}
            <Link 
              to="/auth/login" 
              className="text-blue-600 hover:underline"
            >
              {t('nav.login', 'Login')}
            </Link>
          </div>
        </form>
        </CardContent>
      </Card>
    </LoadingStateManager>
  )
})

// Export wrapped component with error boundary
export function RegisterForm(props: RegisterFormProps) {
  return (
    <FormErrorBoundary formName="Register">
      <RegisterFormComponent {...props} />
    </FormErrorBoundary>
  )
}