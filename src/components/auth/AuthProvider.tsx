import React, { 
  create<PERSON>ontext, 
  useReducer, 
  useEffect, 
  ReactNode, 
  useCallback, 
  useContext 
} from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Lock, AlertTriangle } from 'lucide-react'
import { Link } from 'react-router-dom'
import { UserRole, UserData, UserRegistrationData, UserUpdateData } from '@/types'
import { User, Session, AuthError } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'

// Auth state type
type AuthState = {
  user: User | null
  userData: UserData | null
  session: Session | null
  loading: boolean
  userDataLoading: boolean
  error: AuthError | null
  userDataError: string | null
}

// Auth actions type
type AuthAction =
  | { type: 'INIT'; payload: { user: User | null; session: Session | null } }
  | { type: 'LOADING' }
  | { type: 'USER_DATA_LOADING'; payload: boolean }
  | { type: 'SIGN_IN'; payload: { user: User; session: Session } }
  | { type: 'SIGN_OUT' }
  | { type: 'SET_USER_DATA'; payload: UserData | null }
  | { type: 'ERROR'; payload: AuthError | null }
  | { type: 'USER_DATA_ERROR'; payload: string | null }

// Initial state
const initialState: AuthState = {
  user: null,
  userData: null,
  session: null,
  loading: true,
  userDataLoading: false,
  error: null,
  userDataError: null,
}

// Reducer function
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'INIT':
      return { 
        ...state, 
        user: action.payload.user, 
        session: action.payload.session,
        loading: false 
      }
    case 'LOADING':
      return { ...state, loading: true, error: null }
    case 'USER_DATA_LOADING':
      return { ...state, userDataLoading: action.payload }
    case 'SIGN_IN':
      return { 
        ...state, 
        user: action.payload.user, 
        session: action.payload.session,
        loading: false, 
        error: null 
      }
    case 'SIGN_OUT':
      return { 
        ...state, 
        user: null, 
        userData: null,
        session: null,
        loading: false, 
        error: null 
      }
    case 'SET_USER_DATA':
      return { ...state, userData: action.payload, userDataLoading: false }
    case 'ERROR':
      return { ...state, loading: false, error: action.payload }
    case 'USER_DATA_ERROR':
      return { ...state, userDataLoading: false, userDataError: action.payload }
    default:
      return state
  }
}

// Context interface
interface AuthContextType extends AuthState {
  signUp: (email: string, password: string, userData?: Partial<UserRegistrationData>) => Promise<{ error: AuthError | null }>
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<{ error: AuthError | null }>
  resetPassword: (email: string) => Promise<{ error: AuthError | null }>
  updateProfile: (updates: UserUpdateData) => Promise<{ error: AuthError | null }>
  hasRole: (role: UserRole) => boolean
  hasAnyRole: (roles: UserRole[]) => boolean
  isAdmin: boolean
  isExpert: boolean
  isMinistryUser: boolean
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// AuthProvider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState)

  // Fetch user data function
  const fetchUserData = useCallback(async (userId: string) => {
    dispatch({ type: 'USER_DATA_LOADING', payload: true })
    
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching user data:', error)
        dispatch({ type: 'USER_DATA_ERROR', payload: error.message })
        return
      }

      dispatch({ type: 'SET_USER_DATA', payload: data })
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unexpected error fetching user data'
      console.error('Unexpected error fetching user data:', err)
      dispatch({ type: 'USER_DATA_ERROR', payload: errorMessage })
    }
  }, [])

  // Initialize auth state and listen for changes
  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      dispatch({ 
        type: 'INIT', 
        payload: { 
          user: session?.user ?? null, 
          session: session ?? null 
        } 
      })
      
      // Fetch user data if user exists
      if (session?.user) {
        fetchUserData(session.user.id)
      }
    })

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        dispatch({ 
          type: 'SIGN_IN', 
          payload: { 
            user: session.user, 
            session: session 
          } 
        })
        
        // Fetch user data
        fetchUserData(session.user.id)
        
        // Update last_login_at when user signs in
        if (event === 'SIGNED_IN') {
          try {
            await supabase
              .from('users')
              .update({ last_login_at: new Date().toISOString() })
              .eq('id', session.user.id)
          } catch (error) {
            console.error('Error updating last login:', error)
          }
        }
      } else {
        dispatch({ type: 'SIGN_OUT' })
      }
    })

    return () => subscription.unsubscribe()
  }, [fetchUserData])

  // Auth action callbacks
  const signUp = useCallback(async (email: string, password: string, userData?: Partial<UserRegistrationData>) => {
    dispatch({ type: 'LOADING' })
    
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name: userData?.name,
          role: userData?.role || 'ministry_user',
          location: userData?.location || 'Damascus, Syria',
          organization: userData?.organization,
          position: userData?.position,
          phone_number: userData?.phone_number,
          bio: userData?.bio
        }
      }
    })

    if (!error && data.user) {
      try {
        // Create user profile
        const { error: profileError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            email: data.user.email!,
            name: userData?.name || data.user.email!,
            role: userData?.role || 'ministry_user',
            location: userData?.location || 'Damascus, Syria',
            organization: userData?.organization || null,
            position: userData?.position || null,
            phone_number: userData?.phone_number || null,
            bio: userData?.bio || null,
            languages: ['ar']
          })

        if (profileError) {
          console.error('Error creating user profile:', profileError)
        }

        // Create expert profile if needed
        if (userData?.role === 'expert') {
          const { error: expertError } = await supabase
            .from('experts')
            .insert({
              user_id: data.user.id,
              expertise_areas: [],
              experience_years: 0,
              availability: 'available',
              rating: 0,
              total_contributions: 0,
              success_rate: 0,
              response_time_hours: 24,
              portfolio: [],
              certifications: []
            })

          if (expertError) {
            console.error('Error creating expert profile:', expertError)
          }
        }
      } catch (profileCreationError) {
        console.error('Error in profile creation:', profileCreationError)
      }
    }

    dispatch({ type: 'ERROR', payload: error })
    return { error }
  }, [])

  const signIn = useCallback(async (email: string, password: string) => {
    dispatch({ type: 'LOADING' })
    
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    
    if (error) dispatch({ type: 'ERROR', payload: error })
    return { error }
  }, [])

  const signOut = useCallback(async () => {
    dispatch({ type: 'LOADING' })
    
    const { error } = await supabase.auth.signOut()
    
    if (error) dispatch({ type: 'ERROR', payload: error })
    return { error }
  }, [])

  const resetPassword = useCallback(async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`
    })
    
    if (error) dispatch({ type: 'ERROR', payload: error })
    return { error }
  }, [])

  const updateProfile = useCallback(async (updates: UserUpdateData) => {
    dispatch({ type: 'LOADING' })
    
    const { error } = await supabase.auth.updateUser({
      data: updates
    })
    
    if (error) dispatch({ type: 'ERROR', payload: error })
    return { error }
  }, [])

  // Role checking utilities
  const hasRole = useCallback((role: UserRole): boolean => {
    return state.userData?.role === role
  }, [state.userData])

  const hasAnyRole = useCallback((roles: UserRole[]): boolean => {
    return state.userData ? roles.includes(state.userData.role) : false
  }, [state.userData])

  // Context value
  const contextValue: AuthContextType = {
    ...state,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updateProfile,
    hasRole,
    hasAnyRole,
    isAdmin: state.userData?.role === 'admin',
    isExpert: state.userData?.role === 'expert',
    isMinistryUser: state.userData?.role === 'ministry_user',
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  )
}

// Hook to use auth context
export function useAuthContext() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider')
  }
  return context
}

// Loading component
function LoadingScreen() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center">
        <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-blue-600" />
        <h2 className="text-xl font-semibold mb-2">جاري التحميل...</h2>
        <p className="text-gray-600">يرجى الانتظار</p>
      </div>
    </div>
  )
}

// Authentication required component
function AuthRequired() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <Lock className="h-12 w-12 mx-auto mb-4 text-blue-600" />
          <CardTitle className="text-2xl">تسجيل الدخول مطلوب</CardTitle>
          <CardDescription>
            يجب تسجيل الدخول للوصول إلى هذه الصفحة
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button asChild className="w-full">
            <Link to="/auth/login">تسجيل الدخول</Link>
          </Button>
          <Button asChild variant="outline" className="w-full">
            <Link to="/auth/register">إنشاء حساب جديد</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

// Access denied component
function AccessDenied({ requiredRole }: { requiredRole?: string }) {
  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin': return 'مدير'
      case 'expert': return 'خبير'
      case 'ministry_user': return 'موظف وزارة'
      default: return role
    }
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-red-600" />
          <CardTitle className="text-2xl">غير مصرح بالوصول</CardTitle>
          <CardDescription>
            {requiredRole 
              ? `هذه الصفحة متاحة فقط للمستخدمين من نوع: ${getRoleLabel(requiredRole)}`
              : 'ليس لديك صلاحية للوصول إلى هذه الصفحة'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button asChild variant="outline" className="w-full">
            <Link to="/">العودة إلى الصفحة الرئيسية</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

// Account inactive component
function AccountInactive() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-yellow-600" />
          <CardTitle className="text-2xl">الحساب غير نشط</CardTitle>
          <CardDescription>
            تم إلغاء تفعيل حسابك. يرجى التواصل مع الإدارة
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertDescription>
              إذا كنت تعتقد أن هذا خطأ، يرجى التواصل مع فريق الدعم
            </AlertDescription>
          </Alert>
          <Button asChild variant="outline" className="w-full">
            <Link to="/contact">التواصل مع الدعم</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

// Protected route component with enhanced role-based access control
export function ProtectedRoute({ 
  children, 
  requiredRole,
  requiredRoles,
  requireAuth = true
}: { 
  children: ReactNode
  requiredRole?: UserRole
  requiredRoles?: UserRole[]
  requireAuth?: boolean
}) {
  const { user, userData, loading, hasRole, hasAnyRole } = useAuthContext()

  // Show loading screen while authentication is being checked
  if (loading) {
    return <LoadingScreen />
  }

  // Check if authentication is required
  if (requireAuth && !user) {
    return <AuthRequired />
  }

  // If user is authenticated but no userData, show loading
  if (user && !userData && requireAuth) {
    return <LoadingScreen />
  }

  // Check if account is active
  if (userData && !userData.is_active) {
    return <AccountInactive />
  }

  // Check role-based access control
  if (requiredRole && !hasRole(requiredRole)) {
    return <AccessDenied requiredRole={requiredRole} />
  }

  if (requiredRoles && !hasAnyRole(requiredRoles)) {
    return <AccessDenied />
  }

  return <>{children}</>
}

// Hook for role-based conditional rendering
export function useRoleAccess() {
  const { hasRole, hasAnyRole, isAdmin, isExpert, isMinistryUser } = useAuthContext()
  
  return {
    hasRole,
    hasAnyRole,
    isAdmin,
    isExpert,
    isMinistryUser,
    canCreateProblems: hasAnyRole(['ministry_user', 'admin']),
    canCreateSolutions: hasAnyRole(['expert', 'admin']),
    canModerateContent: isAdmin,
    canManageUsers: isAdmin,
    canViewAnalytics: hasAnyRole(['admin', 'ministry_user'])
  }
}