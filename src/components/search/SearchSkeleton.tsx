import { SearchResultsSkeleton as BaseSearchResultsSkeleton } from '@/components/ui/skeleton-variants'
import { useDeviceType } from '@/hooks/use-mobile'
import { cn } from '@/lib/utils'

interface SearchResultsSkeletonProps {
  resultCount?: number
  showTabs?: boolean
  showFilters?: boolean
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

export function SearchResultsSkeleton({
  resultCount = 5,
  showTabs = true,
  showFilters = true,
  animation = 'pulse',
  className = ''
}: SearchResultsSkeletonProps) {
  const { isMobile } = useDeviceType()

  return (
    <div className={cn('space-y-6', className)}>
      {/* Search header */}
      <div className={`flex ${isMobile ? 'flex-col gap-3' : 'flex-col sm:flex-row sm:items-center sm:justify-between gap-4'}`}>
        <div className={`flex items-center ${isMobile ? 'flex-wrap gap-2' : 'gap-4'}`}>
          <div className={`${isMobile ? 'h-5 w-24' : 'h-6 w-32'} bg-muted animate-${animation} rounded-md`} />
          <div className={`${isMobile ? 'h-5 w-16' : 'h-6 w-20'} bg-muted animate-${animation} rounded-full`} />
          {!isMobile && (
            <div className={`h-4 w-24 bg-muted animate-${animation} rounded-md`} />
          )}
        </div>
        
        {/* Sort dropdown */}
        <div className={`${isMobile ? 'w-full' : 'w-48'} h-10 bg-muted animate-${animation} rounded-md`} />
      </div>

      {/* Search filters */}
      {showFilters && (
        <div className="p-6 border rounded-lg bg-card">
          <div className="space-y-4">
            {/* Search input */}
            <div className={`h-10 w-full bg-muted animate-${animation} rounded-md`} />
            
            {/* Filter controls */}
            <div className={`grid grid-cols-1 gap-3 ${isMobile ? '' : 'md:grid-cols-2 lg:grid-cols-4 gap-4'}`}>
              {Array.from({ length: 4 }, (_, i) => (
                <div key={i} className={`h-10 bg-muted animate-${animation} rounded-md`} />
              ))}
            </div>

            {/* Active filters */}
            <div className="flex flex-wrap gap-2 pt-2 border-t">
              <div className={`h-4 w-20 bg-muted animate-${animation} rounded-md`} />
              {Array.from({ length: 3 }, (_, i) => (
                <div key={i} className={`h-6 w-${16 + i * 4} bg-muted animate-${animation} rounded-full`} />
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Results tabs */}
      {showTabs && (
        <div className={`grid ${isMobile ? 'grid-cols-2 gap-1' : 'grid-cols-2 lg:grid-cols-5'} w-full`}>
          {Array.from({ length: isMobile ? 4 : 5 }, (_, i) => (
            <div key={i} className={`flex items-center gap-2 ${isMobile ? 'text-xs p-2 flex-col' : 'text-sm'} h-10 bg-muted animate-${animation} rounded-md`}>
              <div className="w-4 h-4 bg-muted/50 animate-pulse rounded" />
              <div className={`${isMobile ? 'w-12' : 'w-16'} h-3 bg-muted/50 animate-pulse rounded-md`} />
              <div className="w-6 h-4 bg-muted/50 animate-pulse rounded-full" />
            </div>
          ))}
        </div>
      )}

      {/* Search results */}
      <div className="space-y-4">
        {Array.from({ length: resultCount }, (_, i) => (
          <SearchResultCardSkeleton key={i} animation={animation} />
        ))}
      </div>

      {/* Pagination */}
      <div className={`flex items-center ${isMobile ? 'flex-col gap-3' : 'justify-between'}`}>
        <div className={`${isMobile ? 'text-xs order-2' : 'text-sm'} h-4 w-32 bg-muted animate-${animation} rounded-md`} />
        
        <div className={`flex items-center gap-2 ${isMobile ? 'order-1' : ''}`}>
          <div className={`${isMobile ? 'w-16 h-10' : 'w-16 h-8'} bg-muted animate-${animation} rounded-md`} />
          
          {/* Page numbers */}
          <div className="flex items-center gap-1">
            {Array.from({ length: isMobile ? 3 : 5 }, (_, i) => (
              <div key={i} className={`${isMobile ? 'w-10 h-10' : 'w-8 h-8'} bg-muted animate-${animation} rounded-md`} />
            ))}
          </div>
          
          <div className={`${isMobile ? 'w-16 h-10' : 'w-16 h-8'} bg-muted animate-${animation} rounded-md`} />
        </div>
      </div>
    </div>
  )
}

interface SearchResultCardSkeletonProps {
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

export function SearchResultCardSkeleton({
  animation = 'pulse',
  className = ''
}: SearchResultCardSkeletonProps) {
  const { isMobile } = useDeviceType()

  return (
    <div className={cn('p-6 border rounded-lg bg-card hover:shadow-md transition-shadow', className)}>
      <div className="space-y-4">
        {/* Result header */}
        <div className="flex items-start justify-between">
          <div className="flex-1 space-y-3">
            {/* Type and status indicators */}
            <div className={`flex items-center gap-2 ${isMobile ? 'flex-wrap' : ''}`}>
              <div className="w-4 h-4 bg-muted animate-pulse rounded" />
              <div className={`h-5 w-16 bg-muted animate-${animation} rounded-full`} />
              <div className="flex items-center gap-1">
                <div className="w-4 h-4 bg-muted animate-pulse rounded" />
                <div className={`h-4 w-12 bg-muted animate-${animation} rounded-md`} />
              </div>
            </div>
            
            {/* Title */}
            <div className={`${isMobile ? 'h-5 w-full' : 'h-6 w-3/4'} bg-muted animate-${animation} rounded-md`} />
            
            {/* Description */}
            <div className="space-y-2">
              <div className={`h-4 w-full bg-muted animate-${animation} rounded-md`} />
              <div className={`h-4 w-2/3 bg-muted animate-${animation} rounded-md`} />
            </div>
          </div>
          
          {/* Relevance score */}
          {!isMobile && (
            <div className="text-right flex-shrink-0 space-y-1">
              <div className={`h-3 w-16 bg-muted animate-${animation} rounded-md`} />
              <div className={`h-4 w-12 bg-muted animate-${animation} rounded-md`} />
            </div>
          )}
        </div>

        {/* Tags and categories */}
        <div className="flex flex-wrap gap-2">
          {Array.from({ length: 4 }, (_, i) => (
            <div key={i} className={`h-6 w-${12 + i * 2} bg-muted animate-${animation} rounded-full`} />
          ))}
        </div>

        {/* Metadata */}
        <div className={`flex items-center ${isMobile ? 'gap-2 flex-wrap' : 'gap-4'} ${isMobile ? 'text-xs' : 'text-sm'}`}>
          {Array.from({ length: 3 }, (_, i) => (
            <div key={i} className="flex items-center gap-1">
              <div className="w-3 h-3 bg-muted animate-pulse rounded" />
              <div className={`h-3 w-${12 + i * 4} bg-muted animate-${animation} rounded-md`} />
            </div>
          ))}
        </div>

        {/* Mobile relevance score */}
        {isMobile && (
          <div className="flex items-center gap-1 text-xs">
            <div className={`h-3 w-8 bg-muted animate-${animation} rounded-md`} />
            <div className={`h-3 w-8 bg-muted animate-${animation} rounded-md`} />
          </div>
        )}
      </div>
    </div>
  )
}

interface SearchFiltersSkeletonProps {
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

export function SearchFiltersSkeleton({
  animation = 'pulse',
  className = ''
}: SearchFiltersSkeletonProps) {
  const { isMobile } = useDeviceType()

  return (
    <div className={cn('p-6 border rounded-lg bg-card', className)}>
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center gap-2">
          <div className="w-5 h-5 bg-muted animate-pulse rounded" />
          <div className={`h-5 w-24 bg-muted animate-${animation} rounded-md`} />
        </div>

        {/* Search input */}
        <div className={`h-10 w-full bg-muted animate-${animation} rounded-md`} />

        {/* Filter categories */}
        <div className="space-y-4">
          {Array.from({ length: 3 }, (_, i) => (
            <div key={i} className="space-y-2">
              <div className={`h-4 w-20 bg-muted animate-${animation} rounded-md`} />
              <div className={`grid grid-cols-1 gap-2 ${isMobile ? '' : 'md:grid-cols-2 lg:grid-cols-3'}`}>
                {Array.from({ length: 6 }, (_, j) => (
                  <div key={j} className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-muted animate-pulse rounded" />
                    <div className={`h-4 w-${16 + j * 2} bg-muted animate-${animation} rounded-md`} />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Range sliders */}
        <div className="space-y-4">
          {Array.from({ length: 2 }, (_, i) => (
            <div key={i} className="space-y-2">
              <div className={`h-4 w-24 bg-muted animate-${animation} rounded-md`} />
              <div className="px-2">
                <div className={`h-2 w-full bg-muted animate-${animation} rounded-full`} />
                <div className="flex justify-between mt-1">
                  <div className={`h-3 w-4 bg-muted animate-${animation} rounded-md`} />
                  <div className={`h-3 w-6 bg-muted animate-${animation} rounded-md`} />
                  <div className={`h-3 w-4 bg-muted animate-${animation} rounded-md`} />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Clear filters button */}
        <div className={`h-10 w-32 bg-muted animate-${animation} rounded-md`} />
      </div>
    </div>
  )
}

// Enhanced search interface skeleton
interface EnhancedSearchSkeletonProps {
  animation?: 'pulse' | 'wave' | 'none'
  className?: string
}

export function EnhancedSearchSkeleton({
  animation = 'pulse',
  className = ''
}: EnhancedSearchSkeletonProps) {
  const { isMobile } = useDeviceType()

  return (
    <div className={cn('space-y-4', className)}>
      {/* Search input with suggestions */}
      <div className="relative">
        <div className={`h-12 w-full bg-muted animate-${animation} rounded-lg`} />
        
        {/* Search suggestions dropdown */}
        <div className="absolute top-full left-0 right-0 mt-1 bg-card border rounded-lg shadow-lg z-10">
          <div className="p-2 space-y-1">
            {Array.from({ length: 5 }, (_, i) => (
              <div key={i} className="flex items-center gap-3 p-2 hover:bg-muted/50 rounded-md">
                <div className="w-4 h-4 bg-muted animate-pulse rounded" />
                <div className={`h-4 w-${32 + i * 8} bg-muted animate-${animation} rounded-md`} />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick filters */}
      <div className="flex flex-wrap gap-2">
        {Array.from({ length: 6 }, (_, i) => (
          <div key={i} className={`h-8 w-${16 + i * 4} bg-muted animate-${animation} rounded-full`} />
        ))}
      </div>

      {/* Recent searches */}
      <div className="space-y-2">
        <div className={`h-4 w-24 bg-muted animate-${animation} rounded-md`} />
        <div className="flex flex-wrap gap-2">
          {Array.from({ length: 4 }, (_, i) => (
            <div key={i} className={`h-6 w-${20 + i * 6} bg-muted animate-${animation} rounded-full`} />
          ))}
        </div>
      </div>
    </div>
  )
}