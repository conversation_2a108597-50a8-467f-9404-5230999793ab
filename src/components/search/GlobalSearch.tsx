import { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { searchService } from '@/lib/search'
import { SearchSuggestion, filtersToUrlParams } from '@/lib/search'
import { 
  Search, 
  FileText, 
  User, 
  Tag, 
  Clock, 
  TrendingUp,
  X,
  Loader2,
  ArrowRight,
  History
} from 'lucide-react'

interface SearchResult {
  id: string
  type: 'problem' | 'expert' | 'solution'
  title: string
  description: string
  category?: string
  sector?: string
  tags?: string[]
  relevanceScore: number
  metadata?: any
}

export function GlobalSearch() {
  const navigate = useNavigate()
  const { toast } = useToast()
  const [query, setQuery] = useState('')
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const debounceRef = useRef<NodeJS.Timeout>()

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches')
    if (saved) {
      setRecentSearches(JSON.parse(saved))
    }
  }, [])

  // Handle click outside to close results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Debounced suggestions loading
  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    debounceRef.current = setTimeout(() => {
      if (query.trim().length >= 2) {
        loadSuggestions(query.trim())
      } else {
        loadRecentSuggestions()
      }
    }, 300)

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
    }
  }, [query])

  const loadSuggestions = async (searchQuery: string) => {
    setIsLoadingSuggestions(true)
    try {
      const suggestions = await searchService.suggest(searchQuery, 6)
      setSuggestions(suggestions)
    } catch (error) {
      console.error('Error loading suggestions:', error)
      setSuggestions([])
    } finally {
      setIsLoadingSuggestions(false)
    }
  }

  const loadRecentSuggestions = () => {
    const recentSuggestions: SearchSuggestion[] = recentSearches.slice(0, 5).map(search => ({
      text: search,
      type: 'recent'
    }))
    setSuggestions(recentSuggestions)
  }



  const handleSearch = (searchQuery: string) => {
    if (!searchQuery.trim()) return
    
    // Add to recent searches
    const newRecentSearches = [
      searchQuery,
      ...recentSearches.filter(s => s !== searchQuery)
    ].slice(0, 10)
    
    setRecentSearches(newRecentSearches)
    localStorage.setItem('recentSearches', JSON.stringify(newRecentSearches))
    
    // Navigate to search results page with enhanced URL parameters
    const params = filtersToUrlParams({}, searchQuery.trim(), 'relevance')
    navigate(`/search?${params.toString()}`)
    setShowResults(false)
  }

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.text)
    handleSearch(suggestion.text)
  }

  const clearRecentSearches = () => {
    setRecentSearches([])
    localStorage.removeItem('recentSearches')
    setSuggestions([])
  }



  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'recent': return <History className="w-4 h-4 text-gray-400" />
      case 'popular': return <TrendingUp className="w-4 h-4 text-orange-400" />
      case 'category': return <FileText className="w-4 h-4 text-blue-400" />
      case 'expert': return <User className="w-4 h-4 text-green-400" />
      case 'tag': return <Tag className="w-4 h-4 text-purple-400" />
      default: return <Search className="w-4 h-4 text-gray-400" />
    }
  }

  return (
    <div ref={searchRef} className="relative w-full max-w-2xl">
      {/* Search Input */}
      <div className="relative">
        <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <Input
          ref={inputRef}
          type="text"
          placeholder="ابحث في الحلول الاستراتيجية، شبكة الخبراء، والتحديات الحرجة..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => {
            setShowResults(true)
            if (!query.trim()) loadSuggestions()
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && query.trim()) {
              handleSearch(query.trim())
            }
            if (e.key === 'Escape') {
              setShowResults(false)
            }
          }}
          className="pr-10 pl-4 h-12 text-lg"
          dir="rtl"
        />
        {query && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setQuery('')
              inputRef.current?.focus()
            }}
            className="absolute left-2 top-1/2 transform -translate-y-1/2 p-1 h-auto"
          >
            <X className="w-4 h-4" />
          </Button>
        )}
      </div>

      {/* Search Suggestions Dropdown */}
      {showResults && (
        <Card className="absolute top-full left-0 right-0 mt-2 max-h-96 overflow-y-auto z-50 shadow-lg">
          <CardContent className="p-0">
            {isLoadingSuggestions ? (
              <div className="flex items-center justify-center p-6">
                <Loader2 className="w-5 h-5 animate-spin text-blue-600 mr-2" />
                <span className="text-gray-600">جاري تحميل الاقتراحات...</span>
              </div>
            ) : suggestions.length > 0 ? (
              <div className="space-y-1">
                <div className="p-3 border-b">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-gray-900">
                      {query.trim().length >= 2 ? 'اقتراحات البحث' : 'عمليات البحث الأخيرة'}
                    </h3>
                    {recentSearches.length > 0 && query.trim().length < 2 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={clearRecentSearches}
                        className="text-xs text-gray-500 hover:text-gray-700"
                      >
                        مسح السجل
                      </Button>
                    )}
                  </div>
                </div>
                
                {suggestions.map((suggestion, index) => (
                  <div
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="flex items-center gap-3 p-3 hover:bg-gray-50 cursor-pointer transition-colors"
                  >
                    {getSuggestionIcon(suggestion.type)}
                    <span className="flex-1 text-gray-700">
                      {suggestion.text}
                    </span>
                    {suggestion.count && (
                      <Badge variant="outline" className="text-xs">
                        {suggestion.count}
                      </Badge>
                    )}
                    <ArrowRight className="w-4 h-4 text-gray-400" />
                  </div>
                ))}
                
                {query.trim().length >= 2 && (
                  <div className="p-3 border-t text-center">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSearch(query)}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      عرض جميع النتائج لـ "{query}"
                    </Button>
                  </div>
                )}
              </div>
            ) : query.trim().length >= 2 ? (
              <div className="p-6 text-center">
                <Search className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-600 text-sm">
                  لا توجد اقتراحات لـ "{query}"
                </p>
              </div>
            ) : (
              <div className="p-3">
                <h4 className="text-xs font-medium text-gray-500 mb-2">
                  المجالات الاستراتيجية
                </h4>
                <div className="flex flex-wrap gap-2">
                  {[
                    'الزراعة الذكية', 
                    'الصناعة والتصنيع الذكي',
                    'الطاقة والمرافق الذكية',
                    'Predictive Maintenance AI',
                    'الذكاء الاصطناعي التنبؤي للصيانة',
                    'Additive Manufacturing',
                    'الطباعة ثلاثية الأبعاد',
                    'Industrial IoT',
                    'إنترنت الأشياء الصناعي',
                    'Robotics & Cobots',
                    'الروبوتات التعاونية'
                  ].map((category) => (
                    <Button
                      key={category}
                      variant="outline"
                      size="sm"
                      onClick={() => handleSuggestionClick({ text: category, type: 'category' })}
                      className="text-xs h-7"
                    >
                      {category}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}