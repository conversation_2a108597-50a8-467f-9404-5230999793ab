import { useState, useRef, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useSearchIntegration } from './SearchIntegration'
import { SearchSuggestion } from '@/lib/search'
import { 
  Search, 
  TrendingUp, 
  Clock, 
  ArrowRight,
  Loader2,
  X
} from 'lucide-react'

interface DashboardSearchWidgetProps {
  placeholder?: string
  showSuggestions?: boolean
  maxSuggestions?: number
  className?: string
  onSearch?: (query: string) => void
}

export function DashboardSearchWidget({
  placeholder = 'ابحث في المنصة...',
  showSuggestions = true,
  maxSuggestions = 5,
  className = '',
  onSearch
}: DashboardSearchWidgetProps) {
  const navigate = useNavigate()
  const [query, setQuery] = useState('')
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([])
  const [showSuggestionsList, setShowSuggestionsList] = useState(false)
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const inputRef = useRef<HTMLInputElement>(null)
  const widgetRef = useRef<HTMLDivElement>(null)
  const debounceRef = useRef<NodeJS.Timeout>()

  const { getSuggestions, navigateToSearch, isSearching } = useSearchIntegration()

  // Load recent searches from localStorage
  useEffect(() => {
    try {
      const saved = localStorage.getItem('recentSearches')
      if (saved) {
        setRecentSearches(JSON.parse(saved).slice(0, 3))
      }
    } catch (error) {
      console.error('Error loading recent searches:', error)
    }
  }, [])

  // Handle click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (widgetRef.current && !widgetRef.current.contains(event.target as Node)) {
        setShowSuggestionsList(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Debounced suggestions loading
  useEffect(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }

    debounceRef.current = setTimeout(async () => {
      if (query.trim().length >= 2 && showSuggestions) {
        try {
          const newSuggestions = await getSuggestions(query.trim(), maxSuggestions)
          setSuggestions(newSuggestions)
        } catch (error) {
          console.error('Error loading suggestions:', error)
          setSuggestions([])
        }
      } else if (query.trim().length === 0) {
        // Show recent searches when input is empty
        const recentSuggestions: SearchSuggestion[] = recentSearches.map(search => ({
          text: search,
          type: 'recent'
        }))
        setSuggestions(recentSuggestions)
      } else {
        setSuggestions([])
      }
    }, 300)

    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
    }
  }, [query, showSuggestions, maxSuggestions, recentSearches, getSuggestions])

  const handleSearch = (searchQuery?: string) => {
    const finalQuery = searchQuery || query
    if (!finalQuery.trim()) return

    // Save to recent searches
    const newRecentSearches = [
      finalQuery,
      ...recentSearches.filter(s => s !== finalQuery)
    ].slice(0, 10)
    
    setRecentSearches(newRecentSearches)
    localStorage.setItem('recentSearches', JSON.stringify(newRecentSearches))

    // Close suggestions
    setShowSuggestionsList(false)

    // Call custom handler or navigate
    if (onSearch) {
      onSearch(finalQuery)
    } else {
      navigateToSearch(finalQuery)
    }
  }

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.text)
    handleSearch(suggestion.text)
  }

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'recent':
        return <Clock className="w-4 h-4 text-gray-400" />
      case 'popular':
        return <TrendingUp className="w-4 h-4 text-orange-400" />
      default:
        return <Search className="w-4 h-4 text-gray-400" />
    }
  }

  return (
    <div ref={widgetRef} className={`relative ${className}`}>
      <Card className="shadow-sm">
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              ref={inputRef}
              type="text"
              placeholder={placeholder}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onFocus={() => setShowSuggestionsList(true)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch()
                }
                if (e.key === 'Escape') {
                  setShowSuggestionsList(false)
                }
              }}
              className="pr-10 pl-12 h-11"
              dir="rtl"
            />
            
            {/* Loading indicator */}
            {isSearching && (
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
              </div>
            )}
            
            {/* Clear button */}
            {query && !isSearching && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setQuery('')
                  inputRef.current?.focus()
                }}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 p-1 h-auto"
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Suggestions Dropdown */}
      {showSuggestionsList && suggestions.length > 0 && (
        <Card className="absolute top-full left-0 right-0 mt-2 max-h-64 overflow-y-auto z-50 shadow-lg">
          <CardContent className="p-0">
            <div className="space-y-1">
              <div className="p-3 border-b">
                <h3 className="text-sm font-medium text-gray-900">
                  {query.trim().length >= 2 ? 'اقتراحات البحث' : 'عمليات البحث الأخيرة'}
                </h3>
              </div>
              
              {suggestions.map((suggestion, index) => (
                <div
                  key={index}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className="flex items-center gap-3 p-3 hover:bg-gray-50 cursor-pointer transition-colors"
                >
                  {getSuggestionIcon(suggestion.type)}
                  <span className="flex-1 text-gray-700">
                    {suggestion.text}
                  </span>
                  {suggestion.count && (
                    <Badge variant="outline" className="text-xs">
                      {suggestion.count}
                    </Badge>
                  )}
                  <ArrowRight className="w-4 h-4 text-gray-400" />
                </div>
              ))}
              
              {query.trim().length >= 2 && (
                <div className="p-3 border-t text-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSearch()}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    عرض جميع النتائج لـ "{query}"
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}