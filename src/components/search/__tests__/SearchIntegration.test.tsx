import { describe, it, expect, beforeEach, vi } from 'vitest'
import { SearchIntegration } from '../SearchIntegration'
import { searchService } from '@/lib/search'

// Mock the search service
vi.mock('@/lib/search', () => ({
  searchService: {
    search: vi.fn(),
    suggest: vi.fn()
  }
}))

// Mock the toast hook
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}))

// Mock react-router-dom
const mockNavigate = vi.fn()
vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate
}))

describe('SearchIntegration', () => {
  let integration: ReturnType<typeof SearchIntegration>

  beforeEach(() => {
    vi.clearAllMocks()
    integration = SearchIntegration({})
  })

  describe('performSearch', () => {
    it('should perform a basic search successfully', async () => {
      const mockResults = {
        items: [
          {
            id: '1',
            type: 'problem' as const,
            title: 'Test Problem',
            description: 'Test Description',
            tags: ['test'],
            relevanceScore: 0.8,
            metadata: {}
          }
        ],
        totalCount: 1,
        hasMore: false,
        facets: {}
      }

      vi.mocked(searchService.search).mockResolvedValue(mockResults)
      
      const searchResults = await integration.performSearch('test query')
      
      expect(searchService.search).toHaveBeenCalledWith({
        text: 'test query',
        filters: {},
        sortBy: 'relevance',
        pagination: { limit: 20, offset: 0 },
        language: 'auto'
      })
      
      expect(searchResults).toEqual(mockResults)
    })

    it('should handle search errors gracefully', async () => {
      const mockError = new Error('Search failed')
      vi.mocked(searchService.search).mockRejectedValue(mockError)

      const mockOnError = vi.fn()
      const integration = SearchIntegration({ onError: mockOnError })
      
      const searchResults = await integration.performSearch('test query')
      
      expect(mockOnError).toHaveBeenCalledWith('حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.')
      expect(searchResults).toBeUndefined()
    })

    it('should not search with empty query', async () => {
      const integration = SearchIntegration({})
      
      const searchResults = await integration.performSearch('')
      
      expect(searchService.search).not.toHaveBeenCalled()
      expect(searchResults).toBeUndefined()
    })

    it('should apply default filters', async () => {
      const defaultFilters = { contentType: ['problem' as const] }
      const mockResults = { items: [], totalCount: 0, hasMore: false, facets: {} }
      
      vi.mocked(searchService.search).mockResolvedValue(mockResults)

      const integration = SearchIntegration({ defaultFilters })
      
      await integration.performSearch('test query', { sectors: ['technology'] })
      
      expect(searchService.search).toHaveBeenCalledWith({
        text: 'test query',
        filters: {
          contentType: ['problem'],
          sectors: ['technology']
        },
        sortBy: 'relevance',
        pagination: { limit: 20, offset: 0 },
        language: 'auto'
      })
    })
  })

  describe('navigateToSearch', () => {
    it('should navigate to search page with correct parameters', () => {
      const integration = SearchIntegration({})
      
      integration.navigateToSearch('test query', {
        contentType: ['problem'],
        categories: ['tech'],
        sectors: ['technology']
      })
      
      expect(mockNavigate).toHaveBeenCalledWith('/search?q=test+query&type=problem&categories=tech&sectors=technology')
    })

    it('should handle query with special characters', () => {
      const integration = SearchIntegration({})
      
      integration.navigateToSearch('test & query')
      
      expect(mockNavigate).toHaveBeenCalledWith('/search?q=test+%26+query')
    })
  })

  describe('searchProblems', () => {
    it('should search for problems specifically', async () => {
      const mockResults = { items: [], totalCount: 0, hasMore: false, facets: {} }
      vi.mocked(searchService.search).mockResolvedValue(mockResults)

      const integration = SearchIntegration({})
      
      await integration.searchProblems('software bug', { sectors: ['technology'] })
      
      expect(searchService.search).toHaveBeenCalledWith({
        text: 'software bug',
        filters: {
          contentType: ['problem'],
          sectors: ['technology']
        },
        sortBy: 'relevance',
        pagination: { limit: 20, offset: 0 },
        language: 'auto'
      })
    })
  })

  describe('searchExperts', () => {
    it('should search for experts specifically', async () => {
      const mockResults = { items: [], totalCount: 0, hasMore: false, facets: {} }
      vi.mocked(searchService.search).mockResolvedValue(mockResults)

      const integration = SearchIntegration({})
      
      await integration.searchExperts('javascript developer', { availability: ['available'] })
      
      expect(searchService.search).toHaveBeenCalledWith({
        text: 'javascript developer',
        filters: {
          contentType: ['expert'],
          availability: ['available']
        },
        sortBy: 'relevance',
        pagination: { limit: 20, offset: 0 },
        language: 'auto'
      })
    })
  })

  describe('searchSolutions', () => {
    it('should search for solutions specifically', async () => {
      const mockResults = { items: [], totalCount: 0, hasMore: false, facets: {} }
      vi.mocked(searchService.search).mockResolvedValue(mockResults)

      const integration = SearchIntegration({})
      
      await integration.searchSolutions('database optimization', { status: ['approved'] })
      
      expect(searchService.search).toHaveBeenCalledWith({
        text: 'database optimization',
        filters: {
          contentType: ['solution'],
          status: ['approved']
        },
        sortBy: 'relevance',
        pagination: { limit: 20, offset: 0 },
        language: 'auto'
      })
    })
  })

  describe('getSuggestions', () => {
    it('should get search suggestions', async () => {
      const mockSuggestions = [
        { text: 'javascript', type: 'popular' },
        { text: 'react', type: 'popular' }
      ]
      
      vi.mocked(searchService.suggest).mockResolvedValue(mockSuggestions)

      const integration = SearchIntegration({})
      
      const suggestions = await integration.getSuggestions('java')
      
      expect(searchService.suggest).toHaveBeenCalledWith('java', 5)
      expect(suggestions).toEqual(mockSuggestions)
    })

    it('should handle suggestion errors gracefully', async () => {
      vi.mocked(searchService.suggest).mockRejectedValue(new Error('Suggestion failed'))

      const integration = SearchIntegration({})
      
      const suggestions = await integration.getSuggestions('java')
      
      expect(suggestions).toEqual([])
    })

    it('should use custom limit for suggestions', async () => {
      const mockSuggestions = []
      vi.mocked(searchService.suggest).mockResolvedValue(mockSuggestions)

      const integration = SearchIntegration({})
      
      await integration.getSuggestions('java', 10)
      
      expect(searchService.suggest).toHaveBeenCalledWith('java', 10)
    })
  })

  describe('loading state', () => {
    it('should track loading state during search', async () => {
      let resolveSearch: (value: any) => void
      const searchPromise = new Promise(resolve => {
        resolveSearch = resolve
      })
      
      vi.mocked(searchService.search).mockReturnValue(searchPromise)

      const integration = SearchIntegration({})
      
      // Start search
      const searchPromiseResult = integration.performSearch('test')
      
      // Should be loading
      expect(integration.isSearching).toBe(true)
      
      // Resolve search
      resolveSearch!({ items: [], totalCount: 0, hasMore: false, facets: {} })
      await searchPromiseResult
      
      // Should not be loading anymore
      expect(integration.isSearching).toBe(false)
    })
  })

  describe('onResults callback', () => {
    it('should call onResults callback when search completes', async () => {
      const mockResults = { items: [], totalCount: 0, hasMore: false, facets: {} }
      const mockOnResults = vi.fn()
      
      vi.mocked(searchService.search).mockResolvedValue(mockResults)

      const integration = SearchIntegration({ onResults: mockOnResults })
      
      await integration.performSearch('test query')
      
      expect(mockOnResults).toHaveBeenCalledWith(mockResults)
    })
  })
})