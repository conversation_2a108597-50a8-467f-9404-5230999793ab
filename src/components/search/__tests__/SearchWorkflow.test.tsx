import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { GlobalSearch } from '../GlobalSearch'
import { EnhancedSearchInterface } from '../EnhancedSearchInterface'
import { SearchResultsDisplay } from '../SearchResultsDisplay'

// Mock all external dependencies
vi.mock('@/lib/search', () => ({
  searchService: {
    search: vi.fn(),
    suggest: vi.fn()
  },
  filtersToUrlParams: vi.fn().mockReturnValue(new URLSearchParams()),
  urlParamsToFilters: vi.fn().mockReturnValue({ query: '', filters: {}, sortBy: 'relevance' }),
  validateFilters: vi.fn().mockImplementation((filters) => filters)
}))

vi.mock('@/lib/search/SearchAnalytics', () => ({
  useSearchAnalytics: () => ({
    trackSuggestion: vi.fn(),
    trackFilter: vi.fn(),
    trackSearch: vi.fn().mockResolvedValue('analytics-id'),
    trackClick: vi.fn()
  })
}))

vi.mock('@/hooks/use-mobile', () => ({
  useDeviceType: () => ({
    isMobile: false,
    isTablet: false,
    isTouchDevice: false
  }),
  useTouchSupport: () => false
}))

vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}))

// Mock react-router-dom
const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useSearchParams: () => [new URLSearchParams(), vi.fn()]
  }
})

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>{children}</BrowserRouter>
)

describe('Search Workflow End-to-End Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Clear localStorage
    localStorage.clear()
  })

  describe('Complete Search Flow', () => {
    it('should complete a full search workflow from input to results', async () => {
      const mockSuggestions = [
        { text: 'تطوير البرمجيات', type: 'popular' },
        { text: 'تطوير المواقع', type: 'category' }
      ]

      const mockSearchResults = {
        items: [
          {
            id: '1',
            type: 'problem' as const,
            title: 'مشكلة في تطوير البرمجيات',
            description: 'وصف المشكلة التقنية',
            tags: ['برمجة', 'تطوير'],
            relevanceScore: 0.9,
            metadata: {
              status: 'open',
              created_at: '2024-01-01T00:00:00Z',
              submitter_name: 'أحمد محمد'
            }
          }
        ],
        totalCount: 1,
        hasMore: false,
        facets: {}
      }

      const { searchService } = await import('@/lib/search')
      vi.mocked(searchService.suggest).mockResolvedValue(mockSuggestions)
      vi.mocked(searchService.search).mockResolvedValue(mockSearchResults)

      render(
        <TestWrapper>
          <GlobalSearch />
        </TestWrapper>
      )

      // Step 1: User types in search input
      const searchInput = screen.getByRole('textbox')
      fireEvent.change(searchInput, { target: { value: 'تطوير' } })

      // Step 2: Wait for suggestions to appear
      await waitFor(() => {
        expect(searchService.suggest).toHaveBeenCalledWith('تطوير', 6)
      })

      // Step 3: User clicks on a suggestion
      await waitFor(() => {
        const suggestion = screen.getByText('تطوير البرمجيات')
        fireEvent.click(suggestion)
      })

      // Step 4: Verify navigation to search results
      expect(mockNavigate).toHaveBeenCalledWith(expect.stringContaining('/search'))
    })

    it('should handle search with filters', async () => {
      const mockOnSearch = vi.fn()

      render(
        <TestWrapper>
          <EnhancedSearchInterface
            onSearch={mockOnSearch}
            showFilters={true}
          />
        </TestWrapper>
      )

      // Enter search query
      const searchInput = screen.getByRole('searchbox')
      fireEvent.change(searchInput, { target: { value: 'database optimization' } })

      // Click search button
      const searchButton = screen.getByRole('button', { name: /بحث/i })
      fireEvent.click(searchButton)

      // Verify search was called
      await waitFor(() => {
        expect(mockOnSearch).toHaveBeenCalledWith(
          'database optimization',
          expect.any(Object),
          'relevance'
        )
      })
    })

    it('should handle empty search gracefully', async () => {
      const mockOnSearch = vi.fn()

      render(
        <TestWrapper>
          <EnhancedSearchInterface onSearch={mockOnSearch} />
        </TestWrapper>
      )

      // Try to search with empty input
      const searchButton = screen.getByRole('button', { name: /بحث/i })
      fireEvent.click(searchButton)

      // Should not call search with empty query
      expect(mockOnSearch).not.toHaveBeenCalled()
    })
  })

  describe('Search Results Display', () => {
    it('should display search results correctly', async () => {
      const mockResults = {
        items: [
          {
            id: '1',
            type: 'problem' as const,
            title: 'Database Performance Issue',
            description: 'Slow query performance in production database',
            tags: ['database', 'performance', 'sql'],
            relevanceScore: 0.95,
            metadata: {
              status: 'open',
              urgency: 'high',
              created_at: '2024-01-15T10:30:00Z',
              submitter_name: 'John Doe',
              organization: 'Tech Corp'
            }
          },
          {
            id: '2',
            type: 'expert' as const,
            title: 'Database Expert',
            description: 'Senior database administrator with 10+ years experience',
            tags: ['database', 'postgresql', 'optimization'],
            relevanceScore: 0.88,
            metadata: {
              rating: 4.8,
              availability: 'available',
              experience_years: 12,
              location: 'Damascus'
            }
          }
        ],
        totalCount: 2,
        hasMore: false,
        facets: {
          contentType: { problem: 1, expert: 1 },
          sectors: { technology: 2 }
        }
      }

      render(
        <TestWrapper>
          <SearchResultsDisplay
            results={mockResults}
            loading={false}
            error={null}
            onSortChange={vi.fn()}
            onPageChange={vi.fn()}
            onResultClick={vi.fn()}
            currentPage={1}
            pageSize={20}
          />
        </TestWrapper>
      )

      // Verify results are displayed
      expect(screen.getByText('Database Performance Issue')).toBeInTheDocument()
      expect(screen.getByText('Database Expert')).toBeInTheDocument()
      
      // Verify metadata is shown
      expect(screen.getByText(/Slow query performance/)).toBeInTheDocument()
      expect(screen.getByText(/Senior database administrator/)).toBeInTheDocument()
    })

    it('should handle loading state', () => {
      render(
        <TestWrapper>
          <SearchResultsDisplay
            results={null}
            loading={true}
            error={null}
            onSortChange={vi.fn()}
            onPageChange={vi.fn()}
            onResultClick={vi.fn()}
            currentPage={1}
            pageSize={20}
          />
        </TestWrapper>
      )

      expect(screen.getByText(/جاري البحث/)).toBeInTheDocument()
    })

    it('should handle error state', () => {
      render(
        <TestWrapper>
          <SearchResultsDisplay
            results={null}
            loading={false}
            error="Search failed"
            onSortChange={vi.fn()}
            onPageChange={vi.fn()}
            onResultClick={vi.fn()}
            currentPage={1}
            pageSize={20}
          />
        </TestWrapper>
      )

      expect(screen.getByText(/خطأ في البحث/)).toBeInTheDocument()
    })

    it('should handle empty results', () => {
      const emptyResults = {
        items: [],
        totalCount: 0,
        hasMore: false,
        facets: {}
      }

      render(
        <TestWrapper>
          <SearchResultsDisplay
            results={emptyResults}
            loading={false}
            error={null}
            onSortChange={vi.fn()}
            onPageChange={vi.fn()}
            onResultClick={vi.fn()}
            currentPage={1}
            pageSize={20}
          />
        </TestWrapper>
      )

      expect(screen.getByText(/لا توجد نتائج/)).toBeInTheDocument()
    })
  })

  describe('Search Analytics Integration', () => {
    it('should track search analytics events', async () => {
      const { useSearchAnalytics } = await import('@/lib/search/SearchAnalytics')
      const mockTrackSearch = vi.fn().mockResolvedValue('analytics-id')
      const mockTrackSuggestion = vi.fn()

      vi.mocked(useSearchAnalytics).mockReturnValue({
        trackSearch: mockTrackSearch,
        trackSuggestion: mockTrackSuggestion,
        trackFilter: vi.fn(),
        trackClick: vi.fn()
      })

      const mockOnSearch = vi.fn()

      render(
        <TestWrapper>
          <EnhancedSearchInterface onSearch={mockOnSearch} />
        </TestWrapper>
      )

      // Perform search
      const searchInput = screen.getByRole('searchbox')
      fireEvent.change(searchInput, { target: { value: 'test query' } })
      
      const searchButton = screen.getByRole('button', { name: /بحث/i })
      fireEvent.click(searchButton)

      await waitFor(() => {
        expect(mockOnSearch).toHaveBeenCalled()
      })
    })
  })

  describe('Accessibility in Search Workflow', () => {
    it('should maintain focus management during search', async () => {
      render(
        <TestWrapper>
          <GlobalSearch />
        </TestWrapper>
      )

      const searchInput = screen.getByRole('textbox')
      
      // Focus the input
      searchInput.focus()
      expect(document.activeElement).toBe(searchInput)

      // Type to trigger suggestions
      fireEvent.change(searchInput, { target: { value: 'test' } })

      // Input should maintain focus
      expect(document.activeElement).toBe(searchInput)
    })

    it('should support keyboard navigation', async () => {
      const mockSuggestions = [
        { text: 'suggestion 1', type: 'popular' },
        { text: 'suggestion 2', type: 'recent' }
      ]

      const { searchService } = await import('@/lib/search')
      vi.mocked(searchService.suggest).mockResolvedValue(mockSuggestions)

      render(
        <TestWrapper>
          <GlobalSearch />
        </TestWrapper>
      )

      const searchInput = screen.getByRole('textbox')
      
      // Type to show suggestions
      fireEvent.change(searchInput, { target: { value: 'sug' } })

      // Wait for suggestions
      await waitFor(() => {
        expect(screen.getByText('suggestion 1')).toBeInTheDocument()
      })

      // Test arrow key navigation
      fireEvent.keyDown(searchInput, { key: 'ArrowDown' })
      
      // Test Enter key to select
      fireEvent.keyDown(searchInput, { key: 'Enter' })

      // Should navigate to search results
      expect(mockNavigate).toHaveBeenCalled()
    })

    it('should announce search results to screen readers', async () => {
      const mockResults = {
        items: [
          {
            id: '1',
            type: 'problem' as const,
            title: 'Test Problem',
            description: 'Test Description',
            tags: ['test'],
            relevanceScore: 0.8,
            metadata: {}
          }
        ],
        totalCount: 1,
        hasMore: false,
        facets: {}
      }

      render(
        <TestWrapper>
          <SearchResultsDisplay
            results={mockResults}
            loading={false}
            error={null}
            onSortChange={vi.fn()}
            onPageChange={vi.fn()}
            onResultClick={vi.fn()}
            currentPage={1}
            pageSize={20}
          />
        </TestWrapper>
      )

      // Check for screen reader announcements
      const statusElement = screen.getByRole('status')
      expect(statusElement).toBeInTheDocument()
    })
  })

  describe('Mobile Search Experience', () => {
    it('should adapt to mobile viewport', () => {
      // Mock mobile device
      vi.mocked(require('@/hooks/use-mobile').useDeviceType).mockReturnValue({
        isMobile: true,
        isTablet: false,
        isTouchDevice: true
      })

      render(
        <TestWrapper>
          <EnhancedSearchInterface showFilters={true} />
        </TestWrapper>
      )

      // Should show mobile-optimized interface
      const searchInput = screen.getByRole('searchbox')
      expect(searchInput).toBeInTheDocument()
      
      // Mobile-specific elements should be present
      expect(screen.getByRole('button', { name: /بحث/i })).toBeInTheDocument()
    })
  })

  describe('Error Recovery', () => {
    it('should recover from search service errors', async () => {
      const { searchService } = await import('@/lib/search')
      
      // First call fails
      vi.mocked(searchService.search).mockRejectedValueOnce(new Error('Network error'))
      
      // Second call succeeds
      vi.mocked(searchService.search).mockResolvedValueOnce({
        items: [],
        totalCount: 0,
        hasMore: false,
        facets: {}
      })

      const mockOnSearch = vi.fn()

      render(
        <TestWrapper>
          <EnhancedSearchInterface onSearch={mockOnSearch} />
        </TestWrapper>
      )

      const searchInput = screen.getByRole('searchbox')
      const searchButton = screen.getByRole('button', { name: /بحث/i })

      // First search attempt
      fireEvent.change(searchInput, { target: { value: 'test' } })
      fireEvent.click(searchButton)

      await waitFor(() => {
        expect(mockOnSearch).toHaveBeenCalled()
      })

      // Should handle error gracefully and allow retry
      fireEvent.click(searchButton)

      await waitFor(() => {
        expect(mockOnSearch).toHaveBeenCalledTimes(2)
      })
    })
  })
})