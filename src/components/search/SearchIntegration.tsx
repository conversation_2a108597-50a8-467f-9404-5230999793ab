import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { searchService } from '@/lib/search'
import { SearchFilters, SearchQuery, SearchResults } from '@/lib/search'
import { useToast } from '@/hooks/use-toast'

interface SearchIntegrationProps {
  onResults?: (results: SearchResults) => void
  onError?: (error: string) => void
  autoSearch?: boolean
  defaultFilters?: SearchFilters
}

/**
 * SearchIntegration component provides a centralized way to integrate
 * search functionality across different parts of the platform
 */
export function SearchIntegration({
  onResults,
  onError,
  autoSearch = false,
  defaultFilters = {}
}: SearchIntegrationProps) {
  const navigate = useNavigate()
  const { toast } = useToast()
  const [isSearching, setIsSearching] = useState(false)

  /**
   * Perform a search with the given query and filters
   */
  const performSearch = async (query: string, filters: SearchFilters = {}) => {
    if (!query.trim()) return

    setIsSearching(true)
    
    try {
      const searchQuery: SearchQuery = {
        text: query.trim(),
        filters: { ...defaultFilters, ...filters },
        sortBy: 'relevance',
        pagination: { limit: 20, offset: 0 },
        language: 'auto'
      }

      const results = await searchService.search(searchQuery)
      
      if (onResults) {
        onResults(results)
      }

      return results
    } catch (error) {
      console.error('Search integration error:', error)
      const errorMessage = 'حدث خطأ أثناء البحث. يرجى المحاولة مرة أخرى.'
      
      if (onError) {
        onError(errorMessage)
      } else {
        toast({
          title: 'خطأ في البحث',
          description: errorMessage,
          variant: 'destructive'
        })
      }
    } finally {
      setIsSearching(false)
    }
  }

  /**
   * Navigate to search results page with the given query and filters
   */
  const navigateToSearch = (query: string, filters: SearchFilters = {}) => {
    const params = new URLSearchParams()
    params.set('q', query.trim())
    
    if (filters.contentType?.length) {
      params.set('type', filters.contentType.join(','))
    }
    if (filters.categories?.length) {
      params.set('categories', filters.categories.join(','))
    }
    if (filters.sectors?.length) {
      params.set('sectors', filters.sectors.join(','))
    }
    if (filters.status?.length) {
      params.set('status', filters.status.join(','))
    }
    
    navigate(`/search?${params.toString()}`)
  }

  /**
   * Search for problems with specific filters
   */
  const searchProblems = async (query: string, additionalFilters: SearchFilters = {}) => {
    const filters: SearchFilters = {
      contentType: ['problem'],
      ...additionalFilters
    }
    
    return performSearch(query, filters)
  }

  /**
   * Search for experts with specific filters
   */
  const searchExperts = async (query: string, additionalFilters: SearchFilters = {}) => {
    const filters: SearchFilters = {
      contentType: ['expert'],
      ...additionalFilters
    }
    
    return performSearch(query, filters)
  }

  /**
   * Search for solutions with specific filters
   */
  const searchSolutions = async (query: string, additionalFilters: SearchFilters = {}) => {
    const filters: SearchFilters = {
      contentType: ['solution'],
      ...additionalFilters
    }
    
    return performSearch(query, filters)
  }

  /**
   * Get search suggestions for a given query
   */
  const getSuggestions = async (query: string, limit: number = 5) => {
    try {
      return await searchService.suggest(query, limit)
    } catch (error) {
      console.error('Error getting suggestions:', error)
      return []
    }
  }

  return {
    performSearch,
    navigateToSearch,
    searchProblems,
    searchExperts,
    searchSolutions,
    getSuggestions,
    isSearching
  }
}

/**
 * Hook to use search integration functionality
 */
export function useSearchIntegration(props?: SearchIntegrationProps) {
  const integration = SearchIntegration(props || {})
  return integration
}