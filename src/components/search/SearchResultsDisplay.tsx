import { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Skeleton } from '@/components/ui/skeleton'
import { Separator } from '@/components/ui/separator'
import { useDeviceType, useTouchSupport } from '@/hooks/use-mobile'
import { 
  SearchResults, 
  SearchResult, 
  ContentType, 
  SortOption 
} from '@/lib/search/types'
import { useSearchAnalytics } from '@/lib/search/SearchAnalytics'
import { 
  FileText, 
  User, 
  Lightbulb, 
  Video,
  Calendar,
  MapPin,
  Star,
  Clock,
  TrendingUp,
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  Eye,
  MessageSquare,
  ThumbsUp,
  Award,
  Briefcase,
  GraduationCap,
  Zap,
  CheckCircle,
  XCircle,
  Pause,
  Play
} from 'lucide-react'
import { format } from 'date-fns'
import { ar } from 'date-fns/locale'

interface SearchResultsDisplayProps {
  results: SearchResults | null
  loading?: boolean
  error?: string | null
  onSortChange?: (sortBy: SortOption) => void
  onPageChange?: (page: number) => void
  onResultClick?: (result: SearchResult) => void
  currentPage?: number
  pageSize?: number
  className?: string
  analyticsId?: string // For tracking clicks
}

interface TabInfo {
  id: string
  label: string
  icon: React.ReactNode
  count: number
  description: string
}

const SORT_OPTIONS = [
  { value: 'relevance', label: 'الأكثر صلة' },
  { value: 'date_created', label: 'الأحدث' },
  { value: 'date_updated', label: 'آخر تحديث' },
  { value: 'rating', label: 'الأعلى تقييماً' },
  { value: 'popularity', label: 'الأكثر شعبية' },
  { value: 'proximity', label: 'الأقرب جغرافياً' },
  { value: 'implementation_success', label: 'نجاح التطبيق' }
] as const

export function SearchResultsDisplay({
  results,
  loading = false,
  error = null,
  onSortChange,
  onPageChange,
  onResultClick,
  currentPage = 1,
  pageSize = 20,
  className = '',
  analyticsId
}: SearchResultsDisplayProps) {
  const { isMobile, isTablet, isTouchDevice } = useDeviceType()
  const hasTouch = useTouchSupport()
  const [activeTab, setActiveTab] = useState('all')
  const [highlightedText, setHighlightedText] = useState('')
  const { trackClick } = useSearchAnalytics()

  // Extract search query for highlighting
  useEffect(() => {
    if (results?.query?.text) {
      setHighlightedText(results.query.text.toLowerCase())
    }
  }, [results?.query?.text])

  if (loading) {
    return <SearchResultsSkeleton />
  }

  if (error) {
    return <SearchErrorDisplay error={error} />
  }

  if (!results || results.items.length === 0) {
    return <EmptySearchResults query={results?.query?.text || ''} />
  }

  // Group results by type
  const resultsByType = results.items.reduce((acc, result) => {
    if (!acc[result.type]) {
      acc[result.type] = []
    }
    acc[result.type].push(result)
    return acc
  }, {} as Record<ContentType, SearchResult[]>)

  // Create tab information
  const tabs: TabInfo[] = [
    {
      id: 'all',
      label: 'جميع النتائج',
      icon: <FileText className="w-4 h-4" />,
      count: results.items.length,
      description: 'جميع أنواع المحتوى'
    },
    {
      id: 'problem',
      label: 'المشاكل',
      icon: <FileText className="w-4 h-4" />,
      count: resultsByType.problem?.length || 0,
      description: 'المشاكل التقنية والتحديات'
    },
    {
      id: 'expert',
      label: 'الخبراء',
      icon: <User className="w-4 h-4" />,
      count: resultsByType.expert?.length || 0,
      description: 'الخبراء والمختصون'
    },
    {
      id: 'solution',
      label: 'الحلول',
      icon: <Lightbulb className="w-4 h-4" />,
      count: resultsByType.solution?.length || 0,
      description: 'الحلول والاقتراحات'
    },
    {
      id: 'webinar',
      label: 'الندوات',
      icon: <Video className="w-4 h-4" />,
      count: resultsByType.webinar?.length || 0,
      description: 'الندوات والمحاضرات'
    }
  ].filter(tab => tab.id === 'all' || tab.count > 0)

  const highlightText = (text: string, highlight: string): React.ReactNode => {
    if (!highlight.trim()) return text
    
    const parts = text.split(new RegExp(`(${highlight})`, 'gi'))
    return parts.map((part, index) => 
      part.toLowerCase() === highlight.toLowerCase() ? (
        <mark key={index} className="bg-yellow-200 px-1 rounded">
          {part}
        </mark>
      ) : (
        part
      )
    )
  }

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP', { locale: ar })
    } catch (error) {
      return dateString
    }
  }

  const getStatusIcon = (status: string, type: ContentType) => {
    if (type === 'problem') {
      switch (status) {
        case 'open': return <AlertCircle className="w-4 h-4 text-red-500" />
        case 'in_progress': return <Play className="w-4 h-4 text-yellow-500" />
        case 'resolved': return <CheckCircle className="w-4 h-4 text-green-500" />
        case 'closed': return <XCircle className="w-4 h-4 text-gray-500" />
        default: return <Pause className="w-4 h-4 text-gray-400" />
      }
    }
    
    if (type === 'expert') {
      switch (status) {
        case 'available': return <CheckCircle className="w-4 h-4 text-green-500" />
        case 'busy': return <Pause className="w-4 h-4 text-yellow-500" />
        case 'unavailable': return <XCircle className="w-4 h-4 text-red-500" />
        default: return <User className="w-4 h-4 text-gray-400" />
      }
    }
    
    return null
  }

  const getStatusLabel = (status: string, type: ContentType) => {
    if (type === 'problem') {
      switch (status) {
        case 'open': return 'مفتوحة'
        case 'in_progress': return 'قيد المعالجة'
        case 'resolved': return 'محلولة'
        case 'closed': return 'مغلقة'
        default: return status
      }
    }
    
    if (type === 'expert') {
      switch (status) {
        case 'available': return 'متاح'
        case 'busy': return 'مشغول'
        case 'unavailable': return 'غير متاح'
        default: return status
      }
    }
    
    return status
  }

  const ResultCard = ({ result, position }: { result: SearchResult; position: number }) => {
    const handleClick = async () => {
      // Track click analytics if analyticsId is available
      if (analyticsId) {
        try {
          await trackClick(analyticsId, result.id, result.type, position)
        } catch (error) {
          console.warn('Failed to track result click:', error)
        }
      }
      
      if (onResultClick) {
        onResultClick(result)
      }
    }

    return (
      <Card 
        className={`hover:shadow-md transition-all duration-200 cursor-pointer group ${isTouchDevice ? 'active:scale-[0.98]' : ''} focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2`}
        onClick={handleClick}
        role="article"
        aria-labelledby={`result-title-${result.id}`}
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault()
            handleClick()
          }
        }}
      >
        <CardHeader className={`${isMobile ? 'pb-2 p-4' : 'pb-3'}`}>
          <div className={`flex items-start justify-between gap-4 ${isMobile ? 'flex-col gap-2' : ''}`}>
            <div className="flex-1 min-w-0">
              {/* Type and status indicators */}
              <div className={`flex items-center gap-2 mb-2 ${isMobile ? 'flex-wrap' : ''}`}>
                {result.type === 'problem' && <FileText className="w-4 h-4 text-blue-600" />}
                {result.type === 'expert' && <User className="w-4 h-4 text-green-600" />}
                {result.type === 'solution' && <Lightbulb className="w-4 h-4 text-purple-600" />}
                {result.type === 'webinar' && <Video className="w-4 h-4 text-orange-600" />}
                
                <Badge variant="outline" className="text-xs">
                  {result.type === 'problem' ? 'مشكلة' : 
                   result.type === 'expert' ? 'خبير' : 
                   result.type === 'solution' ? 'حل' : 'ندوة'}
                </Badge>
                
                {result.metadata.status && (
                  <div className="flex items-center gap-1">
                    {getStatusIcon(result.metadata.status, result.type)}
                    <span className="text-xs text-gray-600">
                      {getStatusLabel(result.metadata.status, result.type)}
                    </span>
                  </div>
                )}
              </div>
              
              {/* Title */}
              <CardTitle className={`${isMobile ? 'text-base' : 'text-lg'} mb-2 group-hover:text-blue-600 transition-colors`}>
                <Link 
                  id={`result-title-${result.id}`}
                  to={result.type === 'problem' ? `/problems/${result.id}` : 
                      result.type === 'expert' ? `/experts/${result.id}` :
                      result.type === 'solution' ? `/solutions/${result.id}` :
                      `/webinars/${result.id}`}
                  className="block focus:outline-none focus:underline"
                  onClick={(e) => e.stopPropagation()}
                  aria-describedby={`result-desc-${result.id}`}
                >
                  {highlightText(result.title, highlightedText)}
                </Link>
              </CardTitle>
              
              {/* Description */}
              <CardDescription 
                id={`result-desc-${result.id}`}
                className={`${isMobile ? 'line-clamp-3' : 'line-clamp-2'} mb-3 ${isMobile ? 'text-xs' : 'text-sm'}`}
              >
                {highlightText(result.description, highlightedText)}
              </CardDescription>

              {/* Tags and categories */}
              <div className="flex flex-wrap gap-2 mb-3">
                {result.category && (
                  <Badge variant="secondary" className="text-xs">
                    {result.category}
                  </Badge>
                )}
                {result.sector && (
                  <Badge variant="outline" className="text-xs">
                    {result.sector}
                  </Badge>
                )}
                
                {/* Cross-sector indicator */}
                {result.metadata.cross_sectors && result.metadata.cross_sectors.length > 0 && (
                  <Badge variant="outline" className="text-xs bg-gradient-to-r from-blue-50 to-green-50 border-blue-200">
                    <span className="flex items-center gap-1">
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                      </svg>
                      متعدد القطاعات
                    </span>
                  </Badge>
                )}
                
                {result.tags.slice(0, isMobile ? 2 : 3).map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {highlightText(tag, highlightedText)}
                  </Badge>
                ))}
                {result.tags.length > (isMobile ? 2 : 3) && (
                  <Badge variant="outline" className="text-xs">
                    +{result.tags.length - (isMobile ? 2 : 3)} المزيد
                  </Badge>
                )}
              </div>

              {/* Metadata */}
              <div className={`flex items-center ${isMobile ? 'gap-2 flex-wrap' : 'gap-4'} ${isMobile ? 'text-xs' : 'text-sm'} text-gray-600`}>
                {/* Date */}
                <div className="flex items-center gap-1">
                  <Calendar className="w-3 h-3" />
                  <span className={isMobile ? 'truncate max-w-[100px]' : ''}>
                    {isMobile ? formatDate(result.metadata.created_at).split(' ').slice(0, 2).join(' ') : formatDate(result.metadata.created_at)}
                  </span>
                </div>
                
                {/* Type-specific metadata */}
                {result.type === 'problem' && (
                  <>
                    {result.metadata.urgency && (
                      <div className="flex items-center gap-1">
                        <Zap className="w-3 h-3" />
                        <span>
                          {result.metadata.urgency === 'low' ? 'منخفضة' :
                           result.metadata.urgency === 'medium' ? 'متوسطة' :
                           result.metadata.urgency === 'high' ? 'عالية' : 'حرجة'}
                        </span>
                      </div>
                    )}
                    {result.metadata.submitter_name && (
                      <div className="flex items-center gap-1">
                        <User className="w-3 h-3" />
                        <span>{result.metadata.submitter_name}</span>
                      </div>
                    )}
                  </>
                )}
                
                {result.type === 'expert' && (
                  <>
                    {result.metadata.rating && (
                      <div className="flex items-center gap-1">
                        <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                        <span>{result.metadata.rating.toFixed(1)}</span>
                      </div>
                    )}
                    {result.metadata.experience_years && (
                      <div className="flex items-center gap-1">
                        <Briefcase className="w-3 h-3" />
                        <span>{result.metadata.experience_years} سنة خبرة</span>
                      </div>
                    )}
                    {result.metadata.location && (
                      <div className="flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        <span>{result.metadata.location}</span>
                      </div>
                    )}
                  </>
                )}
                
                {result.type === 'solution' && (
                  <>
                    {result.metadata.rating && (
                      <div className="flex items-center gap-1">
                        <ThumbsUp className="w-3 h-3" />
                        <span>{result.metadata.rating.toFixed(1)}</span>
                      </div>
                    )}
                    {result.metadata.implementation_count && (
                      <div className="flex items-center gap-1">
                        <Award className="w-3 h-3" />
                        <span>{result.metadata.implementation_count} تطبيق</span>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
            
            {/* Relevance score */}
            {!isMobile && (
              <div className="text-right flex-shrink-0">
                <div className="text-xs text-gray-500 mb-1">
                  درجة التطابق
                </div>
                <div className="text-sm font-medium text-blue-600">
                  {Math.round(result.relevanceScore * 100)}%
                </div>
              </div>
            )}
            
            {/* Mobile relevance score */}
            {isMobile && (
              <div className="flex items-center gap-1 text-xs text-blue-600">
                <span>تطابق:</span>
                <span className="font-medium">{Math.round(result.relevanceScore * 100)}%</span>
              </div>
            )}
          </div>
        </CardHeader>
      </Card>
    )
  }

  const ResultsList = ({ results: resultsToShow }: { results: SearchResult[] }) => (
    <div className="space-y-4">
      {resultsToShow.map((result, index) => (
        <ResultCard 
          key={`${result.type}-${result.id}`} 
          result={result} 
          position={index + 1 + (currentPage - 1) * pageSize}
        />
      ))}
    </div>
  )

  // Calculate pagination
  const totalPages = Math.ceil(results.totalCount / pageSize)
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = Math.min(startIndex + pageSize, results.totalCount)

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Results header */}
      <div className={`flex ${isMobile ? 'flex-col gap-3' : 'flex-col sm:flex-row sm:items-center sm:justify-between gap-4'}`}>
        <div className={`flex items-center ${isMobile ? 'flex-wrap gap-2' : 'gap-4'}`}>
          <h2 className={`${isMobile ? 'text-lg' : 'text-xl'} font-semibold text-gray-900`}>
            نتائج البحث
          </h2>
          <Badge variant="secondary" className={`${isMobile ? 'text-xs' : 'text-sm'}`}>
            {results.totalCount.toLocaleString('ar-SA')} نتيجة
          </Badge>
          {!isMobile && (
            <span className="text-sm text-gray-600">
              في {results.executionTime}ms
            </span>
          )}
        </div>
        
        {/* Sort options */}
        {onSortChange && (
          <Select
            value={results.query.sortBy}
            onValueChange={(value) => onSortChange(value as SortOption)}
          >
            <SelectTrigger className={`${isMobile ? 'w-full' : 'w-48'} ${isTouchDevice ? 'min-h-[44px]' : ''}`}>
              <SelectValue placeholder="ترتيب حسب" />
            </SelectTrigger>
            <SelectContent>
              {SORT_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>

      {/* Results tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className={`grid ${isMobile ? 'grid-cols-2 gap-1' : 'grid-cols-2 lg:grid-cols-5'} w-full ${isMobile ? 'h-auto' : ''}`} role="tablist" aria-label="تصنيف نتائج البحث">
          {tabs.slice(0, isMobile ? 4 : tabs.length).map(tab => (
            <TabsTrigger 
              key={tab.id} 
              value={tab.id}
              className={`flex items-center gap-2 ${isMobile ? 'text-xs p-2 flex-col' : 'text-sm'} ${isTouchDevice ? 'min-h-[44px]' : ''}`}
              role="tab"
              aria-selected={activeTab === tab.id}
              aria-controls={`results-panel-${tab.id}`}
              aria-label={`${tab.label} (${tab.count} نتيجة)`}
            >
              {tab.icon}
              <span className={isMobile ? 'text-xs' : 'hidden sm:inline'}>{tab.label}</span>
              <Badge variant="secondary" className="text-xs" aria-hidden="true">
                {tab.count}
              </Badge>
            </TabsTrigger>
          ))}
          
          {/* Show overflow tabs on mobile */}
          {isMobile && tabs.length > 4 && (
            <TabsTrigger 
              value="more"
              className="flex items-center gap-1 text-xs p-2 flex-col min-h-[44px]"
              onClick={(e) => {
                e.preventDefault()
                // Could implement a dropdown or sheet for additional tabs
              }}
            >
              <span>المزيد</span>
              <Badge variant="secondary" className="text-xs">
                {tabs.slice(4).reduce((sum, tab) => sum + tab.count, 0)}
              </Badge>
            </TabsTrigger>
          )}
        </TabsList>

        {/* All results */}
        <TabsContent value="all" className="space-y-4" role="tabpanel" id="results-panel-all" aria-labelledby="tab-all">
          <div role="region" aria-label={`جميع النتائج (${results.items.length} نتيجة)`}>
            <ResultsList results={results.items} />
          </div>
        </TabsContent>

        {/* Results by type */}
        {(['problem', 'expert', 'solution', 'webinar'] as ContentType[]).map(type => (
          <TabsContent key={type} value={type} className="space-y-4" role="tabpanel" id={`results-panel-${type}`} aria-labelledby={`tab-${type}`}>
            {resultsByType[type] && resultsByType[type].length > 0 ? (
              <div role="region" aria-label={`نتائج ${type === 'problem' ? 'المشاكل' : type === 'expert' ? 'الخبراء' : type === 'solution' ? 'الحلول' : 'الندوات'} (${resultsByType[type].length} نتيجة)`}>
                <ResultsList results={resultsByType[type]} />
              </div>
            ) : (
              <Card role="status" aria-live="polite">
                <CardContent className="p-8 text-center">
                  {type === 'problem' && <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" aria-hidden="true" />}
                  {type === 'expert' && <User className="w-12 h-12 text-gray-400 mx-auto mb-4" aria-hidden="true" />}
                  {type === 'solution' && <Lightbulb className="w-12 h-12 text-gray-400 mx-auto mb-4" aria-hidden="true" />}
                  {type === 'webinar' && <Video className="w-12 h-12 text-gray-400 mx-auto mb-4" aria-hidden="true" />}
                  <p className="text-gray-600">
                    لا توجد {type === 'problem' ? 'مشاكل' : 
                            type === 'expert' ? 'خبراء' : 
                            type === 'solution' ? 'حلول' : 'ندوات'} تطابق البحث
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        ))}
      </Tabs>

      {/* Pagination */}
      {totalPages > 1 && onPageChange && (
        <nav className={`flex items-center ${isMobile ? 'flex-col gap-3' : 'justify-between'}`} role="navigation" aria-label="تنقل بين صفحات النتائج">
          <div className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600 ${isMobile ? 'order-2' : ''}`} role="status" aria-live="polite">
            عرض {startIndex + 1} - {endIndex} من {results.totalCount} نتيجة
          </div>
          
          <div className={`flex items-center gap-2 ${isMobile ? 'order-1' : ''}`} role="group" aria-label="أزرار التنقل">
            <Button
              variant="outline"
              size={isMobile ? "sm" : "sm"}
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage <= 1}
              className={isTouchDevice ? 'min-h-[44px]' : ''}
              aria-label="الصفحة السابقة"
            >
              <ChevronRight className="w-4 h-4" />
              {!isMobile && <span className="mr-1">السابق</span>}
            </Button>
            
            {/* Page numbers */}
            <div className="flex items-center gap-1" role="group" aria-label="أرقام الصفحات">
              {Array.from({ length: Math.min(isMobile ? 3 : 5, totalPages) }, (_, i) => {
                const pageNum = Math.max(1, Math.min(totalPages - (isMobile ? 2 : 4), currentPage - (isMobile ? 1 : 2))) + i
                return (
                  <Button
                    key={pageNum}
                    variant={pageNum === currentPage ? "default" : "outline"}
                    size="sm"
                    onClick={() => onPageChange(pageNum)}
                    className={`${isMobile ? 'w-10 h-10' : 'w-8 h-8'} p-0 ${isTouchDevice ? 'min-h-[44px] min-w-[44px]' : ''}`}
                    aria-label={`صفحة ${pageNum}`}
                    aria-current={pageNum === currentPage ? 'page' : undefined}
                  >
                    {pageNum}
                  </Button>
                )
              })}
            </div>
            
            <Button
              variant="outline"
              size={isMobile ? "sm" : "sm"}
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage >= totalPages}
              className={isTouchDevice ? 'min-h-[44px]' : ''}
              aria-label="الصفحة التالية"
            >
              {!isMobile && <span className="ml-1">التالي</span>}
              <ChevronLeft className="w-4 h-4" />
            </Button>
          </div>
        </nav>
      )}
    </div>
  )
}

// Loading skeleton component
function SearchResultsSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header skeleton */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Skeleton className="h-6 w-24" />
          <Skeleton className="h-5 w-16" />
        </div>
        <Skeleton className="h-10 w-48" />
      </div>
      
      {/* Tabs skeleton */}
      <div className="flex gap-2">
        {Array.from({ length: 5 }, (_, i) => (
          <Skeleton key={i} className="h-10 w-24" />
        ))}
      </div>
      
      {/* Results skeleton */}
      <div className="space-y-4">
        {Array.from({ length: 5 }, (_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-5 w-16" />
                </div>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <div className="flex gap-2">
                  <Skeleton className="h-5 w-16" />
                  <Skeleton className="h-5 w-20" />
                  <Skeleton className="h-5 w-14" />
                </div>
              </div>
            </CardHeader>
          </Card>
        ))}
      </div>
    </div>
  )
}

// Error display component
function SearchErrorDisplay({ error }: { error: string }) {
  return (
    <Card>
      <CardContent className="p-8 text-center">
        <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          خطأ في البحث
        </h3>
        <p className="text-gray-600 mb-4">
          {error}
        </p>
        <Button 
          variant="outline" 
          onClick={() => window.location.reload()}
        >
          إعادة المحاولة
        </Button>
      </CardContent>
    </Card>
  )
}

// Empty results component
function EmptySearchResults({ query }: { query: string }) {
  return (
    <Card>
      <CardContent className="p-12 text-center">
        <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          لا توجد نتائج
        </h3>
        <p className="text-gray-600 mb-4">
          {query ? `لم يتم العثور على نتائج لـ "${query}"` : 'لم يتم العثور على نتائج'}
        </p>
        <div className="space-y-2 text-sm text-gray-500">
          <p>جرب:</p>
          <ul className="list-disc list-inside space-y-1">
            <li>استخدام كلمات مختلفة أو أكثر عمومية</li>
            <li>التحقق من الإملاء</li>
            <li>تقليل عدد الفلاتر المطبقة</li>
            <li>البحث في فئات مختلفة</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}