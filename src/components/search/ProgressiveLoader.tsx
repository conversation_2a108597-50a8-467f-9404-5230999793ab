import { useState, useEffect } from 'react'
import { Skeleton } from '@/components/ui/skeleton'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { useDeviceType } from '@/hooks/use-mobile'

interface ProgressiveLoaderProps {
  isLoading: boolean
  hasResults: boolean
  resultCount?: number
  loadingStage?: 'initial' | 'searching' | 'filtering' | 'complete'
  className?: string
}

export function ProgressiveLoader({ 
  isLoading, 
  hasResults, 
  resultCount = 0,
  loadingStage = 'initial',
  className = '' 
}: ProgressiveLoaderProps) {
  const { isMobile } = useDeviceType()
  const [loadingText, setLoadingText] = useState('جاري البحث...')
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    if (!isLoading) return

    const stages = [
      { stage: 'initial', text: 'جاري البحث...', progress: 25 },
      { stage: 'searching', text: 'البحث في قاعدة البيانات...', progress: 50 },
      { stage: 'filtering', text: 'تطبيق الفلاتر...', progress: 75 },
      { stage: 'complete', text: 'تحضير النتائج...', progress: 100 }
    ]

    const currentStage = stages.find(s => s.stage === loadingStage)
    if (currentStage) {
      setLoadingText(currentStage.text)
      setProgress(currentStage.progress)
    }
  }, [isLoading, loadingStage])

  if (!isLoading && hasResults) {
    return null
  }

  if (!isLoading && !hasResults) {
    return (
      <div className={`text-center py-12 ${className}`} role="status" aria-live="polite">
        <div className="text-gray-500 mb-4">
          <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد نتائج</h3>
          <p className="text-gray-600">جرب تعديل مصطلحات البحث أو الفلاتر</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-4 ${className}`} role="status" aria-live="polite">
      {/* Progress indicator */}
      <div className="flex items-center gap-3 mb-6">
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600">{loadingText}</span>
            <span className="text-sm text-gray-500">{progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${progress}%` }}
              role="progressbar"
              aria-valuenow={progress}
              aria-valuemin={0}
              aria-valuemax={100}
              aria-label="تقدم البحث"
            />
          </div>
        </div>
      </div>

      {/* Loading skeletons */}
      <div className="space-y-4">
        {Array.from({ length: isMobile ? 3 : 5 }, (_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className={`${isMobile ? 'p-4' : 'pb-3'}`}>
              <div className="flex items-start justify-between gap-4">
                <div className="flex-1 space-y-3">
                  {/* Type indicators */}
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-4 w-4 rounded" />
                    <Skeleton className="h-5 w-16 rounded" />
                    <Skeleton className="h-4 w-12 rounded" />
                  </div>
                  
                  {/* Title */}
                  <Skeleton className={`h-6 ${isMobile ? 'w-full' : 'w-3/4'} rounded`} />
                  
                  {/* Description */}
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full rounded" />
                    <Skeleton className={`h-4 ${isMobile ? 'w-4/5' : 'w-2/3'} rounded`} />
                  </div>
                  
                  {/* Tags */}
                  <div className="flex gap-2">
                    <Skeleton className="h-5 w-16 rounded" />
                    <Skeleton className="h-5 w-20 rounded" />
                    <Skeleton className="h-5 w-14 rounded" />
                  </div>
                  
                  {/* Metadata */}
                  <div className="flex items-center gap-4">
                    <Skeleton className="h-4 w-20 rounded" />
                    <Skeleton className="h-4 w-16 rounded" />
                    <Skeleton className="h-4 w-24 rounded" />
                  </div>
                </div>
                
                {/* Relevance score */}
                {!isMobile && (
                  <div className="text-right">
                    <Skeleton className="h-4 w-16 rounded mb-1" />
                    <Skeleton className="h-5 w-12 rounded" />
                  </div>
                )}
              </div>
            </CardHeader>
          </Card>
        ))}
      </div>

      {/* Loading hints */}
      <div className="text-center py-4">
        <div className="text-xs text-gray-500 space-y-1">
          <p>💡 نصيحة: استخدم كلمات مفتاحية محددة للحصول على نتائج أفضل</p>
          {resultCount > 0 && (
            <p>تم العثور على {resultCount} نتيجة حتى الآن...</p>
          )}
        </div>
      </div>
    </div>
  )
}

// Enhanced skeleton for search results
export function SearchResultSkeleton({ isMobile = false }: { isMobile?: boolean }) {
  return (
    <Card className="animate-pulse">
      <CardContent className={`${isMobile ? 'p-4' : 'p-6'}`}>
        <div className="flex items-start justify-between gap-4">
          <div className="flex-1 space-y-3">
            {/* Type and status */}
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-4 rounded" />
              <Skeleton className="h-5 w-16 rounded" />
              <Skeleton className="h-4 w-4 rounded" />
              <Skeleton className="h-4 w-12 rounded" />
            </div>
            
            {/* Title */}
            <Skeleton className={`h-6 ${isMobile ? 'w-full' : 'w-3/4'} rounded`} />
            
            {/* Description */}
            <div className="space-y-2">
              <Skeleton className="h-4 w-full rounded" />
              <Skeleton className={`h-4 ${isMobile ? 'w-4/5' : 'w-2/3'} rounded`} />
              {!isMobile && <Skeleton className="h-4 w-1/2 rounded" />}
            </div>
            
            {/* Tags */}
            <div className="flex flex-wrap gap-2">
              <Skeleton className="h-5 w-16 rounded" />
              <Skeleton className="h-5 w-20 rounded" />
              <Skeleton className="h-5 w-14 rounded" />
              {!isMobile && <Skeleton className="h-5 w-18 rounded" />}
            </div>
            
            {/* Metadata */}
            <div className={`flex items-center ${isMobile ? 'gap-2 flex-wrap' : 'gap-4'}`}>
              <div className="flex items-center gap-1">
                <Skeleton className="h-3 w-3 rounded" />
                <Skeleton className="h-4 w-20 rounded" />
              </div>
              <div className="flex items-center gap-1">
                <Skeleton className="h-3 w-3 rounded" />
                <Skeleton className="h-4 w-16 rounded" />
              </div>
              <div className="flex items-center gap-1">
                <Skeleton className="h-3 w-3 rounded" />
                <Skeleton className="h-4 w-24 rounded" />
              </div>
            </div>
          </div>
          
          {/* Relevance score */}
          {!isMobile && (
            <div className="text-right">
              <Skeleton className="h-4 w-16 rounded mb-1" />
              <Skeleton className="h-5 w-12 rounded" />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}