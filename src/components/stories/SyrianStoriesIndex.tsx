/**
 * Syrian Stories Index - Essential Documentation Hub
 * 
 * Central hub for all Syrian identity component documentation.
 * Provides quick access to all component stories and examples.
 */

import React, { useState } from 'react';
import { SyrianButtonStories } from './SyrianButtonStories';
import { SyrianCardStories } from './SyrianCardStories';
import { PatternTest } from '../test/PatternTest';
import { SyrianVariantsTest } from '../test/SyrianVariantsTest';
import { SyrianColorsTest } from '../test/SyrianColorsTest';
import { RTLTest } from '../test/RTLTest';
import { AccessibilityTest } from '../test/AccessibilityTest';

type StorySection = 
  | 'overview'
  | 'buttons' 
  | 'cards' 
  | 'patterns' 
  | 'variants' 
  | 'colors' 
  | 'rtl' 
  | 'accessibility';

export function SyrianStoriesIndex() {
  const [activeSection, setActiveSection] = useState<StorySection>('overview');

  const sections = [
    { id: 'overview', label: 'Overview', icon: '📋' },
    { id: 'buttons', label: 'Button Stories', icon: '🔘' },
    { id: 'cards', label: 'Card Stories', icon: '🃏' },
    { id: 'patterns', label: 'Pattern System', icon: '🎨' },
    { id: 'variants', label: 'Component Variants', icon: '🎯' },
    { id: 'colors', label: 'Color System', icon: '🌈' },
    { id: 'rtl', label: 'RTL & Arabic', icon: '🔤' },
    { id: 'accessibility', label: 'Accessibility', icon: '♿' },
  ] as const;

  const renderContent = () => {
    switch (activeSection) {
      case 'buttons':
        return <SyrianButtonStories />;
      case 'cards':
        return <SyrianCardStories />;
      case 'patterns':
        return <PatternTest />;
      case 'variants':
        return <SyrianVariantsTest />;
      case 'colors':
        return <SyrianColorsTest />;
      case 'rtl':
        return <RTLTest />;
      case 'accessibility':
        return <AccessibilityTest />;
      default:
        return (
          <div className="p-8 space-y-8 max-w-4xl">
            <div className="text-center">
              <h1 className="text-4xl font-bold mb-4">Syrian Identity Design System</h1>
              <p className="text-xl text-muted-foreground mb-8">
                Essential documentation for Syrian cultural UI components
              </p>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="p-4 border rounded-lg text-center">
                <div className="text-2xl font-bold text-syrian-gold">4</div>
                <div className="text-sm text-muted-foreground">Cultural Patterns</div>
              </div>
              <div className="p-4 border rounded-lg text-center">
                <div className="text-2xl font-bold text-syrian-blue">6</div>
                <div className="text-sm text-muted-foreground">Syrian Colors</div>
              </div>
              <div className="p-4 border rounded-lg text-center">
                <div className="text-2xl font-bold text-syrian-green">100%</div>
                <div className="text-sm text-muted-foreground">Backward Compatible</div>
              </div>
              <div className="p-4 border rounded-lg text-center">
                <div className="text-2xl font-bold text-syrian-red">4.2KB</div>
                <div className="text-sm text-muted-foreground">Total Bundle Size</div>
              </div>
            </div>

            {/* System Overview */}
            <div className="space-y-6">
              <h2 className="text-2xl font-semibold">System Overview</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="p-6 border rounded-lg">
                  <h3 className="font-semibold mb-3 text-syrian-gold">🎨 Design Tokens</h3>
                  <ul className="space-y-2 text-sm">
                    <li>• 6 Syrian colors with 3 variants each</li>
                    <li>• WCAG AA compliant contrast ratios</li>
                    <li>• Light/dark theme support</li>
                    <li>• CSS custom properties + TypeScript constants</li>
                  </ul>
                </div>

                <div className="p-6 border rounded-lg">
                  <h3 className="font-semibold mb-3 text-syrian-blue">🔘 Enhanced Components</h3>
                  <ul className="space-y-2 text-sm">
                    <li>• Button: 4 new Syrian variants</li>
                    <li>• Card: Pattern backgrounds + styling</li>
                    <li>• 100% backward compatibility</li>
                    <li>• Opt-in Syrian styling</li>
                  </ul>
                </div>

                <div className="p-6 border rounded-lg">
                  <h3 className="font-semibold mb-3 text-syrian-green">🎯 Pattern System</h3>
                  <ul className="space-y-2 text-sm">
                    <li>• 4 authentic Syrian cultural patterns</li>
                    <li>• Performance optimized (≤2KB each)</li>
                    <li>• Responsive degradation matrix</li>
                    <li>• Hardware acceleration support</li>
                  </ul>
                </div>

                <div className="p-6 border rounded-lg">
                  <h3 className="font-semibold mb-3 text-syrian-purple">♿ Accessibility</h3>
                  <ul className="space-y-2 text-sm">
                    <li>• WCAG AA compliance maintained</li>
                    <li>• Screen reader compatibility</li>
                    <li>• Respects motion preferences</li>
                    <li>• High contrast mode support</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Cultural Patterns */}
            <div className="space-y-4">
              <h2 className="text-2xl font-semibold">Cultural Patterns</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="p-4 border rounded-lg text-center">
                  <div className="w-16 h-16 mx-auto mb-3 bg-syrian-gold/10 rounded pattern-damascus-star"></div>
                  <h3 className="font-medium">Damascus Star</h3>
                  <p className="text-xs text-muted-foreground">Islamic geometric pattern</p>
                </div>
                <div className="p-4 border rounded-lg text-center">
                  <div className="w-16 h-16 mx-auto mb-3 bg-syrian-stone/10 rounded pattern-palmyra-columns"></div>
                  <h3 className="font-medium">Palmyra Columns</h3>
                  <p className="text-xs text-muted-foreground">Ancient architecture</p>
                </div>
                <div className="p-4 border rounded-lg text-center">
                  <div className="w-16 h-16 mx-auto mb-3 bg-syrian-blue/10 rounded pattern-ebla-script"></div>
                  <h3 className="font-medium">Ebla Script</h3>
                  <p className="text-xs text-muted-foreground">Cuneiform writing</p>
                </div>
                <div className="p-4 border rounded-lg text-center">
                  <div className="w-16 h-16 mx-auto mb-3 bg-syrian-green/10 rounded pattern-geometric-weave"></div>
                  <h3 className="font-medium">Geometric Weave</h3>
                  <p className="text-xs text-muted-foreground">Textile patterns</p>
                </div>
              </div>
            </div>

            {/* Quick Start */}
            <div className="space-y-4">
              <h2 className="text-2xl font-semibold">Quick Start</h2>
              <div className="p-6 border rounded-lg bg-gray-50">
                <h3 className="font-medium mb-4">Basic Usage Examples</h3>
                <div className="space-y-4 text-sm">
                  <div>
                    <p className="font-medium mb-2">Syrian Button:</p>
                    <code className="block p-2 bg-white rounded border">
                      {`<Button variant="syrian">Syrian Button</Button>`}
                    </code>
                  </div>
                  <div>
                    <p className="font-medium mb-2">Syrian Card:</p>
                    <code className="block p-2 bg-white rounded border">
                      {`<Card syrianStyle={true} syrianPattern="eblaScript">
  <CardContent syrianStyle={true}>Content</CardContent>
</Card>`}
                    </code>
                  </div>
                  <div>
                    <p className="font-medium mb-2">Pattern Background:</p>
                    <code className="block p-2 bg-white rounded border">
                      {`<PatternBackground pattern="damascusStar" intensity="moderate">
  <div>Your content</div>
</PatternBackground>`}
                    </code>
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation Help */}
            <div className="p-6 border rounded-lg bg-blue-50">
              <h3 className="font-medium mb-3 text-blue-800">📚 Explore Documentation</h3>
              <p className="text-sm text-blue-700 mb-3">
                Use the navigation menu on the left to explore different aspects of the Syrian Identity Design System:
              </p>
              <ul className="space-y-1 text-sm text-blue-700">
                <li>• <strong>Button Stories</strong>: All Syrian button variants and usage examples</li>
                <li>• <strong>Card Stories</strong>: Syrian card styling with patterns and themes</li>
                <li>• <strong>Pattern System</strong>: Interactive pattern testing and configuration</li>
                <li>• <strong>Component Variants</strong>: Comprehensive component testing</li>
                <li>• <strong>Color System</strong>: Syrian color palette and accessibility testing</li>
                <li>• <strong>RTL & Arabic</strong>: Right-to-left layout and Arabic typography</li>
                <li>• <strong>Accessibility</strong>: WCAG compliance and screen reader testing</li>
              </ul>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="flex min-h-screen">
      {/* Navigation Sidebar */}
      <div className="w-64 bg-gray-50 border-r p-4">
        <div className="space-y-2">
          <h2 className="font-semibold text-lg mb-4">Syrian Design System</h2>
          {sections.map((section) => (
            <button
              key={section.id}
              onClick={() => setActiveSection(section.id as StorySection)}
              className={`w-full text-left p-3 rounded-lg transition-colors ${
                activeSection === section.id
                  ? 'bg-syrian-gold text-white'
                  : 'hover:bg-gray-100'
              }`}
            >
              <span className="mr-2">{section.icon}</span>
              {section.label}
            </button>
          ))}
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-1 overflow-auto">
        {renderContent()}
      </div>
    </div>
  );
}
