/**
 * Syrian Card Stories - Essential Documentation
 * 
 * Minimal documentation component showcasing Syrian card variants.
 * Can be viewed directly in the app without Storybook setup.
 */

import React from 'react';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardContent, 
  CardFooter 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export function SyrianCardStories() {
  return (
    <div className="p-8 space-y-8 max-w-6xl">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Syrian Card Variants</h1>
        <p className="text-muted-foreground">
          Essential documentation for Syrian card styling
        </p>
      </div>

      {/* Before/After Comparison */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Before/After Comparison</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          
          {/* Standard Card */}
          <Card>
            <CardHeader>
              <CardTitle>Standard Card</CardTitle>
              <CardDescription>
                This is a standard card without Syrian styling. All existing functionality is preserved.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm">
                Standard card content with normal styling and behavior.
              </p>
            </CardContent>
            <CardFooter>
              <Button variant="default">Standard Action</Button>
            </CardFooter>
          </Card>

          {/* Syrian Card */}
          <Card 
            syrianStyle={true}
            syrianPattern="eblaScript"
            syrianIntensity="subtle"
          >
            <CardHeader syrianStyle={true}>
              <CardTitle syrianStyle={true}>Syrian Card</CardTitle>
              <CardDescription syrianStyle={true}>
                This card demonstrates Syrian cultural styling with authentic patterns and colors.
              </CardDescription>
            </CardHeader>
            <CardContent syrianStyle={true}>
              <p className="text-sm">
                Syrian styled content with cultural patterns and enhanced visual appeal.
              </p>
            </CardContent>
            <CardFooter syrianStyle={true}>
              <Button variant="syrian">Syrian Action</Button>
            </CardFooter>
          </Card>
        </div>
      </div>

      {/* Pattern Variants */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Pattern Variants</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          
          <Card syrianStyle={true} syrianPattern="damascusStar">
            <CardHeader syrianStyle={true}>
              <CardTitle syrianStyle={true}>Damascus Star</CardTitle>
              <CardDescription syrianStyle={true}>
                Traditional Islamic star pattern
              </CardDescription>
            </CardHeader>
            <CardContent syrianStyle={true}>
              <p className="text-xs">Cultural significance: Divine guidance</p>
            </CardContent>
          </Card>

          <Card syrianStyle={true} syrianPattern="eblaScript">
            <CardHeader syrianStyle={true}>
              <CardTitle syrianStyle={true}>Ebla Script</CardTitle>
              <CardDescription syrianStyle={true}>
                Ancient cuneiform pattern
              </CardDescription>
            </CardHeader>
            <CardContent syrianStyle={true}>
              <p className="text-xs">Cultural significance: Early writing</p>
            </CardContent>
          </Card>

          <Card syrianStyle={true} syrianPattern="palmyraColumns">
            <CardHeader syrianStyle={true}>
              <CardTitle syrianStyle={true}>Palmyra Columns</CardTitle>
              <CardDescription syrianStyle={true}>
                Ancient architectural pattern
              </CardDescription>
            </CardHeader>
            <CardContent syrianStyle={true}>
              <p className="text-xs">Cultural significance: Heritage</p>
            </CardContent>
          </Card>

          <Card syrianStyle={true} syrianPattern="geometricWeave">
            <CardHeader syrianStyle={true}>
              <CardTitle syrianStyle={true}>Geometric Weave</CardTitle>
              <CardDescription syrianStyle={true}>
                Traditional textile pattern
              </CardDescription>
            </CardHeader>
            <CardContent syrianStyle={true}>
              <p className="text-xs">Cultural significance: Craftsmanship</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Intensity Levels */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Intensity Levels</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          
          <Card syrianStyle={true} syrianPattern="damascusStar" syrianIntensity="subtle">
            <CardHeader syrianStyle={true}>
              <CardTitle syrianStyle={true}>Subtle</CardTitle>
              <CardDescription syrianStyle={true}>
                Very light pattern, minimal visual impact
              </CardDescription>
            </CardHeader>
            <CardContent syrianStyle={true}>
              <p className="text-sm">Opacity: 0.02</p>
            </CardContent>
          </Card>

          <Card syrianStyle={true} syrianPattern="damascusStar" syrianIntensity="moderate">
            <CardHeader syrianStyle={true}>
              <CardTitle syrianStyle={true}>Moderate</CardTitle>
              <CardDescription syrianStyle={true}>
                Balanced visibility (default)
              </CardDescription>
            </CardHeader>
            <CardContent syrianStyle={true}>
              <p className="text-sm">Opacity: 0.04</p>
            </CardContent>
          </Card>

          <Card syrianStyle={true} syrianPattern="damascusStar" syrianIntensity="rich">
            <CardHeader syrianStyle={true}>
              <CardTitle syrianStyle={true}>Rich</CardTitle>
              <CardDescription syrianStyle={true}>
                More prominent, use sparingly
              </CardDescription>
            </CardHeader>
            <CardContent syrianStyle={true}>
              <p className="text-sm">Opacity: 0.06</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Interactive Features */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Interactive Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          
          <Card syrianStyle={true} syrianPattern="damascusStar">
            <CardHeader syrianStyle={true}>
              <CardTitle syrianStyle={true}>Static Card</CardTitle>
              <CardDescription syrianStyle={true}>
                Standard card without hover effects
              </CardDescription>
            </CardHeader>
            <CardContent syrianStyle={true}>
              <p className="text-sm">No interactive effects</p>
            </CardContent>
          </Card>

          <Card 
            syrianStyle={true} 
            syrianPattern="damascusStar" 
            syrianInteractive={true}
            className="cursor-pointer"
          >
            <CardHeader syrianStyle={true}>
              <CardTitle syrianStyle={true}>Interactive Card</CardTitle>
              <CardDescription syrianStyle={true}>
                Hover over this card to see effects
              </CardDescription>
            </CardHeader>
            <CardContent syrianStyle={true}>
              <p className="text-sm">Hover for lift effect</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Animation */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Animation Variants</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          
          <Card syrianStyle={true} syrianPattern="damascusStar">
            <CardHeader syrianStyle={true}>
              <CardTitle syrianStyle={true}>Static Pattern</CardTitle>
              <CardDescription syrianStyle={true}>
                Pattern without animation
              </CardDescription>
            </CardHeader>
            <CardContent syrianStyle={true}>
              <p className="text-sm">No pattern animation</p>
            </CardContent>
          </Card>

          <Card 
            syrianStyle={true} 
            syrianPattern="damascusStar" 
            syrianAnimated={true}
          >
            <CardHeader syrianStyle={true}>
              <CardTitle syrianStyle={true}>Animated Pattern</CardTitle>
              <CardDescription syrianStyle={true}>
                Pattern with subtle pulse animation
              </CardDescription>
            </CardHeader>
            <CardContent syrianStyle={true}>
              <p className="text-sm">Subtle pattern animation</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Theme Variants */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Theme Variants</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          
          <div className="p-4 bg-white rounded-lg border">
            <h3 className="font-medium mb-4">Light Theme</h3>
            <Card syrianStyle={true} syrianPattern="eblaScript">
              <CardHeader syrianStyle={true}>
                <CardTitle syrianStyle={true}>Light Theme Card</CardTitle>
                <CardDescription syrianStyle={true}>
                  Syrian styling in light mode
                </CardDescription>
              </CardHeader>
              <CardContent syrianStyle={true}>
                <Button variant="syrian" size="sm">Light Action</Button>
              </CardContent>
            </Card>
          </div>

          <div className="p-4 bg-gray-900 rounded-lg border">
            <h3 className="font-medium mb-4 text-white">Dark Theme</h3>
            <Card syrianStyle={true} syrianPattern="eblaScript" className="bg-gray-800 border-gray-700">
              <CardHeader syrianStyle={true}>
                <CardTitle syrianStyle={true} className="text-white">Dark Theme Card</CardTitle>
                <CardDescription syrianStyle={true} className="text-gray-300">
                  Syrian styling in dark mode
                </CardDescription>
              </CardHeader>
              <CardContent syrianStyle={true}>
                <Button variant="syrian" size="sm">Dark Action</Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Usage Examples */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Usage Examples</h2>
        <div className="p-6 border rounded-lg bg-gray-50">
          <h3 className="font-medium mb-4">Code Examples</h3>
          <div className="space-y-4 text-sm">
            <div>
              <p className="font-medium mb-2">Basic Syrian Card:</p>
              <code className="block p-2 bg-white rounded border text-xs">
                {`<Card syrianStyle={true}>
  <CardHeader syrianStyle={true}>
    <CardTitle syrianStyle={true}>Title</CardTitle>
  </CardHeader>
</Card>`}
              </code>
            </div>
            <div>
              <p className="font-medium mb-2">Card with Pattern:</p>
              <code className="block p-2 bg-white rounded border text-xs">
                {`<Card 
  syrianStyle={true} 
  syrianPattern="eblaScript"
  syrianIntensity="subtle"
>
  <CardContent syrianStyle={true}>
    Content
  </CardContent>
</Card>`}
              </code>
            </div>
            <div>
              <p className="font-medium mb-2">Interactive Card:</p>
              <code className="block p-2 bg-white rounded border text-xs">
                {`<Card 
  syrianStyle={true}
  syrianInteractive={true}
  syrianAnimated={true}
>
  <CardContent syrianStyle={true}>
    Interactive content
  </CardContent>
</Card>`}
              </code>
            </div>
          </div>
        </div>
      </div>

      {/* Best Practices */}
      <div className="space-y-4">
        <h2 className="text-2xl font-semibold">Best Practices</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="p-6 border rounded-lg bg-green-50">
            <h3 className="font-medium mb-3 text-green-800">✅ Do's</h3>
            <ul className="space-y-1 text-sm text-green-700">
              <li>• Use subtle intensity for content cards</li>
              <li>• Enable interactive effects for clickable cards</li>
              <li>• Test on mobile devices</li>
              <li>• Use appropriate patterns for context</li>
              <li>• Maintain consistent Syrian styling across card parts</li>
            </ul>
          </div>
          <div className="p-6 border rounded-lg bg-red-50">
            <h3 className="font-medium mb-3 text-red-800">❌ Don'ts</h3>
            <ul className="space-y-1 text-sm text-red-700">
              <li>• Don't use rich intensity on mobile</li>
              <li>• Don't animate complex patterns on low-end devices</li>
              <li>• Don't rely on patterns for essential information</li>
              <li>• Don't stack multiple high-complexity patterns</li>
              <li>• Don't override responsive behavior without testing</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
