import React, { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'

export function SupabaseTest() {
  const [connectionStatus, setConnectionStatus] = useState<'testing' | 'connected' | 'error'>('testing')
  const [error, setError] = useState<string | null>(null)
  const [testEmail, setTestEmail] = useState('')
  const [testPassword, setTestPassword] = useState('')
  const [authStatus, setAuthStatus] = useState<string>('')

  useEffect(() => {
    testConnection()
  }, [])

  const testConnection = async () => {
    try {
      // Test basic connection
      const { data, error } = await supabase.from('users').select('count').limit(1)
      
      if (error) {
        setConnectionStatus('error')
        setError(error.message)
      } else {
        setConnectionStatus('connected')
        setError(null)
      }
    } catch (err) {
      setConnectionStatus('error')
      setError(err instanceof Error ? err.message : 'Unknown error')
    }
  }

  const testAuth = async () => {
    if (!testEmail || !testPassword) {
      setAuthStatus('Please enter email and password')
      return
    }

    try {
      setAuthStatus('Testing authentication...')
      
      // Try to sign up (will fail if user exists, which is fine)
      const { error: signUpError } = await supabase.auth.signUp({
        email: testEmail,
        password: testPassword,
        options: {
          data: {
            name: 'Test User',
            location: 'Damascus, Syria'
          }
        }
      })

      if (signUpError && !signUpError.message.includes('already registered')) {
        setAuthStatus(`Sign up error: ${signUpError.message}`)
        return
      }

      // Try to sign in
      const { data, error: signInError } = await supabase.auth.signInWithPassword({
        email: testEmail,
        password: testPassword
      })

      if (signInError) {
        setAuthStatus(`Sign in error: ${signInError.message}`)
        return
      }

      setAuthStatus(`Authentication successful! User ID: ${data.user?.id}`)

      // Sign out
      await supabase.auth.signOut()
    } catch (err) {
      setAuthStatus(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`)
    }
  }

  const testStorage = async () => {
    try {
      setAuthStatus('Testing storage...')
      
      // Create a test file
      const testFile = new File(['Hello, Supabase!'], 'test.txt', { type: 'text/plain' })
      
      // Try to upload
      const { data, error } = await supabase.storage
        .from('attachments')
        .upload(`test/${Date.now()}-test.txt`, testFile)

      if (error) {
        setAuthStatus(`Storage error: ${error.message}`)
        return
      }

      setAuthStatus(`Storage test successful! File uploaded: ${data.path}`)

      // Clean up - delete the test file
      await supabase.storage.from('attachments').remove([data.path])
    } catch (err) {
      setAuthStatus(`Storage error: ${err instanceof Error ? err.message : 'Unknown error'}`)
    }
  }

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Supabase Connection Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <span>Connection Status:</span>
              <span className={`font-semibold ${
                connectionStatus === 'connected' ? 'text-green-600' : 
                connectionStatus === 'error' ? 'text-red-600' : 'text-yellow-600'
              }`}>
                {connectionStatus === 'testing' && 'Testing...'}
                {connectionStatus === 'connected' && '✅ Connected'}
                {connectionStatus === 'error' && '❌ Error'}
              </span>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <Button onClick={testConnection} variant="outline">
              Test Connection Again
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Authentication Test</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Input
              type="email"
              placeholder="Test email"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
            />
            <Input
              type="password"
              placeholder="Test password"
              value={testPassword}
              onChange={(e) => setTestPassword(e.target.value)}
            />
            <Button onClick={testAuth} disabled={!testEmail || !testPassword}>
              Test Authentication
            </Button>
            <Button onClick={testStorage} variant="outline">
              Test Storage
            </Button>
            {authStatus && (
              <Alert>
                <AlertDescription>{authStatus}</AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Setup Checklist</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <span className={connectionStatus === 'connected' ? 'text-green-600' : 'text-gray-400'}>
                {connectionStatus === 'connected' ? '✅' : '⏳'}
              </span>
              <span>Database connection</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-gray-400">⏳</span>
              <span>Authentication setup</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-gray-400">⏳</span>
              <span>Storage buckets</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-gray-400">⏳</span>
              <span>Row Level Security policies</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Alert>
        <AlertDescription>
          <strong>Next Steps:</strong>
          <ol className="list-decimal list-inside mt-2 space-y-1">
            <li>Follow the setup guide in <code>scripts/setup-supabase.md</code></li>
            <li>Create your Supabase project and get the credentials</li>
            <li>Add the credentials to your <code>.env</code> file</li>
            <li>Run the database schema from <code>supabase/schema.sql</code></li>
            <li>Create the storage buckets and policies</li>
          </ol>
        </AlertDescription>
      </Alert>
    </div>
  )
}