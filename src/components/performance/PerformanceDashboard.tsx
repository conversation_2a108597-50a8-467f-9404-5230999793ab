import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { performanceMonitor } from '@/utils/performanceMonitor';
import { Activity, Clock, Zap, AlertTriangle, CheckCircle, RefreshCw } from 'lucide-react';

interface PerformanceDashboardProps {
  className?: string;
}

export function PerformanceDashboard({ className = '' }: PerformanceDashboardProps) {
  const [summary, setSummary] = useState(performanceMonitor.getPerformanceSummary());
  const [isRefreshing, setIsRefreshing] = useState(false);

  const refreshData = async () => {
    setIsRefreshing(true);
    // Simulate refresh delay
    await new Promise(resolve => setTimeout(resolve, 500));
    setSummary(performanceMonitor.getPerformanceSummary());
    setIsRefreshing(false);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      setSummary(performanceMonitor.getPerformanceSummary());
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  const webVitalsStatus = useMemo(() => {
    const { webVitals } = summary;
    const status = {
      FCP: webVitals.FCP ? (webVitals.FCP < 1800 ? 'good' : webVitals.FCP < 3000 ? 'needs-improvement' : 'poor') : 'unknown',
      LCP: webVitals.LCP ? (webVitals.LCP < 2500 ? 'good' : webVitals.LCP < 4000 ? 'needs-improvement' : 'poor') : 'unknown',
      FID: webVitals.FID ? (webVitals.FID < 100 ? 'good' : webVitals.FID < 300 ? 'needs-improvement' : 'poor') : 'unknown',
      CLS: webVitals.CLS ? (webVitals.CLS < 0.1 ? 'good' : webVitals.CLS < 0.25 ? 'needs-improvement' : 'poor') : 'unknown'
    };
    return status;
  }, [summary.webVitals]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'bg-green-100 text-green-800';
      case 'needs-improvement': return 'bg-yellow-100 text-yellow-800';
      case 'poor': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good': return <CheckCircle className="w-4 h-4" />;
      case 'needs-improvement': return <AlertTriangle className="w-4 h-4" />;
      case 'poor': return <AlertTriangle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Performance Dashboard</h2>
          <p className="text-gray-600">Monitor application performance and optimization metrics</p>
        </div>
        <Button onClick={refreshData} disabled={isRefreshing}>
          <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="web-vitals">Web Vitals</TabsTrigger>
          <TabsTrigger value="components">Components</TabsTrigger>
          <TabsTrigger value="bundle">Bundle</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Metrics</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{summary.totalMetrics}</div>
                <p className="text-xs text-muted-foreground">Performance data points</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Components Tracked</CardTitle>
                <Zap className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{summary.componentMetrics.length}</div>
                <p className="text-xs text-muted-foreground">React components monitored</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Slow Components</CardTitle>
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{summary.slowComponents.length}</div>
                <p className="text-xs text-muted-foreground">Components needing optimization</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Bundle Size</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {summary.bundleMetrics ? `${(summary.bundleMetrics.gzippedSize / 1024).toFixed(1)}KB` : 'N/A'}
                </div>
                <p className="text-xs text-muted-foreground">Gzipped bundle size</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="web-vitals" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(summary.webVitals).map(([metric, value]) => (
              <Card key={metric}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    {metric}
                    <div className="flex items-center gap-2">
                      {getStatusIcon(webVitalsStatus[metric as keyof typeof webVitalsStatus])}
                      <Badge 
                        variant={webVitalsStatus[metric as keyof typeof webVitalsStatus] === 'good' ? 'default' : 
                                webVitalsStatus[metric as keyof typeof webVitalsStatus] === 'needs-improvement' ? 'secondary' : 'destructive'}
                        className={getStatusColor(webVitalsStatus[metric as keyof typeof webVitalsStatus])}
                      >
                        {webVitalsStatus[metric as keyof typeof webVitalsStatus]}
                      </Badge>
                    </div>
                  </CardTitle>
                  <CardDescription>
                    {metric === 'FCP' && 'First Contentful Paint - Time until first content appears'}
                    {metric === 'LCP' && 'Largest Contentful Paint - Time until largest content appears'}
                    {metric === 'FID' && 'First Input Delay - Time until page becomes interactive'}
                    {metric === 'CLS' && 'Cumulative Layout Shift - Visual stability measure'}
                    {metric === 'TTFB' && 'Time to First Byte - Server response time'}
                    {metric === 'TTI' && 'Time to Interactive - Full interactivity time'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold">
                    {value ? (metric === 'CLS' ? value.toFixed(3) : `${value.toFixed(0)}ms`) : 'N/A'}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="components" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Component Performance</CardTitle>
              <CardDescription>Render times and optimization opportunities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {summary.componentMetrics.length === 0 ? (
                  <p className="text-gray-500">No component metrics available</p>
                ) : (
                  summary.componentMetrics
                    .sort((a, b) => b.averageRenderTime - a.averageRenderTime)
                    .slice(0, 10)
                    .map((component) => (
                      <div key={component.componentName} className="flex items-center justify-between p-3 border rounded">
                        <div>
                          <h4 className="font-medium">{component.componentName}</h4>
                          <p className="text-sm text-gray-600">
                            {component.renderCount} renders • {component.slowRenders} slow
                          </p>
                        </div>
                        <div className="text-right">
                          <div className="font-bold">
                            {component.averageRenderTime.toFixed(2)}ms
                          </div>
                          <div className="text-sm text-gray-600">
                            avg render time
                          </div>
                        </div>
                        {component.averageRenderTime > 16 && (
                          <Badge variant="destructive">Slow</Badge>
                        )}
                      </div>
                    ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bundle" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bundle Analysis</CardTitle>
              <CardDescription>Bundle size and loading performance</CardDescription>
            </CardHeader>
            <CardContent>
              {summary.bundleMetrics ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium">Total Size</h4>
                      <p className="text-2xl font-bold">{(summary.bundleMetrics.totalSize / 1024).toFixed(1)}KB</p>
                    </div>
                    <div>
                      <h4 className="font-medium">Gzipped Size</h4>
                      <p className="text-2xl font-bold">{(summary.bundleMetrics.gzippedSize / 1024).toFixed(1)}KB</p>
                    </div>
                    <div>
                      <h4 className="font-medium">Cache Hit Rate</h4>
                      <p className="text-2xl font-bold">{(summary.bundleMetrics.cacheHitRate * 100).toFixed(1)}%</p>
                    </div>
                    <div>
                      <h4 className="font-medium">Chunks</h4>
                      <p className="text-2xl font-bold">{Object.keys(summary.bundleMetrics.chunkSizes).length}</p>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">Chunk Sizes</h4>
                    <div className="space-y-2">
                      {Object.entries(summary.bundleMetrics.chunkSizes)
                        .sort(([,a], [,b]) => b - a)
                        .slice(0, 5)
                        .map(([chunk, size]) => (
                          <div key={chunk} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <span className="font-mono text-sm">{chunk}</span>
                            <span className="font-bold">{(size / 1024).toFixed(1)}KB</span>
                          </div>
                        ))}
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-gray-500">No bundle metrics available</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
