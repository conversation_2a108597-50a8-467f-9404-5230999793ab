/**
 * Matching Algorithm Configuration Component
 * Allows admins to configure the expert matching algorithm weights
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Switch } from '../ui/switch';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { Slider } from '../ui/slider';
import { 
  Settings, 
  Save, 
  RotateCcw, 
  AlertCircle, 
  CheckCircle,
  TrendingUp,
  Users,
  Target,
  Clock,
  Star
} from 'lucide-react';
import { matchingAlgorithmAPI } from '../../lib/expertMatching';
import type { MatchingAlgorithmConfig, MatchingWeights } from '../../types/user';

interface MatchingAlgorithmConfigProps {
  onConfigUpdate?: (config: MatchingAlgorithmConfig) => void;
}

const DEFAULT_WEIGHTS: MatchingWeights = {
  category_match: 0.30,
  sector_match: 0.25,
  expertise_match: 0.20,
  availability: 0.15,
  response_time: 0.10
};

const WEIGHT_DESCRIPTIONS = {
  category_match: 'How much weight to give to exact problem category matches',
  sector_match: 'How much weight to give to exact sector matches',
  expertise_match: 'How much weight to give to expert\'s expertise areas alignment',
  availability: 'How much weight to give to expert\'s current availability status',
  response_time: 'How much weight to give to expert\'s historical response time'
};

const WEIGHT_ICONS = {
  category_match: Target,
  sector_match: TrendingUp,
  expertise_match: Star,
  availability: Users,
  response_time: Clock
};

export const MatchingAlgorithmConfig: React.FC<MatchingAlgorithmConfigProps> = ({
  onConfigUpdate
}) => {
  const [configs, setConfigs] = useState<MatchingAlgorithmConfig[]>([]);
  const [activeConfig, setActiveConfig] = useState<MatchingAlgorithmConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  
  // Form state
  const [formData, setFormData] = useState<{
    name: string;
    description: string;
    weights: MatchingWeights;
    is_active: boolean;
  }>({
    name: '',
    description: '',
    weights: DEFAULT_WEIGHTS,
    is_active: false
  });

  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [allConfigs, activeConfigData] = await Promise.all([
        matchingAlgorithmAPI.getAllConfigs(),
        matchingAlgorithmAPI.getActiveConfig()
      ]);
      
      setConfigs(allConfigs);
      setActiveConfig(activeConfigData);
      
      if (activeConfigData) {
        setFormData({
          name: activeConfigData.name,
          description: activeConfigData.description || '',
          weights: activeConfigData.weights,
          is_active: activeConfigData.is_active
        });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load algorithm configs');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);
      
      // Validate weights sum to 1.0
      const totalWeight = Object.values(formData.weights).reduce((sum, weight) => sum + weight, 0);
      if (Math.abs(totalWeight - 1.0) > 0.01) {
        throw new Error('Weights must sum to 1.0 (100%)');
      }
      
      if (activeConfig) {
        const updatedConfig = await matchingAlgorithmAPI.updateConfig(activeConfig.id, formData);
        setActiveConfig(updatedConfig);
        onConfigUpdate?.(updatedConfig);
        setSuccess('Algorithm configuration updated successfully');
      }
      
      // Reload configs to get latest data
      await loadConfigs();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save configuration');
    } finally {
      setSaving(false);
    }
  };

  const handleWeightChange = (key: keyof MatchingWeights, value: number) => {
    setFormData(prev => ({
      ...prev,
      weights: {
        ...prev.weights,
        [key]: value / 100 // Convert percentage to decimal
      }
    }));
  };

  const resetToDefaults = () => {
    setFormData(prev => ({
      ...prev,
      weights: DEFAULT_WEIGHTS
    }));
  };

  const getTotalWeight = () => {
    return Object.values(formData.weights).reduce((sum, weight) => sum + weight, 0);
  };

  const isWeightValid = () => {
    const total = getTotalWeight();
    return Math.abs(total - 1.0) <= 0.01;
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Matching Algorithm Configuration
          </CardTitle>
          <CardDescription>
            Configure the weights for the expert-problem matching algorithm
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-500" />
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            </div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 rounded-md p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <p className="text-green-600 text-sm">{success}</p>
              </div>
            </div>
          )}

          {/* Configuration Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="config-name">Configuration Name</Label>
              <Input
                id="config-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., Default Algorithm"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="is-active">Active Configuration</Label>
                <p className="text-sm text-gray-500">
                  This configuration is currently being used
                </p>
              </div>
              <Switch
                id="is-active"
                checked={formData.is_active}
                onCheckedChange={(checked) => 
                  setFormData(prev => ({ ...prev, is_active: checked }))
                }
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="config-description">Description</Label>
            <Textarea
              id="config-description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Describe this algorithm configuration..."
              rows={3}
            />
          </div>

          <Separator />

          {/* Weight Configuration */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Algorithm Weights</h3>
              <div className="flex items-center gap-2">
                <Badge 
                  variant={isWeightValid() ? 'default' : 'destructive'}
                  className="text-xs"
                >
                  Total: {(getTotalWeight() * 100).toFixed(1)}%
                </Badge>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={resetToDefaults}
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Reset to Defaults
                </Button>
              </div>
            </div>

            <div className="space-y-6">
              {Object.entries(formData.weights).map(([key, value]) => {
                const weightKey = key as keyof MatchingWeights;
                const Icon = WEIGHT_ICONS[weightKey];
                const percentage = Math.round(value * 100);
                
                return (
                  <div key={key} className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4 text-gray-500" />
                        <Label className="font-medium capitalize">
                          {key.replace('_', ' ')}
                        </Label>
                      </div>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          value={percentage}
                          onChange={(e) => handleWeightChange(weightKey, parseInt(e.target.value) || 0)}
                          className="w-20 text-center"
                        />
                        <span className="text-sm text-gray-500">%</span>
                      </div>
                    </div>
                    
                    <Slider
                      value={[percentage]}
                      onValueChange={([newValue]) => handleWeightChange(weightKey, newValue)}
                      max={100}
                      step={1}
                      className="w-full"
                    />
                    
                    <p className="text-sm text-gray-500">
                      {WEIGHT_DESCRIPTIONS[weightKey]}
                    </p>
                  </div>
                );
              })}
            </div>

            {!isWeightValid() && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-yellow-500" />
                  <p className="text-yellow-700 text-sm">
                    Warning: Weights must sum to exactly 100% for the algorithm to work correctly.
                    Current total: {(getTotalWeight() * 100).toFixed(1)}%
                  </p>
                </div>
              </div>
            )}
          </div>

          <Separator />

          {/* Save Button */}
          <div className="flex justify-end">
            <Button 
              onClick={handleSave} 
              disabled={saving || !isWeightValid()}
            >
              <Save className="h-4 w-4 mr-2" />
              {saving ? 'Saving...' : 'Save Configuration'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Algorithm Performance Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Algorithm Preview</CardTitle>
          <CardDescription>
            How the current weights will affect matching decisions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(formData.weights).map(([key, value]) => {
              const weightKey = key as keyof MatchingWeights;
              const Icon = WEIGHT_ICONS[weightKey];
              const percentage = Math.round(value * 100);
              
              return (
                <div key={key} className="p-4 border rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Icon className="h-5 w-5 text-blue-500" />
                    <h4 className="font-medium capitalize">
                      {key.replace('_', ' ')}
                    </h4>
                  </div>
                  <div className="text-2xl font-bold text-blue-600 mb-1">
                    {percentage}%
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};