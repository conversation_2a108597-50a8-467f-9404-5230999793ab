import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { HardDrive, Image, FileText, Presentation, CheckCircle } from 'lucide-react'
import { formatBytes } from '@/lib/utils'

const STORAGE_BUCKETS = [
  {
    id: 'attachments',
    name: 'المرفقات',
    description: 'ملفات المشاكل والحلول',
    icon: FileText,
    sizeLimit: 10485760, // 10MB
    allowedTypes: ['PDF', 'Word', 'PowerPoint', 'Excel', 'Images'],
    color: 'blue'
  },
  {
    id: 'avatars',
    name: 'الصور الشخصية',
    description: 'صور المستخدمين والخبراء',
    icon: Image,
    sizeLimit: 2097152, // 2MB
    allowedTypes: ['JPEG', 'PNG', 'GIF', 'WebP'],
    color: 'green'
  },
  {
    id: 'presentations',
    name: 'العروض التقديمية',
    description: 'ملفات الندوات والعروض',
    icon: Presentation,
    sizeLimit: 52428800, // 50MB
    allowedTypes: ['PDF', 'PowerPoint', 'Word', 'Excel', 'Images'],
    color: 'purple'
  }
]

export default function StorageSummary() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">إعدادات التخزين</h3>
        <p className="text-sm text-muted-foreground">
          تم إعداد ثلاثة أنواع من التخزين لملفات المنصة
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {STORAGE_BUCKETS.map((bucket) => {
          const IconComponent = bucket.icon
          
          return (
            <Card key={bucket.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`p-2 rounded-lg bg-${bucket.color}-100`}>
                      <IconComponent className={`h-4 w-4 text-${bucket.color}-600`} />
                    </div>
                    <div>
                      <CardTitle className="text-sm">{bucket.name}</CardTitle>
                      <CardDescription className="text-xs">
                        {bucket.description}
                      </CardDescription>
                    </div>
                  </div>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                </div>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">الحد الأقصى:</span>
                    <Badge variant="outline">
                      {formatBytes(bucket.sizeLimit)}
                    </Badge>
                  </div>
                  
                  <div className="space-y-1">
                    <span className="text-xs text-muted-foreground">الأنواع المدعومة:</span>
                    <div className="flex flex-wrap gap-1">
                      {bucket.allowedTypes.map((type) => (
                        <Badge key={type} variant="secondary" className="text-xs">
                          {type}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center gap-2 text-xs text-green-600">
                    <CheckCircle className="h-3 w-3" />
                    <span>تم الإعداد بنجاح</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-sm flex items-center gap-2">
            <HardDrive className="h-4 w-4" />
            ميزات التخزين
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <h4 className="font-medium">الأمان والخصوصية</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  تشفير الملفات أثناء النقل والتخزين
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  التحكم في الوصول على مستوى المستخدم
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  نسخ احتياطية تلقائية
                </li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">الأداء والتوفر</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  شبكة توصيل المحتوى العالمية
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  ضغط الملفات التلقائي
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  توفر 99.9% من الوقت
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}