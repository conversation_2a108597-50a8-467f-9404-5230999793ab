import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { problemOperations, expertOperations, userOperations, solutionOperations } from '@/lib/database'
import { 
  Users, 
  FileText, 
  MessageSquare, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  Clock,
  UserCheck,
  Loader2
} from 'lucide-react'

interface AdminStatsData {
  totalUsers: number
  totalExperts: number
  totalProblems: number
  totalSolutions: number
  pendingProblems: number
  resolvedProblems: number
  activeExperts: number
  recentActivity: number
}

export function AdminStats() {
  const { toast } = useToast()
  const [stats, setStats] = useState<AdminStatsData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadStats()
  }, [])

  const loadStats = async () => {
    try {
      setLoading(true)
      
      // Load all data in parallel
      const [usersResult, expertsResult, problemsResult, solutionsResult] = await Promise.all([
        userOperations.getAllUsers(),
        expertOperations.getAllExperts(),
        problemOperations.getAllProblems(),
        // We'll need to add this to solutionOperations
        Promise.resolve({ data: [], error: null }) // Placeholder for solutions
      ])

      if (usersResult.error || expertsResult.error || problemsResult.error) {
        throw new Error('Failed to load admin stats')
      }

      const users = usersResult.data || []
      const experts = expertsResult.data || []
      const problems = problemsResult.data || []
      const solutions = solutionsResult.data || []

      // Calculate stats
      const statsData: AdminStatsData = {
        totalUsers: users.length,
        totalExperts: experts.length,
        totalProblems: problems.length,
        totalSolutions: solutions.length,
        pendingProblems: problems.filter(p => p.status === 'open').length,
        resolvedProblems: problems.filter(p => p.status === 'resolved').length,
        activeExperts: experts.filter(e => e.availability === 'available').length,
        recentActivity: problems.filter(p => {
          const createdAt = new Date(p.created_at)
          const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
          return createdAt > dayAgo
        }).length
      }

      setStats(statsData)
    } catch (error) {
      console.error('Error loading admin stats:', error)
      toast({
        title: 'خطأ في تحميل الإحصائيات',
        description: 'حدث خطأ أثناء تحميل إحصائيات المنصة',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[...Array(8)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="flex items-center justify-center">
                <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="text-center py-8">
        <AlertTriangle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600">فشل في تحميل الإحصائيات</p>
      </div>
    )
  }

  const statCards = [
    {
      title: 'إجمالي المستخدمين',
      value: stats.totalUsers,
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+12%',
      changeType: 'positive' as const
    },
    {
      title: 'الخبراء المسجلين',
      value: stats.totalExperts,
      icon: UserCheck,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+8%',
      changeType: 'positive' as const
    },
    {
      title: 'إجمالي المشاكل',
      value: stats.totalProblems,
      icon: FileText,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+15%',
      changeType: 'positive' as const
    },
    {
      title: 'الحلول المقترحة',
      value: stats.totalSolutions,
      icon: MessageSquare,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '+22%',
      changeType: 'positive' as const
    },
    {
      title: 'المشاكل المفتوحة',
      value: stats.pendingProblems,
      icon: Clock,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      change: '-5%',
      changeType: 'negative' as const
    },
    {
      title: 'المشاكل المحلولة',
      value: stats.resolvedProblems,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+18%',
      changeType: 'positive' as const
    },
    {
      title: 'الخبراء النشطين',
      value: stats.activeExperts,
      icon: TrendingUp,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      change: '+10%',
      changeType: 'positive' as const
    },
    {
      title: 'النشاط اليومي',
      value: stats.recentActivity,
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      change: '+25%',
      changeType: 'positive' as const
    }
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {statCards.map((stat, index) => (
        <Card key={index} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">
                  {stat.title}
                </p>
                <p className="text-3xl font-bold text-gray-900">
                  {stat.value.toLocaleString('ar-SA')}
                </p>
                <div className="flex items-center mt-2">
                  <Badge 
                    variant={stat.changeType === 'positive' ? 'default' : 'destructive'}
                    className="text-xs"
                  >
                    {stat.change}
                  </Badge>
                  <span className="text-xs text-gray-500 mr-2">
                    مقارنة بالشهر الماضي
                  </span>
                </div>
              </div>
              <div className={`p-3 rounded-full ${stat.bgColor}`}>
                <stat.icon className={`w-6 h-6 ${stat.color}`} />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}