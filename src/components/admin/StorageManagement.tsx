import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  HardDrive, 
  Image, 
  FileText, 
  Presentation,
  Upload,
  Download,
  Trash2,
  RefreshCw,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { formatBytes } from '@/lib/utils'

interface StorageBucket {
  id: string
  name: string
  public: boolean
  file_size_limit: number
  allowed_mime_types: string[]
}

interface StorageStats {
  bucket_id: string
  count: number
  total_size: number
}

interface StorageFile {
  name: string
  id: string
  updated_at: string
  created_at: string
  last_accessed_at: string
  metadata: {
    size: number
    mimetype: string
    cacheControl: string
  }
}

export default function StorageManagement() {
  const [buckets, setBuckets] = useState<StorageBucket[]>([])
  const [stats, setStats] = useState<StorageStats[]>([])
  const [files, setFiles] = useState<{ [key: string]: StorageFile[] }>({})
  const [loading, setLoading] = useState(true)
  const [selectedBucket, setSelectedBucket] = useState<string>('attachments')

  useEffect(() => {
    loadStorageData()
  }, [])

  const loadStorageData = async () => {
    setLoading(true)
    try {
      // Load bucket information
      const { data: bucketsData, error: bucketsError } = await supabase
        .from('storage.buckets')
        .select('*')
        .order('name')

      if (bucketsError) throw bucketsError
      setBuckets(bucketsData || [])

      // Load storage statistics
      const { data: statsData, error: statsError } = await supabase
        .rpc('get_storage_stats')

      if (!statsError && statsData) {
        setStats(statsData)
      }

      // Load files for each bucket
      const filesData: { [key: string]: StorageFile[] } = {}
      for (const bucket of bucketsData || []) {
        const { data: bucketFiles, error: filesError } = await supabase
          .storage
          .from(bucket.id)
          .list('', { limit: 100, sortBy: { column: 'created_at', order: 'desc' } })

        if (!filesError && bucketFiles) {
          filesData[bucket.id] = bucketFiles
        }
      }
      setFiles(filesData)

    } catch (error) {
      console.error('Error loading storage data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getBucketIcon = (bucketId: string) => {
    switch (bucketId) {
      case 'avatars':
        return <Image className="h-5 w-5" />
      case 'attachments':
        return <FileText className="h-5 w-5" />
      case 'presentations':
        return <Presentation className="h-5 w-5" />
      default:
        return <HardDrive className="h-5 w-5" />
    }
  }

  const getBucketStats = (bucketId: string) => {
    return stats.find(s => s.bucket_id === bucketId) || { count: 0, total_size: 0 }
  }

  const getUsagePercentage = (bucket: StorageBucket) => {
    const bucketStats = getBucketStats(bucket.id)
    return bucket.file_size_limit > 0 
      ? (bucketStats.total_size / bucket.file_size_limit) * 100 
      : 0
  }

  const deleteFile = async (bucketId: string, fileName: string) => {
    try {
      const { error } = await supabase.storage
        .from(bucketId)
        .remove([fileName])

      if (error) throw error

      // Refresh files list
      loadStorageData()
    } catch (error) {
      console.error('Error deleting file:', error)
    }
  }

  const downloadFile = async (bucketId: string, fileName: string) => {
    try {
      const { data, error } = await supabase.storage
        .from(bucketId)
        .download(fileName)

      if (error) throw error

      // Create download link
      const url = URL.createObjectURL(data)
      const a = document.createElement('a')
      a.href = url
      a.download = fileName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error downloading file:', error)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading storage data...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Storage Management</h2>
          <p className="text-muted-foreground">
            Manage file storage buckets and monitor usage
          </p>
        </div>
        <Button onClick={loadStorageData} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Storage Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {buckets.map((bucket) => {
          const bucketStats = getBucketStats(bucket.id)
          const usagePercentage = getUsagePercentage(bucket)
          
          return (
            <Card key={bucket.id} className="relative">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium capitalize">
                  {bucket.name}
                </CardTitle>
                {getBucketIcon(bucket.id)}
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold">{bucketStats.count}</span>
                    <Badge variant={bucket.public ? "default" : "secondary"}>
                      {bucket.public ? "Public" : "Private"}
                    </Badge>
                  </div>
                  
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>Storage Used</span>
                      <span>{formatBytes(bucketStats.total_size)} / {formatBytes(bucket.file_size_limit)}</span>
                    </div>
                    <Progress value={usagePercentage} className="h-2" />
                  </div>

                  <div className="flex items-center text-sm text-muted-foreground">
                    {usagePercentage > 90 ? (
                      <AlertCircle className="h-4 w-4 mr-1 text-red-500" />
                    ) : (
                      <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                    )}
                    {usagePercentage.toFixed(1)}% used
                  </div>

                  <div className="text-xs text-muted-foreground">
                    Max file size: {formatBytes(bucket.file_size_limit)}
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* File Management Tabs */}
      <Card>
        <CardHeader>
          <CardTitle>File Management</CardTitle>
          <CardDescription>
            Browse and manage files in each storage bucket
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedBucket} onValueChange={setSelectedBucket}>
            <TabsList className="grid w-full grid-cols-3">
              {buckets.map((bucket) => (
                <TabsTrigger key={bucket.id} value={bucket.id} className="capitalize">
                  {getBucketIcon(bucket.id)}
                  <span className="ml-2">{bucket.name}</span>
                </TabsTrigger>
              ))}
            </TabsList>

            {buckets.map((bucket) => (
              <TabsContent key={bucket.id} value={bucket.id} className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold capitalize">{bucket.name} Files</h3>
                  <div className="flex gap-2">
                    <Badge variant="outline">
                      {files[bucket.id]?.length || 0} files
                    </Badge>
                    <Badge variant="outline">
                      {formatBytes(getBucketStats(bucket.id).total_size)}
                    </Badge>
                  </div>
                </div>

                {/* Allowed MIME Types */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Allowed File Types:</h4>
                  <div className="flex flex-wrap gap-1">
                    {bucket.allowed_mime_types?.map((mimeType) => (
                      <Badge key={mimeType} variant="secondary" className="text-xs">
                        {mimeType.split('/')[1]}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Files List */}
                <div className="border rounded-lg">
                  {files[bucket.id]?.length > 0 ? (
                    <div className="divide-y">
                      {files[bucket.id].slice(0, 10).map((file) => (
                        <div key={file.name} className="flex items-center justify-between p-4">
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">{file.name}</p>
                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                              <span>{formatBytes(file.metadata?.size || 0)}</span>
                              <span>{file.metadata?.mimetype}</span>
                              <span>{new Date(file.created_at).toLocaleDateString()}</span>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => downloadFile(bucket.id, file.name)}
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => deleteFile(bucket.id, file.name)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="p-8 text-center text-muted-foreground">
                      <HardDrive className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No files in this bucket</p>
                    </div>
                  )}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}