import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart
} from 'recharts'
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  FileText, 
  MessageSquare,
  Download,
  Calendar,
  BarChart3,
  <PERSON><PERSON>hart as PieChartIcon,
  Activity
} from 'lucide-react'

interface AnalyticsData {
  problemsOverTime: Array<{ date: string; problems: number; solutions: number }>
  expertsByCategory: Array<{ category: string; count: number; color: string }>
  problemsByUrgency: Array<{ urgency: string; count: number; color: string }>
  userGrowth: Array<{ month: string; users: number; experts: number }>
  topCategories: Array<{ category: string; problems: number; solutions: number }>
  resolutionRate: Array<{ month: string; resolved: number; total: number; rate: number }>
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

export function AnalyticsDashboard() {
  const { toast } = useToast()
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')

  useEffect(() => {
    loadAnalytics()
  }, [timeRange])

  const loadAnalytics = async () => {
    try {
      setLoading(true)
      
      // Mock analytics data - in real implementation, this would come from your database
      const mockData: AnalyticsData = {
        problemsOverTime: [
          { date: '2024-01-01', problems: 12, solutions: 8 },
          { date: '2024-01-02', problems: 15, solutions: 12 },
          { date: '2024-01-03', problems: 18, solutions: 15 },
          { date: '2024-01-04', problems: 22, solutions: 18 },
          { date: '2024-01-05', problems: 25, solutions: 22 },
          { date: '2024-01-06', problems: 28, solutions: 25 },
          { date: '2024-01-07', problems: 32, solutions: 28 }
        ],
        expertsByCategory: [
          { category: 'تطوير البرمجيات', count: 25, color: '#0088FE' },
          { category: 'أمن المعلومات', count: 18, color: '#00C49F' },
          { category: 'قواعد البيانات', count: 15, color: '#FFBB28' },
          { category: 'الذكاء الاصطناعي', count: 12, color: '#FF8042' },
          { category: 'الشبكات', count: 10, color: '#8884D8' }
        ],
        problemsByUrgency: [
          { urgency: 'منخفضة', count: 45, color: '#00C49F' },
          { urgency: 'متوسطة', count: 35, color: '#FFBB28' },
          { urgency: 'عالية', count: 25, color: '#FF8042' },
          { urgency: 'حرجة', count: 15, color: '#FF0000' }
        ],
        userGrowth: [
          { month: 'يناير', users: 120, experts: 25 },
          { month: 'فبراير', users: 145, experts: 32 },
          { month: 'مارس', users: 168, experts: 38 },
          { month: 'أبريل', users: 192, experts: 45 },
          { month: 'مايو', users: 218, experts: 52 },
          { month: 'يونيو', users: 245, experts: 58 }
        ],
        topCategories: [
          { category: 'تطوير البرمجيات', problems: 45, solutions: 38 },
          { category: 'أمن المعلومات', problems: 32, solutions: 28 },
          { category: 'قواعد البيانات', problems: 28, solutions: 25 },
          { category: 'الذكاء الاصطناعي', problems: 22, solutions: 18 },
          { category: 'الشبكات', problems: 18, solutions: 15 }
        ],
        resolutionRate: [
          { month: 'يناير', resolved: 25, total: 35, rate: 71 },
          { month: 'فبراير', resolved: 32, total: 42, rate: 76 },
          { month: 'مارس', resolved: 38, total: 48, rate: 79 },
          { month: 'أبريل', resolved: 42, total: 52, rate: 81 },
          { month: 'مايو', resolved: 48, total: 58, rate: 83 },
          { month: 'يونيو', resolved: 52, total: 62, rate: 84 }
        ]
      }

      setAnalyticsData(mockData)
    } catch (error) {
      console.error('Error loading analytics:', error)
      toast({
        title: 'خطأ في تحميل التحليلات',
        description: 'حدث خطأ أثناء تحميل بيانات التحليلات',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const exportData = () => {
    toast({
      title: 'تم التصدير',
      description: 'تم تصدير البيانات بنجاح',
    })
  }

  if (loading) {
    return (
      <div className="text-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">جاري تحميل التحليلات...</p>
      </div>
    )
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-8">
        <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600">فشل في تحميل بيانات التحليلات</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">آخر 7 أيام</SelectItem>
              <SelectItem value="30d">آخر 30 يوم</SelectItem>
              <SelectItem value="90d">آخر 3 أشهر</SelectItem>
              <SelectItem value="1y">آخر سنة</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <Button onClick={exportData} variant="outline">
          <Download className="w-4 h-4 ml-2" />
          تصدير البيانات
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">معدل الحل</p>
                <p className="text-3xl font-bold text-green-600">84%</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                  <span className="text-sm text-green-600">+3% من الشهر الماضي</span>
                </div>
              </div>
              <Activity className="w-8 h-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">متوسط وقت الحل</p>
                <p className="text-3xl font-bold text-blue-600">2.3</p>
                <p className="text-sm text-gray-500">أيام</p>
                <div className="flex items-center mt-1">
                  <TrendingDown className="w-4 h-4 text-green-500 mr-1" />
                  <span className="text-sm text-green-600">-0.5 يوم</span>
                </div>
              </div>
              <Calendar className="w-8 h-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">الخبراء النشطين</p>
                <p className="text-3xl font-bold text-purple-600">58</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                  <span className="text-sm text-green-600">+6 هذا الشهر</span>
                </div>
              </div>
              <Users className="w-8 h-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">رضا المستخدمين</p>
                <p className="text-3xl font-bold text-orange-600">4.7</p>
                <p className="text-sm text-gray-500">من 5</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                  <span className="text-sm text-green-600">+0.2</span>
                </div>
              </div>
              <MessageSquare className="w-8 h-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Problems and Solutions Over Time */}
        <Card>
          <CardHeader>
            <CardTitle>المشاكل والحلول عبر الزمن</CardTitle>
            <CardDescription>تطور عدد المشاكل والحلول المطروحة</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={analyticsData.problemsOverTime}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Area type="monotone" dataKey="problems" stackId="1" stroke="#8884d8" fill="#8884d8" />
                <Area type="monotone" dataKey="solutions" stackId="1" stroke="#82ca9d" fill="#82ca9d" />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* User Growth */}
        <Card>
          <CardHeader>
            <CardTitle>نمو المستخدمين</CardTitle>
            <CardDescription>نمو عدد المستخدمين والخبراء</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={analyticsData.userGrowth}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="users" stroke="#8884d8" strokeWidth={2} />
                <Line type="monotone" dataKey="experts" stroke="#82ca9d" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Experts by Category */}
        <Card>
          <CardHeader>
            <CardTitle>توزيع الخبراء حسب التخصص</CardTitle>
            <CardDescription>عدد الخبراء في كل تخصص</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={analyticsData.expertsByCategory}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ category, percent }) => `${category} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {analyticsData.expertsByCategory.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Problems by Urgency */}
        <Card>
          <CardHeader>
            <CardTitle>المشاكل حسب الأولوية</CardTitle>
            <CardDescription>توزيع المشاكل حسب مستوى الأولوية</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={analyticsData.problemsByUrgency}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="urgency" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Top Categories */}
        <Card>
          <CardHeader>
            <CardTitle>أهم الفئات</CardTitle>
            <CardDescription>الفئات الأكثر نشاطاً</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={analyticsData.topCategories}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="category" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="problems" fill="#8884d8" />
                <Bar dataKey="solutions" fill="#82ca9d" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Resolution Rate */}
        <Card>
          <CardHeader>
            <CardTitle>معدل الحل الشهري</CardTitle>
            <CardDescription>تطور معدل حل المشاكل</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={analyticsData.resolutionRate}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="rate" stroke="#82ca9d" strokeWidth={3} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}