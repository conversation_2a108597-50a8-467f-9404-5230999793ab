/**
 * Translation Monitor
 * Tracks missing translation keys and provides fallback mechanisms
 */

import { errorLogger } from './errorLogger';

interface MissingTranslation {
  key: string;
  language: string;
  fallbackUsed: string;
  timestamp: string;
  context?: string;
  count: number;
}

interface TranslationUsage {
  key: string;
  language: string;
  value: string;
  timestamp: string;
  context?: string;
}

class TranslationMonitor {
  private missingKeys: Map<string, MissingTranslation> = new Map();
  private usageStats: Map<string, TranslationUsage[]> = new Map();
  private fallbackStrategies: Map<string, (key: string) => string> = new Map();

  constructor() {
    this.setupDefaultFallbackStrategies();
  }

  private setupDefaultFallbackStrategies(): void {
    // Strategy 1: Use the last part of the key
    this.fallbackStrategies.set('lastPart', (key: string) => {
      const parts = key.split('.');
      return parts[parts.length - 1].replace(/_/g, ' ');
    });

    // Strategy 2: Convert snake_case to Title Case
    this.fallbackStrategies.set('titleCase', (key: string) => {
      const lastPart = key.split('.').pop() || key;
      return lastPart
        .replace(/_/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase());
    });

    // Strategy 3: Provide generic error message
    this.fallbackStrategies.set('genericError', () => {
      return 'Text not available';
    });

    // Strategy 4: Arabic fallback
    this.fallbackStrategies.set('arabicFallback', (key: string) => {
      const arabicFallbacks: Record<string, string> = {
        'error': 'خطأ',
        'loading': 'جاري التحميل',
        'save': 'حفظ',
        'cancel': 'إلغاء',
        'submit': 'إرسال',
        'delete': 'حذف',
        'edit': 'تعديل',
        'view': 'عرض',
        'search': 'بحث',
        'filter': 'تصفية',
        'sort': 'ترتيب',
        'next': 'التالي',
        'previous': 'السابق',
        'close': 'إغلاق',
        'open': 'فتح',
        'success': 'نجح',
        'failed': 'فشل',
        'warning': 'تحذير',
        'info': 'معلومات',
      };

      const lastPart = key.split('.').pop()?.toLowerCase() || '';
      return arabicFallbacks[lastPart] || this.fallbackStrategies.get('titleCase')!(key);
    });
  }

  /**
   * Log a missing translation key
   */
  logMissingKey(
    key: string, 
    language: string, 
    fallbackUsed: string, 
    context?: string
  ): void {
    const mapKey = `${key}:${language}`;
    const existing = this.missingKeys.get(mapKey);

    if (existing) {
      existing.count++;
      existing.timestamp = new Date().toISOString();
    } else {
      const missing: MissingTranslation = {
        key,
        language,
        fallbackUsed,
        timestamp: new Date().toISOString(),
        context,
        count: 1,
      };

      this.missingKeys.set(mapKey, missing);

      // Log to error logger
      errorLogger.logError({
        message: `Missing translation key: ${key}`,
        timestamp: missing.timestamp,
        source: 'translationMonitor',
        type: 'missingTranslation',
        context: {
          key,
          language,
          fallbackUsed,
          context,
        },
      });

      // Log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.warn(`Missing translation key: ${key} (${language})`, {
          fallbackUsed,
          context,
        });
      }
    }
  }

  /**
   * Track translation usage
   */
  trackUsage(key: string, language: string, value: string, context?: string): void {
    const usage: TranslationUsage = {
      key,
      language,
      value,
      timestamp: new Date().toISOString(),
      context,
    };

    const existing = this.usageStats.get(key) || [];
    existing.push(usage);
    
    // Keep only last 100 usages per key
    if (existing.length > 100) {
      existing.splice(0, existing.length - 100);
    }
    
    this.usageStats.set(key, existing);
  }

  /**
   * Get fallback text for a missing key
   */
  getFallback(key: string, language: string = 'en', strategy: string = 'titleCase'): string {
    const fallbackFn = this.fallbackStrategies.get(strategy);
    
    if (!fallbackFn) {
      console.warn(`Unknown fallback strategy: ${strategy}`);
      return this.fallbackStrategies.get('titleCase')!(key);
    }

    const fallback = fallbackFn(key);
    
    // Log the missing key
    this.logMissingKey(key, language, fallback);
    
    return fallback;
  }

  /**
   * Enhanced translation function with monitoring
   */
  translate(
    translations: Record<string, Record<string, string>>,
    key: string,
    language: string = 'en',
    params?: Record<string, string | number>,
    context?: string
  ): string {
    // Try to get translation
    let translation = translations[language]?.[key];
    
    if (!translation) {
      // Try fallback language (English)
      translation = translations['en']?.[key];
      
      if (!translation) {
        // Use fallback strategy
        const strategy = language === 'ar' ? 'arabicFallback' : 'titleCase';
        translation = this.getFallback(key, language, strategy);
      } else {
        // Log that we used fallback language
        this.logMissingKey(key, language, translation, context);
      }
    } else {
      // Track successful usage
      this.trackUsage(key, language, translation, context);
    }

    // Handle parameter substitution
    if (params && translation) {
      Object.entries(params).forEach(([param, value]) => {
        const placeholder = `{{${param}}}`;
        translation = translation.replace(new RegExp(placeholder, 'g'), String(value));
      });
    }

    return translation;
  }

  /**
   * Get missing translation statistics
   */
  getMissingKeyStats(): {
    totalMissing: number;
    missingByLanguage: Record<string, number>;
    mostMissedKeys: Array<{ key: string; count: number; languages: string[] }>;
    recentMissing: MissingTranslation[];
  } {
    const missing = Array.from(this.missingKeys.values());
    const missingByLanguage: Record<string, number> = {};
    const keyStats: Record<string, { count: number; languages: Set<string> }> = {};

    missing.forEach(item => {
      missingByLanguage[item.language] = (missingByLanguage[item.language] || 0) + item.count;
      
      if (!keyStats[item.key]) {
        keyStats[item.key] = { count: 0, languages: new Set() };
      }
      keyStats[item.key].count += item.count;
      keyStats[item.key].languages.add(item.language);
    });

    const mostMissedKeys = Object.entries(keyStats)
      .map(([key, stats]) => ({
        key,
        count: stats.count,
        languages: Array.from(stats.languages),
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Get recent missing keys (last 24 hours)
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentMissing = missing.filter(
      item => new Date(item.timestamp) > oneDayAgo
    );

    return {
      totalMissing: missing.length,
      missingByLanguage,
      mostMissedKeys,
      recentMissing,
    };
  }

  /**
   * Get translation usage statistics
   */
  getUsageStats(): {
    totalUsages: number;
    usagesByKey: Record<string, number>;
    usagesByLanguage: Record<string, number>;
    mostUsedKeys: Array<{ key: string; count: number }>;
  } {
    const allUsages = Array.from(this.usageStats.values()).flat();
    const usagesByKey: Record<string, number> = {};
    const usagesByLanguage: Record<string, number> = {};

    allUsages.forEach(usage => {
      usagesByKey[usage.key] = (usagesByKey[usage.key] || 0) + 1;
      usagesByLanguage[usage.language] = (usagesByLanguage[usage.language] || 0) + 1;
    });

    const mostUsedKeys = Object.entries(usagesByKey)
      .map(([key, count]) => ({ key, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalUsages: allUsages.length,
      usagesByKey,
      usagesByLanguage,
      mostUsedKeys,
    };
  }

  /**
   * Generate translation report
   */
  generateReport(): {
    missing: MissingTranslation[];
    usage: TranslationUsage[];
    summary: {
      totalMissingKeys: number;
      totalUsages: number;
      languagesCovered: string[];
      topMissingKeys: string[];
      recommendedTranslations: Array<{ key: string; suggestedValue: string; language: string }>;
    };
  } {
    const missing = Array.from(this.missingKeys.values());
    const usage = Array.from(this.usageStats.values()).flat();
    
    const languagesCovered = [...new Set([
      ...missing.map(m => m.language),
      ...usage.map(u => u.language),
    ])];

    const topMissingKeys = missing
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)
      .map(m => m.key);

    // Generate recommended translations based on fallbacks
    const recommendedTranslations = missing
      .filter(m => m.count > 1) // Only keys that are frequently missing
      .slice(0, 10)
      .map(m => ({
        key: m.key,
        suggestedValue: m.fallbackUsed,
        language: m.language,
      }));

    return {
      missing,
      usage,
      summary: {
        totalMissingKeys: missing.length,
        totalUsages: usage.length,
        languagesCovered,
        topMissingKeys,
        recommendedTranslations,
      },
    };
  }

  /**
   * Clear monitoring data
   */
  clear(): void {
    this.missingKeys.clear();
    this.usageStats.clear();
  }

  /**
   * Add custom fallback strategy
   */
  addFallbackStrategy(name: string, strategy: (key: string) => string): void {
    this.fallbackStrategies.set(name, strategy);
  }
}

// Create singleton instance
export const translationMonitor = new TranslationMonitor();

// Development tools
if (process.env.NODE_ENV === 'development') {
  // Add to window for debugging
  (window as any).translationMonitor = translationMonitor;
}