import { toast } from 'sonner';
import { errorLogger } from './errorLogger';

export interface ApiError extends Error {
  status?: number;
  statusText?: string;
  data?: any;
  endpoint?: string;
  method?: string;
}

export interface ErrorContext {
  endpoint: string;
  method: string;
  requestData?: any;
  userId?: string;
  timestamp: string;
}

/**
 * Enhanced API error handler with user-friendly messages and retry logic
 */
export class ApiErrorHandler {
  private static instance: ApiErrorHandler;
  private retryAttempts: Map<string, number> = new Map();
  private maxRetries = 3;

  static getInstance(): ApiErrorHandler {
    if (!ApiErrorHandler.instance) {
      ApiErrorHandler.instance = new ApiErrorHandler();
    }
    return ApiErrorHandler.instance;
  }

  /**
   * Handle API errors with appropriate user feedback and logging
   */
  async handleError(
    error: ApiError,
    context: ErrorContext,
    options: {
      showToast?: boolean;
      enableRetry?: boolean;
      customMessage?: string;
      onRetry?: () => Promise<any>;
    } = {}
  ): Promise<void> {
    const {
      showToast = true,
      enableRetry = false,
      customMessage,
      onRetry,
    } = options;

    // Log the error
    await this.logError(error, context);

    // Get user-friendly message
    const userMessage = customMessage || this.getUserFriendlyMessage(error);

    // Show toast notification
    if (showToast) {
      this.showErrorToast(error, userMessage, enableRetry ? onRetry : undefined);
    }

    // Handle specific error types
    await this.handleSpecificErrors(error, context);
  }

  /**
   * Get user-friendly error message based on error type
   */
  private getUserFriendlyMessage(error: ApiError): string {
    // Network errors
    if (this.isNetworkError(error)) {
      return 'Network connection error. Please check your internet connection and try again.';
    }

    // Timeout errors
    if (this.isTimeoutError(error)) {
      return 'Request timed out. Please try again.';
    }

    // Authentication errors
    if (error.status === 401) {
      return 'Your session has expired. Please log in again.';
    }

    // Authorization errors
    if (error.status === 403) {
      return 'You do not have permission to perform this action.';
    }

    // Not found errors
    if (error.status === 404) {
      return 'The requested resource was not found.';
    }

    // Validation errors
    if (error.status === 422) {
      return 'Please check your input and try again.';
    }

    // Rate limiting
    if (error.status === 429) {
      return 'Too many requests. Please wait a moment and try again.';
    }

    // Server errors
    if (error.status && error.status >= 500) {
      return 'Server error occurred. Please try again later.';
    }

    // Default message
    return error.message || 'An unexpected error occurred. Please try again.';
  }

  /**
   * Show error toast with optional retry functionality
   */
  private showErrorToast(error: ApiError, message: string, onRetry?: () => Promise<any>): void {
    const toastOptions: any = {
      description: this.getErrorDescription(error),
    };

    if (onRetry && this.shouldAllowRetry(error)) {
      toastOptions.action = {
        label: 'Retry',
        onClick: async () => {
          try {
            await onRetry();
            toast.success('Operation completed successfully');
          } catch (retryError) {
            toast.error('Retry failed. Please try again later.');
          }
        },
      };
    }

    toast.error(message, toastOptions);
  }

  /**
   * Get additional error description for toast
   */
  private getErrorDescription(error: ApiError): string | undefined {
    if (error.status === 401) {
      return 'Please log in to continue.';
    }

    if (error.status === 403) {
      return 'Contact your administrator if you believe this is an error.';
    }

    if (error.status === 429) {
      return 'Please wait before making another request.';
    }

    if (error.status && error.status >= 500) {
      return 'Our team has been notified and is working on a fix.';
    }

    return undefined;
  }

  /**
   * Handle specific error types with custom logic
   */
  private async handleSpecificErrors(error: ApiError, context: ErrorContext): Promise<void> {
    // Handle authentication errors
    if (error.status === 401) {
      // Redirect to login or refresh token
      this.handleAuthenticationError();
    }

    // Handle rate limiting
    if (error.status === 429) {
      this.handleRateLimitError(context);
    }

    // Handle server errors
    if (error.status && error.status >= 500) {
      this.handleServerError(error, context);
    }
  }

  /**
   * Handle authentication errors
   */
  private handleAuthenticationError(): void {
    // Clear user session
    localStorage.removeItem('auth-token');
    
    // Redirect to login if not already there
    if (!window.location.pathname.includes('/auth/login')) {
      window.location.href = '/auth/login';
    }
  }

  /**
   * Handle rate limiting errors
   */
  private handleRateLimitError(context: ErrorContext): void {
    const key = `${context.method}:${context.endpoint}`;
    const attempts = this.retryAttempts.get(key) || 0;
    this.retryAttempts.set(key, attempts + 1);

    // Clear retry count after delay
    setTimeout(() => {
      this.retryAttempts.delete(key);
    }, 60000); // 1 minute
  }

  /**
   * Handle server errors
   */
  private handleServerError(error: ApiError, context: ErrorContext): void {
    // Log additional context for server errors
    errorLogger.logError({
      message: `Server error: ${error.message}`,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      source: 'apiErrorHandler',
      type: 'serverError',
      context: {
        status: error.status,
        endpoint: context.endpoint,
        method: context.method,
        requestData: context.requestData,
      },
    });
  }

  /**
   * Log error details
   */
  private async logError(error: ApiError, context: ErrorContext): Promise<void> {
    try {
      await errorLogger.logError({
        message: error.message,
        stack: error.stack,
        timestamp: context.timestamp,
        source: 'apiError',
        type: 'apiError',
        context: {
          status: error.status,
          statusText: error.statusText,
          endpoint: context.endpoint,
          method: context.method,
          requestData: context.requestData ? JSON.stringify(context.requestData) : undefined,
          userId: context.userId,
        },
      });
    } catch (loggingError) {
      console.error('Failed to log API error:', loggingError);
    }
  }

  /**
   * Check if error is a network error
   */
  private isNetworkError(error: ApiError): boolean {
    return (
      error.message.includes('Network Error') ||
      error.message.includes('fetch') ||
      error.message.includes('ERR_NETWORK') ||
      !error.status
    );
  }

  /**
   * Check if error is a timeout error
   */
  private isTimeoutError(error: ApiError): boolean {
    return (
      error.message.includes('timeout') ||
      error.message.includes('TIMEOUT') ||
      error.status === 408
    );
  }

  /**
   * Check if retry should be allowed for this error
   */
  private shouldAllowRetry(error: ApiError): boolean {
    // Don't allow retry for client errors
    if (error.status && error.status >= 400 && error.status < 500) {
      return false;
    }

    // Allow retry for network and server errors
    return this.isNetworkError(error) || this.isTimeoutError(error) || (error.status && error.status >= 500);
  }

  /**
   * Create API error from response
   */
  static createApiError(
    response: Response,
    endpoint: string,
    method: string,
    data?: any
  ): ApiError {
    const error = new Error(`API Error: ${response.status} ${response.statusText}`) as ApiError;
    error.status = response.status;
    error.statusText = response.statusText;
    error.endpoint = endpoint;
    error.method = method;
    error.data = data;
    return error;
  }

  /**
   * Create network error
   */
  static createNetworkError(endpoint: string, method: string): ApiError {
    const error = new Error('Network Error: Unable to connect to server') as ApiError;
    error.endpoint = endpoint;
    error.method = method;
    return error;
  }
}

// Export singleton instance
export const apiErrorHandler = ApiErrorHandler.getInstance();

// Utility functions for common scenarios
export const handleApiError = (
  error: ApiError,
  context: ErrorContext,
  options?: Parameters<typeof apiErrorHandler.handleError>[2]
) => {
  return apiErrorHandler.handleError(error, context, options);
};

export const createApiError = ApiErrorHandler.createApiError;
export const createNetworkError = ApiErrorHandler.createNetworkError;
