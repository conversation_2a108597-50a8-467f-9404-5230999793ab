import { QueryClient } from '@tanstack/react-query';
import { persistQueryClient } from '@tanstack/react-query-persist-client';
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';

// Storage interface for different storage types
interface StorageAdapter {
  getItem: (key: string) => string | null | Promise<string | null>;
  setItem: (key: string, value: string) => void | Promise<void>;
  removeItem: (key: string) => void | Promise<void>;
}

// IndexedDB storage adapter for better performance and larger storage
class IndexedDBAdapter implements StorageAdapter {
  private dbName = 'react-query-cache';
  private storeName = 'queries';
  private version = 1;
  private db: IDBDatabase | null = null;

  private async getDB(): Promise<IDBDatabase> {
    if (this.db) return this.db;

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve(this.db);
      };
      
      request.onupgradeneeded = () => {
        const db = request.result;
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.createObjectStore(this.storeName);
        }
      };
    });
  }

  async getItem(key: string): Promise<string | null> {
    try {
      const db = await this.getDB();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction([this.storeName], 'readonly');
        const store = transaction.objectStore(this.storeName);
        const request = store.get(key);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result || null);
      });
    } catch (error) {
      console.warn('IndexedDB getItem failed:', error);
      return null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      const db = await this.getDB();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);
        const request = store.put(value, key);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
      });
    } catch (error) {
      console.warn('IndexedDB setItem failed:', error);
    }
  }

  async removeItem(key: string): Promise<void> {
    try {
      const db = await this.getDB();
      return new Promise((resolve, reject) => {
        const transaction = db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);
        const request = store.delete(key);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
      });
    } catch (error) {
      console.warn('IndexedDB removeItem failed:', error);
    }
  }
}

// LocalStorage adapter as fallback
class LocalStorageAdapter implements StorageAdapter {
  getItem(key: string): string | null {
    try {
      return localStorage.getItem(key);
    } catch (error) {
      console.warn('LocalStorage getItem failed:', error);
      return null;
    }
  }

  setItem(key: string, value: string): void {
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      console.warn('LocalStorage setItem failed:', error);
    }
  }

  removeItem(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.warn('LocalStorage removeItem failed:', error);
    }
  }
}

// Memory adapter as last resort
class MemoryAdapter implements StorageAdapter {
  private storage = new Map<string, string>();

  getItem(key: string): string | null {
    return this.storage.get(key) || null;
  }

  setItem(key: string, value: string): void {
    this.storage.set(key, value);
  }

  removeItem(key: string): void {
    this.storage.delete(key);
  }
}

// Get the best available storage adapter
function getBestStorageAdapter(): StorageAdapter {
  // Check for IndexedDB support
  if (typeof window !== 'undefined' && 'indexedDB' in window) {
    try {
      return new IndexedDBAdapter();
    } catch (error) {
      console.warn('IndexedDB not available, falling back to localStorage');
    }
  }

  // Check for localStorage support
  if (typeof window !== 'undefined' && 'localStorage' in window) {
    try {
      // Test localStorage availability
      const testKey = '__test__';
      localStorage.setItem(testKey, 'test');
      localStorage.removeItem(testKey);
      return new LocalStorageAdapter();
    } catch (error) {
      console.warn('localStorage not available, falling back to memory storage');
    }
  }

  // Fallback to memory storage
  return new MemoryAdapter();
}

// Cache persistence configuration
export const cacheConfig = {
  maxAge: 1000 * 60 * 60 * 24 * 7, // 7 days
  buster: 'v1.0.0', // Increment to invalidate all persisted cache
  hydrateOptions: {
    // Only hydrate queries that are not stale
    defaultOptions: {
      queries: {
        staleTime: 1000 * 60 * 5, // 5 minutes
      },
    },
  },
};

// Create storage persister
export const createCachePersister = () => {
  const storageAdapter = getBestStorageAdapter();
  
  return createSyncStoragePersister({
    storage: storageAdapter,
    key: 'react-query-cache',
    serialize: JSON.stringify,
    deserialize: JSON.parse,
  });
};

// Setup cache persistence
export const setupCachePersistence = async (queryClient: QueryClient) => {
  if (typeof window === 'undefined') {
    // Skip persistence on server-side
    return;
  }

  try {
    const persister = createCachePersister();
    
    await persistQueryClient({
      queryClient,
      persister,
      maxAge: cacheConfig.maxAge,
      buster: cacheConfig.buster,
      hydrateOptions: cacheConfig.hydrateOptions,
    });
    
    console.log('Cache persistence initialized');
  } catch (error) {
    console.warn('Failed to setup cache persistence:', error);
  }
};

// Cache cleanup utilities
export const cacheUtils = {
  // Clear all persisted cache
  clearPersistedCache: async () => {
    try {
      const storageAdapter = getBestStorageAdapter();
      await storageAdapter.removeItem('react-query-cache');
      console.log('Persisted cache cleared');
    } catch (error) {
      console.warn('Failed to clear persisted cache:', error);
    }
  },

  // Get cache size (approximate)
  getCacheSize: async (): Promise<number> => {
    try {
      const storageAdapter = getBestStorageAdapter();
      const cacheData = await storageAdapter.getItem('react-query-cache');
      return cacheData ? new Blob([cacheData]).size : 0;
    } catch (error) {
      console.warn('Failed to get cache size:', error);
      return 0;
    }
  },

  // Check if cache is available
  isCacheAvailable: (): boolean => {
    try {
      const storageAdapter = getBestStorageAdapter();
      return !(storageAdapter instanceof MemoryAdapter);
    } catch (error) {
      return false;
    }
  },

  // Migrate cache between versions
  migrateCacheVersion: async (oldVersion: string, newVersion: string) => {
    try {
      const storageAdapter = getBestStorageAdapter();
      const oldCacheKey = `react-query-cache-${oldVersion}`;
      const newCacheKey = `react-query-cache-${newVersion}`;
      
      const oldCache = await storageAdapter.getItem(oldCacheKey);
      if (oldCache) {
        // Perform any necessary data transformations here
        await storageAdapter.setItem(newCacheKey, oldCache);
        await storageAdapter.removeItem(oldCacheKey);
        console.log(`Cache migrated from ${oldVersion} to ${newVersion}`);
      }
    } catch (error) {
      console.warn('Cache migration failed:', error);
    }
  },
};

// Background cache cleanup
export const setupCacheCleanup = () => {
  if (typeof window === 'undefined') return;

  // Clean up expired cache entries periodically
  const cleanupInterval = setInterval(async () => {
    try {
      const storageAdapter = getBestStorageAdapter();
      const cacheData = await storageAdapter.getItem('react-query-cache');
      
      if (cacheData) {
        const parsed = JSON.parse(cacheData);
        const now = Date.now();
        let hasExpiredEntries = false;

        // Check for expired entries
        if (parsed.clientState?.queries) {
          Object.keys(parsed.clientState.queries).forEach(key => {
            const query = parsed.clientState.queries[key];
            if (query.dataUpdatedAt && (now - query.dataUpdatedAt) > cacheConfig.maxAge) {
              delete parsed.clientState.queries[key];
              hasExpiredEntries = true;
            }
          });
        }

        // Update cache if we removed expired entries
        if (hasExpiredEntries) {
          await storageAdapter.setItem('react-query-cache', JSON.stringify(parsed));
        }
      }
    } catch (error) {
      console.warn('Cache cleanup failed:', error);
    }
  }, 1000 * 60 * 60); // Run every hour

  // Clear interval on page unload
  window.addEventListener('beforeunload', () => {
    clearInterval(cleanupInterval);
  });

  return () => clearInterval(cleanupInterval);
};

export default {
  setupCachePersistence,
  createCachePersister,
  cacheUtils,
  setupCacheCleanup,
};