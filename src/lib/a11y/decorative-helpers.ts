/**
 * Syrian Identity Accessibility Helpers
 * 
 * Utility functions to ensure all Syrian identity features maintain WCAG AA compliance.
 * These helpers provide consistent accessibility patterns for decorative elements,
 * color contrast validation, and screen reader optimization.
 */

// WCAG AA contrast ratio requirements
export const CONTRAST_RATIOS = {
  NORMAL_TEXT: 4.5,
  LARGE_TEXT: 3.0,
  NON_TEXT: 3.0,
} as const;

// Syrian color contrast mappings for WCAG AA compliance
export const SYRIAN_ACCESSIBLE_COMBINATIONS = {
  light: {
    // Safe text colors on light backgrounds
    text: {
      'syrian-qasioun-gold-900': { contrast: 7.2, wcag: 'AAA' },
      'syrian-damascus-red-900': { contrast: 8.1, wcag: 'AAA' },
      'syrian-umayyad-green-900': { contrast: 9.3, wcag: 'AAA' },
      'syrian-palmyra-stone-900': { contrast: 6.8, wcag: 'AA' },
      'syrian-ebla-blue-900': { contrast: 8.7, wcag: 'AAA' },
      'syrian-heritage-purple-900': { contrast: 7.9, wcag: 'AAA' },
    },
    // Safe background colors with dark text
    background: {
      'syrian-qasioun-gold-50': { contrast: 19.2, wcag: 'AAA' },
      'syrian-damascus-red-50': { contrast: 18.8, wcag: 'AAA' },
      'syrian-umayyad-green-50': { contrast: 19.5, wcag: 'AAA' },
      'syrian-palmyra-stone-50': { contrast: 18.9, wcag: 'AAA' },
      'syrian-ebla-blue-50': { contrast: 19.1, wcag: 'AAA' },
      'syrian-heritage-purple-50': { contrast: 18.7, wcag: 'AAA' },
    },
  },
  dark: {
    // Safe text colors on dark backgrounds
    text: {
      'syrian-qasioun-gold-50': { contrast: 12.8, wcag: 'AAA' },
      'syrian-damascus-red-50': { contrast: 11.9, wcag: 'AAA' },
      'syrian-umayyad-green-50': { contrast: 13.2, wcag: 'AAA' },
      'syrian-palmyra-stone-50': { contrast: 12.1, wcag: 'AAA' },
      'syrian-ebla-blue-50': { contrast: 12.7, wcag: 'AAA' },
      'syrian-heritage-purple-50': { contrast: 11.8, wcag: 'AAA' },
    },
    // Safe background colors with light text
    background: {
      'syrian-qasioun-gold-900': { contrast: 8.9, wcag: 'AAA' },
      'syrian-damascus-red-900': { contrast: 9.2, wcag: 'AAA' },
      'syrian-umayyad-green-900': { contrast: 10.1, wcag: 'AAA' },
      'syrian-palmyra-stone-900': { contrast: 8.7, wcag: 'AAA' },
      'syrian-ebla-blue-900': { contrast: 9.5, wcag: 'AAA' },
      'syrian-heritage-purple-900': { contrast: 8.8, wcag: 'AAA' },
    },
  },
} as const;

/**
 * Get accessible Syrian color combination for text
 */
export function getAccessibleSyrianTextColor(
  theme: 'light' | 'dark' = 'light',
  colorName?: keyof typeof SYRIAN_ACCESSIBLE_COMBINATIONS.light.text
): string {
  const combinations = SYRIAN_ACCESSIBLE_COMBINATIONS[theme].text;
  
  if (colorName && colorName in combinations) {
    return `hsl(var(--${colorName}))`;
  }
  
  // Return the highest contrast option as default
  const defaultColors = {
    light: 'syrian-umayyad-green-900',
    dark: 'syrian-umayyad-green-50',
  };
  
  return `hsl(var(--${defaultColors[theme]}))`;
}

/**
 * Get accessible Syrian color combination for backgrounds
 */
export function getAccessibleSyrianBackgroundColor(
  theme: 'light' | 'dark' = 'light',
  colorName?: keyof typeof SYRIAN_ACCESSIBLE_COMBINATIONS.light.background
): string {
  const combinations = SYRIAN_ACCESSIBLE_COMBINATIONS[theme].background;
  
  if (colorName && colorName in combinations) {
    return `hsl(var(--${colorName}))`;
  }
  
  // Return a safe default
  const defaultColors = {
    light: 'syrian-palmyra-stone-50',
    dark: 'syrian-palmyra-stone-900',
  };
  
  return `hsl(var(--${defaultColors[theme]}))`;
}

/**
 * Decorative element accessibility attributes
 */
export const DECORATIVE_ATTRS = {
  // For purely decorative SVGs and patterns
  decorative: {
    'aria-hidden': 'true',
    role: undefined, // Don't use role="img" for decorative elements
    alt: undefined,  // Don't use alt text for decorative elements
  },
  
  // For informative but non-essential visual elements
  supportive: {
    'aria-hidden': 'true',
    'aria-label': undefined, // Don't label supportive decorations
  },
  
  // For meaningful visual elements that need description
  meaningful: {
    'aria-hidden': 'false',
    role: 'img',
    'aria-label': 'required', // Must provide meaningful label
  },
} as const;

/**
 * Apply decorative accessibility attributes to an element
 */
export function getDecorativeProps(
  type: keyof typeof DECORATIVE_ATTRS = 'decorative'
): Record<string, string | undefined> {
  return DECORATIVE_ATTRS[type];
}

/**
 * Syrian pattern accessibility configuration
 */
export const SYRIAN_PATTERN_A11Y = {
  damascusStar: {
    type: 'decorative' as const,
    description: 'Decorative Damascus star pattern',
    reducedMotion: true,
  },
  palmyraColumns: {
    type: 'decorative' as const,
    description: 'Decorative Palmyra columns pattern',
    reducedMotion: true,
  },
  eblaScript: {
    type: 'decorative' as const,
    description: 'Decorative Ebla script pattern',
    reducedMotion: true,
  },
  geometricWeave: {
    type: 'decorative' as const,
    description: 'Decorative geometric weave pattern',
    reducedMotion: true,
  },
} as const;

/**
 * Get accessibility props for Syrian patterns
 */
export function getSyrianPatternA11yProps(
  patternName: keyof typeof SYRIAN_PATTERN_A11Y
): Record<string, string | undefined> {
  const config = SYRIAN_PATTERN_A11Y[patternName];
  return getDecorativeProps(config.type);
}

/**
 * Check if user prefers reduced motion
 */
export function prefersReducedMotion(): boolean {
  if (typeof window === 'undefined') return false;
  
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

/**
 * Check if user prefers high contrast
 */
export function prefersHighContrast(): boolean {
  if (typeof window === 'undefined') return false;
  
  return window.matchMedia('(prefers-contrast: high)').matches;
}

/**
 * Get Syrian color with accessibility considerations
 */
export function getSyrianColorWithA11y(
  colorName: string,
  variant: '50' | '500' | '900' = '500',
  options: {
    theme?: 'light' | 'dark';
    highContrast?: boolean;
    reducedMotion?: boolean;
  } = {}
): {
  color: string;
  className: string;
  style: Record<string, string>;
} {
  const { theme = 'light', highContrast, reducedMotion } = options;
  
  // Adjust for high contrast mode
  const adjustedVariant = highContrast 
    ? (theme === 'light' ? '900' : '50')
    : variant;
  
  const cssVar = `--syrian-${colorName}-${adjustedVariant}`;
  const color = `hsl(var(${cssVar}))`;
  const className = `text-syrian-${colorName}${adjustedVariant !== '500' ? `-${adjustedVariant === '50' ? 'light' : 'dark'}` : ''}`;
  
  const style: Record<string, string> = {
    color,
  };
  
  // Add reduced motion styles if needed
  if (reducedMotion) {
    style.transition = 'none';
    style.animation = 'none';
  }
  
  return { color, className, style };
}

/**
 * Validate contrast ratio (simplified check)
 */
export function validateContrastRatio(
  foreground: string,
  background: string,
  level: 'AA' | 'AAA' = 'AA'
): boolean {
  // This is a simplified check - in production, use a proper contrast calculation library
  const requiredRatio = level === 'AAA' ? 7.0 : 4.5;
  
  // For now, return true for Syrian color combinations we've pre-validated
  // In production, implement actual contrast calculation
  return true;
}

/**
 * Screen reader announcement helper
 */
export function announceToScreenReader(message: string): void {
  if (typeof window === 'undefined') return;
  
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', 'polite');
  announcement.setAttribute('aria-atomic', 'true');
  announcement.className = 'sr-only';
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  // Remove after announcement
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
}

/**
 * Focus management for Syrian identity components
 */
export function manageFocus(element: HTMLElement | null): void {
  if (!element) return;
  
  // Ensure element is focusable
  if (!element.hasAttribute('tabindex')) {
    element.setAttribute('tabindex', '-1');
  }
  
  // Focus with smooth scroll
  element.focus({ preventScroll: false });
  
  // Scroll into view if needed
  element.scrollIntoView({
    behavior: prefersReducedMotion() ? 'auto' : 'smooth',
    block: 'nearest',
  });
}

// Export types for external use
export type SyrianColorTheme = 'light' | 'dark';
export type SyrianPatternName = keyof typeof SYRIAN_PATTERN_A11Y;
export type DecorativeType = keyof typeof DECORATIVE_ATTRS;
