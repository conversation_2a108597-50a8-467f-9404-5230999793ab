import { supabase } from './supabase'
import type { Database } from './supabase'
import { 
  UserData, 
  UserFilters, 
  ExpertData, 
  ExpertFilters,
  Problem,
  ProblemFilters,
  ProblemCreateData,
  ProblemUpdateData,
  Solution,
  SolutionFilters,
  SolutionCreateData,
  SolutionUpdateData,
  Vote,
  RatingData,
  DatabaseResult
} from '@/types'

// Type helpers
type Tables = Database['public']['Tables']
type UserRow = Tables['users']['Row']
type ExpertRow = Tables['experts']['Row']
type ProblemRow = Tables['problems']['Row']
type SolutionRow = Tables['solutions']['Row']
type WebinarRow = Tables['webinars']['Row']

// User operations
export const userOperations = {
  async getProfile(userId: string): Promise<DatabaseResult<UserData>> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()
    
    return { data, error }
  },

  async updateProfile(userId: string, updates: Partial<UserRow>): Promise<DatabaseResult<UserData>> {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()
    
    return { data, error }
  },

  async getAllUsers(filters?: UserFilters): Promise<DatabaseResult<UserData[]>> {
    let query = supabase.from('users').select('*')
    
    // By default, exclude deleted users unless specifically requested
    if (!filters?.includeDeleted) {
      query = query.eq('is_deleted', false)
    }
    
    if (filters?.role) {
      query = query.eq('role', filters.role)
    }
    if (filters?.location) {
      query = query.ilike('location', `%${filters.location}%`)
    }
    
    const { data, error } = await query.order('created_at', { ascending: false })
    return { data, error }
  },

  async softDeleteUser(userId: string): Promise<DatabaseResult<boolean>> {
    const { data, error } = await supabase.rpc('soft_delete_user', {
      user_id: userId
    })
    return { data, error }
  },

  async restoreUser(userId: string): Promise<DatabaseResult<boolean>> {
    const { data, error } = await supabase.rpc('restore_user', {
      user_id: userId
    })
    return { data, error }
  }
}

// Expert operations
export const expertOperations = {
  async getExpertProfile(userId: string): Promise<DatabaseResult<ExpertData>> {
    const { data, error } = await supabase
      .from('experts')
      .select(`
        *,
        users (*)
      `)
      .eq('user_id', userId)
      .single()
    
    return { data, error }
  },

  async createExpertProfile(expertData: Tables['experts']['Insert']): Promise<DatabaseResult<ExpertData>> {
    const { data, error } = await supabase
      .from('experts')
      .insert(expertData)
      .select(`
        *,
        users (*)
      `)
      .single()
    
    return { data, error }
  },

  async updateExpertProfile(userId: string, updates: Partial<ExpertRow>): Promise<DatabaseResult<ExpertData>> {
    const { data, error } = await supabase
      .from('experts')
      .update(updates)
      .eq('user_id', userId)
      .select(`
        *,
        users (*)
      `)
      .single()
    
    return { data, error }
  },

  async getAllExperts(filters?: ExpertFilters): Promise<DatabaseResult<ExpertData[]>> {
    let query = supabase
      .from('experts')
      .select(`
        *,
        users (*)
      `)
    
    // By default, exclude deleted experts unless specifically requested
    if (!filters?.includeDeleted) {
      query = query.eq('is_deleted', false)
    }
    
    if (filters?.availability) {
      query = query.eq('availability', filters.availability)
    }
    if (filters?.minRating) {
      query = query.gte('rating', filters.minRating)
    }
    if (filters?.expertise) {
      query = query.contains('expertise_areas', [{ category: filters.expertise }])
    }
    
    const { data, error } = await query.order('rating', { ascending: false })
    return { data, error }
  }
}

// Problem operations
export const problemOperations = {
  async createProblem(problemData: Tables['problems']['Insert']): Promise<DatabaseResult<Problem>> {
    const { data, error } = await supabase
      .from('problems')
      .insert(problemData)
      .select()
      .single()
    
    return { data, error }
  },

  async getProblem(problemId: string): Promise<DatabaseResult<Problem>> {
    const { data, error } = await supabase
      .from('problems')
      .select(`
        *,
        users!problems_submitted_by_fkey (*),
        solutions (
          *,
          users!solutions_expert_id_fkey (*)
        )
      `)
      .eq('id', problemId)
      .single()
    
    return { data, error }
  },

  async getAllProblems(filters?: ProblemFilters): Promise<DatabaseResult<Problem[]>> {
    let query = supabase
      .from('problems')
      .select(`
        *,
        users!problems_submitted_by_fkey (name, organization)
      `)
    
    // By default, exclude deleted problems unless specifically requested
    if (!filters?.includeDeleted) {
      query = query.eq('is_deleted', false)
    }
    
    if (filters?.status) {
      query = query.eq('status', filters.status)
    }
    if (filters?.urgency) {
      query = query.eq('urgency', filters.urgency)
    }
    if (filters?.category) {
      query = query.eq('category', filters.category)
    }
    if (filters?.sector) {
      query = query.eq('sector', filters.sector)
    }
    if (filters?.submittedBy) {
      query = query.eq('submitted_by', filters.submittedBy)
    }
    
    const { data, error } = await query.order('created_at', { ascending: false })
    return { data, error }
  },

  async updateProblem(problemId: string, updates: Partial<ProblemRow>): Promise<DatabaseResult<Problem>> {
    const { data, error } = await supabase
      .from('problems')
      .update(updates)
      .eq('id', problemId)
      .select()
      .single()
    
    return { data, error }
  },

  async searchProblems(searchTerm: string, language: 'ar' | 'en' = 'ar', includeDeleted: boolean = false): Promise<DatabaseResult<Problem[]>> {
    const config = language === 'ar' ? 'arabic' : 'english'
    
    let query = supabase
      .from('problems')
      .select(`
        *,
        users!problems_submitted_by_fkey (name, organization)
      `)
      .textSearch('title', searchTerm, { config })
    
    // By default, exclude deleted problems unless specifically requested
    if (!includeDeleted) {
      query = query.eq('is_deleted', false)
    }
    
    const { data, error } = await query.order('created_at', { ascending: false })
    return { data, error }
  },

  async softDeleteProblem(problemId: string): Promise<DatabaseResult<boolean>> {
    const { data, error } = await supabase.rpc('soft_delete_problem', {
      problem_id: problemId
    })
    return { data, error }
  },

  async restoreProblem(problemId: string): Promise<DatabaseResult<boolean>> {
    const { data, error } = await supabase.rpc('restore_problem', {
      problem_id: problemId
    })
    return { data, error }
  }
}

// Solution operations
export const solutionOperations = {
  async createSolution(solutionData: Tables['solutions']['Insert']): Promise<DatabaseResult<Solution>> {
    const { data, error } = await supabase
      .from('solutions')
      .insert(solutionData)
      .select()
      .single()
    
    return { data, error }
  },

  async getSolutionsForProblem(problemId: string, includeDeleted: boolean = false): Promise<DatabaseResult<Solution[]>> {
    let query = supabase
      .from('solutions')
      .select(`
        *,
        users!solutions_expert_id_fkey (*)
      `)
      .eq('problem_id', problemId)
    
    // By default, exclude deleted solutions unless specifically requested
    if (!includeDeleted) {
      query = query.eq('is_deleted', false)
    }
    
    const { data, error } = await query.order('created_at', { ascending: false })
    return { data, error }
  },

  async updateSolution(solutionId: string, updates: Partial<SolutionRow>): Promise<DatabaseResult<Solution>> {
    const { data, error } = await supabase
      .from('solutions')
      .update(updates)
      .eq('id', solutionId)
      .select()
      .single()
    
    return { data, error }
  },

  async voteSolution(solutionId: string, userId: string, voteType: 'up' | 'down'): Promise<DatabaseResult<Solution>> {
    // First get current votes
    const { data: solution, error: fetchError } = await supabase
      .from('solutions')
      .select('votes')
      .eq('id', solutionId)
      .single()
    
    if (fetchError) return { data: null, error: fetchError }
    
    const votes = (solution.votes as Vote[]) || []
    const existingVoteIndex = votes.findIndex((vote: Vote) => vote.userId === userId)
    
    if (existingVoteIndex >= 0) {
      // Update existing vote
      votes[existingVoteIndex].type = voteType
    } else {
      // Add new vote
      votes.push({ userId, type: voteType, createdAt: new Date().toISOString() })
    }
    
    const { data, error } = await supabase
      .from('solutions')
      .update({ votes })
      .eq('id', solutionId)
      .select()
      .single()
    
    return { data, error }
  },

  async getSolutionsForExpert(expertId: string, includeDeleted: boolean = false): Promise<DatabaseResult<Solution[]>> {
    let query = supabase
      .from('solutions')
      .select(`
        *,
        problems (
          id,
          title,
          category,
          sector,
          urgency,
          status
        )
      `)
      .eq('expert_id', expertId)
    
    // By default, exclude deleted solutions unless specifically requested
    if (!includeDeleted) {
      query = query.eq('is_deleted', false)
    }
    
    const { data, error } = await query.order('created_at', { ascending: false })
    return { data, error }
  },

  async rateSolution(solutionId: string, ratingData: RatingData): Promise<DatabaseResult<Solution>> {
    // First get current ratings
    const { data: solution, error: fetchError } = await supabase
      .from('solutions')
      .select('rating, votes')
      .eq('id', solutionId)
      .single()
    
    if (fetchError) return { data: null, error: fetchError }
    
    // Add or update rating in votes array
    const votes = (solution.votes as Vote[]) || []
    const existingRatingIndex = votes.findIndex((vote: Vote) => vote.userId === ratingData.userId && vote.type === 'rating')
    
    const ratingVote: Vote = {
      userId: ratingData.userId,
      type: 'rating' as const,
      rating: ratingData.rating,
      feedback: ratingData.feedback,
      createdAt: new Date().toISOString()
    }
    
    if (existingRatingIndex >= 0) {
      votes[existingRatingIndex] = ratingVote
    } else {
      votes.push(ratingVote)
    }
    
    // Calculate new average rating
    const ratings = votes.filter((vote: Vote) => vote.type === 'rating' && vote.rating)
    const avgRating = ratings.length > 0 
      ? ratings.reduce((sum: number, vote: Vote) => sum + (vote.rating || 0), 0) / ratings.length 
      : 0
    
    const { data, error } = await supabase
      .from('solutions')
      .update({ 
        votes,
        rating: avgRating,
        updated_at: new Date().toISOString()
      })
      .eq('id', solutionId)
      .select()
      .single()
    
    return { data, error }
  },

  async softDeleteSolution(solutionId: string): Promise<DatabaseResult<boolean>> {
    const { data, error } = await supabase.rpc('soft_delete_solution', {
      solution_id: solutionId
    })
    return { data, error }
  }
}

// Webinar operations
export const webinarOperations = {
  async createWebinar(webinarData: Tables['webinars']['Insert']): Promise<DatabaseResult<WebinarRow>> {
    const { data, error } = await supabase
      .from('webinars')
      .insert(webinarData)
      .select()
      .single()
    
    return { data, error }
  },

  async getWebinar(webinarId: string): Promise<DatabaseResult<WebinarRow>> {
    const { data, error } = await supabase
      .from('webinars')
      .select('*')
      .eq('id', webinarId)
      .single()
    
    return { data, error }
  },

  async getAllWebinars(filters?: {
    status?: string
    category?: string
    upcoming?: boolean
    includeDeleted?: boolean
  }): Promise<DatabaseResult<WebinarRow[]>> {
    let query = supabase.from('webinars').select('*')
    
    // By default, exclude deleted webinars unless specifically requested
    if (!filters?.includeDeleted) {
      query = query.eq('is_deleted', false)
    }
    
    if (filters?.status) {
      query = query.eq('status', filters.status)
    }
    if (filters?.category) {
      query = query.eq('category', filters.category)
    }
    if (filters?.upcoming) {
      query = query.gte('scheduled_at', new Date().toISOString())
    }
    
    const { data, error } = await query.order('scheduled_at', { ascending: true })
    return { data, error }
  },

  async updateWebinar(webinarId: string, updates: Partial<WebinarRow>): Promise<DatabaseResult<WebinarRow>> {
    const { data, error } = await supabase
      .from('webinars')
      .update(updates)
      .eq('id', webinarId)
      .select()
      .single()
    
    return { data, error }
  },

  async softDeleteWebinar(webinarId: string): Promise<DatabaseResult<boolean>> {
    const { data, error } = await supabase.rpc('soft_delete_webinar', {
      webinar_id: webinarId
    })
    return { data, error }
  }
}

// Real-time subscriptions
export const subscriptions = {
  subscribeToProblems(callback: (payload: { eventType: string; new: Problem; old: Problem }) => void) {
    return supabase
      .channel('problems')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'problems' },
        callback
      )
      .subscribe()
  },

  subscribeToSolutions(problemId: string, callback: (payload: { eventType: string; new: Solution; old: Solution }) => void) {
    return supabase
      .channel(`solutions:${problemId}`)
      .on('postgres_changes',
        { 
          event: '*', 
          schema: 'public', 
          table: 'solutions',
          filter: `problem_id=eq.${problemId}`
        },
        callback
      )
      .subscribe()
  },

  subscribeToWebinars(callback: (payload: { eventType: string; new: any; old: any }) => void) {
    return supabase
      .channel('webinars')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'webinars' },
        callback
      )
      .subscribe()
  }
}