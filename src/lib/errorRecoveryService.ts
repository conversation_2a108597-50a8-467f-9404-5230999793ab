import React from 'react';

/**
 * Comprehensive Error Recovery Service
 * Handles different types of errors and provides appropriate recovery strategies
 */

export interface ErrorContext {
  component?: string;
  operation?: string;
  userId?: string;
  timestamp: number;
  userAgent?: string;
  url?: string;
  additionalData?: Record<string, any>;
}

export interface RecoveryStrategy {
  type: 'retry' | 'fallback' | 'redirect' | 'refresh' | 'ignore';
  maxAttempts?: number;
  delay?: number;
  exponentialBackoff?: boolean;
  fallbackComponent?: string;
  redirectUrl?: string;
  condition?: (error: Error, context: ErrorContext) => boolean;
}

export interface ErrorPattern {
  name: string;
  matcher: (error: Error) => boolean;
  strategy: RecoveryStrategy;
  priority: number;
}

export class ErrorRecoveryService {
  private static instance: ErrorRecoveryService;
  private errorPatterns: ErrorPattern[] = [];
  private errorHistory: Map<string, { count: number; lastOccurrence: number }> = new Map();
  private recoveryAttempts: Map<string, number> = new Map();

  private constructor() {
    this.initializeDefaultPatterns();
  }

  static getInstance(): ErrorRecoveryService {
    if (!ErrorRecoveryService.instance) {
      ErrorRecoveryService.instance = new ErrorRecoveryService();
    }
    return ErrorRecoveryService.instance;
  }

  private initializeDefaultPatterns() {
    // Network errors
    this.addErrorPattern({
      name: 'NetworkError',
      matcher: (error) => 
        error.message.includes('fetch') ||
        error.message.includes('network') ||
        error.message.includes('Failed to fetch') ||
        error.name === 'NetworkError',
      strategy: {
        type: 'retry',
        maxAttempts: 3,
        delay: 1000,
        exponentialBackoff: true
      },
      priority: 1
    });

    // Authentication errors
    this.addErrorPattern({
      name: 'AuthenticationError',
      matcher: (error) =>
        error.message.includes('401') ||
        error.message.includes('Unauthorized') ||
        error.message.includes('authentication'),
      strategy: {
        type: 'redirect',
        redirectUrl: '/auth/login'
      },
      priority: 2
    });

    // Permission errors
    this.addErrorPattern({
      name: 'PermissionError',
      matcher: (error) =>
        error.message.includes('403') ||
        error.message.includes('Forbidden') ||
        error.message.includes('permission'),
      strategy: {
        type: 'fallback',
        fallbackComponent: 'PermissionErrorFallback'
      },
      priority: 2
    });

    // Import/Module errors
    this.addErrorPattern({
      name: 'ModuleError',
      matcher: (error) =>
        error.message.includes('Cannot resolve module') ||
        error.message.includes('Module not found') ||
        error.message.includes('import') ||
        error.name === 'ModuleError',
      strategy: {
        type: 'fallback',
        fallbackComponent: 'ComponentErrorFallback'
      },
      priority: 3
    });

    // Translation errors
    this.addErrorPattern({
      name: 'TranslationError',
      matcher: (error) =>
        error.message.includes('translation') ||
        error.message.includes('i18n') ||
        error.message.includes('locale'),
      strategy: {
        type: 'fallback',
        fallbackComponent: 'TranslationErrorFallback'
      },
      priority: 4
    });

    // Database/API errors
    this.addErrorPattern({
      name: 'DatabaseError',
      matcher: (error) =>
        error.message.includes('database') ||
        error.message.includes('SQL') ||
        error.message.includes('connection') ||
        error.message.includes('timeout'),
      strategy: {
        type: 'retry',
        maxAttempts: 2,
        delay: 2000,
        exponentialBackoff: false
      },
      priority: 2
    });

    // Chunk loading errors (code splitting)
    this.addErrorPattern({
      name: 'ChunkLoadError',
      matcher: (error) =>
        error.message.includes('Loading chunk') ||
        error.message.includes('ChunkLoadError') ||
        error.name === 'ChunkLoadError',
      strategy: {
        type: 'refresh'
      },
      priority: 1
    });

    // Memory errors
    this.addErrorPattern({
      name: 'MemoryError',
      matcher: (error) =>
        error.message.includes('out of memory') ||
        error.message.includes('Maximum call stack'),
      strategy: {
        type: 'refresh'
      },
      priority: 1
    });

    // Generic server errors
    this.addErrorPattern({
      name: 'ServerError',
      matcher: (error) =>
        error.message.includes('500') ||
        error.message.includes('502') ||
        error.message.includes('503') ||
        error.message.includes('504'),
      strategy: {
        type: 'retry',
        maxAttempts: 2,
        delay: 3000,
        exponentialBackoff: true
      },
      priority: 3
    });
  }

  addErrorPattern(pattern: ErrorPattern) {
    this.errorPatterns.push(pattern);
    this.errorPatterns.sort((a, b) => a.priority - b.priority);
  }

  removeErrorPattern(name: string) {
    this.errorPatterns = this.errorPatterns.filter(p => p.name !== name);
  }

  async handleError(
    error: Error,
    context: ErrorContext
  ): Promise<RecoveryStrategy | null> {
    const errorKey = this.generateErrorKey(error, context);
    
    // Update error history
    this.updateErrorHistory(errorKey);
    
    // Find matching pattern
    const pattern = this.findMatchingPattern(error);
    if (!pattern) {
      return null;
    }

    // Check if we should apply the strategy
    if (pattern.strategy.condition && !pattern.strategy.condition(error, context)) {
      return null;
    }

    // Check retry limits
    if (pattern.strategy.type === 'retry') {
      const attempts = this.recoveryAttempts.get(errorKey) || 0;
      if (attempts >= (pattern.strategy.maxAttempts || 3)) {
        // Switch to fallback strategy if retries exhausted
        return {
          type: 'fallback',
          fallbackComponent: 'MaxRetriesErrorFallback'
        };
      }
      this.recoveryAttempts.set(errorKey, attempts + 1);
    }

    // Log error for monitoring
    this.logError(error, context, pattern);

    return pattern.strategy;
  }

  private findMatchingPattern(error: Error): ErrorPattern | null {
    return this.errorPatterns.find(pattern => pattern.matcher(error)) || null;
  }

  private generateErrorKey(error: Error, context: ErrorContext): string {
    return `${error.name}-${error.message.substring(0, 50)}-${context.component || 'unknown'}`;
  }

  private updateErrorHistory(errorKey: string) {
    const existing = this.errorHistory.get(errorKey);
    this.errorHistory.set(errorKey, {
      count: (existing?.count || 0) + 1,
      lastOccurrence: Date.now()
    });
  }

  private logError(error: Error, context: ErrorContext, pattern: ErrorPattern) {
    const logData = {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack
      },
      context,
      pattern: pattern.name,
      timestamp: new Date().toISOString()
    };

    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 Error Recovery: ${pattern.name}`);
      console.error('Error:', error);
      console.log('Context:', context);
      console.log('Strategy:', pattern.strategy);
      console.groupEnd();
    }

    // In production, send to error tracking service
    if (process.env.NODE_ENV === 'production') {
      // Send to error tracking service (e.g., Sentry, LogRocket)
      this.sendToErrorTracking(logData);
    }
  }

  private sendToErrorTracking(logData: any) {
    // Placeholder for error tracking integration
    // This would integrate with services like Sentry, LogRocket, etc.
    try {
      // Example: Sentry.captureException(logData.error, { extra: logData });
      console.log('Error logged to tracking service:', logData);
    } catch (trackingError) {
      console.error('Failed to log error to tracking service:', trackingError);
    }
  }

  // Utility methods for components
  getErrorHistory(): Map<string, { count: number; lastOccurrence: number }> {
    return new Map(this.errorHistory);
  }

  clearErrorHistory(errorKey?: string) {
    if (errorKey) {
      this.errorHistory.delete(errorKey);
      this.recoveryAttempts.delete(errorKey);
    } else {
      this.errorHistory.clear();
      this.recoveryAttempts.clear();
    }
  }

  getRecoveryAttempts(errorKey: string): number {
    return this.recoveryAttempts.get(errorKey) || 0;
  }

  // Health check method
  getSystemHealth(): {
    totalErrors: number;
    recentErrors: number;
    topErrors: Array<{ key: string; count: number }>;
    recoverySuccess: number;
  } {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);
    
    let recentErrors = 0;
    const errorCounts: Array<{ key: string; count: number }> = [];

    this.errorHistory.forEach((data, key) => {
      if (data.lastOccurrence > oneHourAgo) {
        recentErrors++;
      }
      errorCounts.push({ key, count: data.count });
    });

    errorCounts.sort((a, b) => b.count - a.count);

    return {
      totalErrors: this.errorHistory.size,
      recentErrors,
      topErrors: errorCounts.slice(0, 5),
      recoverySuccess: this.calculateRecoverySuccessRate()
    };
  }

  private calculateRecoverySuccessRate(): number {
    // This would be more sophisticated in a real implementation
    // For now, return a placeholder value
    return 0.85; // 85% success rate
  }
}

// React hook for using the error recovery service
export function useErrorRecovery() {
  const service = ErrorRecoveryService.getInstance();

  const handleError = async (error: Error, context: Partial<ErrorContext> = {}) => {
    const fullContext: ErrorContext = {
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      ...context
    };

    return service.handleError(error, fullContext);
  };

  const clearHistory = (errorKey?: string) => {
    service.clearErrorHistory(errorKey);
  };

  const getHealth = () => {
    return service.getSystemHealth();
  };

  return {
    handleError,
    clearHistory,
    getHealth,
    service
  };
}

// Error boundary integration
export function createErrorRecoveryBoundary(
  component: string,
  fallbackComponent?: React.ComponentType<any>
) {
  return class ErrorRecoveryBoundary extends React.Component<
    { children: React.ReactNode },
    { hasError: boolean; error?: Error; strategy?: RecoveryStrategy }
  > {
    private service = ErrorRecoveryService.getInstance();

    constructor(props: { children: React.ReactNode }) {
      super(props);
      this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error) {
      return { hasError: true, error };
    }

    async componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
      const context: ErrorContext = {
        component,
        timestamp: Date.now(),
        url: window.location.href,
        additionalData: errorInfo
      };

      const strategy = await this.service.handleError(error, context);
      
      if (strategy) {
        this.setState({ strategy });
        
        // Execute strategy
        switch (strategy.type) {
          case 'refresh':
            setTimeout(() => window.location.reload(), 1000);
            break;
          case 'redirect':
            if (strategy.redirectUrl) {
              setTimeout(() => window.location.href = strategy.redirectUrl!, 1000);
            }
            break;
          case 'retry':
            // This would trigger a component re-render
            setTimeout(() => this.setState({ hasError: false, error: undefined }), strategy.delay || 1000);
            break;
        }
      }
    }

    render() {
      if (this.state.hasError) {
        if (fallbackComponent) {
          const FallbackComponent = fallbackComponent;
          return React.createElement(FallbackComponent, {
            error: this.state.error,
            strategy: this.state.strategy,
            onRetry: () => this.setState({ hasError: false, error: undefined })
          });
        }

        return React.createElement('div', {
          className: 'error-boundary-fallback'
        }, 'Something went wrong.');
      }

      return this.props.children;
    }
  };
}

export default ErrorRecoveryService;