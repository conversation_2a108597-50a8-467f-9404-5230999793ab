import { QueryClient, QueryCache, MutationCache } from '@tanstack/react-query';
import { toast } from 'sonner';
import { errorLogger, logApiError } from './errorLogger';

// Cache configuration constants
export const CACHE_TIMES = {
  // Static data that rarely changes
  STATIC: 1000 * 60 * 60 * 24, // 24 hours
  // User-specific data
  USER: 1000 * 60 * 30, // 30 minutes
  // Dynamic content that changes frequently
  DYNAMIC: 1000 * 60 * 5, // 5 minutes
  // Real-time data
  REALTIME: 1000 * 60, // 1 minute
  // Search results
  SEARCH: 1000 * 60 * 10, // 10 minutes
} as const;

export const STALE_TIMES = {
  STATIC: 1000 * 60 * 60 * 12, // 12 hours
  USER: 1000 * 60 * 15, // 15 minutes
  DYNAMIC: 1000 * 60 * 2, // 2 minutes
  REALTIME: 1000 * 30, // 30 seconds
  SEARCH: 1000 * 60 * 5, // 5 minutes
} as const;

// Query key factories for consistent cache management
export const queryKeys = {
  // User-related queries
  user: {
    all: ['users'] as const,
    profile: (id: string) => [...queryKeys.user.all, 'profile', id] as const,
    preferences: (id: string) => [...queryKeys.user.all, 'preferences', id] as const,
    activity: (id: string) => [...queryKeys.user.all, 'activity', id] as const,
  },
  
  // Problem-related queries
  problems: {
    all: ['problems'] as const,
    lists: () => [...queryKeys.problems.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.problems.lists(), filters] as const,
    details: () => [...queryKeys.problems.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.problems.details(), id] as const,
    solutions: (id: string) => [...queryKeys.problems.detail(id), 'solutions'] as const,
    comments: (id: string) => [...queryKeys.problems.detail(id), 'comments'] as const,
  },
  
  // Expert-related queries
  experts: {
    all: ['experts'] as const,
    lists: () => [...queryKeys.experts.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.experts.lists(), filters] as const,
    details: () => [...queryKeys.experts.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.experts.details(), id] as const,
    reviews: (id: string) => [...queryKeys.experts.detail(id), 'reviews'] as const,
    availability: (id: string) => [...queryKeys.experts.detail(id), 'availability'] as const,
  },
  
  // Search-related queries
  search: {
    all: ['search'] as const,
    results: (query: string, filters: Record<string, any>) => 
      [...queryKeys.search.all, 'results', query, filters] as const,
    suggestions: (query: string) => [...queryKeys.search.all, 'suggestions', query] as const,
    analytics: () => [...queryKeys.search.all, 'analytics'] as const,
  },
  
  // System-related queries
  system: {
    all: ['system'] as const,
    config: () => [...queryKeys.system.all, 'config'] as const,
    stats: () => [...queryKeys.system.all, 'stats'] as const,
    health: () => [...queryKeys.system.all, 'health'] as const,
  },
} as const;

// Enhanced error handling for queries
const handleQueryError = (error: Error, query: { state: { fetchStatus: string; data?: unknown }; queryKey: unknown[] }) => {
  console.error('Query error:', error, query);

  // Log error for monitoring
  logApiError(error, String(query.queryKey[0] || 'unknown'), 'GET');

  // Don't show toast for background refetch errors
  if (query.state.fetchStatus === 'fetching' && query.state.data !== undefined) {
    return;
  }

  // Get user-friendly error message
  const errorMessage = getUserFriendlyErrorMessage(error);
  toast.error(errorMessage, {
    action: {
      label: 'Retry',
      onClick: () => {
        // Retry the query
        queryClient.refetchQueries({ queryKey: query.queryKey });
      },
    },
  });
};

// Enhanced error handling for mutations
const handleMutationError = (error: Error, variables: unknown, context: unknown, mutation: { mutationKey?: unknown[] }) => {
  console.error('Mutation error:', error, { variables, context, mutation });

  // Log error for monitoring
  logApiError(error, String(mutation.mutationKey?.[0] || 'unknown'), 'POST');

  // Get user-friendly error message
  const errorMessage = getUserFriendlyErrorMessage(error);
  toast.error(errorMessage, {
    description: 'Your changes could not be saved. Please try again.',
  });
};

// Get user-friendly error messages
const getUserFriendlyErrorMessage = (error: Error): string => {
  // Check for specific error types
  if (error.message.includes('Network Error') || error.message.includes('fetch')) {
    return 'Network connection error. Please check your internet connection and try again.';
  }

  if (error.message.includes('timeout')) {
    return 'Request timed out. Please try again.';
  }

  if (error.message.includes('401') || error.message.includes('Unauthorized')) {
    return 'Your session has expired. Please log in again.';
  }

  if (error.message.includes('403') || error.message.includes('Forbidden')) {
    return 'You do not have permission to perform this action.';
  }

  if (error.message.includes('404') || error.message.includes('Not Found')) {
    return 'The requested resource was not found.';
  }

  if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
    return 'Server error occurred. Please try again later.';
  }

  if (error.message.includes('503') || error.message.includes('Service Unavailable')) {
    return 'Service is temporarily unavailable. Please try again later.';
  }

  // Default fallback
  return error.message || 'An unexpected error occurred. Please try again.';
};

// Create query cache with error handling
const queryCache = new QueryCache({
  onError: handleQueryError,
});

// Create mutation cache with error handling
const mutationCache = new MutationCache({
  onError: handleMutationError,
});

// Enhanced retry logic for different error types
const shouldRetryError = (failureCount: number, error: any): boolean => {
  // Don't retry if we've exceeded max attempts
  if (failureCount >= 3) {
    return false;
  }

  // Don't retry on client errors (4xx)
  if (error?.status >= 400 && error?.status < 500) {
    return false;
  }

  // Don't retry on authentication errors
  if (error?.status === 401 || error?.status === 403) {
    return false;
  }

  // Don't retry on not found errors
  if (error?.status === 404) {
    return false;
  }

  // Retry on network errors
  if (error?.message?.includes('Network Error') || error?.message?.includes('fetch')) {
    return true;
  }

  // Retry on timeout errors
  if (error?.message?.includes('timeout')) {
    return true;
  }

  // Retry on server errors (5xx)
  if (error?.status >= 500) {
    return true;
  }

  // Default: retry for unknown errors
  return true;
};

// Enhanced retry delay with jitter
const getRetryDelay = (attemptIndex: number, error?: any): number => {
  // Base delay with exponential backoff
  const baseDelay = Math.min(1000 * 2 ** attemptIndex, 30000);

  // Add jitter to prevent thundering herd
  const jitter = Math.random() * 1000;

  // Longer delay for rate limit errors
  if (error?.status === 429) {
    return baseDelay * 2 + jitter;
  }

  return baseDelay + jitter;
};

// Default query options based on data type
export const getDefaultQueryOptions = (type: keyof typeof CACHE_TIMES) => ({
  staleTime: STALE_TIMES[type],
  cacheTime: CACHE_TIMES[type],
  retry: shouldRetryError,
  retryDelay: getRetryDelay,
});

// Create the main query client
export const queryClient = new QueryClient({
  queryCache,
  mutationCache,
  defaultOptions: {
    queries: {
      ...getDefaultQueryOptions('DYNAMIC'),
      refetchOnWindowFocus: false,
      refetchOnMount: true,
      refetchOnReconnect: true,
      // Enable background refetch for better UX
      refetchInterval: false,
      // Network mode for offline support
      networkMode: 'online',
    },
    mutations: {
      retry: (failureCount: number, error: any) => {
        // Don't retry client errors
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        // Retry once for server errors
        return failureCount < 1;
      },
      retryDelay: getRetryDelay,
      networkMode: 'online',
      onSuccess: () => {
        // Optionally show success message
        // toast.success('Changes saved successfully');
      },
      onError: (error: Error, variables: unknown, context: unknown) => {
        // Additional mutation error handling
        console.error('Mutation failed:', error);

        // Log the error
        errorLogger.logError({
          message: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString(),
          source: 'mutation',
          type: 'mutationError',
          context: {
            variables: JSON.stringify(variables),
            context: JSON.stringify(context),
          },
        });
      },
    },
  },
});

// Cache invalidation helpers
export const invalidateQueries = {
  user: (userId?: string) => {
    if (userId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.user.profile(userId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.user.preferences(userId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.user.activity(userId) });
    } else {
      queryClient.invalidateQueries({ queryKey: queryKeys.user.all });
    }
  },
  
  problems: (problemId?: string) => {
    if (problemId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.problems.detail(problemId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.problems.solutions(problemId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.problems.comments(problemId) });
    } else {
      queryClient.invalidateQueries({ queryKey: queryKeys.problems.all });
    }
  },
  
  experts: (expertId?: string) => {
    if (expertId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.experts.detail(expertId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.experts.reviews(expertId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.experts.availability(expertId) });
    } else {
      queryClient.invalidateQueries({ queryKey: queryKeys.experts.all });
    }
  },
  
  search: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.search.all });
  },
  
  all: () => {
    queryClient.invalidateQueries();
  },
};

// Cache prefetching helpers
export const prefetchQueries = {
  userProfile: async (userId: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.user.profile(userId),
      queryFn: () => fetch(`/api/users/${userId}`).then(res => res.json()),
      ...getDefaultQueryOptions('USER'),
    });
  },
  
  problemDetails: async (problemId: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.problems.detail(problemId),
      queryFn: () => fetch(`/api/problems/${problemId}`).then(res => res.json()),
      ...getDefaultQueryOptions('DYNAMIC'),
    });
  },
  
  expertDetails: async (expertId: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.experts.detail(expertId),
      queryFn: () => fetch(`/api/experts/${expertId}`).then(res => res.json()),
      ...getDefaultQueryOptions('USER'),
    });
  },
};

// Cache persistence configuration (for offline support)
export const persistOptions = {
  maxAge: 1000 * 60 * 60 * 24 * 7, // 7 days
  buster: 'v1', // Increment to invalidate all persisted cache
};

export default queryClient;