import { describe, it, expect, beforeEach, vi } from 'vitest';
import { QueryClient } from '@tanstack/react-query';
import { 
  queryClient, 
  queryKeys, 
  getDefaultQueryOptions, 
  invalidateQueries,
  prefetchQueries,
  CACHE_TIMES,
  STALE_TIMES 
} from '../queryClient';

// Mock Supabase
vi.mock('@/lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(() => Promise.resolve({ data: { id: '1', name: 'Test' }, error: null }))
        }))
      }))
    }))
  }
}));

describe('QueryClient Configuration', () => {
  beforeEach(() => {
    queryClient.clear();
  });

  it('should have correct cache times configured', () => {
    expect(CACHE_TIMES.STATIC).toBe(1000 * 60 * 60 * 24); // 24 hours
    expect(CACHE_TIMES.USER).toBe(1000 * 60 * 30); // 30 minutes
    expect(CACHE_TIMES.DYNAMIC).toBe(1000 * 60 * 5); // 5 minutes
    expect(CACHE_TIMES.REALTIME).toBe(1000 * 60); // 1 minute
    expect(CACHE_TIMES.SEARCH).toBe(1000 * 60 * 10); // 10 minutes
  });

  it('should have correct stale times configured', () => {
    expect(STALE_TIMES.STATIC).toBe(1000 * 60 * 60 * 12); // 12 hours
    expect(STALE_TIMES.USER).toBe(1000 * 60 * 15); // 15 minutes
    expect(STALE_TIMES.DYNAMIC).toBe(1000 * 60 * 2); // 2 minutes
    expect(STALE_TIMES.REALTIME).toBe(1000 * 30); // 30 seconds
    expect(STALE_TIMES.SEARCH).toBe(1000 * 60 * 5); // 5 minutes
  });

  it('should return correct default query options', () => {
    const staticOptions = getDefaultQueryOptions('STATIC');
    expect(staticOptions.staleTime).toBe(STALE_TIMES.STATIC);
    expect(staticOptions.cacheTime).toBe(CACHE_TIMES.STATIC);
    expect(typeof staticOptions.retry).toBe('function');
    expect(typeof staticOptions.retryDelay).toBe('function');

    const dynamicOptions = getDefaultQueryOptions('DYNAMIC');
    expect(dynamicOptions.staleTime).toBe(STALE_TIMES.DYNAMIC);
    expect(dynamicOptions.cacheTime).toBe(CACHE_TIMES.DYNAMIC);
  });

  it('should have proper retry logic', () => {
    const options = getDefaultQueryOptions('DYNAMIC');
    const retryFn = options.retry as Function;

    // Should not retry on 4xx errors
    expect(retryFn(1, { status: 400 })).toBe(false);
    expect(retryFn(1, { status: 404 })).toBe(false);
    expect(retryFn(1, { status: 422 })).toBe(false);

    // Should retry on 5xx errors up to 3 times
    expect(retryFn(1, { status: 500 })).toBe(true);
    expect(retryFn(2, { status: 500 })).toBe(true);
    expect(retryFn(3, { status: 500 })).toBe(false);

    // Should retry on network errors
    expect(retryFn(1, new Error('Network error'))).toBe(true);
    expect(retryFn(4, new Error('Network error'))).toBe(false);
  });

  it('should have exponential backoff retry delay', () => {
    const options = getDefaultQueryOptions('DYNAMIC');
    const retryDelayFn = options.retryDelay as Function;

    expect(retryDelayFn(0)).toBe(1000); // 2^0 * 1000
    expect(retryDelayFn(1)).toBe(2000); // 2^1 * 1000
    expect(retryDelayFn(2)).toBe(4000); // 2^2 * 1000
    expect(retryDelayFn(10)).toBe(30000); // Max 30 seconds
  });
});

describe('Query Keys', () => {
  it('should generate consistent user query keys', () => {
    const userId = 'user123';
    
    expect(queryKeys.user.all).toEqual(['users']);
    expect(queryKeys.user.profile(userId)).toEqual(['users', 'profile', userId]);
    expect(queryKeys.user.preferences(userId)).toEqual(['users', 'preferences', userId]);
    expect(queryKeys.user.activity(userId)).toEqual(['users', 'activity', userId]);
  });

  it('should generate consistent problem query keys', () => {
    const problemId = 'problem123';
    const filters = { category: 'tech', status: 'open' };
    
    expect(queryKeys.problems.all).toEqual(['problems']);
    expect(queryKeys.problems.lists()).toEqual(['problems', 'list']);
    expect(queryKeys.problems.list(filters)).toEqual(['problems', 'list', filters]);
    expect(queryKeys.problems.detail(problemId)).toEqual(['problems', 'detail', problemId]);
    expect(queryKeys.problems.solutions(problemId)).toEqual(['problems', 'detail', problemId, 'solutions']);
  });

  it('should generate consistent expert query keys', () => {
    const expertId = 'expert123';
    const filters = { specialization: 'javascript' };
    
    expect(queryKeys.experts.all).toEqual(['experts']);
    expect(queryKeys.experts.list(filters)).toEqual(['experts', 'list', filters]);
    expect(queryKeys.experts.detail(expertId)).toEqual(['experts', 'detail', expertId]);
    expect(queryKeys.experts.reviews(expertId)).toEqual(['experts', 'detail', expertId, 'reviews']);
  });

  it('should generate consistent search query keys', () => {
    const query = 'javascript';
    const filters = { type: 'problem' };
    
    expect(queryKeys.search.all).toEqual(['search']);
    expect(queryKeys.search.results(query, filters)).toEqual(['search', 'results', query, filters]);
    expect(queryKeys.search.suggestions(query)).toEqual(['search', 'suggestions', query]);
  });
});

describe('Cache Invalidation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    queryClient.clear();
  });

  it('should invalidate user queries correctly', () => {
    const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');
    const userId = 'user123';

    invalidateQueries.user(userId);

    expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: queryKeys.user.profile(userId) });
    expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: queryKeys.user.preferences(userId) });
    expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: queryKeys.user.activity(userId) });
  });

  it('should invalidate all user queries when no userId provided', () => {
    const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');

    invalidateQueries.user();

    expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: queryKeys.user.all });
  });

  it('should invalidate problem queries correctly', () => {
    const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');
    const problemId = 'problem123';

    invalidateQueries.problems(problemId);

    expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: queryKeys.problems.detail(problemId) });
    expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: queryKeys.problems.solutions(problemId) });
    expect(invalidateQueriesSpy).toHaveBeenCalledWith({ queryKey: queryKeys.problems.comments(problemId) });
  });

  it('should invalidate all queries', () => {
    const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');

    invalidateQueries.all();

    expect(invalidateQueriesSpy).toHaveBeenCalledWith();
  });
});

describe('Cache Prefetching', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    queryClient.clear();
  });

  it('should prefetch user profile', async () => {
    const prefetchQuerySpy = vi.spyOn(queryClient, 'prefetchQuery');
    const userId = 'user123';

    // Mock fetch
    global.fetch = vi.fn(() =>
      Promise.resolve({
        json: () => Promise.resolve({ id: userId, name: 'Test User' }),
      })
    ) as any;

    await prefetchQueries.userProfile(userId);

    expect(prefetchQuerySpy).toHaveBeenCalledWith({
      queryKey: queryKeys.user.profile(userId),
      queryFn: expect.any(Function),
      ...getDefaultQueryOptions('USER'),
    });
  });

  it('should prefetch problem details', async () => {
    const prefetchQuerySpy = vi.spyOn(queryClient, 'prefetchQuery');
    const problemId = 'problem123';

    // Mock fetch
    global.fetch = vi.fn(() =>
      Promise.resolve({
        json: () => Promise.resolve({ id: problemId, title: 'Test Problem' }),
      })
    ) as any;

    await prefetchQueries.problemDetails(problemId);

    expect(prefetchQuerySpy).toHaveBeenCalledWith({
      queryKey: queryKeys.problems.detail(problemId),
      queryFn: expect.any(Function),
      ...getDefaultQueryOptions('DYNAMIC'),
    });
  });

  it('should prefetch expert details', async () => {
    const prefetchQuerySpy = vi.spyOn(queryClient, 'prefetchQuery');
    const expertId = 'expert123';

    // Mock fetch
    global.fetch = vi.fn(() =>
      Promise.resolve({
        json: () => Promise.resolve({ id: expertId, name: 'Test Expert' }),
      })
    ) as any;

    await prefetchQueries.expertDetails(expertId);

    expect(prefetchQuerySpy).toHaveBeenCalledWith({
      queryKey: queryKeys.experts.detail(expertId),
      queryFn: expect.any(Function),
      ...getDefaultQueryOptions('USER'),
    });
  });
});

describe('QueryClient Instance', () => {
  it('should be properly configured', () => {
    expect(queryClient).toBeInstanceOf(QueryClient);
    
    const defaultOptions = queryClient.getDefaultOptions();
    expect(defaultOptions.queries?.refetchOnWindowFocus).toBe(false);
    expect(defaultOptions.queries?.refetchOnMount).toBe(true);
    expect(defaultOptions.queries?.refetchOnReconnect).toBe(true);
    expect(defaultOptions.queries?.networkMode).toBe('online');
    expect(defaultOptions.mutations?.retry).toBe(1);
    expect(defaultOptions.mutations?.networkMode).toBe('online');
  });

  it('should handle query cache properly', () => {
    const testData = { id: '1', name: 'Test' };
    const queryKey = ['test'];

    queryClient.setQueryData(queryKey, testData);
    const cachedData = queryClient.getQueryData(queryKey);

    expect(cachedData).toEqual(testData);
  });

  it('should handle query removal', () => {
    const testData = { id: '1', name: 'Test' };
    const queryKey = ['test'];

    queryClient.setQueryData(queryKey, testData);
    expect(queryClient.getQueryData(queryKey)).toEqual(testData);

    queryClient.removeQueries({ queryKey });
    expect(queryClient.getQueryData(queryKey)).toBeUndefined();
  });
});