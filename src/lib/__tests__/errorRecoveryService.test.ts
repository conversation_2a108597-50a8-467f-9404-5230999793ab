import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import ErrorRecoveryService, { 
  useErrorRecovery, 
  createErrorRecoveryBoundary,
  type ErrorContext,
  type RecoveryStrategy,
  type ErrorPattern
} from '../errorRecoveryService';

// Mock React for the boundary tests
vi.mock('react', () => ({
  Component: class Component {
    constructor(props: any) {}
    render() { return null; }
  },
  createElement: vi.fn()
}));

describe('ErrorRecoveryService', () => {
  let service: ErrorRecoveryService;

  beforeEach(() => {
    // Reset singleton instance
    (ErrorRecoveryService as any).instance = undefined;
    service = ErrorRecoveryService.getInstance();
  });

  afterEach(() => {
    vi.clearAllMocks();
    vi.restoreAllMocks();
  });

  describe('Singleton pattern', () => {
    it('returns the same instance', () => {
      const instance1 = ErrorRecoveryService.getInstance();
      const instance2 = ErrorRecoveryService.getInstance();
      expect(instance1).toBe(instance2);
    });
  });

  describe('Error pattern matching', () => {
    it('matches network errors correctly', async () => {
      const networkError = new Error('Failed to fetch');
      const context: ErrorContext = {
        component: 'TestComponent',
        timestamp: Date.now()
      };

      const strategy = await service.handleError(networkError, context);
      
      expect(strategy).toBeDefined();
      expect(strategy?.type).toBe('retry');
      expect(strategy?.maxAttempts).toBe(3);
      expect(strategy?.exponentialBackoff).toBe(true);
    });

    it('matches authentication errors correctly', async () => {
      const authError = new Error('401 Unauthorized');
      const context: ErrorContext = {
        component: 'TestComponent',
        timestamp: Date.now()
      };

      const strategy = await service.handleError(authError, context);
      
      expect(strategy).toBeDefined();
      expect(strategy?.type).toBe('redirect');
      expect(strategy?.redirectUrl).toBe('/auth/login');
    });

    it('matches permission errors correctly', async () => {
      const permissionError = new Error('403 Forbidden');
      const context: ErrorContext = {
        component: 'TestComponent',
        timestamp: Date.now()
      };

      const strategy = await service.handleError(permissionError, context);
      
      expect(strategy).toBeDefined();
      expect(strategy?.type).toBe('fallback');
      expect(strategy?.fallbackComponent).toBe('PermissionErrorFallback');
    });

    it('matches module errors correctly', async () => {
      const moduleError = new Error('Cannot resolve module');
      const context: ErrorContext = {
        component: 'TestComponent',
        timestamp: Date.now()
      };

      const strategy = await service.handleError(moduleError, context);
      
      expect(strategy).toBeDefined();
      expect(strategy?.type).toBe('fallback');
      expect(strategy?.fallbackComponent).toBe('ComponentErrorFallback');
    });

    it('matches chunk load errors correctly', async () => {
      const chunkError = new Error('Loading chunk 1 failed');
      const context: ErrorContext = {
        component: 'TestComponent',
        timestamp: Date.now()
      };

      const strategy = await service.handleError(chunkError, context);
      
      expect(strategy).toBeDefined();
      expect(strategy?.type).toBe('refresh');
    });

    it('matches database errors correctly', async () => {
      const dbError = new Error('Database connection timeout');
      const context: ErrorContext = {
        component: 'TestComponent',
        timestamp: Date.now()
      };

      const strategy = await service.handleError(dbError, context);
      
      expect(strategy).toBeDefined();
      expect(strategy?.type).toBe('retry');
      expect(strategy?.maxAttempts).toBe(2);
      expect(strategy?.exponentialBackoff).toBe(false);
    });

    it('returns null for unmatched errors', async () => {
      const unknownError = new Error('Some unknown error');
      const context: ErrorContext = {
        component: 'TestComponent',
        timestamp: Date.now()
      };

      const strategy = await service.handleError(unknownError, context);
      expect(strategy).toBeNull();
    });
  });

  describe('Retry limit handling', () => {
    it('tracks retry attempts correctly', async () => {
      const networkError = new Error('Failed to fetch');
      const context: ErrorContext = {
        component: 'TestComponent',
        timestamp: Date.now()
      };

      // First attempt
      let strategy = await service.handleError(networkError, context);
      expect(strategy?.type).toBe('retry');

      // Second attempt
      strategy = await service.handleError(networkError, context);
      expect(strategy?.type).toBe('retry');

      // Third attempt
      strategy = await service.handleError(networkError, context);
      expect(strategy?.type).toBe('retry');

      // Fourth attempt should switch to fallback
      strategy = await service.handleError(networkError, context);
      expect(strategy?.type).toBe('fallback');
      expect(strategy?.fallbackComponent).toBe('MaxRetriesErrorFallback');
    });

    it('resets retry count for different errors', async () => {
      const networkError1 = new Error('Failed to fetch from API 1');
      const networkError2 = new Error('Failed to fetch from API 2');
      const context: ErrorContext = {
        component: 'TestComponent',
        timestamp: Date.now()
      };

      // Exhaust retries for first error
      await service.handleError(networkError1, context);
      await service.handleError(networkError1, context);
      await service.handleError(networkError1, context);
      const strategy1 = await service.handleError(networkError1, context);
      expect(strategy1?.type).toBe('fallback');

      // Second error should still allow retries
      const strategy2 = await service.handleError(networkError2, context);
      expect(strategy2?.type).toBe('retry');
    });
  });

  describe('Custom error patterns', () => {
    it('allows adding custom error patterns', async () => {
      const customPattern: ErrorPattern = {
        name: 'CustomError',
        matcher: (error) => error.message.includes('CUSTOM_ERROR'),
        strategy: {
          type: 'fallback',
          fallbackComponent: 'CustomErrorFallback'
        },
        priority: 1
      };

      service.addErrorPattern(customPattern);

      const customError = new Error('CUSTOM_ERROR occurred');
      const context: ErrorContext = {
        component: 'TestComponent',
        timestamp: Date.now()
      };

      const strategy = await service.handleError(customError, context);
      expect(strategy?.type).toBe('fallback');
      expect(strategy?.fallbackComponent).toBe('CustomErrorFallback');
    });

    it('respects pattern priority', async () => {
      const highPriorityPattern: ErrorPattern = {
        name: 'HighPriority',
        matcher: (error) => error.message.includes('PRIORITY_TEST'),
        strategy: {
          type: 'refresh'
        },
        priority: 1
      };

      const lowPriorityPattern: ErrorPattern = {
        name: 'LowPriority',
        matcher: (error) => error.message.includes('PRIORITY_TEST'),
        strategy: {
          type: 'fallback',
          fallbackComponent: 'LowPriorityFallback'
        },
        priority: 5
      };

      service.addErrorPattern(lowPriorityPattern);
      service.addErrorPattern(highPriorityPattern);

      const testError = new Error('PRIORITY_TEST error');
      const context: ErrorContext = {
        component: 'TestComponent',
        timestamp: Date.now()
      };

      const strategy = await service.handleError(testError, context);
      expect(strategy?.type).toBe('refresh'); // High priority pattern should match first
    });

    it('allows removing error patterns', async () => {
      const customPattern: ErrorPattern = {
        name: 'RemovablePattern',
        matcher: (error) => error.message.includes('REMOVABLE'),
        strategy: {
          type: 'fallback',
          fallbackComponent: 'RemovableFallback'
        },
        priority: 1
      };

      service.addErrorPattern(customPattern);
      service.removeErrorPattern('RemovablePattern');

      const testError = new Error('REMOVABLE error');
      const context: ErrorContext = {
        component: 'TestComponent',
        timestamp: Date.now()
      };

      const strategy = await service.handleError(testError, context);
      expect(strategy).toBeNull(); // Pattern should be removed
    });
  });

  describe('Error history tracking', () => {
    it('tracks error history correctly', async () => {
      const error = new Error('Test error');
      const context: ErrorContext = {
        component: 'TestComponent',
        timestamp: Date.now()
      };

      await service.handleError(error, context);
      await service.handleError(error, context);

      const history = service.getErrorHistory();
      expect(history.size).toBeGreaterThan(0);
      
      const errorKey = Array.from(history.keys())[0];
      const errorData = history.get(errorKey);
      expect(errorData?.count).toBe(2);
    });

    it('clears error history', async () => {
      const error = new Error('Test error');
      const context: ErrorContext = {
        component: 'TestComponent',
        timestamp: Date.now()
      };

      await service.handleError(error, context);
      service.clearErrorHistory();

      const history = service.getErrorHistory();
      expect(history.size).toBe(0);
    });
  });

  describe('System health monitoring', () => {
    it('provides system health information', async () => {
      const error1 = new Error('Error 1');
      const error2 = new Error('Error 2');
      const context: ErrorContext = {
        component: 'TestComponent',
        timestamp: Date.now()
      };

      await service.handleError(error1, context);
      await service.handleError(error2, context);
      await service.handleError(error1, context); // Duplicate

      const health = service.getSystemHealth();
      
      expect(health.totalErrors).toBeGreaterThan(0);
      expect(health.recentErrors).toBeGreaterThan(0);
      expect(health.topErrors).toBeInstanceOf(Array);
      expect(health.recoverySuccess).toBeGreaterThan(0);
    });
  });

  describe('Conditional strategies', () => {
    it('applies conditional strategies correctly', async () => {
      const conditionalPattern: ErrorPattern = {
        name: 'ConditionalError',
        matcher: (error) => error.message.includes('CONDITIONAL'),
        strategy: {
          type: 'retry',
          condition: (error, context) => context.component === 'AllowedComponent'
        },
        priority: 1
      };

      service.addErrorPattern(conditionalPattern);

      const error = new Error('CONDITIONAL error');
      
      // Should not apply strategy for disallowed component
      const disallowedContext: ErrorContext = {
        component: 'DisallowedComponent',
        timestamp: Date.now()
      };
      
      let strategy = await service.handleError(error, disallowedContext);
      expect(strategy).toBeNull();

      // Should apply strategy for allowed component
      const allowedContext: ErrorContext = {
        component: 'AllowedComponent',
        timestamp: Date.now()
      };
      
      strategy = await service.handleError(error, allowedContext);
      expect(strategy?.type).toBe('retry');
    });
  });

  describe('Development vs Production behavior', () => {
    const originalEnv = process.env.NODE_ENV;

    afterEach(() => {
      process.env.NODE_ENV = originalEnv;
    });

    it('logs differently in development vs production', async () => {
      const consoleSpy = vi.spyOn(console, 'group').mockImplementation(() => {});
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      const consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      const consoleGroupEndSpy = vi.spyOn(console, 'groupEnd').mockImplementation(() => {});

      process.env.NODE_ENV = 'development';
      
      const error = new Error('Test error');
      const context: ErrorContext = {
        component: 'TestComponent',
        timestamp: Date.now()
      };

      await service.handleError(error, context);

      expect(consoleSpy).toHaveBeenCalled();
      expect(consoleErrorSpy).toHaveBeenCalled();
      expect(consoleLogSpy).toHaveBeenCalled();
      expect(consoleGroupEndSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
      consoleErrorSpy.mockRestore();
      consoleLogSpy.mockRestore();
      consoleGroupEndSpy.mockRestore();
    });
  });
});

describe('useErrorRecovery hook', () => {
  it('provides error handling functionality', () => {
    const mockHandleError = vi.fn();
    const mockClearHistory = vi.fn();
    const mockGetHealth = vi.fn();

    // Mock the hook implementation
    const mockUseErrorRecovery = () => ({
      handleError: mockHandleError,
      clearHistory: mockClearHistory,
      getHealth: mockGetHealth,
      service: ErrorRecoveryService.getInstance()
    });

    const result = mockUseErrorRecovery();

    expect(result.handleError).toBeDefined();
    expect(result.clearHistory).toBeDefined();
    expect(result.getHealth).toBeDefined();
    expect(result.service).toBeInstanceOf(ErrorRecoveryService);
  });
});

describe('Error boundary integration', () => {
  it('creates error recovery boundary', () => {
    const boundary = createErrorRecoveryBoundary('TestComponent');
    expect(boundary).toBeDefined();
    expect(boundary.name).toBe('ErrorRecoveryBoundary');
  });
});