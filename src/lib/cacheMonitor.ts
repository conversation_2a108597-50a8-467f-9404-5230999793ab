import { QueryClient } from '@tanstack/react-query';
import { queryClient } from './queryClient';

// Cache statistics interface
export interface CacheStats {
  totalQueries: number;
  activeQueries: number;
  staleQueries: number;
  errorQueries: number;
  cacheSize: number;
  hitRate: number;
  missRate: number;
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
}

// Cache performance metrics
export interface CacheMetrics {
  queryKey: string;
  status: 'success' | 'error' | 'loading';
  dataUpdatedAt: number;
  errorUpdatedAt: number;
  fetchStatus: 'fetching' | 'paused' | 'idle';
  isStale: boolean;
  cacheTime: number;
  staleTime: number;
  refetchCount: number;
}

// Cache monitoring class
export class CacheMonitor {
  private client: QueryClient;
  private stats: Map<string, number> = new Map();
  private startTime: number = Date.now();

  constructor(client: QueryClient) {
    this.client = client;
    this.initializeMonitoring();
  }

  private initializeMonitoring() {
    // Track cache hits and misses
    const originalGetQueryData = this.client.getQueryData.bind(this.client);
    this.client.getQueryData = (queryKey: any) => {
      const result = originalGetQueryData(queryKey);
      const keyString = JSON.stringify(queryKey);
      
      if (result !== undefined) {
        this.incrementStat('cache_hits');
        this.incrementStat(`cache_hit_${keyString}`);
      } else {
        this.incrementStat('cache_misses');
        this.incrementStat(`cache_miss_${keyString}`);
      }
      
      return result;
    };

    // Track query executions
    const originalFetchQuery = this.client.fetchQuery.bind(this.client);
    this.client.fetchQuery = async (options: any) => {
      const keyString = JSON.stringify(options.queryKey);
      this.incrementStat('query_executions');
      this.incrementStat(`query_execution_${keyString}`);
      
      const startTime = performance.now();
      try {
        const result = await originalFetchQuery(options);
        const duration = performance.now() - startTime;
        this.recordQueryDuration(keyString, duration);
        return result;
      } catch (error) {
        const duration = performance.now() - startTime;
        this.recordQueryDuration(keyString, duration);
        this.incrementStat('query_errors');
        this.incrementStat(`query_error_${keyString}`);
        throw error;
      }
    };
  }

  private incrementStat(key: string) {
    this.stats.set(key, (this.stats.get(key) || 0) + 1);
  }

  private recordQueryDuration(queryKey: string, duration: number) {
    const durationKey = `query_duration_${queryKey}`;
    const durations = this.stats.get(durationKey) || 0;
    this.stats.set(durationKey, durations + duration);
  }

  // Get comprehensive cache statistics
  getCacheStats(): CacheStats {
    const queryCache = this.client.getQueryCache();
    const queries = queryCache.getAll();
    
    const totalQueries = queries.length;
    const activeQueries = queries.filter(q => q.state.fetchStatus === 'fetching').length;
    const staleQueries = queries.filter(q => q.isStale()).length;
    const errorQueries = queries.filter(q => q.state.status === 'error').length;
    
    const cacheHits = this.stats.get('cache_hits') || 0;
    const cacheMisses = this.stats.get('cache_misses') || 0;
    const totalRequests = cacheHits + cacheMisses;
    
    const hitRate = totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0;
    const missRate = totalRequests > 0 ? (cacheMisses / totalRequests) * 100 : 0;
    
    // Estimate cache size (rough approximation)
    const cacheSize = this.estimateCacheSize(queries);
    
    // Memory usage (if available)
    const memoryUsage = this.getMemoryUsage();

    return {
      totalQueries,
      activeQueries,
      staleQueries,
      errorQueries,
      cacheSize,
      hitRate: Math.round(hitRate * 100) / 100,
      missRate: Math.round(missRate * 100) / 100,
      memoryUsage,
    };
  }

  // Get detailed metrics for all queries
  getCacheMetrics(): CacheMetrics[] {
    const queryCache = this.client.getQueryCache();
    const queries = queryCache.getAll();
    
    return queries.map(query => ({
      queryKey: JSON.stringify(query.queryKey),
      status: query.state.status,
      dataUpdatedAt: query.state.dataUpdatedAt,
      errorUpdatedAt: query.state.errorUpdatedAt,
      fetchStatus: query.state.fetchStatus,
      isStale: query.isStale(),
      cacheTime: query.cacheTime,
      staleTime: query.options.staleTime || 0,
      refetchCount: query.state.fetchFailureCount + (query.state.dataUpdateCount || 0),
    }));
  }

  // Get performance metrics for specific query
  getQueryPerformance(queryKey: string): {
    executions: number;
    errors: number;
    averageDuration: number;
    hits: number;
    misses: number;
  } {
    const keyString = JSON.stringify(queryKey);
    const executions = this.stats.get(`query_execution_${keyString}`) || 0;
    const errors = this.stats.get(`query_error_${keyString}`) || 0;
    const totalDuration = this.stats.get(`query_duration_${keyString}`) || 0;
    const hits = this.stats.get(`cache_hit_${keyString}`) || 0;
    const misses = this.stats.get(`cache_miss_${keyString}`) || 0;
    
    const averageDuration = executions > 0 ? totalDuration / executions : 0;
    
    return {
      executions,
      errors,
      averageDuration: Math.round(averageDuration * 100) / 100,
      hits,
      misses,
    };
  }

  // Get top performing queries
  getTopQueries(limit = 10): Array<{
    queryKey: string;
    hitRate: number;
    executions: number;
    averageDuration: number;
  }> {
    const queryCache = this.client.getQueryCache();
    const queries = queryCache.getAll();
    
    return queries
      .map(query => {
        const keyString = JSON.stringify(query.queryKey);
        const performance = this.getQueryPerformance(keyString);
        const totalRequests = performance.hits + performance.misses;
        const hitRate = totalRequests > 0 ? (performance.hits / totalRequests) * 100 : 0;
        
        return {
          queryKey: keyString,
          hitRate: Math.round(hitRate * 100) / 100,
          executions: performance.executions,
          averageDuration: performance.averageDuration,
        };
      })
      .sort((a, b) => b.hitRate - a.hitRate)
      .slice(0, limit);
  }

  // Get problematic queries (high error rate or slow)
  getProblematicQueries(limit = 10): Array<{
    queryKey: string;
    errorRate: number;
    averageDuration: number;
    executions: number;
  }> {
    const queryCache = this.client.getQueryCache();
    const queries = queryCache.getAll();
    
    return queries
      .map(query => {
        const keyString = JSON.stringify(query.queryKey);
        const performance = this.getQueryPerformance(keyString);
        const errorRate = performance.executions > 0 
          ? (performance.errors / performance.executions) * 100 
          : 0;
        
        return {
          queryKey: keyString,
          errorRate: Math.round(errorRate * 100) / 100,
          averageDuration: performance.averageDuration,
          executions: performance.executions,
        };
      })
      .filter(q => q.errorRate > 0 || q.averageDuration > 1000) // Errors or > 1s
      .sort((a, b) => b.errorRate - a.errorRate || b.averageDuration - a.averageDuration)
      .slice(0, limit);
  }

  // Clear monitoring statistics
  clearStats() {
    this.stats.clear();
    this.startTime = Date.now();
  }

  // Get monitoring uptime
  getUptime(): number {
    return Date.now() - this.startTime;
  }

  // Export cache data for analysis
  exportCacheData(): {
    stats: CacheStats;
    metrics: CacheMetrics[];
    topQueries: ReturnType<typeof this.getTopQueries>;
    problematicQueries: ReturnType<typeof this.getProblematicQueries>;
    uptime: number;
    timestamp: number;
  } {
    return {
      stats: this.getCacheStats(),
      metrics: this.getCacheMetrics(),
      topQueries: this.getTopQueries(),
      problematicQueries: this.getProblematicQueries(),
      uptime: this.getUptime(),
      timestamp: Date.now(),
    };
  }

  private estimateCacheSize(queries: any[]): number {
    // Rough estimation of cache size in bytes
    let totalSize = 0;
    
    queries.forEach(query => {
      if (query.state.data) {
        try {
          const serialized = JSON.stringify(query.state.data);
          totalSize += serialized.length * 2; // Rough estimate (UTF-16)
        } catch (error) {
          // Skip queries with non-serializable data
        }
      }
    });
    
    return totalSize;
  }

  private getMemoryUsage(): CacheStats['memoryUsage'] {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
      const memory = (window.performance as any).memory;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percentage: Math.round((memory.usedJSHeapSize / memory.totalJSHeapSize) * 100),
      };
    }
    
    return {
      used: 0,
      total: 0,
      percentage: 0,
    };
  }
}

// Create global cache monitor instance
export const cacheMonitor = new CacheMonitor(queryClient);

// Development helper functions
export const devTools = {
  // Log cache statistics to console
  logCacheStats: () => {
    console.group('🔍 Cache Statistics');
    console.table(cacheMonitor.getCacheStats());
    console.groupEnd();
  },

  // Log top performing queries
  logTopQueries: (limit = 5) => {
    console.group('🚀 Top Performing Queries');
    console.table(cacheMonitor.getTopQueries(limit));
    console.groupEnd();
  },

  // Log problematic queries
  logProblematicQueries: (limit = 5) => {
    console.group('⚠️ Problematic Queries');
    console.table(cacheMonitor.getProblematicQueries(limit));
    console.groupEnd();
  },

  // Full cache report
  logFullReport: () => {
    devTools.logCacheStats();
    devTools.logTopQueries();
    devTools.logProblematicQueries();
  },

  // Clear all cache data
  clearCache: () => {
    queryClient.clear();
    cacheMonitor.clearStats();
    console.log('🧹 Cache cleared');
  },
};

// Make dev tools available globally in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).cacheDevTools = devTools;
  console.log('🛠️ Cache dev tools available at window.cacheDevTools');
}

export default cacheMonitor;