import { supabase } from './supabase'

export interface UploadResult {
  data?: {
    path: string
    fullPath: string
    publicUrl: string
  }
  error?: Error
}

export interface FileValidation {
  maxSize: number // in bytes
  allowedTypes: string[]
}

const DEFAULT_VALIDATION: FileValidation = {
  maxSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'application/zip',
    'application/x-zip-compressed'
  ]
}

export function validateFile(file: File, validation: FileValidation = DEFAULT_VALIDATION): { valid: boolean; error?: string } {
  if (file.size > validation.maxSize) {
    return {
      valid: false,
      error: `File size must be less than ${Math.round(validation.maxSize / (1024 * 1024))}MB`
    }
  }

  if (!validation.allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: `File type ${file.type} is not allowed`
    }
  }

  return { valid: true }
}

export async function uploadFile(
  file: File,
  bucket: string,
  path: string,
  validation?: FileValidation
): Promise<UploadResult> {
  try {
    // Validate file
    const validationResult = validateFile(file, validation)
    if (!validationResult.valid) {
      return { error: new Error(validationResult.error) }
    }

    // Generate unique filename
    const fileExt = file.name.split('.').pop()
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
    const filePath = `${path}/${fileName}`

    // Upload file
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      })

    if (error) {
      return { error }
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from(bucket)
      .getPublicUrl(filePath)

    return {
      data: {
        path: data.path,
        fullPath: data.fullPath,
        publicUrl
      }
    }
  } catch (error) {
    return { error: error as Error }
  }
}

export async function deleteFile(bucket: string, path: string): Promise<{ error?: Error }> {
  try {
    const { error } = await supabase.storage
      .from(bucket)
      .remove([path])

    return { error: error || undefined }
  } catch (error) {
    return { error: error as Error }
  }
}

export async function getFileUrl(bucket: string, path: string): Promise<{ url?: string; error?: Error }> {
  try {
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(path)

    return { url: data.publicUrl }
  } catch (error) {
    return { error: error as Error }
  }
}

// Utility function to create storage buckets (for admin use)
export async function createBucket(name: string, isPublic: boolean = true): Promise<{ error?: Error }> {
  try {
    const { error } = await supabase.storage.createBucket(name, {
      public: isPublic,
      allowedMimeTypes: DEFAULT_VALIDATION.allowedTypes,
      fileSizeLimit: DEFAULT_VALIDATION.maxSize
    })

    return { error: error || undefined }
  } catch (error) {
    return { error: error as Error }
  }
}

// Storage bucket names
export const STORAGE_BUCKETS = {
  ATTACHMENTS: 'attachments',
  AVATARS: 'avatars',
  PRESENTATIONS: 'presentations',
  WEBINAR_RECORDINGS: 'webinar-recordings'
} as const