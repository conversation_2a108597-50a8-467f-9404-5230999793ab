/**
 * Component Monitor
 * Tracks component failure rates, render performance, and error patterns
 */

import { errorLogger } from './errorLogger';

interface ComponentFailure {
  componentName: string;
  errorMessage: string;
  errorStack?: string;
  props?: Record<string, any>;
  timestamp: string;
  errorBoundary?: string;
  recoveryAttempts: number;
  recovered: boolean;
}

interface ComponentPerformance {
  componentName: string;
  renderTime: number;
  timestamp: string;
  props?: Record<string, any>;
  phase: 'mount' | 'update' | 'unmount';
}

interface ComponentHealth {
  componentName: string;
  totalRenders: number;
  totalFailures: number;
  averageRenderTime: number;
  failureRate: number;
  lastFailure?: string;
  lastSuccess?: string;
  status: 'healthy' | 'warning' | 'critical';
}

class ComponentMonitor {
  private failures: Map<string, ComponentFailure[]> = new Map();
  private performance: Map<string, ComponentPerformance[]> = new Map();
  private renderCounts: Map<string, number> = new Map();
  private failureCounts: Map<string, number> = new Map();
  private recoveryAttempts: Map<string, number> = new Map();

  // Thresholds for component health
  private readonly FAILURE_RATE_WARNING = 0.1; // 10%
  private readonly FAILURE_RATE_CRITICAL = 0.25; // 25%
  private readonly SLOW_RENDER_THRESHOLD = 100; // 100ms
  private readonly MAX_PERFORMANCE_RECORDS = 100;

  /**
   * Log a component failure
   */
  logComponentFailure(
    componentName: string,
    error: Error,
    props?: Record<string, any>,
    errorBoundary?: string
  ): void {
    const attemptKey = `${componentName}:${error.message}`;
    const recoveryAttempts = (this.recoveryAttempts.get(attemptKey) || 0) + 1;
    this.recoveryAttempts.set(attemptKey, recoveryAttempts);

    const failure: ComponentFailure = {
      componentName,
      errorMessage: error.message,
      errorStack: error.stack,
      props: this.sanitizeProps(props),
      timestamp: new Date().toISOString(),
      errorBoundary,
      recoveryAttempts,
      recovered: false,
    };

    // Add to failures list
    const componentFailures = this.failures.get(componentName) || [];
    componentFailures.push(failure);
    
    // Keep only last 50 failures per component
    if (componentFailures.length > 50) {
      componentFailures.splice(0, componentFailures.length - 50);
    }
    
    this.failures.set(componentName, componentFailures);

    // Update failure count
    this.failureCounts.set(componentName, (this.failureCounts.get(componentName) || 0) + 1);

    // Log to error logger
    errorLogger.logError({
      message: `Component failure: ${componentName} - ${error.message}`,
      stack: error.stack,
      componentName,
      errorBoundary,
      timestamp: failure.timestamp,
      source: 'componentMonitor',
      type: 'componentFailure',
      context: {
        props: failure.props,
        recoveryAttempts,
      },
    });

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`Component failure: ${componentName}`, {
        error: error.message,
        props: failure.props,
        recoveryAttempts,
      });
    }
  }

  /**
   * Log component recovery
   */
  logComponentRecovery(componentName: string, errorMessage: string): void {
    const componentFailures = this.failures.get(componentName) || [];
    const lastFailure = componentFailures
      .slice()
      .reverse()
      .find(f => f.errorMessage === errorMessage && !f.recovered);

    if (lastFailure) {
      lastFailure.recovered = true;
      
      // Log recovery
      errorLogger.logError({
        message: `Component recovered: ${componentName}`,
        componentName,
        timestamp: new Date().toISOString(),
        source: 'componentMonitor',
        type: 'componentRecovery',
        context: {
          originalError: errorMessage,
          recoveryTime: new Date().toISOString(),
        },
      });

      if (process.env.NODE_ENV === 'development') {
        console.info(`Component recovered: ${componentName}`, { originalError: errorMessage });
      }
    }
  }

  /**
   * Track component render performance
   */
  trackRenderPerformance(
    componentName: string,
    renderTime: number,
    phase: 'mount' | 'update' | 'unmount',
    props?: Record<string, any>
  ): void {
    const performance: ComponentPerformance = {
      componentName,
      renderTime,
      timestamp: new Date().toISOString(),
      props: this.sanitizeProps(props),
      phase,
    };

    // Add to performance list
    const componentPerformance = this.performance.get(componentName) || [];
    componentPerformance.push(performance);
    
    // Keep only last N records per component
    if (componentPerformance.length > this.MAX_PERFORMANCE_RECORDS) {
      componentPerformance.splice(0, componentPerformance.length - this.MAX_PERFORMANCE_RECORDS);
    }
    
    this.performance.set(componentName, componentPerformance);

    // Update render count
    this.renderCounts.set(componentName, (this.renderCounts.get(componentName) || 0) + 1);

    // Log slow renders
    if (renderTime > this.SLOW_RENDER_THRESHOLD) {
      errorLogger.logError({
        message: `Slow render: ${componentName} took ${renderTime}ms`,
        componentName,
        timestamp: performance.timestamp,
        source: 'componentMonitor',
        type: 'slowRender',
        context: {
          renderTime,
          phase,
          props: performance.props,
        },
      });

      if (process.env.NODE_ENV === 'development') {
        console.warn(`Slow render: ${componentName}`, { renderTime, phase });
      }
    }
  }

  /**
   * Get component health status
   */
  getComponentHealth(componentName: string): ComponentHealth {
    const totalRenders = this.renderCounts.get(componentName) || 0;
    const totalFailures = this.failureCounts.get(componentName) || 0;
    const failureRate = totalRenders > 0 ? totalFailures / totalRenders : 0;

    const componentPerformance = this.performance.get(componentName) || [];
    const averageRenderTime = componentPerformance.length > 0
      ? componentPerformance.reduce((sum, p) => sum + p.renderTime, 0) / componentPerformance.length
      : 0;

    const componentFailures = this.failures.get(componentName) || [];
    const lastFailure = componentFailures[componentFailures.length - 1];
    const lastSuccess = componentPerformance[componentPerformance.length - 1];

    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (failureRate >= this.FAILURE_RATE_CRITICAL) {
      status = 'critical';
    } else if (failureRate >= this.FAILURE_RATE_WARNING || averageRenderTime > this.SLOW_RENDER_THRESHOLD) {
      status = 'warning';
    }

    return {
      componentName,
      totalRenders,
      totalFailures,
      averageRenderTime,
      failureRate,
      lastFailure: lastFailure?.timestamp,
      lastSuccess: lastSuccess?.timestamp,
      status,
    };
  }

  /**
   * Get all component health statuses
   */
  getAllComponentHealth(): ComponentHealth[] {
    const allComponents = new Set([
      ...this.renderCounts.keys(),
      ...this.failureCounts.keys(),
    ]);

    return Array.from(allComponents).map(componentName => 
      this.getComponentHealth(componentName)
    );
  }

  /**
   * Get component failure patterns
   */
  getFailurePatterns(): {
    mostFailedComponents: Array<{ name: string; failures: number; rate: number }>;
    commonErrors: Array<{ error: string; count: number; components: string[] }>;
    errorTrends: Array<{ date: string; failures: number }>;
  } {
    const allFailures = Array.from(this.failures.values()).flat();
    
    // Most failed components
    const failuresByComponent: Record<string, number> = {};
    allFailures.forEach(failure => {
      failuresByComponent[failure.componentName] = 
        (failuresByComponent[failure.componentName] || 0) + 1;
    });

    const mostFailedComponents = Object.entries(failuresByComponent)
      .map(([name, failures]) => ({
        name,
        failures,
        rate: this.getComponentHealth(name).failureRate,
      }))
      .sort((a, b) => b.failures - a.failures)
      .slice(0, 10);

    // Common errors
    const errorCounts: Record<string, { count: number; components: Set<string> }> = {};
    allFailures.forEach(failure => {
      if (!errorCounts[failure.errorMessage]) {
        errorCounts[failure.errorMessage] = { count: 0, components: new Set() };
      }
      errorCounts[failure.errorMessage].count++;
      errorCounts[failure.errorMessage].components.add(failure.componentName);
    });

    const commonErrors = Object.entries(errorCounts)
      .map(([error, data]) => ({
        error,
        count: data.count,
        components: Array.from(data.components),
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Error trends (last 7 days)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const recentFailures = allFailures.filter(
      failure => new Date(failure.timestamp) > sevenDaysAgo
    );

    const errorTrends: Record<string, number> = {};
    recentFailures.forEach(failure => {
      const date = failure.timestamp.split('T')[0];
      errorTrends[date] = (errorTrends[date] || 0) + 1;
    });

    const errorTrendsArray = Object.entries(errorTrends)
      .map(([date, failures]) => ({ date, failures }))
      .sort((a, b) => a.date.localeCompare(b.date));

    return {
      mostFailedComponents,
      commonErrors,
      errorTrends: errorTrendsArray,
    };
  }

  /**
   * Generate component monitoring report
   */
  generateReport(): {
    componentHealth: ComponentHealth[];
    failurePatterns: ReturnType<ComponentMonitor['getFailurePatterns']>;
    performanceMetrics: {
      slowestComponents: Array<{ name: string; averageRenderTime: number }>;
      totalRenders: number;
      totalFailures: number;
      overallFailureRate: number;
    };
    recommendations: string[];
  } {
    const componentHealth = this.getAllComponentHealth();
    const failurePatterns = this.getFailurePatterns();

    const slowestComponents = componentHealth
      .filter(h => h.averageRenderTime > 0)
      .sort((a, b) => b.averageRenderTime - a.averageRenderTime)
      .slice(0, 10)
      .map(h => ({ name: h.componentName, averageRenderTime: h.averageRenderTime }));

    const totalRenders = Array.from(this.renderCounts.values()).reduce((sum, count) => sum + count, 0);
    const totalFailures = Array.from(this.failureCounts.values()).reduce((sum, count) => sum + count, 0);
    const overallFailureRate = totalRenders > 0 ? totalFailures / totalRenders : 0;

    // Generate recommendations
    const recommendations: string[] = [];
    
    const criticalComponents = componentHealth.filter(h => h.status === 'critical');
    if (criticalComponents.length > 0) {
      recommendations.push(`Critical components need immediate attention: ${criticalComponents.map(c => c.componentName).join(', ')}`);
    }

    const slowComponents = componentHealth.filter(h => h.averageRenderTime > this.SLOW_RENDER_THRESHOLD);
    if (slowComponents.length > 0) {
      recommendations.push(`Optimize render performance for: ${slowComponents.map(c => c.componentName).join(', ')}`);
    }

    if (overallFailureRate > this.FAILURE_RATE_WARNING) {
      recommendations.push('Overall failure rate is high. Consider implementing better error boundaries and fallback components.');
    }

    return {
      componentHealth,
      failurePatterns,
      performanceMetrics: {
        slowestComponents,
        totalRenders,
        totalFailures,
        overallFailureRate,
      },
      recommendations,
    };
  }

  /**
   * Clear monitoring data
   */
  clear(): void {
    this.failures.clear();
    this.performance.clear();
    this.renderCounts.clear();
    this.failureCounts.clear();
    this.recoveryAttempts.clear();
  }

  /**
   * Sanitize props for logging (remove sensitive data)
   */
  private sanitizeProps(props?: Record<string, any>): Record<string, any> | undefined {
    if (!props) return undefined;

    const sanitized: Record<string, any> = {};
    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'auth'];

    Object.entries(props).forEach(([key, value]) => {
      const isSensitive = sensitiveKeys.some(sensitive => 
        key.toLowerCase().includes(sensitive)
      );

      if (isSensitive) {
        sanitized[key] = '[REDACTED]';
      } else if (typeof value === 'function') {
        sanitized[key] = '[FUNCTION]';
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = '[OBJECT]';
      } else {
        sanitized[key] = value;
      }
    });

    return sanitized;
  }
}

// Create singleton instance
export const componentMonitor = new ComponentMonitor();

// Development tools
if (process.env.NODE_ENV === 'development') {
  // Add to window for debugging
  (window as any).componentMonitor = componentMonitor;
}