/**
 * Import/Export Monitor
 * Tracks and logs import/export failures for debugging
 */

import { errorLogger } from './errorLogger';

interface ImportFailure {
  modulePath: string;
  importName?: string;
  error: Error;
  timestamp: string;
  stackTrace?: string;
  attemptCount: number;
}

interface ExportValidation {
  modulePath: string;
  expectedExports: string[];
  actualExports: string[];
  missingExports: string[];
  timestamp: string;
}

class ImportExportMonitor {
  private importFailures: Map<string, ImportFailure> = new Map();
  private exportValidations: Map<string, ExportValidation> = new Map();
  private retryAttempts: Map<string, number> = new Map();
  private maxRetries = 3;

  /**
   * Log an import failure
   */
  logImportFailure(modulePath: string, error: Error, importName?: string): void {
    const key = `${modulePath}:${importName || 'default'}`;
    const attemptCount = (this.retryAttempts.get(key) || 0) + 1;
    this.retryAttempts.set(key, attemptCount);

    const failure: ImportFailure = {
      modulePath,
      importName,
      error,
      timestamp: new Date().toISOString(),
      stackTrace: error.stack,
      attemptCount,
    };

    this.importFailures.set(key, failure);

    // Log to error logger
    errorLogger.logError({
      message: `Import failure: ${error.message}`,
      stack: error.stack,
      timestamp: failure.timestamp,
      source: 'importExportMonitor',
      type: 'importFailure',
      context: {
        modulePath,
        importName,
        attemptCount,
      },
    });

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`Import failure in ${modulePath}:`, {
        importName,
        error: error.message,
        attemptCount,
      });
    }
  }

  /**
   * Validate module exports
   */
  async validateExports(modulePath: string, expectedExports: string[]): Promise<ExportValidation> {
    try {
      const module = await import(modulePath);
      const actualExports = Object.keys(module);
      const missingExports = expectedExports.filter(exp => !actualExports.includes(exp));

      const validation: ExportValidation = {
        modulePath,
        expectedExports,
        actualExports,
        missingExports,
        timestamp: new Date().toISOString(),
      };

      this.exportValidations.set(modulePath, validation);

      // Log missing exports
      if (missingExports.length > 0) {
        errorLogger.logError({
          message: `Missing exports in ${modulePath}: ${missingExports.join(', ')}`,
          timestamp: validation.timestamp,
          source: 'importExportMonitor',
          type: 'missingExports',
          context: {
            modulePath,
            expectedExports,
            actualExports,
            missingExports,
          },
        });

        if (process.env.NODE_ENV === 'development') {
          console.warn(`Missing exports in ${modulePath}:`, missingExports);
        }
      }

      return validation;
    } catch (error) {
      this.logImportFailure(modulePath, error as Error);
      throw error;
    }
  }

  /**
   * Safe import with retry logic
   */
  async safeImport<T = any>(
    modulePath: string, 
    importName?: string,
    fallback?: T
  ): Promise<T | undefined> {
    const key = `${modulePath}:${importName || 'default'}`;
    const attemptCount = this.retryAttempts.get(key) || 0;

    if (attemptCount >= this.maxRetries) {
      console.warn(`Max retries exceeded for ${key}, using fallback`);
      return fallback;
    }

    try {
      const module = await import(modulePath);
      
      if (importName) {
        if (!(importName in module)) {
          throw new Error(`Export '${importName}' not found in ${modulePath}`);
        }
        return module[importName];
      }
      
      return module.default || module;
    } catch (error) {
      this.logImportFailure(modulePath, error as Error, importName);
      
      // Retry with exponential backoff
      const delay = Math.pow(2, attemptCount) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
      
      return this.safeImport(modulePath, importName, fallback);
    }
  }

  /**
   * Get import failure statistics
   */
  getImportFailureStats(): {
    totalFailures: number;
    failuresByModule: Record<string, number>;
    recentFailures: ImportFailure[];
  } {
    const failures = Array.from(this.importFailures.values());
    const failuresByModule: Record<string, number> = {};

    failures.forEach(failure => {
      failuresByModule[failure.modulePath] = (failuresByModule[failure.modulePath] || 0) + 1;
    });

    // Get failures from last 24 hours
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentFailures = failures.filter(
      failure => new Date(failure.timestamp) > oneDayAgo
    );

    return {
      totalFailures: failures.length,
      failuresByModule,
      recentFailures,
    };
  }

  /**
   * Get export validation results
   */
  getExportValidationResults(): ExportValidation[] {
    return Array.from(this.exportValidations.values());
  }

  /**
   * Clear monitoring data
   */
  clear(): void {
    this.importFailures.clear();
    this.exportValidations.clear();
    this.retryAttempts.clear();
  }

  /**
   * Generate monitoring report
   */
  generateReport(): {
    importFailures: ImportFailure[];
    exportValidations: ExportValidation[];
    summary: {
      totalImportFailures: number;
      modulesWithMissingExports: number;
      mostFailedModule: string | null;
    };
  } {
    const importFailures = Array.from(this.importFailures.values());
    const exportValidations = Array.from(this.exportValidations.values());
    
    const failuresByModule: Record<string, number> = {};
    importFailures.forEach(failure => {
      failuresByModule[failure.modulePath] = (failuresByModule[failure.modulePath] || 0) + 1;
    });

    const mostFailedModule = Object.entries(failuresByModule)
      .sort(([, a], [, b]) => b - a)[0]?.[0] || null;

    const modulesWithMissingExports = exportValidations.filter(
      validation => validation.missingExports.length > 0
    ).length;

    return {
      importFailures,
      exportValidations,
      summary: {
        totalImportFailures: importFailures.length,
        modulesWithMissingExports,
        mostFailedModule,
      },
    };
  }
}

// Create singleton instance
export const importExportMonitor = new ImportExportMonitor();

// Utility functions for common scenarios
export const validateHookExports = async (hookPath: string, expectedHooks: string[]) => {
  return importExportMonitor.validateExports(hookPath, expectedHooks);
};

export const safeImportHook = async <T>(hookPath: string, hookName: string, fallback?: T) => {
  return importExportMonitor.safeImport<T>(hookPath, hookName, fallback);
};

export const safeImportComponent = async <T>(componentPath: string, fallback?: T) => {
  return importExportMonitor.safeImport<T>(componentPath, 'default', fallback);
};

// Development tools
if (process.env.NODE_ENV === 'development') {
  // Add to window for debugging
  (window as any).importExportMonitor = importExportMonitor;
}