/**
 * Syrian Analytics Initialization
 * 
 * Initialize Syrian identity analytics system with proper configuration.
 * Call this once in your app's entry point.
 */

import { initializeSyrianAnalytics, type SyrianAnalyticsConfig } from './syrian-analytics';

// Default configuration for different environments
const getDefaultConfig = (): Partial<SyrianAnalyticsConfig> => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isAnalyticsEnabled = process.env.ENABLE_SYRIAN_ANALYTICS === 'true';

  return {
    enabled: isDevelopment || isAnalyticsEnabled,
    endpoint: process.env.SYRIAN_ANALYTICS_ENDPOINT,
    batchSize: isDevelopment ? 5 : 10, // Smaller batches in dev for faster feedback
    flushInterval: isDevelopment ? 10000 : 30000, // More frequent flushing in dev
    enablePerformanceMonitoring: true,
    enableErrorTracking: true,
    enableUserPreferences: true,
    privacyMode: true, // Always respect privacy
  };
};

/**
 * Initialize Syrian analytics with default configuration
 */
export function initSyrianAnalytics(customConfig?: Partial<SyrianAnalyticsConfig>) {
  const config = {
    ...getDefaultConfig(),
    ...customConfig,
  };

  const analytics = initializeSyrianAnalytics(config);

  // Log initialization in development
  if (process.env.NODE_ENV === 'development') {
    console.log('🇸🇾 Syrian Analytics initialized', {
      enabled: config.enabled,
      performanceMonitoring: config.enablePerformanceMonitoring,
      errorTracking: config.enableErrorTracking,
      privacyMode: config.privacyMode,
    });
  }

  return analytics;
}

/**
 * Initialize analytics for production environment
 */
export function initSyrianAnalyticsProduction(endpoint: string, customConfig?: Partial<SyrianAnalyticsConfig>) {
  return initSyrianAnalytics({
    enabled: true,
    endpoint,
    batchSize: 20,
    flushInterval: 60000, // 1 minute
    enablePerformanceMonitoring: true,
    enableErrorTracking: true,
    enableUserPreferences: false, // Disable in production for privacy
    privacyMode: true,
    ...customConfig,
  });
}

/**
 * Initialize analytics for development environment
 */
export function initSyrianAnalyticsDevelopment(customConfig?: Partial<SyrianAnalyticsConfig>) {
  return initSyrianAnalytics({
    enabled: true,
    batchSize: 3,
    flushInterval: 5000, // 5 seconds for quick feedback
    enablePerformanceMonitoring: true,
    enableErrorTracking: true,
    enableUserPreferences: true,
    privacyMode: true,
    ...customConfig,
  });
}

/**
 * Disable analytics completely
 */
export function disableSyrianAnalytics() {
  return initializeSyrianAnalytics({
    enabled: false,
    enablePerformanceMonitoring: false,
    enableErrorTracking: false,
    enableUserPreferences: false,
  });
}
