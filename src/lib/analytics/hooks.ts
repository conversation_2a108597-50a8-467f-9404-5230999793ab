/**
 * Syrian Analytics React Hooks
 * 
 * React hooks for tracking Syrian identity component usage and performance.
 * Designed to be lightweight and non-intrusive.
 */

import { useEffect, useRef, useCallback } from 'react';
import { 
  trackSyrianComponent, 
  trackSyrianPattern, 
  trackSyrianPerformance,
  getSyrianAnalytics 
} from './syrian-analytics';

/**
 * Hook to track Syrian component usage
 */
export function useSyrianComponentTracking(
  component: string,
  variant?: string,
  pattern?: string,
  intensity?: string,
  enabled: boolean = true
) {
  const hasTracked = useRef(false);

  useEffect(() => {
    if (enabled && !hasTracked.current) {
      trackSyrianComponent(component, variant, pattern, intensity);
      hasTracked.current = true;
    }
  }, [component, variant, pattern, intensity, enabled]);
}

/**
 * Hook to track Syrian pattern rendering performance
 */
export function useSyrianPatternTracking(
  pattern: string,
  intensity: string,
  enabled: boolean = true
) {
  const startTimeRef = useRef<number>();
  const hasTracked = useRef(false);

  useEffect(() => {
    if (!enabled) return;

    // Mark start time
    startTimeRef.current = performance.now();

    // Track after render
    const timeoutId = setTimeout(() => {
      if (startTimeRef.current && !hasTracked.current) {
        const paintTime = performance.now() - startTimeRef.current;
        trackSyrianPattern(pattern, intensity, paintTime);
        hasTracked.current = true;
      }
    }, 0);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [pattern, intensity, enabled]);
}

/**
 * Hook to track Syrian component performance
 */
export function useSyrianPerformanceTracking(
  componentName: string,
  enabled: boolean = true
) {
  const startTimeRef = useRef<number>();
  const observerRef = useRef<PerformanceObserver>();

  useEffect(() => {
    if (!enabled || typeof PerformanceObserver === 'undefined') return;

    startTimeRef.current = performance.now();

    // Set up performance observer for this component
    try {
      observerRef.current = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name.includes(componentName.toLowerCase())) {
            trackSyrianPerformance({
              paintTime: entry.duration,
              renderTime: entry.startTime,
            });
          }
        });
      });

      observerRef.current.observe({ entryTypes: ['measure', 'paint'] });
    } catch (error) {
      console.warn('Syrian Performance Tracking: Observer not supported', error);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [componentName, enabled]);

  // Return performance measurement function
  const measurePerformance = useCallback((operationName: string) => {
    if (!enabled) return () => {};

    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      trackSyrianPerformance({
        paintTime: duration,
      });

      // Also create a performance mark for debugging
      if (typeof performance.mark === 'function') {
        try {
          performance.mark(`syrian-${componentName}-${operationName}-end`);
          performance.measure(
            `syrian-${componentName}-${operationName}`,
            `syrian-${componentName}-${operationName}-start`,
            `syrian-${componentName}-${operationName}-end`
          );
        } catch (error) {
          // Ignore performance API errors
        }
      }
    };
  }, [componentName, enabled]);

  return { measurePerformance };
}

/**
 * Hook to get analytics summary data
 */
export function useSyrianAnalyticsSummary() {
  const analytics = getSyrianAnalytics();
  
  const getSummary = useCallback(() => {
    return analytics?.getAnalyticsSummary() || {
      totalEvents: 0,
      componentUsage: {},
      patternUsage: {},
      performanceMetrics: {
        averagePaintTime: 0,
        maxPaintTime: 0,
        totalRenders: 0,
      },
      errorCount: 0,
    };
  }, [analytics]);

  const clearData = useCallback(() => {
    analytics?.clearStoredEvents();
  }, [analytics]);

  return {
    getSummary,
    clearData,
    isEnabled: !!analytics,
  };
}

/**
 * Hook for tracking user interactions with Syrian components
 */
export function useSyrianInteractionTracking(
  component: string,
  enabled: boolean = true
) {
  const trackInteraction = useCallback((
    action: string,
    details?: Record<string, any>
  ) => {
    if (!enabled) return;

    trackSyrianComponent(
      component,
      `interaction_${action}`,
      details?.pattern,
      details?.intensity
    );
  }, [component, enabled]);

  return { trackInteraction };
}

/**
 * Hook for tracking Syrian feature adoption
 */
export function useSyrianAdoptionTracking() {
  const trackAdoption = useCallback((
    feature: string,
    adopted: boolean,
    context?: Record<string, any>
  ) => {
    trackSyrianComponent(
      'adoption',
      adopted ? 'enabled' : 'disabled',
      feature,
      context?.intensity
    );
  }, []);

  return { trackAdoption };
}

/**
 * Hook for development-only analytics debugging
 */
export function useSyrianAnalyticsDebug() {
  const analytics = getSyrianAnalytics();

  const logSummary = useCallback(() => {
    if (process.env.NODE_ENV !== 'development') return;
    
    const summary = analytics?.getAnalyticsSummary();
    if (summary) {
      console.group('🇸🇾 Syrian Analytics Summary');
      console.log('Total Events:', summary.totalEvents);
      console.log('Component Usage:', summary.componentUsage);
      console.log('Pattern Usage:', summary.patternUsage);
      console.log('Performance:', summary.performanceMetrics);
      console.log('Errors:', summary.errorCount);
      console.groupEnd();
    }
  }, [analytics]);

  const logEvents = useCallback(() => {
    if (process.env.NODE_ENV !== 'development') return;
    
    const events = analytics?.getStoredEvents() || [];
    console.group('🇸🇾 Syrian Analytics Events');
    events.forEach((event, index) => {
      console.log(`${index + 1}.`, event);
    });
    console.groupEnd();
  }, [analytics]);

  return {
    logSummary,
    logEvents,
    isEnabled: process.env.NODE_ENV === 'development',
  };
}
