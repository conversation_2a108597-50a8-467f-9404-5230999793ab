/**
 * Syrian Identity Analytics System
 * 
 * Lightweight analytics for tracking Syrian identity feature usage and performance.
 * Designed to be privacy-focused and performance-optimized.
 * 
 * Features:
 * - Event tracking for Syrian component usage
 * - Performance monitoring for patterns and animations
 * - Adoption rate tracking
 * - Error monitoring for Syrian features
 * - Privacy-compliant data collection
 */

// Analytics event types
export interface SyrianAnalyticsEvent {
  type: 'component_usage' | 'pattern_render' | 'performance' | 'error' | 'user_preference';
  component?: string;
  variant?: string;
  pattern?: string;
  intensity?: string;
  performance?: {
    paintTime?: number;
    bundleSize?: number;
    renderTime?: number;
  };
  error?: {
    message: string;
    stack?: string;
    component: string;
  };
  userAgent?: string;
  viewport?: {
    width: number;
    height: number;
  };
  timestamp: number;
  sessionId: string;
}

// Analytics configuration
export interface SyrianAnalyticsConfig {
  enabled: boolean;
  endpoint?: string;
  batchSize: number;
  flushInterval: number;
  enablePerformanceMonitoring: boolean;
  enableErrorTracking: boolean;
  enableUserPreferences: boolean;
  privacyMode: boolean;
}

// Default configuration
const DEFAULT_CONFIG: SyrianAnalyticsConfig = {
  enabled: process.env.NODE_ENV === 'development' || process.env.ENABLE_SYRIAN_ANALYTICS === 'true',
  batchSize: 10,
  flushInterval: 30000, // 30 seconds
  enablePerformanceMonitoring: true,
  enableErrorTracking: true,
  enableUserPreferences: true,
  privacyMode: true, // No personal data collection
};

class SyrianAnalytics {
  private config: SyrianAnalyticsConfig;
  private eventQueue: SyrianAnalyticsEvent[] = [];
  private sessionId: string;
  private flushTimer?: NodeJS.Timeout;
  private performanceObserver?: PerformanceObserver;

  constructor(config: Partial<SyrianAnalyticsConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.sessionId = this.generateSessionId();
    
    if (this.config.enabled && typeof window !== 'undefined') {
      this.initializeAnalytics();
    }
  }

  private generateSessionId(): string {
    return `syrian_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private initializeAnalytics(): void {
    // Set up automatic flushing
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.config.flushInterval);

    // Set up performance monitoring
    if (this.config.enablePerformanceMonitoring) {
      this.initializePerformanceMonitoring();
    }

    // Set up error tracking
    if (this.config.enableErrorTracking) {
      this.initializeErrorTracking();
    }

    // Track user preferences
    if (this.config.enableUserPreferences) {
      this.trackUserPreferences();
    }

    // Clean up on page unload
    window.addEventListener('beforeunload', () => {
      this.flush();
      this.cleanup();
    });
  }

  private initializePerformanceMonitoring(): void {
    if (typeof PerformanceObserver === 'undefined') return;

    try {
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name.includes('syrian') || entry.name.includes('pattern')) {
            this.trackPerformance({
              paintTime: entry.duration,
              renderTime: entry.startTime,
            });
          }
        });
      });

      this.performanceObserver.observe({ entryTypes: ['measure', 'paint'] });
    } catch (error) {
      console.warn('Syrian Analytics: Performance monitoring not supported', error);
    }
  }

  private initializeErrorTracking(): void {
    const originalConsoleError = console.error;
    console.error = (...args) => {
      // Check if error is related to Syrian components
      const errorMessage = args.join(' ');
      if (errorMessage.toLowerCase().includes('syrian') || 
          errorMessage.toLowerCase().includes('pattern')) {
        this.trackError({
          message: errorMessage,
          component: 'unknown',
        });
      }
      originalConsoleError.apply(console, args);
    };
  }

  private trackUserPreferences(): void {
    if (typeof window === 'undefined') return;

    const preferences = {
      reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
      highContrast: window.matchMedia('(prefers-contrast: high)').matches,
      darkMode: window.matchMedia('(prefers-color-scheme: dark)').matches,
      language: navigator.language,
    };

    this.track({
      type: 'user_preference',
      ...preferences,
      timestamp: Date.now(),
      sessionId: this.sessionId,
    });
  }

  // Public API methods
  public trackComponentUsage(component: string, variant?: string, pattern?: string, intensity?: string): void {
    if (!this.config.enabled) return;

    this.track({
      type: 'component_usage',
      component,
      variant,
      pattern,
      intensity,
      timestamp: Date.now(),
      sessionId: this.sessionId,
    });
  }

  public trackPatternRender(pattern: string, intensity: string, paintTime?: number): void {
    if (!this.config.enabled) return;

    this.track({
      type: 'pattern_render',
      pattern,
      intensity,
      performance: paintTime ? { paintTime } : undefined,
      timestamp: Date.now(),
      sessionId: this.sessionId,
    });
  }

  public trackPerformance(metrics: { paintTime?: number; bundleSize?: number; renderTime?: number }): void {
    if (!this.config.enabled || !this.config.enablePerformanceMonitoring) return;

    this.track({
      type: 'performance',
      performance: metrics,
      timestamp: Date.now(),
      sessionId: this.sessionId,
    });
  }

  public trackError(error: { message: string; stack?: string; component: string }): void {
    if (!this.config.enabled || !this.config.enableErrorTracking) return;

    this.track({
      type: 'error',
      error,
      timestamp: Date.now(),
      sessionId: this.sessionId,
    });
  }

  private track(event: Partial<SyrianAnalyticsEvent>): void {
    const fullEvent: SyrianAnalyticsEvent = {
      ...event,
      userAgent: this.config.privacyMode ? undefined : navigator.userAgent,
      viewport: typeof window !== 'undefined' ? {
        width: window.innerWidth,
        height: window.innerHeight,
      } : undefined,
      timestamp: event.timestamp || Date.now(),
      sessionId: this.sessionId,
    } as SyrianAnalyticsEvent;

    this.eventQueue.push(fullEvent);

    // Auto-flush if queue is full
    if (this.eventQueue.length >= this.config.batchSize) {
      this.flush();
    }
  }

  public flush(): void {
    if (this.eventQueue.length === 0) return;

    const events = [...this.eventQueue];
    this.eventQueue = [];

    // In development, log to console
    if (process.env.NODE_ENV === 'development') {
      console.group('🇸🇾 Syrian Analytics Events');
      events.forEach((event) => {
        console.log(`${event.type}:`, event);
      });
      console.groupEnd();
    }

    // Send to endpoint if configured
    if (this.config.endpoint) {
      this.sendToEndpoint(events);
    }

    // Store locally for development/debugging
    this.storeLocally(events);
  }

  private async sendToEndpoint(events: SyrianAnalyticsEvent[]): Promise<void> {
    if (!this.config.endpoint) return;

    try {
      await fetch(this.config.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ events }),
      });
    } catch (error) {
      console.warn('Syrian Analytics: Failed to send events', error);
      // Re-queue events for retry
      this.eventQueue.unshift(...events);
    }
  }

  private storeLocally(events: SyrianAnalyticsEvent[]): void {
    if (typeof localStorage === 'undefined') return;

    try {
      const existingEvents = JSON.parse(localStorage.getItem('syrian_analytics') || '[]');
      const allEvents = [...existingEvents, ...events];
      
      // Keep only last 100 events to prevent storage bloat
      const recentEvents = allEvents.slice(-100);
      
      localStorage.setItem('syrian_analytics', JSON.stringify(recentEvents));
    } catch (error) {
      console.warn('Syrian Analytics: Failed to store events locally', error);
    }
  }

  public getStoredEvents(): SyrianAnalyticsEvent[] {
    if (typeof localStorage === 'undefined') return [];

    try {
      return JSON.parse(localStorage.getItem('syrian_analytics') || '[]');
    } catch (error) {
      console.warn('Syrian Analytics: Failed to retrieve stored events', error);
      return [];
    }
  }

  public clearStoredEvents(): void {
    if (typeof localStorage === 'undefined') return;
    localStorage.removeItem('syrian_analytics');
  }

  public getAnalyticsSummary(): {
    totalEvents: number;
    componentUsage: Record<string, number>;
    patternUsage: Record<string, number>;
    performanceMetrics: {
      averagePaintTime: number;
      maxPaintTime: number;
      totalRenders: number;
    };
    errorCount: number;
  } {
    const events = this.getStoredEvents();
    
    const componentUsage: Record<string, number> = {};
    const patternUsage: Record<string, number> = {};
    const paintTimes: number[] = [];
    let errorCount = 0;

    events.forEach((event) => {
      switch (event.type) {
        case 'component_usage':
          if (event.component) {
            componentUsage[event.component] = (componentUsage[event.component] || 0) + 1;
          }
          break;
        case 'pattern_render':
          if (event.pattern) {
            patternUsage[event.pattern] = (patternUsage[event.pattern] || 0) + 1;
          }
          if (event.performance?.paintTime) {
            paintTimes.push(event.performance.paintTime);
          }
          break;
        case 'error':
          errorCount++;
          break;
      }
    });

    const averagePaintTime = paintTimes.length > 0 
      ? paintTimes.reduce((a, b) => a + b, 0) / paintTimes.length 
      : 0;
    const maxPaintTime = paintTimes.length > 0 ? Math.max(...paintTimes) : 0;

    return {
      totalEvents: events.length,
      componentUsage,
      patternUsage,
      performanceMetrics: {
        averagePaintTime,
        maxPaintTime,
        totalRenders: paintTimes.length,
      },
      errorCount,
    };
  }

  public cleanup(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }
  }
}

// Global analytics instance
let analyticsInstance: SyrianAnalytics | null = null;

export function initializeSyrianAnalytics(config?: Partial<SyrianAnalyticsConfig>): SyrianAnalytics {
  if (!analyticsInstance) {
    analyticsInstance = new SyrianAnalytics(config);
  }
  return analyticsInstance;
}

export function getSyrianAnalytics(): SyrianAnalytics | null {
  return analyticsInstance;
}

// Convenience functions
export function trackSyrianComponent(component: string, variant?: string, pattern?: string, intensity?: string): void {
  analyticsInstance?.trackComponentUsage(component, variant, pattern, intensity);
}

export function trackSyrianPattern(pattern: string, intensity: string, paintTime?: number): void {
  analyticsInstance?.trackPatternRender(pattern, intensity, paintTime);
}

export function trackSyrianPerformance(metrics: { paintTime?: number; bundleSize?: number; renderTime?: number }): void {
  analyticsInstance?.trackPerformance(metrics);
}

export function trackSyrianError(error: { message: string; stack?: string; component: string }): void {
  analyticsInstance?.trackError(error);
}

// Export types
export type { SyrianAnalyticsEvent, SyrianAnalyticsConfig };
