import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Database types will be generated from Supabase CLI
export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string
          role: 'expert' | 'ministry_user' | 'admin'
          avatar?: string
          bio?: string
          location: string
          phone_number?: string
          organization?: string
          position?: string
          languages: string[]
          is_active: boolean
          is_deleted: boolean
          deleted_at?: string
          deleted_by?: string
          created_at: string
          updated_at: string
          last_login_at?: string
        }
        Insert: {
          id?: string
          email: string
          name: string
          role?: 'expert' | 'ministry_user' | 'admin'
          avatar?: string
          bio?: string
          location: string
          phone_number?: string
          organization?: string
          position?: string
          languages?: string[]
          is_active?: boolean
          is_deleted?: boolean
          deleted_at?: string
          deleted_by?: string
          created_at?: string
          updated_at?: string
          last_login_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          role?: 'expert' | 'ministry_user' | 'admin'
          avatar?: string
          bio?: string
          location?: string
          phone_number?: string
          organization?: string
          position?: string
          languages?: string[]
          is_active?: boolean
          is_deleted?: boolean
          deleted_at?: string
          deleted_by?: string
          created_at?: string
          updated_at?: string
          last_login_at?: string
        }
      }
      experts: {
        Row: {
          id: string
          user_id: string
          expertise_areas: any[]
          experience_years: number
          availability: 'available' | 'busy' | 'unavailable'
          rating: number
          total_contributions: number
          success_rate: number
          response_time_hours: number
          portfolio: any[]
          certifications: any[]
          is_deleted: boolean
          deleted_at?: string
          deleted_by?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          expertise_areas?: any[]
          experience_years?: number
          availability?: 'available' | 'busy' | 'unavailable'
          rating?: number
          total_contributions?: number
          success_rate?: number
          response_time_hours?: number
          portfolio?: any[]
          certifications?: any[]
          is_deleted?: boolean
          deleted_at?: string
          deleted_by?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          expertise_areas?: any[]
          experience_years?: number
          availability?: 'available' | 'busy' | 'unavailable'
          rating?: number
          total_contributions?: number
          success_rate?: number
          response_time_hours?: number
          portfolio?: any[]
          certifications?: any[]
          is_deleted?: boolean
          deleted_at?: string
          deleted_by?: string
          created_at?: string
          updated_at?: string
        }
      }
      problems: {
        Row: {
          id: string
          title: string
          description: string
          category: string
          sector: string
          urgency: 'low' | 'medium' | 'high' | 'critical'
          status: 'open' | 'in_progress' | 'resolved' | 'closed'
          submitted_by: string
          assigned_experts: string[]
          tags: string[]
          attachments: any[]
          is_deleted: boolean
          deleted_at?: string
          deleted_by?: string
          created_at: string
          updated_at: string
          resolved_at?: string
        }
        Insert: {
          id?: string
          title: string
          description: string
          category: string
          sector: string
          urgency?: 'low' | 'medium' | 'high' | 'critical'
          status?: 'open' | 'in_progress' | 'resolved' | 'closed'
          submitted_by: string
          assigned_experts?: string[]
          tags?: string[]
          attachments?: any[]
          is_deleted?: boolean
          deleted_at?: string
          deleted_by?: string
          created_at?: string
          updated_at?: string
          resolved_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          category?: string
          sector?: string
          urgency?: 'low' | 'medium' | 'high' | 'critical'
          status?: 'open' | 'in_progress' | 'resolved' | 'closed'
          submitted_by?: string
          assigned_experts?: string[]
          tags?: string[]
          attachments?: any[]
          is_deleted?: boolean
          deleted_at?: string
          deleted_by?: string
          created_at?: string
          updated_at?: string
          resolved_at?: string
        }
      }
      solutions: {
        Row: {
          id: string
          problem_id: string
          expert_id: string
          content: string
          attachments: any[]
          status: 'draft' | 'submitted' | 'approved' | 'implemented'
          votes: any[]
          rating: number
          implementation_notes?: string
          is_deleted: boolean
          deleted_at?: string
          deleted_by?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          problem_id: string
          expert_id: string
          content: string
          attachments?: any[]
          status?: 'draft' | 'submitted' | 'approved' | 'implemented'
          votes?: any[]
          rating?: number
          implementation_notes?: string
          is_deleted?: boolean
          deleted_at?: string
          deleted_by?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          problem_id?: string
          expert_id?: string
          content?: string
          attachments?: any[]
          status?: 'draft' | 'submitted' | 'approved' | 'implemented'
          votes?: any[]
          rating?: number
          implementation_notes?: string
          is_deleted?: boolean
          deleted_at?: string
          deleted_by?: string
          created_at?: string
          updated_at?: string
        }
      }
      webinars: {
        Row: {
          id: string
          title: string
          description: string
          presenter: string
          scheduled_at: string
          duration_minutes: number
          category: string
          tags: string[]
          presentation_files: any[]
          recording_url?: string
          transcript?: string
          qa_sessions: any[]
          attendees: string[]
          status: 'scheduled' | 'live' | 'completed' | 'cancelled'
          is_deleted: boolean
          deleted_at?: string
          deleted_by?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description: string
          presenter: string
          scheduled_at: string
          duration_minutes: number
          category: string
          tags?: string[]
          presentation_files?: any[]
          recording_url?: string
          transcript?: string
          qa_sessions?: any[]
          attendees?: string[]
          status?: 'scheduled' | 'live' | 'completed' | 'cancelled'
          is_deleted?: boolean
          deleted_at?: string
          deleted_by?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string
          presenter?: string
          scheduled_at?: string
          duration_minutes?: number
          category?: string
          tags?: string[]
          presentation_files?: any[]
          recording_url?: string
          transcript?: string
          qa_sessions?: any[]
          attendees?: string[]
          status?: 'scheduled' | 'live' | 'completed' | 'cancelled'
          is_deleted?: boolean
          deleted_at?: string
          deleted_by?: string
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'expert' | 'ministry_user' | 'admin'
      problem_urgency: 'low' | 'medium' | 'high' | 'critical'
      problem_status: 'open' | 'in_progress' | 'resolved' | 'closed'
      solution_status: 'draft' | 'submitted' | 'approved' | 'implemented'
      expert_availability: 'available' | 'busy' | 'unavailable'
      webinar_status: 'scheduled' | 'live' | 'completed' | 'cancelled'
    }
  }
}