import { supabase } from './supabase';

interface ErrorLogData {
  message: string;
  stack?: string;
  componentStack?: string;
  componentName?: string;
  routeName?: string;
  errorBoundary?: string;
  timestamp: string;
  userAgent?: string;
  url?: string;
  errorId?: string;
  retryCount?: number;
  source?: string;
  type?: string;
  context?: Record<string, any>;
}

interface ErrorReportData {
  error: {
    message: string;
    stack?: string;
    name: string;
  };
  errorInfo?: {
    componentStack: string;
    errorBoundary?: string;
  };
  context: {
    routeName?: string;
    componentName?: string;
    errorId?: string;
    retryCount?: number;
    timestamp: string;
    url: string;
    userAgent: string;
  };
}

class ErrorLogger {
  private errorQueue: ErrorLogData[] = [];
  private isProcessing = false;
  private maxQueueSize = 50;
  private batchSize = 10;
  private flushInterval = 30000; // 30 seconds
  private retryAttempts = 3;

  constructor() {
    // Start periodic flushing
    setInterval(() => {
      this.flush();
    }, this.flushInterval);

    // Flush on page unload
    window.addEventListener('beforeunload', () => {
      this.flush(true);
    });

    // Handle global errors
    this.setupGlobalErrorHandlers();
  }

  private setupGlobalErrorHandlers() {
    // Handle uncaught JavaScript errors
    window.addEventListener('error', (event) => {
      this.logError({
        message: event.message,
        stack: event.error?.stack,
        timestamp: new Date().toISOString(),
        url: event.filename,
        source: 'globalErrorHandler',
        type: 'uncaughtError',
        context: {
          lineno: event.lineno,
          colno: event.colno,
        },
      });
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));
      this.logError({
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        source: 'globalErrorHandler',
        type: 'unhandledRejection',
      });
    });
  }

  async logError(errorData: ErrorLogData): Promise<void> {
    try {
      // Add to queue
      this.errorQueue.push({
        ...errorData,
        userAgent: errorData.userAgent || navigator.userAgent,
        url: errorData.url || window.location.href,
      });

      // Limit queue size
      if (this.errorQueue.length > this.maxQueueSize) {
        this.errorQueue = this.errorQueue.slice(-this.maxQueueSize);
      }

      // Log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Error logged:', errorData);
      }

      // Flush if queue is getting full
      if (this.errorQueue.length >= this.batchSize) {
        await this.flush();
      }
    } catch (error) {
      console.error('Failed to log error:', error);
    }
  }

  async reportError(reportData: ErrorReportData): Promise<void> {
    try {
      // Send error report to backend or external service
      const response = await fetch('/api/error-reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(reportData),
      });

      if (!response.ok) {
        throw new Error(`Failed to report error: ${response.statusText}`);
      }

      // Also log the report locally
      await this.logError({
        message: reportData.error.message,
        stack: reportData.error.stack,
        componentStack: reportData.errorInfo?.componentStack,
        componentName: reportData.context.componentName,
        routeName: reportData.context.routeName,
        errorBoundary: reportData.errorInfo?.errorBoundary,
        timestamp: reportData.context.timestamp,
        userAgent: reportData.context.userAgent,
        url: reportData.context.url,
        errorId: reportData.context.errorId,
        retryCount: reportData.context.retryCount,
        source: 'errorReport',
        type: 'userReported',
      });
    } catch (error) {
      console.error('Failed to report error:', error);
      throw error;
    }
  }

  private async flush(immediate = false): Promise<void> {
    if (this.isProcessing || this.errorQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      const errorsToProcess = this.errorQueue.splice(0, this.batchSize);
      
      if (immediate) {
        // Use sendBeacon for immediate flush (page unload)
        this.sendErrorsViaBeacon(errorsToProcess);
      } else {
        await this.sendErrorsToDatabase(errorsToProcess);
      }
    } catch (error) {
      console.error('Failed to flush error logs:', error);
      // Put errors back in queue for retry
      this.errorQueue.unshift(...this.errorQueue.splice(0, 0));
    } finally {
      this.isProcessing = false;
    }
  }

  private async sendErrorsToDatabase(errors: ErrorLogData[]): Promise<void> {
    try {
      const { error } = await supabase
        .from('error_logs')
        .insert(
          errors.map(errorData => ({
            message: errorData.message,
            stack: errorData.stack,
            component_stack: errorData.componentStack,
            component_name: errorData.componentName,
            route_name: errorData.routeName,
            error_boundary: errorData.errorBoundary,
            timestamp: errorData.timestamp,
            user_agent: errorData.userAgent,
            url: errorData.url,
            error_id: errorData.errorId,
            retry_count: errorData.retryCount,
            source: errorData.source,
            type: errorData.type,
            context: errorData.context,
            created_at: new Date().toISOString(),
          }))
        );

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Failed to send errors to database:', error);
      throw error;
    }
  }

  private sendErrorsViaBeacon(errors: ErrorLogData[]): void {
    try {
      const data = JSON.stringify({ errors });
      
      if (navigator.sendBeacon) {
        navigator.sendBeacon('/api/error-logs', data);
      } else {
        // Fallback for browsers without sendBeacon
        fetch('/api/error-logs', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: data,
          keepalive: true,
        }).catch(error => {
          console.error('Failed to send errors via beacon fallback:', error);
        });
      }
    } catch (error) {
      console.error('Failed to send errors via beacon:', error);
    }
  }

  // Get error statistics
  getErrorStats(): {
    queueSize: number;
    isProcessing: boolean;
    totalLogged: number;
  } {
    return {
      queueSize: this.errorQueue.length,
      isProcessing: this.isProcessing,
      totalLogged: this.errorQueue.length, // This would be tracked separately in a real implementation
    };
  }

  // Clear error queue (for testing or manual intervention)
  clearQueue(): void {
    this.errorQueue = [];
  }

  // Force flush errors
  async forceFlush(): Promise<void> {
    await this.flush();
  }
}

// Create singleton instance
export const errorLogger = new ErrorLogger();

// Utility functions for common error scenarios
export const logApiError = (error: Error, endpoint: string, method: string) => {
  errorLogger.logError({
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    source: 'apiError',
    type: 'apiError',
    context: {
      endpoint,
      method,
    },
  });
};

export const logComponentError = (error: Error, componentName: string, props?: Record<string, any>) => {
  errorLogger.logError({
    message: error.message,
    stack: error.stack,
    componentName,
    timestamp: new Date().toISOString(),
    source: 'componentError',
    type: 'componentError',
    context: {
      props: props ? JSON.stringify(props) : undefined,
    },
  });
};

export const logRouteError = (error: Error, routeName: string, params?: Record<string, any>) => {
  errorLogger.logError({
    message: error.message,
    stack: error.stack,
    routeName,
    timestamp: new Date().toISOString(),
    source: 'routeError',
    type: 'routeError',
    context: {
      params: params ? JSON.stringify(params) : undefined,
    },
  });
};
