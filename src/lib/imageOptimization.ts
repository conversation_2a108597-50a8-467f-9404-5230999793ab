/**
 * Image optimization utilities for the OptimizedImage component
 */

export interface ImageFormat {
  format: string;
  mimeType: string;
  extension: string;
  quality?: number;
}

export interface ImageOptimizationOptions {
  formats?: string[];
  quality?: number;
  sizes?: number[];
  enableWebP?: boolean;
  enableAVIF?: boolean;
}

// Supported image formats in order of preference
export const SUPPORTED_FORMATS: ImageFormat[] = [
  { format: 'avif', mimeType: 'image/avif', extension: '.avif', quality: 80 },
  { format: 'webp', mimeType: 'image/webp', extension: '.webp', quality: 80 },
  { format: 'jpeg', mimeType: 'image/jpeg', extension: '.jpg', quality: 85 },
  { format: 'png', mimeType: 'image/png', extension: '.png' }
];

// Cache for format support detection to avoid repeated checks
const formatSupportCache = new Map<string, boolean>();

/**
 * Check if the browser supports a specific image format
 */
export const supportsImageFormat = (format: string): boolean => {
  // Return cached result if available
  if (formatSupportCache.has(format)) {
    return formatSupportCache.get(format)!;
  }

  // Server-side rendering fallback
  if (typeof window === 'undefined') {
    // Assume basic formats are supported on server
    const basicFormats = ['jpeg', 'jpg', 'png', 'gif'];
    const isSupported = basicFormats.includes(format.toLowerCase());
    formatSupportCache.set(format, isSupported);
    return isSupported;
  }

  try {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      formatSupportCache.set(format, false);
      return false;
    }

    // Draw a test pixel
    ctx.fillStyle = '#000000';
    ctx.fillRect(0, 0, 1, 1);
    
    // Try to convert to the target format
    const dataUrl = canvas.toDataURL(`image/${format}`, 0.5);
    const isSupported = dataUrl.indexOf(`data:image/${format}`) === 0;
    
    formatSupportCache.set(format, isSupported);
    return isSupported;
  } catch (error) {
    formatSupportCache.set(format, false);
    return false;
  }
};

/**
 * Get the best supported image format from the available options
 */
export const getBestSupportedFormat = (formats: string[] = ['avif', 'webp', 'jpeg']): string => {
  for (const format of formats) {
    if (supportsImageFormat(format)) {
      return format;
    }
  }
  return 'jpeg'; // Fallback to JPEG
};

/**
 * Generate optimized image URLs for different formats
 * In a real implementation, this would integrate with an image optimization service
 */
export const generateOptimizedUrls = (
  originalUrl: string,
  options: ImageOptimizationOptions = {}
): { src: string; format: string; mimeType: string }[] => {
  const {
    formats = ['avif', 'webp', 'jpeg'],
    quality = 80
  } = options;

  const urls: { src: string; format: string; mimeType: string }[] = [];
  
  // Extract base URL and extension
  const lastDotIndex = originalUrl.lastIndexOf('.');
  const baseUrl = lastDotIndex > -1 ? originalUrl.substring(0, lastDotIndex) : originalUrl;
  const originalExtension = lastDotIndex > -1 ? originalUrl.substring(lastDotIndex) : '';

  // Generate URLs for each supported format
  formats.forEach(format => {
    if (supportsImageFormat(format)) {
      const formatInfo = SUPPORTED_FORMATS.find(f => f.format === format);
      if (formatInfo) {
        // In a real implementation, you would generate URLs that point to optimized versions
        // For now, we'll use a URL structure that could be handled by a CDN or image service
        const optimizedUrl = `${baseUrl}${formatInfo.extension}?format=${format}&quality=${quality}`;
        
        urls.push({
          src: optimizedUrl,
          format: formatInfo.format,
          mimeType: formatInfo.mimeType
        });
      }
    }
  });

  // Always include original as fallback
  urls.push({
    src: originalUrl,
    format: 'original',
    mimeType: originalExtension.toLowerCase().includes('png') ? 'image/png' : 'image/jpeg'
  });

  return urls;
};

/**
 * Generate responsive image sizes string
 */
export const generateResponsiveSizes = (
  breakpoints: { [key: string]: string } = {}
): string => {
  const defaultBreakpoints = {
    '(max-width: 320px)': '320px',
    '(max-width: 640px)': '640px',
    '(max-width: 960px)': '960px',
    '(max-width: 1280px)': '1280px',
    ...breakpoints
  };

  const sizeEntries = Object.entries(defaultBreakpoints);
  const sizesArray = sizeEntries.map(([media, size]) => `${media} ${size}`);
  
  // Add default size (no media query)
  sizesArray.push('1920px');
  
  return sizesArray.join(', ');
};

/**
 * Create a blur placeholder data URL
 */
export const createBlurPlaceholder = (
  width: number = 10,
  height: number = 10,
  color: string = '#f3f4f6'
): string => {
  if (typeof window === 'undefined') {
    // Server-side fallback
    return 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMCAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjEwIiBoZWlnaHQ9IjEwIiBmaWxsPSIjZjNmNGY2Ii8+Cjwvc3ZnPgo=';
  }

  try {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Could not get canvas context');
    }

    // Fill with the specified color
    ctx.fillStyle = color;
    ctx.fillRect(0, 0, width, height);
    
    // Convert to low-quality JPEG for blur effect
    return canvas.toDataURL('image/jpeg', 0.1);
  } catch (error) {
    console.warn('Failed to create blur placeholder:', error);
    
    // SVG fallback
    const svg = `
      <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="${width}" height="${height}" fill="${color}"/>
      </svg>
    `;
    
    return `data:image/svg+xml;base64,${btoa(svg)}`;
  }
};

/**
 * Preload an image
 */
export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
};

/**
 * Get optimal image dimensions based on container size and device pixel ratio
 */
export const getOptimalDimensions = (
  containerWidth: number,
  containerHeight: number,
  devicePixelRatio: number = window.devicePixelRatio || 1
): { width: number; height: number } => {
  return {
    width: Math.ceil(containerWidth * devicePixelRatio),
    height: Math.ceil(containerHeight * devicePixelRatio)
  };
};

/**
 * Check if an image URL is likely to be optimized
 */
export const isOptimizedImageUrl = (url: string): boolean => {
  const optimizedIndicators = [
    'webp', 'avif', 'quality=', 'w=', 'h=', 'format=', 'optimize'
  ];
  
  return optimizedIndicators.some(indicator => 
    url.toLowerCase().includes(indicator)
  );
};

/**
 * Extract image metadata from URL or filename
 */
export const extractImageMetadata = (url: string): {
  format: string;
  hasOptimizationParams: boolean;
  estimatedSize?: 'small' | 'medium' | 'large';
} => {
  const extension = url.split('.').pop()?.toLowerCase() || '';
  const hasOptimizationParams = isOptimizedImageUrl(url);
  
  // Estimate size based on common naming conventions
  let estimatedSize: 'small' | 'medium' | 'large' | undefined;
  if (url.includes('thumb') || url.includes('small')) {
    estimatedSize = 'small';
  } else if (url.includes('medium') || url.includes('md')) {
    estimatedSize = 'medium';
  } else if (url.includes('large') || url.includes('xl') || url.includes('full')) {
    estimatedSize = 'large';
  }
  
  return {
    format: extension,
    hasOptimizationParams,
    estimatedSize
  };
};