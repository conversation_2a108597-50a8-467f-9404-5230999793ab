/**
 * Syrian Identity Design Tokens - TypeScript Constants
 * 
 * Type-safe access to Syrian cultural colors with proper IntelliSense support.
 * These constants mirror the CSS custom properties for consistent usage.
 * 
 * Usage:
 * - Use these constants in JavaScript/TypeScript code
 * - For CSS, use the CSS custom properties directly
 * - All colors are WCAG AA compliant
 */

// Syrian Color Variants Type
export type SyrianColorVariant = 50 | 500 | 900;

// Syrian Color Names Type
export type SyrianColorName = 
  | 'qasiounGold'
  | 'damascusRed' 
  | 'umayyadGreen'
  | 'palmyraStone'
  | 'eblaBlue'
  | 'heritagePurple';

// Color value structure
export interface SyrianColorValue {
  50: string;
  500: string;
  900: string;
  css: {
    50: string;
    500: string;
    900: string;
  };
}

/**
 * Syrian Qasioun Gold - Inspired by Qasioun Mountain sunset
 * Primary brand color representing Damascus heritage
 */
export const qasiounGold: SyrianColorValue = {
  50: '45 100% 95%',
  500: '45 85% 55%',
  900: '45 100% 25%',
  css: {
    50: 'hsl(45 100% 95%)',
    500: 'hsl(45 85% 55%)',
    900: 'hsl(45 100% 25%)',
  },
} as const;

/**
 * Syrian Damascus Red - Traditional architecture brick
 * Accent color representing historical Damascus buildings
 */
export const damascusRed: SyrianColorValue = {
  50: '15 80% 95%',
  500: '15 75% 50%',
  900: '15 85% 25%',
  css: {
    50: 'hsl(15 80% 95%)',
    500: 'hsl(15 75% 50%)',
    900: 'hsl(15 85% 25%)',
  },
} as const;

/**
 * Syrian Umayyad Green - Mosque garden inspiration
 * Supporting color representing Islamic architectural heritage
 */
export const umayyadGreen: SyrianColorValue = {
  50: '140 60% 95%',
  500: '140 45% 45%',
  900: '140 60% 20%',
  css: {
    50: 'hsl(140 60% 95%)',
    500: 'hsl(140 45% 45%)',
    900: 'hsl(140 60% 20%)',
  },
} as const;

/**
 * Syrian Palmyra Stone - Ancient limestone
 * Neutral color representing archaeological heritage
 */
export const palmyraStone: SyrianColorValue = {
  50: '35 25% 95%',
  500: '35 20% 70%',
  900: '35 30% 30%',
  css: {
    50: 'hsl(35 25% 95%)',
    500: 'hsl(35 20% 70%)',
    900: 'hsl(35 30% 30%)',
  },
} as const;

/**
 * Syrian Ebla Blue - Archaeological heritage
 * Supporting color representing ancient Syrian civilization
 */
export const eblaBlue: SyrianColorValue = {
  50: '210 100% 95%',
  500: '210 80% 55%',
  900: '210 100% 25%',
  css: {
    50: 'hsl(210 100% 95%)',
    500: 'hsl(210 80% 55%)',
    900: 'hsl(210 100% 25%)',
  },
} as const;

/**
 * Syrian Heritage Purple - Royal Ayyubid inspiration
 * Accent color representing royal and scholarly heritage
 */
export const heritagePurple: SyrianColorValue = {
  50: '280 60% 95%',
  500: '280 50% 50%',
  900: '280 70% 25%',
  css: {
    50: 'hsl(280 60% 95%)',
    500: 'hsl(280 50% 50%)',
    900: 'hsl(280 70% 25%)',
  },
} as const;

// Complete Syrian color palette
export const syrianColors = {
  qasiounGold,
  damascusRed,
  umayyadGreen,
  palmyraStone,
  eblaBlue,
  heritagePurple,
} as const;

// CSS Custom Property Names
export const syrianColorVars = {
  qasiounGold: {
    50: '--syrian-qasioun-gold-50',
    500: '--syrian-qasioun-gold-500',
    900: '--syrian-qasioun-gold-900',
  },
  damascusRed: {
    50: '--syrian-damascus-red-50',
    500: '--syrian-damascus-red-500',
    900: '--syrian-damascus-red-900',
  },
  umayyadGreen: {
    50: '--syrian-umayyad-green-50',
    500: '--syrian-umayyad-green-500',
    900: '--syrian-umayyad-green-900',
  },
  palmyraStone: {
    50: '--syrian-palmyra-stone-50',
    500: '--syrian-palmyra-stone-500',
    900: '--syrian-palmyra-stone-900',
  },
  eblaBlue: {
    50: '--syrian-ebla-blue-50',
    500: '--syrian-ebla-blue-500',
    900: '--syrian-ebla-blue-900',
  },
  heritagePurple: {
    50: '--syrian-heritage-purple-50',
    500: '--syrian-heritage-purple-500',
    900: '--syrian-heritage-purple-900',
  },
} as const;

// Semantic color mappings
export const syrianSemanticColors = {
  primary: 'var(--syrian-primary)',
  primaryForeground: 'var(--syrian-primary-foreground)',
  accent: 'var(--syrian-accent)',
  accentForeground: 'var(--syrian-accent-foreground)',
  muted: 'var(--syrian-muted)',
  mutedForeground: 'var(--syrian-muted-foreground)',
} as const;

// Utility functions
export function getSyrianColor(
  colorName: SyrianColorName,
  variant: SyrianColorVariant = 500
): string {
  return syrianColors[colorName][variant];
}

export function getSyrianColorCSS(
  colorName: SyrianColorName,
  variant: SyrianColorVariant = 500
): string {
  return syrianColors[colorName].css[variant];
}

export function getSyrianColorVar(
  colorName: SyrianColorName,
  variant: SyrianColorVariant = 500
): string {
  return `hsl(var(${syrianColorVars[colorName][variant]}))`;
}

// Accessibility helpers
export const syrianColorContrasts = {
  // WCAG AA compliant combinations
  lightBackground: {
    text: [
      syrianColors.qasiounGold[900],
      syrianColors.damascusRed[900],
      syrianColors.umayyadGreen[900],
      syrianColors.palmyraStone[900],
      syrianColors.eblaBlue[900],
      syrianColors.heritagePurple[900],
    ],
  },
  darkBackground: {
    text: [
      syrianColors.qasiounGold[50],
      syrianColors.damascusRed[50],
      syrianColors.umayyadGreen[50],
      syrianColors.palmyraStone[50],
      syrianColors.eblaBlue[50],
      syrianColors.heritagePurple[50],
    ],
  },
} as const;

// Export types for external use
export type SyrianColors = typeof syrianColors;
export type SyrianColorVars = typeof syrianColorVars;
export type SyrianSemanticColors = typeof syrianSemanticColors;
