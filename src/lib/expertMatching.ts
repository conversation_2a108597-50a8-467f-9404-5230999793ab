/**
 * Expert Matching System API Service
 * Handles intelligent matching between experts and problems
 */

import { supabase } from './supabase';
import type {
  ExpertMatchingPreferences,
  ExpertMatchingPreferencesUpdateData,
  ProblemExpertMatch,
  ExpertMatchResponseData,
  ExpertWorkload,
  MatchingAlgorithmConfig,
  ExpertMatchResult
} from '../types/user';

// Expert Matching Preferences API
export const expertMatchingPreferencesAPI = {
  // Get expert's matching preferences
  async getPreferences(expertId: string): Promise<ExpertMatchingPreferences | null> {
    const { data, error } = await supabase
      .from('expert_matching_preferences')
      .select('*')
      .eq('expert_id', expertId)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new Error(`Failed to fetch expert preferences: ${error.message}`);
    }

    return data;
  },

  // Create or update expert's matching preferences
  async upsertPreferences(
    expertId: string,
    preferences: ExpertMatchingPreferencesUpdateData
  ): Promise<ExpertMatchingPreferences> {
    const { data, error } = await supabase
      .from('expert_matching_preferences')
      .upsert({
        expert_id: expertId,
        ...preferences
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update expert preferences: ${error.message}`);
    }

    return data;
  },

  // Delete expert's matching preferences
  async deletePreferences(expertId: string): Promise<void> {
    const { error } = await supabase
      .from('expert_matching_preferences')
      .delete()
      .eq('expert_id', expertId);

    if (error) {
      throw new Error(`Failed to delete expert preferences: ${error.message}`);
    }
  }
};

// Problem-Expert Matches API
export const problemExpertMatchesAPI = {
  // Get matches for a specific problem
  async getMatchesForProblem(problemId: string): Promise<ProblemExpertMatch[]> {
    const { data, error } = await supabase
      .from('problem_expert_matches')
      .select(`
        *,
        experts:expert_id (
          *,
          users:user_id (
            id,
            name,
            email,
            avatar,
            location
          )
        )
      `)
      .eq('problem_id', problemId)
      .order('match_score', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch problem matches: ${error.message}`);
    }

    return data || [];
  },

  // Get matches for a specific expert
  async getMatchesForExpert(expertId: string): Promise<ProblemExpertMatch[]> {
    const { data, error } = await supabase
      .from('problem_expert_matches')
      .select(`
        *,
        problems:problem_id (
          id,
          title,
          description,
          category,
          sector,
          urgency,
          status,
          created_at
        )
      `)
      .eq('expert_id', expertId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch expert matches: ${error.message}`);
    }

    return data || [];
  },

  // Get pending matches for an expert
  async getPendingMatchesForExpert(expertId: string): Promise<ProblemExpertMatch[]> {
    const { data, error } = await supabase
      .from('problem_expert_matches')
      .select(`
        *,
        problems:problem_id (
          id,
          title,
          description,
          category,
          sector,
          urgency,
          status,
          created_at
        )
      `)
      .eq('expert_id', expertId)
      .eq('expert_response_status', 'pending')
      .gt('expires_at', new Date().toISOString())
      .order('match_score', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch pending matches: ${error.message}`);
    }

    return data || [];
  },

  // Respond to a match (accept/decline)
  async respondToMatch(
    matchId: string,
    response: ExpertMatchResponseData
  ): Promise<ProblemExpertMatch> {
    const { data, error } = await supabase
      .from('problem_expert_matches')
      .update({
        ...response,
        responded_at: new Date().toISOString()
      })
      .eq('id', matchId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to respond to match: ${error.message}`);
    }

    return data;
  },

  // Manually create a match (admin function)
  async createMatch(
    problemId: string,
    expertId: string,
    matchScore?: number,
    matchReasons?: string[]
  ): Promise<ProblemExpertMatch> {
    const { data, error } = await supabase
      .from('problem_expert_matches')
      .insert({
        problem_id: problemId,
        expert_id: expertId,
        match_score: matchScore || 0.5,
        match_reasons: matchReasons || ['Manual assignment'],
        auto_assigned: false
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create match: ${error.message}`);
    }

    return data;
  },

  // Delete a match
  async deleteMatch(matchId: string): Promise<void> {
    const { error } = await supabase
      .from('problem_expert_matches')
      .delete()
      .eq('id', matchId);

    if (error) {
      throw new Error(`Failed to delete match: ${error.message}`);
    }
  }
};

// Expert Workload API
export const expertWorkloadAPI = {
  // Get workload for a specific expert
  async getExpertWorkload(
    expertId: string,
    monthYear?: string
  ): Promise<ExpertWorkload[]> {
    let query = supabase
      .from('expert_workload')
      .select(`
        *,
        experts:expert_id (
          *,
          users:user_id (
            id,
            name,
            email
          )
        )
      `)
      .eq('expert_id', expertId);

    if (monthYear) {
      query = query.eq('month_year', monthYear);
    }

    const { data, error } = await query.order('month_year', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch expert workload: ${error.message}`);
    }

    return data || [];
  },

  // Get workload summary for all experts
  async getAllExpertsWorkload(monthYear?: string): Promise<ExpertWorkload[]> {
    let query = supabase
      .from('expert_workload')
      .select(`
        *,
        experts:expert_id (
          *,
          users:user_id (
            id,
            name,
            email
          )
        )
      `);

    if (monthYear) {
      query = query.eq('month_year', monthYear);
    }

    const { data, error } = await query.order('problems_assigned', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch workload summary: ${error.message}`);
    }

    return data || [];
  },

  // Update expert workload (usually called by system)
  async updateWorkload(expertId: string, monthYear?: string): Promise<void> {
    const { error } = await supabase.rpc('update_expert_workload', {
      p_expert_id: expertId,
      p_month_year: monthYear || new Date().toISOString().slice(0, 7) + '-01'
    });

    if (error) {
      throw new Error(`Failed to update expert workload: ${error.message}`);
    }
  }
};

// Matching Algorithm API
export const matchingAlgorithmAPI = {
  // Get active matching algorithm configuration
  async getActiveConfig(): Promise<MatchingAlgorithmConfig | null> {
    const { data, error } = await supabase
      .from('matching_algorithm_config')
      .select('*')
      .eq('is_active', true)
      .single();

    if (error && error.code !== 'PGRST116') {
      throw new Error(`Failed to fetch algorithm config: ${error.message}`);
    }

    return data;
  },

  // Get all algorithm configurations
  async getAllConfigs(): Promise<MatchingAlgorithmConfig[]> {
    const { data, error } = await supabase
      .from('matching_algorithm_config')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch algorithm configs: ${error.message}`);
    }

    return data || [];
  },

  // Update algorithm configuration (admin only)
  async updateConfig(
    configId: string,
    updates: Partial<MatchingAlgorithmConfig>
  ): Promise<MatchingAlgorithmConfig> {
    const { data, error } = await supabase
      .from('matching_algorithm_config')
      .update(updates)
      .eq('id', configId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update algorithm config: ${error.message}`);
    }

    return data;
  },

  // Find matching experts for a problem
  async findMatchingExperts(
    problemId: string,
    limit: number = 5
  ): Promise<ExpertMatchResult[]> {
    const { data, error } = await supabase.rpc('find_matching_experts', {
      p_problem_id: problemId,
      p_limit: limit
    });

    if (error) {
      throw new Error(`Failed to find matching experts: ${error.message}`);
    }

    return data || [];
  },

  // Auto-assign experts to a problem
  async autoAssignExperts(
    problemId: string,
    maxAssignments: number = 3
  ): Promise<number> {
    const { data, error } = await supabase.rpc('auto_assign_experts_to_problem', {
      p_problem_id: problemId,
      p_max_assignments: maxAssignments
    });

    if (error) {
      throw new Error(`Failed to auto-assign experts: ${error.message}`);
    }

    return data || 0;
  },

  // Calculate match score between expert and problem
  async calculateMatchScore(
    problemId: string,
    expertId: string
  ): Promise<number> {
    const { data, error } = await supabase.rpc('calculate_expert_match_score', {
      p_problem_id: problemId,
      p_expert_id: expertId
    });

    if (error) {
      throw new Error(`Failed to calculate match score: ${error.message}`);
    }

    return data || 0;
  }
};

// Utility functions
export const expertMatchingUtils = {
  // Check if a match has expired
  isMatchExpired(match: ProblemExpertMatch): boolean {
    return new Date(match.expires_at) < new Date();
  },

  // Get match status display text
  getMatchStatusText(match: ProblemExpertMatch): string {
    if (this.isMatchExpired(match) && match.expert_response_status === 'pending') {
      return 'Expired';
    }
    
    switch (match.expert_response_status) {
      case 'pending':
        return 'Pending Response';
      case 'accepted':
        return 'Accepted';
      case 'declined':
        return 'Declined';
      case 'expired':
        return 'Expired';
      default:
        return 'Unknown';
    }
  },

  // Get match score color class
  getMatchScoreColor(score: number): string {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-blue-600';
    if (score >= 0.4) return 'text-yellow-600';
    return 'text-gray-600';
  },

  // Format match score as percentage
  formatMatchScore(score: number): string {
    return `${Math.round(score * 100)}%`;
  },

  // Get default availability schedule
  getDefaultAvailabilitySchedule() {
    const defaultDay = { start: '09:00', end: '17:00', available: true };
    const weekend = { start: '09:00', end: '17:00', available: false };
    
    return {
      monday: defaultDay,
      tuesday: defaultDay,
      wednesday: defaultDay,
      thursday: defaultDay,
      friday: defaultDay,
      saturday: weekend,
      sunday: weekend
    };
  }
};