import { SearchQuery, SearchResults, SearchError, SearchErrorType } from './types'

/**
 * In-memory cache entry with TTL support
 */
interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
  accessCount: number
  lastAccessed: number
}

/**
 * Cache configuration options
 */
interface CacheConfig {
  defaultTTL: number // Time to live in milliseconds
  maxSize: number // Maximum number of entries
  cleanupInterval: number // Cleanup interval in milliseconds
}

/**
 * In-memory search cache with LRU eviction and TTL support
 */
export class SearchCache {
  private cache = new Map<string, CacheEntry<SearchResults>>()
  private config: CacheConfig
  private cleanupTimer?: NodeJS.Timeout

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      defaultTTL: 5 * 60 * 1000, // 5 minutes default
      maxSize: 100, // Store up to 100 search results
      cleanupInterval: 60 * 1000, // Cleanup every minute
      ...config
    }

    // Start periodic cleanup
    this.startCleanup()
  }

  /**
   * Generate a cache key from search query
   */
  generateKey(query: SearchQuery): string {
    // Create a normalized key that includes all relevant query parameters
    const keyObject = {
      text: query.text.toLowerCase().trim(),
      filters: {
        contentType: query.filters.contentType?.sort(),
        sectors: query.filters.sectors?.sort(),
        categories: query.filters.categories?.sort(),
        status: query.filters.status?.sort(),
        dateRange: query.filters.dateRange,
        rating: query.filters.rating,
        location: query.filters.location,
        urgency: query.filters.urgency?.sort(),
        availability: query.filters.availability?.sort(),
        tags: query.filters.tags?.sort()
      },
      sortBy: query.sortBy,
      pagination: query.pagination,
      language: query.language
    }

    // Create a hash of the key object
    return this.hashObject(keyObject)
  }

  /**
   * Get cached search results
   */
  async get(key: string): Promise<SearchResults | null> {
    try {
      const entry = this.cache.get(key)
      
      if (!entry) {
        return null
      }

      const now = Date.now()
      
      // Check if entry has expired
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
        return null
      }

      // Update access statistics
      entry.accessCount++
      entry.lastAccessed = now
      
      // Update the entry in the cache
      this.cache.set(key, entry)

      return entry.data
    } catch (error) {
      console.error('Cache get error:', error)
      throw new SearchError(
        SearchErrorType.CACHE_ERROR,
        'Failed to retrieve from cache',
        error
      )
    }
  }

  /**
   * Store search results in cache
   */
  async set(key: string, results: SearchResults, ttl?: number): Promise<void> {
    try {
      const now = Date.now()
      const entryTTL = ttl || this.config.defaultTTL

      // If this is an update to existing entry, preserve access count
      const existingEntry = this.cache.get(key)
      
      // If cache is at max size and this is a new entry, remove least recently used entry
      if (!existingEntry && this.cache.size >= this.config.maxSize) {
        this.evictLRU()
      }

      const entry: CacheEntry<SearchResults> = {
        data: results,
        timestamp: now,
        ttl: entryTTL,
        accessCount: existingEntry?.accessCount || 1,
        lastAccessed: existingEntry?.lastAccessed || now
      }

      this.cache.set(key, entry)
    } catch (error) {
      console.error('Cache set error:', error)
      throw new SearchError(
        SearchErrorType.CACHE_ERROR,
        'Failed to store in cache',
        error
      )
    }
  }

  /**
   * Check if a key exists in cache and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false

    const now = Date.now()
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  /**
   * Clear specific cache entry
   */
  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear()
  }

  /**
   * Invalidate cache entries based on content type
   * Useful when new content is added or updated
   */
  invalidateByContentType(contentType: string): number {
    let invalidatedCount = 0
    
    for (const [key, entry] of this.cache.entries()) {
      // Check if the cached results contain the specified content type
      const hasContentType = entry.data.items.some(item => item.type === contentType)
      if (hasContentType) {
        this.cache.delete(key)
        invalidatedCount++
      }
    }

    return invalidatedCount
  }

  /**
   * Invalidate cache entries based on sector
   */
  invalidateBySector(sector: string): number {
    let invalidatedCount = 0
    
    for (const [key, entry] of this.cache.entries()) {
      const hasSector = entry.data.items.some(item => item.sector === sector)
      if (hasSector) {
        this.cache.delete(key)
        invalidatedCount++
      }
    }

    return invalidatedCount
  }

  /**
   * Invalidate cache entries that might contain a specific item
   */
  invalidateByItemId(itemId: string): number {
    let invalidatedCount = 0
    
    for (const [key, entry] of this.cache.entries()) {
      const hasItem = entry.data.items.some(item => item.id === itemId)
      if (hasItem) {
        this.cache.delete(key)
        invalidatedCount++
      }
    }

    return invalidatedCount
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number
    maxSize: number
    hitRate: number
    entries: Array<{
      key: string
      size: number
      age: number
      accessCount: number
      lastAccessed: number
    }>
  } {
    const now = Date.now()
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      size: JSON.stringify(entry.data).length,
      age: now - entry.timestamp,
      accessCount: entry.accessCount,
      lastAccessed: entry.lastAccessed
    }))

    // Calculate hit rate (simplified - would need request tracking for accurate rate)
    const totalAccesses = entries.reduce((sum, entry) => sum + entry.accessCount, 0)
    const hitRate = entries.length > 0 ? totalAccesses / entries.length : 0

    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      hitRate,
      entries: entries.sort((a, b) => b.lastAccessed - a.lastAccessed)
    }
  }

  /**
   * Cleanup expired entries
   */
  cleanup(): number {
    const now = Date.now()
    let cleanedCount = 0

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key)
        cleanedCount++
      }
    }

    return cleanedCount
  }

  /**
   * Destroy the cache and cleanup resources
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
    this.cache.clear()
  }

  /**
   * Start periodic cleanup of expired entries
   */
  private startCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }

  /**
   * Evict least recently used entry
   */
  private evictLRU(): void {
    let oldestKey: string | null = null
    let oldestTime = Date.now()

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey)
    }
  }

  /**
   * Create a simple hash from an object
   */
  private hashObject(obj: any): string {
    const str = JSON.stringify(obj)
    let hash = 0
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36)
  }
}

/**
 * Global cache instance
 */
export const searchCache = new SearchCache({
  defaultTTL: 5 * 60 * 1000, // 5 minutes
  maxSize: 100,
  cleanupInterval: 60 * 1000 // 1 minute
})

/**
 * Cache invalidation utilities
 */
export class CacheInvalidator {
  constructor(private cache: SearchCache) {}

  /**
   * Invalidate cache when new content is added
   */
  onContentAdded(contentType: string, sector?: string): void {
    this.cache.invalidateByContentType(contentType)
    if (sector) {
      this.cache.invalidateBySector(sector)
    }
  }

  /**
   * Invalidate cache when content is updated
   */
  onContentUpdated(itemId: string, contentType: string, sector?: string): void {
    this.cache.invalidateByItemId(itemId)
    this.cache.invalidateByContentType(contentType)
    if (sector) {
      this.cache.invalidateBySector(sector)
    }
  }

  /**
   * Invalidate cache when content is deleted
   */
  onContentDeleted(itemId: string, contentType: string): void {
    this.cache.invalidateByItemId(itemId)
    this.cache.invalidateByContentType(contentType)
  }

  /**
   * Invalidate all cache entries (use sparingly)
   */
  invalidateAll(): void {
    this.cache.clear()
  }
}

/**
 * Global cache invalidator instance
 */
export const cacheInvalidator = new CacheInvalidator(searchCache)