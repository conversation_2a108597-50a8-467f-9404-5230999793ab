import { supabase } from '../supabase'
import { SearchQuery, SearchResults, SearchAnalytics, SearchError, SearchErrorType, ContentType } from './types'

/**
 * Search analytics configuration
 */
interface AnalyticsConfig {
  enableTracking: boolean
  batchSize: number
  flushInterval: number // milliseconds
  enableClickTracking: boolean
  enablePerformanceTracking: boolean
}

/**
 * Analytics event types
 */
export interface SearchEvent {
  type: 'search' | 'click' | 'suggestion_click' | 'filter_applied'
  timestamp: number
  sessionId: string
  data: any
}

/**
 * Click tracking data
 */
export interface ClickTrackingData {
  analyticsId: string
  resultId: string
  resultType: ContentType
  position: number
  timestamp: number
}

/**
 * Performance metrics
 */
export interface PerformanceMetrics {
  queryHash: string
  executionTime: number
  cacheHit: boolean
  resultsCount: number
  timestamp: number
}

/**
 * Popular queries data
 */
export interface PopularQuery {
  queryText: string
  queryHash: string
  searchCount: number
  avgResponseTime: number
  avgResultsCount: number
  lastSearched: string
}

/**
 * Search analytics service for tracking and analyzing search behavior
 */
export class SearchAnalyticsService {
  private config: AnalyticsConfig
  private eventQueue: SearchEvent[] = []
  private flushTimer?: NodeJS.Timeout
  private sessionId: string

  constructor(config: Partial<AnalyticsConfig> = {}) {
    this.config = {
      enableTracking: true,
      batchSize: 10,
      flushInterval: 30000, // 30 seconds
      enableClickTracking: true,
      enablePerformanceTracking: true,
      ...config
    }

    this.sessionId = this.generateSessionId()
    
    if (this.config.enableTracking) {
      this.startBatchFlush()
    }
  }

  /**
   * Track a search query and its results
   */
  async trackSearch(
    query: SearchQuery,
    results: SearchResults,
    performanceMetrics?: PerformanceMetrics
  ): Promise<string | null> {
    if (!this.config.enableTracking) {
      return null
    }

    try {
      const analytics: SearchAnalytics = {
        queryText: query.text,
        queryHash: this.generateQueryHash(query),
        filters: query.filters,
        resultsCount: results.totalCount,
        responseTimeMs: results.executionTime,
        sessionId: this.sessionId
      }

      // Log to database
      const { data, error } = await supabase.rpc('log_search_analytics', {
        query_text: analytics.queryText,
        filters: analytics.filters,
        results_count: analytics.resultsCount,
        response_time_ms: analytics.responseTimeMs,
        language: query.language || 'ar',
        session_id: analytics.sessionId
      })

      if (error) {
        console.error('Error logging search analytics:', error)
        return null
      }

      // Add to event queue for additional processing
      this.queueEvent({
        type: 'search',
        timestamp: Date.now(),
        sessionId: this.sessionId,
        data: {
          query,
          results: {
            totalCount: results.totalCount,
            executionTime: results.executionTime,
            hasMore: results.hasMore
          },
          performanceMetrics
        }
      })

      return data as string
    } catch (error) {
      console.error('Failed to track search:', error)
      return null
    }
  }

  /**
   * Track when a user clicks on a search result
   */
  async trackResultClick(
    analyticsId: string,
    resultId: string,
    resultType: ContentType,
    position: number
  ): Promise<void> {
    if (!this.config.enableClickTracking) {
      return
    }

    try {
      // Update database with click information
      const { error } = await supabase.rpc('update_search_analytics_click', {
        analytics_id: analyticsId,
        clicked_result_id: resultId,
        clicked_result_type: resultType
      })

      if (error) {
        console.error('Error tracking result click:', error)
        return
      }

      // Add to event queue
      this.queueEvent({
        type: 'click',
        timestamp: Date.now(),
        sessionId: this.sessionId,
        data: {
          analyticsId,
          resultId,
          resultType,
          position
        }
      })
    } catch (error) {
      console.error('Failed to track result click:', error)
    }
  }

  /**
   * Track suggestion clicks
   */
  async trackSuggestionClick(suggestion: string, suggestionType: string): Promise<void> {
    if (!this.config.enableTracking) {
      return
    }

    this.queueEvent({
      type: 'suggestion_click',
      timestamp: Date.now(),
      sessionId: this.sessionId,
      data: {
        suggestion,
        suggestionType
      }
    })
  }

  /**
   * Track filter applications
   */
  async trackFilterApplied(filterType: string, filterValue: any): Promise<void> {
    if (!this.config.enableTracking) {
      return
    }

    this.queueEvent({
      type: 'filter_applied',
      timestamp: Date.now(),
      sessionId: this.sessionId,
      data: {
        filterType,
        filterValue
      }
    })
  }

  /**
   * Get popular search queries
   */
  async getPopularQueries(limit: number = 10, timeRange?: { from: Date; to: Date }): Promise<PopularQuery[]> {
    try {
      let query = supabase
        .from('search_analytics')
        .select('query_text, query_hash, response_time_ms, results_count, created_at')
        .order('created_at', { ascending: false })

      if (timeRange) {
        query = query
          .gte('created_at', timeRange.from.toISOString())
          .lte('created_at', timeRange.to.toISOString())
      }

      const { data, error } = await query.limit(1000) // Get more data for aggregation

      if (error) {
        throw new SearchError(SearchErrorType.DATABASE_ERROR, 'Failed to fetch popular queries', error)
      }

      // Aggregate data
      const queryStats = new Map<string, {
        queryText: string
        searchCount: number
        totalResponseTime: number
        totalResultsCount: number
        lastSearched: string
      }>()

      for (const record of data || []) {
        const existing = queryStats.get(record.query_hash) || {
          queryText: record.query_text,
          searchCount: 0,
          totalResponseTime: 0,
          totalResultsCount: 0,
          lastSearched: record.created_at
        }

        existing.searchCount++
        existing.totalResponseTime += record.response_time_ms || 0
        existing.totalResultsCount += record.results_count || 0
        existing.lastSearched = record.created_at

        queryStats.set(record.query_hash, existing)
      }

      // Convert to PopularQuery format and sort by search count
      const popularQueries: PopularQuery[] = Array.from(queryStats.entries())
        .map(([queryHash, stats]) => ({
          queryText: stats.queryText,
          queryHash,
          searchCount: stats.searchCount,
          avgResponseTime: stats.totalResponseTime / stats.searchCount,
          avgResultsCount: stats.totalResultsCount / stats.searchCount,
          lastSearched: stats.lastSearched
        }))
        .sort((a, b) => b.searchCount - a.searchCount)
        .slice(0, limit)

      return popularQueries
    } catch (error) {
      console.error('Failed to get popular queries:', error)
      return []
    }
  }

  /**
   * Get search performance metrics
   */
  async getPerformanceMetrics(timeRange?: { from: Date; to: Date }): Promise<{
    averageResponseTime: number
    totalSearches: number
    zeroResultQueries: number
    popularFilters: Array<{ filter: string; count: number }>
    searchesByHour: Array<{ hour: number; count: number }>
  }> {
    try {
      let query = supabase
        .from('search_analytics')
        .select('response_time_ms, results_count, filters, created_at')

      if (timeRange) {
        query = query
          .gte('created_at', timeRange.from.toISOString())
          .lte('created_at', timeRange.to.toISOString())
      }

      const { data, error } = await query.limit(10000)

      if (error) {
        throw new SearchError(SearchErrorType.DATABASE_ERROR, 'Failed to fetch performance metrics', error)
      }

      const records = data || []
      const totalSearches = records.length
      const totalResponseTime = records.reduce((sum, r) => sum + (r.response_time_ms || 0), 0)
      const zeroResultQueries = records.filter(r => r.results_count === 0).length

      // Analyze popular filters
      const filterCounts = new Map<string, number>()
      const hourCounts = new Map<number, number>()

      for (const record of records) {
        // Count filters
        if (record.filters && typeof record.filters === 'object') {
          for (const [filterKey, filterValue] of Object.entries(record.filters)) {
            if (Array.isArray(filterValue) && filterValue.length > 0) {
              for (const value of filterValue) {
                const filterString = `${filterKey}:${value}`
                filterCounts.set(filterString, (filterCounts.get(filterString) || 0) + 1)
              }
            } else if (filterValue) {
              const filterString = `${filterKey}:${filterValue}`
              filterCounts.set(filterString, (filterCounts.get(filterString) || 0) + 1)
            }
          }
        }

        // Count searches by hour (using UTC to be consistent across timezones)
        const hour = new Date(record.created_at).getUTCHours()
        hourCounts.set(hour, (hourCounts.get(hour) || 0) + 1)
      }

      const popularFilters = Array.from(filterCounts.entries())
        .map(([filter, count]) => ({ filter, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10)

      const searchesByHour = Array.from(hourCounts.entries())
        .map(([hour, count]) => ({ hour, count }))
        .sort((a, b) => a.hour - b.hour)

      return {
        averageResponseTime: totalSearches > 0 ? totalResponseTime / totalSearches : 0,
        totalSearches,
        zeroResultQueries,
        popularFilters,
        searchesByHour
      }
    } catch (error) {
      console.error('Failed to get performance metrics:', error)
      return {
        averageResponseTime: 0,
        totalSearches: 0,
        zeroResultQueries: 0,
        popularFilters: [],
        searchesByHour: []
      }
    }
  }

  /**
   * Get click-through rates for search results
   */
  async getClickThroughRates(timeRange?: { from: Date; to: Date }): Promise<{
    overallCTR: number
    ctrByContentType: Array<{ contentType: string; ctr: number; totalSearches: number; totalClicks: number }>
    ctrByPosition: Array<{ position: number; ctr: number }>
  }> {
    try {
      let query = supabase
        .from('search_analytics')
        .select('clicked_result_id, clicked_result_type, results_count')

      if (timeRange) {
        query = query
          .gte('created_at', timeRange.from.toISOString())
          .lte('created_at', timeRange.to.toISOString())
      }

      const { data, error } = await query.limit(10000)

      if (error) {
        throw new SearchError(SearchErrorType.DATABASE_ERROR, 'Failed to fetch CTR data', error)
      }

      const records = data || []
      const totalSearches = records.length
      const totalClicks = records.filter(r => r.clicked_result_id).length
      const overallCTR = totalSearches > 0 ? totalClicks / totalSearches : 0

      // CTR by content type
      const contentTypeStats = new Map<string, { searches: number; clicks: number }>()
      
      for (const record of records) {
        if (record.clicked_result_type) {
          const stats = contentTypeStats.get(record.clicked_result_type) || { searches: 0, clicks: 0 }
          stats.searches++
          if (record.clicked_result_id) {
            stats.clicks++
          }
          contentTypeStats.set(record.clicked_result_type, stats)
        }
      }

      const ctrByContentType = Array.from(contentTypeStats.entries())
        .map(([contentType, stats]) => ({
          contentType,
          ctr: stats.searches > 0 ? stats.clicks / stats.searches : 0,
          totalSearches: stats.searches,
          totalClicks: stats.clicks
        }))
        .sort((a, b) => b.ctr - a.ctr)

      return {
        overallCTR,
        ctrByContentType,
        ctrByPosition: [] // Would need additional position tracking for this
      }
    } catch (error) {
      console.error('Failed to get CTR data:', error)
      return {
        overallCTR: 0,
        ctrByContentType: [],
        ctrByPosition: []
      }
    }
  }

  /**
   * Generate session ID for tracking user sessions
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Generate query hash for caching and analytics
   */
  private generateQueryHash(query: SearchQuery): string {
    const queryString = JSON.stringify({
      text: query.text.toLowerCase().trim(),
      filters: query.filters,
      sortBy: query.sortBy,
      language: query.language
    })
    
    // Simple hash function
    let hash = 0
    for (let i = 0; i < queryString.length; i++) {
      const char = queryString.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36)
  }

  /**
   * Add event to queue for batch processing
   */
  private queueEvent(event: SearchEvent): void {
    this.eventQueue.push(event)
    
    if (this.eventQueue.length >= this.config.batchSize) {
      this.flushEvents()
    }
  }

  /**
   * Start batch flush timer
   */
  private startBatchFlush(): void {
    this.flushTimer = setInterval(() => {
      this.flushEvents()
    }, this.config.flushInterval)
  }

  /**
   * Flush queued events (for future processing/external analytics)
   */
  private flushEvents(): void {
    if (this.eventQueue.length === 0) {
      return
    }

    // For now, just log events. In the future, this could send to external analytics
    console.debug('Flushing search analytics events:', this.eventQueue.length)
    
    // Clear the queue
    this.eventQueue = []
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
      this.flushTimer = undefined
    }
    
    // Flush any remaining events
    this.flushEvents()
  }
}

/**
 * Global analytics service instance
 */
export const searchAnalytics = new SearchAnalyticsService({
  enableTracking: true,
  batchSize: 5,
  flushInterval: 30000,
  enableClickTracking: true,
  enablePerformanceTracking: true
})

/**
 * React hook for search analytics
 */
export function useSearchAnalytics() {
  const trackSearch = async (query: SearchQuery, results: SearchResults) => {
    return await searchAnalytics.trackSearch(query, results)
  }

  const trackClick = async (analyticsId: string, resultId: string, resultType: ContentType, position: number) => {
    return await searchAnalytics.trackResultClick(analyticsId, resultId, resultType, position)
  }

  const trackSuggestion = async (suggestion: string, type: string) => {
    return await searchAnalytics.trackSuggestionClick(suggestion, type)
  }

  const trackFilter = async (filterType: string, filterValue: any) => {
    return await searchAnalytics.trackFilterApplied(filterType, filterValue)
  }

  return {
    trackSearch,
    trackClick,
    trackSuggestion,
    trackFilter
  }
}