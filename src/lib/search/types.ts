// Search system type definitions
export type ContentType = 'problem' | 'expert' | 'solution' | 'webinar'
export type SortOption = 'relevance' | 'date_created' | 'date_updated' | 'rating' | 'popularity'
export type Language = 'ar' | 'en' | 'auto'

export interface DateRange {
  from?: string
  to?: string
}

export interface RatingRange {
  min?: number
  max?: number
}

export interface SearchFilters {
  contentType?: ContentType[]
  sectors?: string[]
  categories?: string[]
  dateRange?: DateRange
  status?: string[]
  location?: string
  rating?: RatingRange
  urgency?: string[]
  availability?: string[]
  tags?: string[]
  sortBy?: SortOption
}

export interface PaginationOptions {
  limit: number
  offset: number
}

export interface SearchQuery {
  text: string
  filters: SearchFilters
  sortBy: SortOption
  pagination: PaginationOptions
  language?: Language
}

export interface SearchResultMetadata {
  status?: string
  urgency?: string
  created_at: string
  updated_at?: string
  submitter_name?: string
  rating?: number
  experience_years?: number
  location?: string
  availability?: string
  sector?: string
  [key: string]: any
}

export interface SearchResult {
  id: string
  type: ContentType
  title: string
  description: string
  category?: string
  sector?: string
  tags: string[]
  relevanceScore: number
  metadata: SearchResultMetadata
}

export interface SearchResults {
  items: SearchResult[]
  totalCount: number
  hasMore: boolean
  query: SearchQuery
  executionTime: number
}

export interface SearchSuggestion {
  text: string
  type: 'recent' | 'popular' | 'category' | 'expert' | 'tag'
  count?: number
}

export interface SearchAnalytics {
  queryText: string
  queryHash: string
  userId?: string
  filters: SearchFilters
  resultsCount: number
  responseTimeMs: number
  clickedResultId?: string
  clickedResultType?: ContentType
  sessionId?: string
}

// Error types
export enum SearchErrorType {
  INVALID_QUERY = 'INVALID_QUERY',
  DATABASE_ERROR = 'DATABASE_ERROR',
  CACHE_ERROR = 'CACHE_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR'
}

export class SearchError extends Error {
  constructor(
    public type: SearchErrorType,
    message: string,
    public details?: any
  ) {
    super(message)
    this.name = 'SearchError'
  }
}