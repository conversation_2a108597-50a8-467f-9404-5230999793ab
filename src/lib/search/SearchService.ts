import { supabase } from '../supabase'
import type { Database } from '../supabase'
import {
  SearchQuery,
  SearchResults,
  SearchResult,
  SearchSuggestion,
  SearchFilters,
  SearchError,
  SearchErrorType,
  ContentType,
  SortOption,
  SearchAnalytics
} from './types'
import { SearchCache, searchCache } from './SearchCache'
import { QueryOptimizer, queryOptimizer, performanceMonitor } from './QueryOptimizer'
import { SearchAnalyticsService, searchAnalytics } from './SearchAnalytics'

// Type helpers
type Tables = Database['public']['Tables']

export interface ISearchService {
  // Core search functionality
  search(query: SearchQuery): Promise<SearchResults>
  suggest(partial: string, limit?: number): Promise<SearchSuggestion[]>
  
  // Filter and sort operations
  applyFilters(results: SearchResult[], filters: SearchFilters): SearchResult[]
  sortResults(results: SearchResult[], sortBy: SortOption): SearchResult[]
  
  // Performance optimization
  getCachedResults(query: SearchQuery): Promise<SearchResults | null>
  cacheResults(query: SearchQuery, results: SearchResults): Promise<void>
  
  // Analytics
  trackSearch(analytics: SearchAnalytics): Promise<void>
  trackResultClick(analyticsId: string, resultId: string, resultType: ContentType, position: number): Promise<void>
  
  // Validation
  validateQuery(query: SearchQuery): void
  sanitizeQuery(text: string): string
}

export class SearchService implements ISearchService {
  private readonly MAX_QUERY_LENGTH = 500
  private readonly MIN_QUERY_LENGTH = 1
  private readonly DEFAULT_LIMIT = 20
  private readonly MAX_LIMIT = 100

  constructor(
    private cache: SearchCache = searchCache,
    private optimizer: QueryOptimizer = queryOptimizer,
    private analytics: SearchAnalyticsService = searchAnalytics
  ) {}

  /**
   * Main search method that performs full-text search across all content types
   */
  async search(query: SearchQuery): Promise<SearchResults> {
    const startTime = Date.now()
    let cacheHit = false
    let error = false
    
    try {
      // Validate and sanitize query
      this.validateQuery(query)
      const sanitizedQuery = {
        ...query,
        text: this.sanitizeQuery(query.text)
      }
      
      // Generate query hash for performance monitoring
      const queryHash = this.cache.generateKey(sanitizedQuery)
      
      // Check cache first
      const cachedResults = await this.getCachedResults(sanitizedQuery)
      if (cachedResults) {
        cacheHit = true
        const executionTime = Date.now() - startTime
        
        // Record performance metrics
        performanceMonitor.recordQueryPerformance(queryHash, executionTime, true)
        
        return {
          ...cachedResults,
          executionTime
        }
      }
      
      // Optimize query for large result sets
      const estimatedCount = this.optimizer.estimateResultCount(sanitizedQuery)
      const optimizedQuery = this.optimizer.optimizeQuery(sanitizedQuery, estimatedCount)
      
      // For very large estimated result sets, use progressive loading
      if (estimatedCount > 1000) {
        return await this.performProgressiveSearch(optimizedQuery, queryHash, startTime)
      }
      
      // Perform standard database search
      const results = await this.performDatabaseSearch(optimizedQuery)
      
      // Apply weighted ranking algorithm
      const rankedResults = this.applyWeightedRanking(results, optimizedQuery)
      
      // Apply client-side filtering if needed
      const filteredResults = this.applyFilters(rankedResults, optimizedQuery.filters)
      
      // Sort results with advanced tie-breaking
      const sortedResults = this.sortResults(filteredResults, optimizedQuery.sortBy)
      
      // Apply pagination
      const paginatedResults = this.applyPagination(sortedResults, optimizedQuery.pagination)
      
      const executionTime = Date.now() - startTime
      
      const searchResults: SearchResults = {
        items: paginatedResults,
        totalCount: filteredResults.length,
        hasMore: optimizedQuery.pagination.offset + optimizedQuery.pagination.limit < filteredResults.length,
        query: optimizedQuery,
        executionTime
      }
      
      // Record performance metrics
      performanceMonitor.recordQueryPerformance(queryHash, executionTime, false)
      
      // Track search analytics asynchronously
      this.analytics.trackSearch(optimizedQuery, searchResults).catch(analyticsError => {
        console.warn('Failed to track search analytics:', analyticsError)
      })
      
      // Cache the results asynchronously (don't wait for it)
      this.cacheResults(optimizedQuery, searchResults).catch(cacheError => {
        console.warn('Failed to cache search results:', cacheError)
      })
      
      return searchResults
    } catch (searchError) {
      error = true
      
      // Record error in performance monitoring
      const queryHash = this.cache.generateKey(query)
      performanceMonitor.recordQueryPerformance(queryHash, Date.now() - startTime, cacheHit, true)
      
      if (searchError instanceof SearchError) {
        throw searchError
      }
      
      console.error('Unexpected search error:', searchError)
      throw new SearchError(
        SearchErrorType.DATABASE_ERROR,
        'Search operation failed',
        searchError
      )
    }
  }

  /**
   * Provide search suggestions based on partial input using database function
   */
  async suggest(partial: string, limit: number = 10): Promise<SearchSuggestion[]> {
    try {
      const sanitizedPartial = this.sanitizeQuery(partial)
      if (sanitizedPartial.length < 2) {
        return []
      }

      // Use the database function for suggestions
      const { data, error } = await supabase.rpc('get_search_suggestions', {
        partial_query: sanitizedPartial,
        suggestion_type: 'all',
        limit_count: limit
      })
      
      if (error) {
        console.error('Error getting suggestions from database:', error)
        return []
      }
      
      return (data || []).map(row => ({
        text: row.suggestion,
        type: this.mapDbSuggestionType(row.type),
        count: row.count
      }))
    } catch (error) {
      console.error('Error getting suggestions:', error)
      return []
    }
  }

  /**
   * Apply filters to search results (client-side filtering for performance)
   */
  applyFilters(results: SearchResult[], filters: SearchFilters): SearchResult[] {
    let filtered = results

    // Filter by content type
    if (filters.contentType && filters.contentType.length > 0) {
      filtered = filtered.filter(result => 
        filters.contentType!.includes(result.type)
      )
    }

    // Filter by sectors
    if (filters.sectors && filters.sectors.length > 0) {
      filtered = filtered.filter(result => 
        result.sector && filters.sectors!.includes(result.sector)
      )
    }

    // Filter by categories
    if (filters.categories && filters.categories.length > 0) {
      filtered = filtered.filter(result => 
        result.category && filters.categories!.includes(result.category)
      )
    }

    // Filter by status
    if (filters.status && filters.status.length > 0) {
      filtered = filtered.filter(result => 
        result.metadata.status && filters.status!.includes(result.metadata.status)
      )
    }

    // Filter by rating range
    if (filters.rating) {
      filtered = filtered.filter(result => {
        const rating = result.metadata.rating
        if (rating === undefined) return false // Exclude items without ratings when rating filter is applied
        
        const { min, max } = filters.rating!
        if (min !== undefined && rating < min) return false
        if (max !== undefined && rating > max) return false
        return true
      })
    }

    // Filter by tags
    if (filters.tags && filters.tags.length > 0) {
      filtered = filtered.filter(result => 
        filters.tags!.some(tag => 
          result.tags.some(resultTag => 
            resultTag.toLowerCase().includes(tag.toLowerCase())
          )
        )
      )
    }

    return filtered
  }

  /**
   * Sort search results with advanced ranking and tie-breaking logic
   */
  sortResults(results: SearchResult[], sortBy: SortOption): SearchResult[] {
    const sorted = [...results]

    switch (sortBy) {
      case 'relevance':
        return sorted.sort((a, b) => {
          // Primary sort by relevance score
          const relevanceDiff = b.relevanceScore - a.relevanceScore
          if (Math.abs(relevanceDiff) > 0.01) return relevanceDiff
          
          // Tie-breaker 1: Content type priority (problems > experts > solutions > webinars)
          const typePriority = { problem: 4, expert: 3, solution: 2, webinar: 1 }
          const typeDiff = (typePriority[b.type] || 0) - (typePriority[a.type] || 0)
          if (typeDiff !== 0) return typeDiff
          
          // Tie-breaker 2: Rating for experts and solutions
          if ((a.type === 'expert' || a.type === 'solution') && (b.type === 'expert' || b.type === 'solution')) {
            const aRating = a.metadata.rating || 0
            const bRating = b.metadata.rating || 0
            const ratingDiff = bRating - aRating
            if (Math.abs(ratingDiff) > 0.1) return ratingDiff
          }
          
          // Tie-breaker 3: Recency
          const aDate = new Date(a.metadata.updated_at || a.metadata.created_at).getTime()
          const bDate = new Date(b.metadata.updated_at || b.metadata.created_at).getTime()
          return bDate - aDate
        })
      
      case 'date_created':
        return sorted.sort((a, b) => {
          const dateDiff = new Date(b.metadata.created_at).getTime() - new Date(a.metadata.created_at).getTime()
          if (dateDiff !== 0) return dateDiff
          
          // Tie-breaker: Relevance score
          return b.relevanceScore - a.relevanceScore
        })
      
      case 'date_updated':
        return sorted.sort((a, b) => {
          const aDate = new Date(a.metadata.updated_at || a.metadata.created_at).getTime()
          const bDate = new Date(b.metadata.updated_at || b.metadata.created_at).getTime()
          const dateDiff = bDate - aDate
          if (dateDiff !== 0) return dateDiff
          
          // Tie-breaker: Relevance score
          return b.relevanceScore - a.relevanceScore
        })
      
      case 'rating':
        return sorted.sort((a, b) => {
          const aRating = a.metadata.rating || 0
          const bRating = b.metadata.rating || 0
          const ratingDiff = bRating - aRating
          if (Math.abs(ratingDiff) > 0.01) return ratingDiff
          
          // Tie-breaker 1: Relevance score
          const relevanceDiff = b.relevanceScore - a.relevanceScore
          if (Math.abs(relevanceDiff) > 0.01) return relevanceDiff
          
          // Tie-breaker 2: Recency
          const aDate = new Date(a.metadata.updated_at || a.metadata.created_at).getTime()
          const bDate = new Date(b.metadata.updated_at || b.metadata.created_at).getTime()
          return bDate - aDate
        })
      
      case 'popularity':
        return sorted.sort((a, b) => {
          // Calculate popularity score based on multiple factors
          const aPopularity = this.calculatePopularityScore(a)
          const bPopularity = this.calculatePopularityScore(b)
          const popularityDiff = bPopularity - aPopularity
          if (Math.abs(popularityDiff) > 0.01) return popularityDiff
          
          // Tie-breaker: Relevance score
          return b.relevanceScore - a.relevanceScore
        })
      
      default:
        return sorted
    }
  }

  /**
   * Get cached search results
   */
  async getCachedResults(query: SearchQuery): Promise<SearchResults | null> {
    try {
      const cacheKey = this.cache.generateKey(query)
      return await this.cache.get(cacheKey)
    } catch (error) {
      console.warn('Cache retrieval failed:', error)
      return null // Gracefully degrade to database search
    }
  }

  /**
   * Cache search results with intelligent TTL
   */
  async cacheResults(query: SearchQuery, results: SearchResults): Promise<void> {
    try {
      const cacheKey = this.cache.generateKey(query)
      
      // Determine TTL based on query characteristics
      let ttl = 5 * 60 * 1000 // Default 5 minutes
      
      // Cache popular queries longer
      if (results.totalCount > 10) {
        ttl = 10 * 60 * 1000 // 10 minutes for queries with many results
      }
      
      // Cache simple queries longer
      if (query.text.split(' ').length <= 2 && Object.keys(query.filters).length === 0) {
        ttl = 15 * 60 * 1000 // 15 minutes for simple queries
      }
      
      // Cache filtered queries shorter (more likely to change)
      if (Object.keys(query.filters).length > 2) {
        ttl = 2 * 60 * 1000 // 2 minutes for complex filtered queries
      }
      
      await this.cache.set(cacheKey, results, ttl)
    } catch (error) {
      console.warn('Cache storage failed:', error)
      // Don't throw error - caching is optional
    }
  }

  /**
   * Track search analytics using database function
   */
  async trackSearch(analytics: SearchAnalytics): Promise<void> {
    try {
      const { data, error } = await supabase.rpc('log_search_analytics', {
        query_text: analytics.queryText,
        filters: analytics.filters,
        results_count: analytics.resultsCount,
        response_time_ms: analytics.responseTimeMs,
        language: 'ar', // Default to Arabic, can be enhanced
        session_id: analytics.sessionId
      })
      
      if (error) {
        console.error('Error tracking search analytics:', error)
      }
      
      // Store analytics ID for potential click tracking
      if (data && analytics.clickedResultId) {
        await this.trackSearchClick(data, analytics.clickedResultId, analytics.clickedResultType)
      }
    } catch (error) {
      console.error('Error tracking search analytics:', error)
      // Don't throw error for analytics failures
    }
  }
  
  /**
   * Track search result clicks
   */
  async trackSearchClick(analyticsId: string, resultId: string, resultType?: ContentType): Promise<void> {
    try {
      await supabase.rpc('update_search_analytics_click', {
        analytics_id: analyticsId,
        clicked_result_id: resultId,
        clicked_result_type: resultType
      })
    } catch (error) {
      console.error('Error tracking search click:', error)
    }
  }

  /**
   * Track result click with position information
   */
  async trackResultClick(analyticsId: string, resultId: string, resultType: ContentType, position: number): Promise<void> {
    try {
      await this.analytics.trackResultClick(analyticsId, resultId, resultType, position)
    } catch (error) {
      console.error('Error tracking result click:', error)
    }
  }

  /**
   * Validate search query parameters
   */
  validateQuery(query: SearchQuery): void {
    if (!query.text || typeof query.text !== 'string') {
      throw new SearchError(
        SearchErrorType.INVALID_QUERY,
        'Search text is required and must be a string'
      )
    }

    if (query.text.length < this.MIN_QUERY_LENGTH) {
      throw new SearchError(
        SearchErrorType.INVALID_QUERY,
        `Search text must be at least ${this.MIN_QUERY_LENGTH} character(s)`
      )
    }

    if (query.text.length > this.MAX_QUERY_LENGTH) {
      throw new SearchError(
        SearchErrorType.INVALID_QUERY,
        `Search text must not exceed ${this.MAX_QUERY_LENGTH} characters`
      )
    }

    if (query.pagination.limit > this.MAX_LIMIT) {
      throw new SearchError(
        SearchErrorType.INVALID_QUERY,
        `Limit must not exceed ${this.MAX_LIMIT}`
      )
    }

    if (query.pagination.limit < 1) {
      throw new SearchError(
        SearchErrorType.INVALID_QUERY,
        'Limit must be at least 1'
      )
    }

    if (query.pagination.offset < 0) {
      throw new SearchError(
        SearchErrorType.INVALID_QUERY,
        'Offset must be non-negative'
      )
    }
  }

  /**
   * Sanitize search query text
   */
  sanitizeQuery(text: string): string {
    return text
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/['"]/g, '') // Remove quotes that might break SQL
      .replace(/\s+/g, ' ') // Normalize whitespace
  }

  /**
   * Perform progressive search for large result sets
   */
  private async performProgressiveSearch(
    query: SearchQuery, 
    queryHash: string, 
    startTime: number
  ): Promise<SearchResults> {
    const strategy = this.optimizer.createProgressiveLoadingStrategy(query)
    
    // Execute initial query
    const initialResults = await this.performDatabaseSearch(strategy.initialQuery)
    const rankedInitial = this.applyWeightedRanking(initialResults, strategy.initialQuery)
    const filteredInitial = this.applyFilters(rankedInitial, strategy.initialQuery.filters)
    const sortedInitial = this.sortResults(filteredInitial, strategy.initialQuery.sortBy)
    
    const executionTime = Date.now() - startTime
    
    const searchResults: SearchResults = {
      items: sortedInitial,
      totalCount: filteredInitial.length,
      hasMore: strategy.nextQueries.length > 0 || sortedInitial.length >= strategy.initialQuery.pagination.limit,
      query: strategy.initialQuery,
      executionTime
    }
    
    // Record performance
    performanceMonitor.recordQueryPerformance(queryHash, executionTime, false)
    
    // Track analytics for progressive search
    this.analytics.trackSearch(strategy.initialQuery, searchResults).catch(error => {
      console.warn('Failed to track progressive search analytics:', error)
    })
    
    // Cache initial results
    this.cacheResults(strategy.initialQuery, searchResults).catch(error => {
      console.warn('Failed to cache progressive search results:', error)
    })
    
    return searchResults
  }

  /**
   * Perform the actual database search using enhanced_search function
   */
  private async performDatabaseSearch(query: SearchQuery): Promise<SearchResult[]> {
    try {
      // Prepare parameters for the enhanced_search function
      const language = query.language === 'en' ? 'en' : 'ar'
      const sortBy = this.mapSortOptionToDb(query.sortBy)
      
      // Convert date range to timestamps
      const dateFrom = query.filters.dateRange?.from ? new Date(query.filters.dateRange.from).toISOString() : null
      const dateTo = query.filters.dateRange?.to ? new Date(query.filters.dateRange.to).toISOString() : null
      
      // Extract minimum rating
      const minRating = query.filters.rating?.min || null
      
      // Call the enhanced_search database function
      const { data, error } = await supabase.rpc('enhanced_search', {
        search_query: query.text,
        content_types: query.filters.contentType || null,
        sectors: query.filters.sectors || null,
        categories: query.filters.categories || null,
        statuses: query.filters.status || null,
        date_from: dateFrom,
        date_to: dateTo,
        min_rating: minRating,
        location_filter: query.filters.location || null,
        language: language,
        sort_by: sortBy,
        sort_order: 'desc',
        limit_count: query.pagination.limit * 2, // Get more for client-side filtering
        offset_count: query.pagination.offset
      })
      
      if (error) {
        throw new SearchError(
          SearchErrorType.DATABASE_ERROR,
          'Enhanced search function failed',
          error
        )
      }
      
      // Convert database results to SearchResult format
      return (data || []).map(row => ({
        id: row.id,
        type: row.content_type as ContentType,
        title: row.title,
        description: row.description,
        category: row.category,
        sector: row.sector,
        tags: row.tags || [],
        relevanceScore: row.relevance_score || 0,
        metadata: {
          ...row.metadata,
          created_at: row.metadata?.created_at || new Date().toISOString(),
          similarity_score: row.similarity_score
        }
      }))
    } catch (error) {
      if (error instanceof SearchError) {
        throw error
      }
      
      console.error('Database search error:', error)
      throw new SearchError(
        SearchErrorType.DATABASE_ERROR,
        'Database search operation failed',
        error
      )
    }
  }





  /**
   * Apply pagination to results
   */
  private applyPagination(results: SearchResult[], pagination: { limit: number; offset: number }): SearchResult[] {
    return results.slice(pagination.offset, pagination.offset + pagination.limit)
  }



  /**
   * Extract skills from expertise areas JSON
   */
  private extractSkillsFromExpertise(expertiseAreas: any[]): string[] {
    if (!Array.isArray(expertiseAreas)) return []
    
    const skills: string[] = []
    expertiseAreas.forEach(area => {
      if (area.skills && Array.isArray(area.skills)) {
        skills.push(...area.skills)
      }
    })
    
    return skills
  }

  /**
   * Get main expertise category from expertise areas
   */
  private getMainExpertiseCategory(expertiseAreas: any[]): string | undefined {
    if (!Array.isArray(expertiseAreas) || expertiseAreas.length === 0) return undefined
    return expertiseAreas[0]?.category
  }
  
  /**
   * Map sort option to database sort parameter
   */
  private mapSortOptionToDb(sortBy: SortOption): string {
    switch (sortBy) {
      case 'relevance':
        return 'relevance'
      case 'date_created':
      case 'date_updated':
        return 'date'
      case 'rating':
        return 'rating'
      case 'popularity':
        return 'relevance' // Use relevance as popularity indicator
      default:
        return 'relevance'
    }
  }
  
  /**
   * Map database suggestion type to our type system
   */
  private mapDbSuggestionType(dbType: string): 'recent' | 'popular' | 'category' | 'expert' | 'tag' {
    switch (dbType) {
      case 'category':
        return 'category'
      case 'tag':
        return 'tag'
      case 'title':
        return 'popular'
      default:
        return 'popular'
    }
  }
  
  /**
   * Calculate popularity score based on multiple engagement factors
   */
  private calculatePopularityScore(result: SearchResult): number {
    let score = 0
    
    // Base score from relevance
    score += result.relevanceScore * 0.4
    
    // Rating factor (for experts and solutions)
    if (result.metadata.rating) {
      score += (result.metadata.rating / 5) * 0.3
    }
    
    // Content type factor
    const typeBoost = {
      problem: 0.8, // Problems are popular when they're urgent/recent
      expert: 1.0,  // Experts with high ratings are popular
      solution: 0.9, // Solutions with good ratings are popular
      webinar: 0.7   // Webinars are less frequently searched
    }
    score += (typeBoost[result.type] || 0.5) * 0.2
    
    // Recency factor (newer content is more popular)
    const daysSinceUpdate = this.getDaysSinceUpdate(result.metadata.updated_at || result.metadata.created_at)
    if (daysSinceUpdate <= 7) {
      score += 0.1 // Recent content boost
    } else if (daysSinceUpdate <= 30) {
      score += 0.05 // Moderate recency boost
    }
    
    // Status factor
    if (result.type === 'problem' && result.metadata.status === 'open') {
      score += 0.1 // Open problems are more popular
    } else if (result.type === 'expert' && result.metadata.availability === 'available') {
      score += 0.1 // Available experts are more popular
    }
    
    return Math.min(score, 1.0) // Cap at 1.0
  }
  
  /**
   * Calculate days since last update
   */
  private getDaysSinceUpdate(dateString: string): number {
    const updateDate = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - updateDate.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }
  
  /**
   * Apply weighted ranking algorithm to enhance search results
   */
  private applyWeightedRanking(results: SearchResult[], query: SearchQuery): SearchResult[] {
    return results.map(result => {
      let weightedScore = result.relevanceScore
      
      // Apply query-specific boosts
      const queryLower = query.text.toLowerCase()
      
      // Exact title match boost
      if (result.title.toLowerCase().includes(queryLower)) {
        weightedScore *= 1.2
      }
      
      // Category match boost (if user is filtering by category)
      if (query.filters.categories && query.filters.categories.length > 0) {
        if (result.category && query.filters.categories.includes(result.category)) {
          weightedScore *= 1.15
        }
      }
      
      // Sector match boost (if user is filtering by sector)
      if (query.filters.sectors && query.filters.sectors.length > 0) {
        if (result.sector && query.filters.sectors.includes(result.sector)) {
          weightedScore *= 1.15
        }
      }
      
      // Tag relevance boost
      if (result.tags.length > 0) {
        const queryWords = queryLower.split(' ')
        const tagMatches = result.tags.filter(tag => 
          queryWords.some(word => tag.toLowerCase().includes(word))
        ).length
        if (tagMatches > 0) {
          weightedScore *= (1 + (tagMatches / result.tags.length) * 0.1)
        }
      }
      
      // Quality indicators boost
      if (result.type === 'expert' && result.metadata.rating && result.metadata.rating > 4.0) {
        weightedScore *= 1.1
      }
      
      if (result.type === 'solution' && result.metadata.rating && result.metadata.rating > 4.0) {
        weightedScore *= 1.1
      }
      
      // Urgency boost for problems
      if (result.type === 'problem') {
        const urgencyBoost = {
          critical: 1.3,
          high: 1.2,
          medium: 1.1,
          low: 1.0
        }
        const urgency = result.metadata.urgency as keyof typeof urgencyBoost
        if (urgency && urgencyBoost[urgency]) {
          weightedScore *= urgencyBoost[urgency]
        }
      }
      
      return {
        ...result,
        relevanceScore: Math.min(weightedScore, 1.0) // Cap at 1.0
      }
    })
  }
}