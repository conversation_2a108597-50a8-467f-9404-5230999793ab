import { SearchQuery, SearchFilters, SearchError, SearchErrorType } from './types'

/**
 * Query optimization strategies for large result sets
 */
export class QueryOptimizer {
  private readonly LARGE_RESULT_THRESHOLD = 1000
  private readonly MAX_CONCURRENT_QUERIES = 3

  /**
   * Optimize search query for better performance
   */
  optimizeQuery(query: SearchQuery, estimatedResultCount?: number): SearchQuery {
    const optimized = { ...query }

    // For large result sets, apply optimizations
    if (estimatedResultCount && estimatedResultCount > this.LARGE_RESULT_THRESHOLD) {
      optimized.pagination = this.optimizePagination(query.pagination)
      optimized.filters = this.optimizeFilters(query.filters)
    }

    // Optimize text query
    optimized.text = this.optimizeTextQuery(query.text)

    return optimized
  }

  /**
   * Split complex queries into smaller, more efficient queries
   */
  splitComplexQuery(query: SearchQuery): SearchQuery[] {
    const queries: SearchQuery[] = []

    // If query has multiple content types, split by content type
    if (query.filters.contentType && query.filters.contentType.length > 1) {
      for (const contentType of query.filters.contentType) {
        queries.push({
          ...query,
          filters: {
            ...query.filters,
            contentType: [contentType]
          }
        })
      }
      return queries
    }

    // If query has multiple sectors, split by sector
    if (query.filters.sectors && query.filters.sectors.length > 2) {
      const sectorChunks = this.chunkArray(query.filters.sectors, 2)
      for (const sectorChunk of sectorChunks) {
        queries.push({
          ...query,
          filters: {
            ...query.filters,
            sectors: sectorChunk
          }
        })
      }
      return queries
    }

    // If no splitting is beneficial, return original query
    return [query]
  }

  /**
   * Merge results from split queries
   */
  mergeQueryResults(results: any[]): any {
    if (results.length === 0) return null
    if (results.length === 1) return results[0]

    const merged = {
      items: [],
      totalCount: 0,
      hasMore: false,
      executionTime: 0,
      query: results[0].query
    }

    // Merge all items
    for (const result of results) {
      merged.items.push(...result.items)
      merged.totalCount += result.totalCount
      merged.hasMore = merged.hasMore || result.hasMore
      merged.executionTime = Math.max(merged.executionTime, result.executionTime)
    }

    // Remove duplicates and sort by relevance
    merged.items = this.deduplicateResults(merged.items)
    merged.items.sort((a, b) => b.relevanceScore - a.relevanceScore)

    return merged
  }

  /**
   * Estimate result count based on query characteristics
   */
  estimateResultCount(query: SearchQuery): number {
    let estimate = 100 // Base estimate

    // Adjust based on query text length
    const wordCount = query.text.split(' ').length
    if (wordCount === 1) {
      estimate *= 3 // Single words tend to match more
    } else if (wordCount > 4) {
      estimate *= 0.3 // Long phrases match less
    }

    // Adjust based on filters
    const filterCount = Object.keys(query.filters).length
    if (filterCount === 0) {
      estimate *= 2 // No filters = more results
    } else {
      estimate *= Math.pow(0.7, filterCount) // Each filter reduces results
    }

    // Adjust based on content type
    if (query.filters.contentType) {
      const typeMultipliers = {
        problem: 1.5, // Problems are common
        expert: 0.8,  // Fewer experts
        solution: 1.2, // Moderate solutions
        webinar: 0.5   // Fewer webinars
      }
      
      const avgMultiplier = query.filters.contentType.reduce(
        (sum, type) => sum + (typeMultipliers[type] || 1), 0
      ) / query.filters.contentType.length
      
      estimate *= avgMultiplier
    }

    return Math.round(estimate)
  }

  /**
   * Create progressive loading strategy for large result sets
   */
  createProgressiveLoadingStrategy(query: SearchQuery): {
    initialQuery: SearchQuery
    nextQueries: SearchQuery[]
  } {
    const initialQuery = {
      ...query,
      pagination: {
        limit: Math.min(query.pagination.limit, 20), // Start with smaller batch
        offset: 0
      }
    }

    const nextQueries: SearchQuery[] = []
    const remainingLimit = query.pagination.limit - initialQuery.pagination.limit

    if (remainingLimit > 0) {
      let offset = initialQuery.pagination.limit
      while (offset < query.pagination.limit) {
        const batchSize = Math.min(20, remainingLimit - (offset - initialQuery.pagination.limit))
        nextQueries.push({
          ...query,
          pagination: {
            limit: batchSize,
            offset
          }
        })
        offset += batchSize
      }
    }

    return { initialQuery, nextQueries }
  }

  /**
   * Optimize pagination for large result sets
   */
  private optimizePagination(pagination: { limit: number; offset: number }): { limit: number; offset: number } {
    return {
      limit: Math.min(pagination.limit, 50), // Cap at 50 for large result sets
      offset: pagination.offset
    }
  }

  /**
   * Optimize filters to reduce database load
   */
  private optimizeFilters(filters: SearchFilters): SearchFilters {
    const optimized = { ...filters }

    // Limit the number of categories/sectors to prevent overly complex queries
    if (optimized.categories && optimized.categories.length > 5) {
      optimized.categories = optimized.categories.slice(0, 5)
    }

    if (optimized.sectors && optimized.sectors.length > 3) {
      optimized.sectors = optimized.sectors.slice(0, 3)
    }

    return optimized
  }

  /**
   * Optimize text query for better database performance
   */
  private optimizeTextQuery(text: string): string {
    // Remove common stop words that don't add search value
    const arabicStopWords = ['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك']
    const englishStopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']
    
    const words = text.split(' ')
    const filteredWords = words.filter(word => {
      const lowerWord = word.toLowerCase()
      return !arabicStopWords.includes(lowerWord) && !englishStopWords.includes(lowerWord)
    })

    // If we filtered out too many words, keep the original
    if (filteredWords.length < words.length * 0.5) {
      return text
    }

    return filteredWords.join(' ')
  }

  /**
   * Remove duplicate results based on ID
   */
  private deduplicateResults(items: any[]): any[] {
    const seen = new Set()
    return items.filter(item => {
      if (seen.has(item.id)) {
        return false
      }
      seen.add(item.id)
      return true
    })
  }

  /**
   * Split array into chunks
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }
}

/**
 * Global query optimizer instance
 */
export const queryOptimizer = new QueryOptimizer()

/**
 * Performance monitoring for search queries
 */
export class SearchPerformanceMonitor {
  private performanceData: Map<string, {
    queryHash: string
    executionTimes: number[]
    cacheHits: number
    cacheMisses: number
    errorCount: number
    lastExecuted: number
  }> = new Map()

  /**
   * Record query performance
   */
  recordQueryPerformance(
    queryHash: string,
    executionTime: number,
    cacheHit: boolean,
    error?: boolean
  ): void {
    const existing = this.performanceData.get(queryHash) || {
      queryHash,
      executionTimes: [],
      cacheHits: 0,
      cacheMisses: 0,
      errorCount: 0,
      lastExecuted: 0
    }

    existing.executionTimes.push(executionTime)
    if (existing.executionTimes.length > 100) {
      existing.executionTimes = existing.executionTimes.slice(-50) // Keep last 50
    }

    if (cacheHit) {
      existing.cacheHits++
    } else {
      existing.cacheMisses++
    }

    if (error) {
      existing.errorCount++
    }

    existing.lastExecuted = Date.now()
    this.performanceData.set(queryHash, existing)
  }

  /**
   * Get performance statistics
   */
  getPerformanceStats(queryHash?: string): any {
    if (queryHash) {
      const data = this.performanceData.get(queryHash)
      if (!data) return null

      return {
        queryHash,
        averageExecutionTime: data.executionTimes.reduce((a, b) => a + b, 0) / data.executionTimes.length,
        medianExecutionTime: this.calculateMedian(data.executionTimes),
        cacheHitRate: data.cacheHits / (data.cacheHits + data.cacheMisses),
        errorRate: data.errorCount / (data.cacheHits + data.cacheMisses + data.errorCount),
        totalExecutions: data.cacheHits + data.cacheMisses,
        lastExecuted: new Date(data.lastExecuted)
      }
    }

    // Return overall stats
    const allData = Array.from(this.performanceData.values())
    const totalExecutions = allData.reduce((sum, data) => sum + data.cacheHits + data.cacheMisses, 0)
    const totalCacheHits = allData.reduce((sum, data) => sum + data.cacheHits, 0)
    const totalErrors = allData.reduce((sum, data) => sum + data.errorCount, 0)
    const allExecutionTimes = allData.flatMap(data => data.executionTimes)

    return {
      totalQueries: this.performanceData.size,
      totalExecutions,
      averageExecutionTime: allExecutionTimes.reduce((a, b) => a + b, 0) / allExecutionTimes.length,
      overallCacheHitRate: totalCacheHits / totalExecutions,
      overallErrorRate: totalErrors / totalExecutions,
      slowestQueries: allData
        .map(data => ({
          queryHash: data.queryHash,
          averageTime: data.executionTimes.reduce((a, b) => a + b, 0) / data.executionTimes.length
        }))
        .sort((a, b) => b.averageTime - a.averageTime)
        .slice(0, 10)
    }
  }

  /**
   * Identify queries that need optimization
   */
  getQueriesNeedingOptimization(): string[] {
    const threshold = 1000 // 1 second
    const queries: string[] = []

    for (const [queryHash, data] of this.performanceData.entries()) {
      const avgTime = data.executionTimes.reduce((a, b) => a + b, 0) / data.executionTimes.length
      if (avgTime > threshold) {
        queries.push(queryHash)
      }
    }

    return queries
  }

  private calculateMedian(numbers: number[]): number {
    const sorted = [...numbers].sort((a, b) => a - b)
    const mid = Math.floor(sorted.length / 2)
    return sorted.length % 2 === 0
      ? (sorted[mid - 1] + sorted[mid]) / 2
      : sorted[mid]
  }
}

/**
 * Global performance monitor instance
 */
export const performanceMonitor = new SearchPerformanceMonitor()