import { SearchFilters, ContentType, SortOption } from './types'

/**
 * Utility functions for persisting search filters in URL state
 */

export interface SearchUrlParams {
  q?: string
  contentType?: string
  sectors?: string
  categories?: string
  status?: string
  dateFrom?: string
  dateTo?: string
  location?: string
  minRating?: string
  maxRating?: string
  urgency?: string
  tags?: string
  sortBy?: SortOption
  page?: string
  limit?: string
}

/**
 * Convert SearchFilters to URL search parameters
 */
export function filtersToUrlParams(filters: SearchFilters, query?: string, sortBy?: SortOption): URLSearchParams {
  const params = new URLSearchParams()
  
  // Add query if provided
  if (query?.trim()) {
    params.set('q', query.trim())
  }
  
  // Add sort option
  if (sortBy && sortBy !== 'relevance') {
    params.set('sortBy', sortBy)
  }
  
  // Content type filter
  if (filters.contentType && filters.contentType.length > 0) {
    params.set('contentType', filters.contentType.join(','))
  }
  
  // Sectors filter
  if (filters.sectors && filters.sectors.length > 0) {
    params.set('sectors', filters.sectors.join(','))
  }
  
  // Categories filter
  if (filters.categories && filters.categories.length > 0) {
    params.set('categories', filters.categories.join(','))
  }
  
  // Status filter
  if (filters.status && filters.status.length > 0) {
    params.set('status', filters.status.join(','))
  }
  
  // Date range filter
  if (filters.dateRange?.from) {
    params.set('dateFrom', filters.dateRange.from)
  }
  if (filters.dateRange?.to) {
    params.set('dateTo', filters.dateRange.to)
  }
  
  // Location filter
  if (filters.location?.trim()) {
    params.set('location', filters.location.trim())
  }
  
  // Rating filter
  if (filters.rating?.min !== undefined) {
    params.set('minRating', filters.rating.min.toString())
  }
  if (filters.rating?.max !== undefined) {
    params.set('maxRating', filters.rating.max.toString())
  }
  
  // Urgency filter
  if (filters.urgency && filters.urgency.length > 0) {
    params.set('urgency', filters.urgency.join(','))
  }
  
  // Tags filter
  if (filters.tags && filters.tags.length > 0) {
    params.set('tags', filters.tags.join(','))
  }
  
  return params
}

/**
 * Convert URL search parameters to SearchFilters
 */
export function urlParamsToFilters(searchParams: URLSearchParams): {
  filters: SearchFilters
  query: string
  sortBy: SortOption
} {
  const filters: SearchFilters = {}
  
  // Parse content type
  const contentTypeParam = searchParams.get('contentType')
  if (contentTypeParam) {
    const contentTypes = contentTypeParam.split(',').filter(type => 
      ['problem', 'expert', 'solution', 'webinar'].includes(type)
    ) as ContentType[]
    if (contentTypes.length > 0) {
      filters.contentType = contentTypes
    }
  }
  
  // Parse sectors
  const sectorsParam = searchParams.get('sectors')
  if (sectorsParam) {
    filters.sectors = sectorsParam.split(',').filter(sector => sector.trim())
  }
  
  // Parse categories
  const categoriesParam = searchParams.get('categories')
  if (categoriesParam) {
    filters.categories = categoriesParam.split(',').filter(category => category.trim())
  }
  
  // Parse status
  const statusParam = searchParams.get('status')
  if (statusParam) {
    filters.status = statusParam.split(',').filter(status => status.trim())
  }
  
  // Parse date range
  const dateFrom = searchParams.get('dateFrom')
  const dateTo = searchParams.get('dateTo')
  if (dateFrom || dateTo) {
    filters.dateRange = {}
    if (dateFrom) {
      try {
        // Validate date format
        new Date(dateFrom).toISOString()
        filters.dateRange.from = dateFrom
      } catch (error) {
        console.warn('Invalid dateFrom parameter:', dateFrom)
      }
    }
    if (dateTo) {
      try {
        // Validate date format
        new Date(dateTo).toISOString()
        filters.dateRange.to = dateTo
      } catch (error) {
        console.warn('Invalid dateTo parameter:', dateTo)
      }
    }
  }
  
  // Parse location
  const location = searchParams.get('location')
  if (location?.trim()) {
    filters.location = location.trim()
  }
  
  // Parse rating range
  const minRating = searchParams.get('minRating')
  const maxRating = searchParams.get('maxRating')
  if (minRating || maxRating) {
    filters.rating = {}
    if (minRating) {
      const min = parseFloat(minRating)
      if (!isNaN(min) && min >= 0 && min <= 5) {
        filters.rating.min = min
      }
    }
    if (maxRating) {
      const max = parseFloat(maxRating)
      if (!isNaN(max) && max >= 0 && max <= 5) {
        filters.rating.max = max
      }
    }
  }
  
  // Parse urgency
  const urgencyParam = searchParams.get('urgency')
  if (urgencyParam) {
    const urgencyLevels = urgencyParam.split(',').filter(level => 
      ['low', 'medium', 'high', 'critical'].includes(level)
    )
    if (urgencyLevels.length > 0) {
      filters.urgency = urgencyLevels
    }
  }
  
  // Parse tags
  const tagsParam = searchParams.get('tags')
  if (tagsParam) {
    const tags = tagsParam.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)
    if (tags.length > 0) {
      filters.tags = tags
    }
  }
  
  // Parse query
  const query = searchParams.get('q') || ''
  
  // Parse sort option
  const sortByParam = searchParams.get('sortBy')
  const sortBy: SortOption = ['relevance', 'date_created', 'date_updated', 'rating', 'popularity'].includes(sortByParam || '')
    ? (sortByParam as SortOption)
    : 'relevance'
  
  return { filters, query, sortBy }
}

/**
 * Generate a shareable URL for search results
 */
export function generateShareableUrl(
  baseUrl: string,
  query: string,
  filters: SearchFilters,
  sortBy?: SortOption
): string {
  const params = filtersToUrlParams(filters, query, sortBy)
  const url = new URL('/search', baseUrl)
  url.search = params.toString()
  return url.toString()
}

/**
 * Create a filter preset that can be saved and reused
 */
export interface FilterPreset {
  id: string
  name: string
  filters: SearchFilters
  sortBy: SortOption
  createdAt: string
  usageCount: number
}

/**
 * Save a filter preset to localStorage
 */
export function saveFilterPreset(name: string, filters: SearchFilters, sortBy: SortOption): FilterPreset {
  const preset: FilterPreset = {
    id: generatePresetId(),
    name: name.trim(),
    filters: { ...filters },
    sortBy,
    createdAt: new Date().toISOString(),
    usageCount: 0
  }
  
  const existingPresets = getFilterPresets()
  const updatedPresets = [...existingPresets, preset]
  
  localStorage.setItem('searchFilterPresets', JSON.stringify(updatedPresets))
  
  return preset
}

/**
 * Get all saved filter presets from localStorage
 */
export function getFilterPresets(): FilterPreset[] {
  try {
    const presetsJson = localStorage.getItem('searchFilterPresets')
    if (!presetsJson) return []
    
    const presets = JSON.parse(presetsJson) as FilterPreset[]
    return Array.isArray(presets) ? presets : []
  } catch (error) {
    console.error('Error loading filter presets:', error)
    return []
  }
}

/**
 * Delete a filter preset
 */
export function deleteFilterPreset(presetId: string): void {
  const existingPresets = getFilterPresets()
  const updatedPresets = existingPresets.filter(preset => preset.id !== presetId)
  
  localStorage.setItem('searchFilterPresets', JSON.stringify(updatedPresets))
}

/**
 * Update usage count for a filter preset
 */
export function incrementPresetUsage(presetId: string): void {
  const existingPresets = getFilterPresets()
  const updatedPresets = existingPresets.map(preset => 
    preset.id === presetId 
      ? { ...preset, usageCount: preset.usageCount + 1 }
      : preset
  )
  
  localStorage.setItem('searchFilterPresets', JSON.stringify(updatedPresets))
}

/**
 * Generate a unique ID for filter presets
 */
function generatePresetId(): string {
  return `preset_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Validate filter values to ensure they're safe for URL encoding
 */
export function validateFilters(filters: SearchFilters): SearchFilters {
  const validated: SearchFilters = {}
  
  // Validate content types
  if (filters.contentType && Array.isArray(filters.contentType)) {
    const validTypes = filters.contentType.filter(type => 
      ['problem', 'expert', 'solution', 'webinar'].includes(type)
    ) as ContentType[]
    if (validTypes.length > 0) {
      validated.contentType = validTypes
    }
  }
  
  // Validate sectors
  if (filters.sectors && Array.isArray(filters.sectors)) {
    const validSectors = filters.sectors.filter(sector => 
      typeof sector === 'string' && sector.trim().length > 0
    )
    if (validSectors.length > 0) {
      validated.sectors = validSectors
    }
  }
  
  // Validate categories
  if (filters.categories && Array.isArray(filters.categories)) {
    const validCategories = filters.categories.filter(category => 
      typeof category === 'string' && category.trim().length > 0
    )
    if (validCategories.length > 0) {
      validated.categories = validCategories
    }
  }
  
  // Validate status
  if (filters.status && Array.isArray(filters.status)) {
    const validStatuses = filters.status.filter(status => 
      typeof status === 'string' && status.trim().length > 0
    )
    if (validStatuses.length > 0) {
      validated.status = validStatuses
    }
  }
  
  // Validate date range
  if (filters.dateRange) {
    const dateRange: { from?: string; to?: string } = {}
    if (filters.dateRange.from) {
      try {
        new Date(filters.dateRange.from).toISOString()
        dateRange.from = filters.dateRange.from
      } catch (error) {
        console.warn('Invalid date range from:', filters.dateRange.from)
      }
    }
    if (filters.dateRange.to) {
      try {
        new Date(filters.dateRange.to).toISOString()
        dateRange.to = filters.dateRange.to
      } catch (error) {
        console.warn('Invalid date range to:', filters.dateRange.to)
      }
    }
    if (dateRange.from || dateRange.to) {
      validated.dateRange = dateRange
    }
  }
  
  // Validate location
  if (filters.location && typeof filters.location === 'string') {
    const location = filters.location.trim()
    if (location.length > 0 && location.length <= 100) {
      validated.location = location
    }
  }
  
  // Validate rating range
  if (filters.rating) {
    const rating: { min?: number; max?: number } = {}
    if (typeof filters.rating.min === 'number' && filters.rating.min >= 0 && filters.rating.min <= 5) {
      rating.min = filters.rating.min
    }
    if (typeof filters.rating.max === 'number' && filters.rating.max >= 0 && filters.rating.max <= 5) {
      rating.max = filters.rating.max
    }
    if (rating.min !== undefined || rating.max !== undefined) {
      validated.rating = rating
    }
  }
  
  // Validate urgency
  if (filters.urgency && Array.isArray(filters.urgency)) {
    const validUrgencies = filters.urgency.filter(urgency => 
      ['low', 'medium', 'high', 'critical'].includes(urgency)
    )
    if (validUrgencies.length > 0) {
      validated.urgency = validUrgencies
    }
  }
  
  // Validate tags
  if (filters.tags && Array.isArray(filters.tags)) {
    const validTags = filters.tags
      .filter(tag => typeof tag === 'string')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0 && tag.length <= 50)
      .slice(0, 10) // Limit to 10 tags
    if (validTags.length > 0) {
      validated.tags = validTags
    }
  }
  
  return validated
}