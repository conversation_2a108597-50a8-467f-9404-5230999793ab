import { useQuery, useQueryClient } from '@tanstack/react-query'
import { SearchQuery, SearchResults, SearchSuggestion } from './types'
import { SearchService } from './SearchService'
import { cacheInvalidator } from './SearchCache'

// Global search service instance
const searchService = new SearchService()

/**
 * React Query key factory for search queries
 */
export const searchQueryKeys = {
  all: ['search'] as const,
  searches: () => [...searchQueryKeys.all, 'searches'] as const,
  search: (query: SearchQuery) => [...searchQueryKeys.searches(), query] as const,
  suggestions: () => [...searchQueryKeys.all, 'suggestions'] as const,
  suggestion: (partial: string) => [...searchQueryKeys.suggestions(), partial] as const,
}

/**
 * Hook for search queries with React Query caching
 */
export function useSearchQuery(
  query: SearchQuery,
  options: {
    enabled?: boolean
    staleTime?: number
    cacheTime?: number
  } = {}
) {
  const {
    enabled = true,
    staleTime = 5 * 60 * 1000, // 5 minutes
    cacheTime = 10 * 60 * 1000, // 10 minutes
  } = options

  return useQuery({
    queryKey: searchQueryKeys.search(query),
    queryFn: () => searchService.search(query),
    enabled: enabled && query.text.length > 0,
    staleTime,
    cacheTime,
    // Retry failed searches with exponential backoff
    retry: (failureCount, error) => {
      // Don't retry validation errors
      if (error instanceof Error && error.message.includes('INVALID_QUERY')) {
        return false
      }
      return failureCount < 3
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  })
}

/**
 * Hook for search suggestions with React Query caching
 */
export function useSearchSuggestions(
  partial: string,
  options: {
    enabled?: boolean
    debounceMs?: number
  } = {}
) {
  const {
    enabled = true,
    debounceMs = 300
  } = options

  return useQuery({
    queryKey: searchQueryKeys.suggestion(partial),
    queryFn: () => searchService.suggest(partial),
    enabled: enabled && partial.length >= 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
    cacheTime: 5 * 60 * 1000, // 5 minutes
    // Debounce suggestions
    refetchOnWindowFocus: false,
    retry: false, // Don't retry suggestion failures
  })
}

/**
 * Hook for search cache management
 */
export function useSearchCache() {
  const queryClient = useQueryClient()

  const invalidateSearches = () => {
    queryClient.invalidateQueries({ queryKey: searchQueryKeys.searches() })
  }

  const invalidateSuggestions = () => {
    queryClient.invalidateQueries({ queryKey: searchQueryKeys.suggestions() })
  }

  const invalidateAll = () => {
    queryClient.invalidateQueries({ queryKey: searchQueryKeys.all })
    cacheInvalidator.invalidateAll()
  }

  const invalidateByContentType = (contentType: string) => {
    // Invalidate React Query cache
    queryClient.invalidateQueries({ queryKey: searchQueryKeys.searches() })
    // Invalidate in-memory cache
    cacheInvalidator.onContentAdded(contentType)
  }

  const invalidateByItemId = (itemId: string, contentType: string) => {
    // Invalidate React Query cache
    queryClient.invalidateQueries({ queryKey: searchQueryKeys.searches() })
    // Invalidate in-memory cache
    cacheInvalidator.onContentUpdated(itemId, contentType)
  }

  const prefetchSearch = (query: SearchQuery) => {
    queryClient.prefetchQuery({
      queryKey: searchQueryKeys.search(query),
      queryFn: () => searchService.search(query),
      staleTime: 5 * 60 * 1000,
    })
  }

  return {
    invalidateSearches,
    invalidateSuggestions,
    invalidateAll,
    invalidateByContentType,
    invalidateByItemId,
    prefetchSearch,
  }
}

/**
 * Optimistic cache updates for better UX
 */
export function useOptimisticSearchUpdates() {
  const queryClient = useQueryClient()

  const updateSearchResultOptimistically = (
    itemId: string,
    contentType: string,
    updater: (item: any) => any
  ) => {
    // Update all search queries that might contain this item
    queryClient.setQueriesData(
      { queryKey: searchQueryKeys.searches() },
      (oldData: SearchResults | undefined) => {
        if (!oldData) return oldData

        return {
          ...oldData,
          items: oldData.items.map(item => 
            item.id === itemId ? updater(item) : item
          )
        }
      }
    )
  }

  const removeSearchResultOptimistically = (itemId: string) => {
    queryClient.setQueriesData(
      { queryKey: searchQueryKeys.searches() },
      (oldData: SearchResults | undefined) => {
        if (!oldData) return oldData

        return {
          ...oldData,
          items: oldData.items.filter(item => item.id !== itemId),
          totalCount: Math.max(0, oldData.totalCount - 1)
        }
      }
    )
  }

  const addSearchResultOptimistically = (newItem: any) => {
    queryClient.setQueriesData(
      { queryKey: searchQueryKeys.searches() },
      (oldData: SearchResults | undefined) => {
        if (!oldData) return oldData

        return {
          ...oldData,
          items: [newItem, ...oldData.items],
          totalCount: oldData.totalCount + 1
        }
      }
    )
  }

  return {
    updateSearchResultOptimistically,
    removeSearchResultOptimistically,
    addSearchResultOptimistically,
  }
}

/**
 * Background cache warming for popular queries
 */
export function useSearchCacheWarming() {
  const queryClient = useQueryClient()

  const warmPopularQueries = async () => {
    // Define popular search queries that should be pre-cached
    const popularQueries: Partial<SearchQuery>[] = [
      { text: 'تطوير البرمجيات', filters: { contentType: ['problem'] } },
      { text: 'الذكاء الاصطناعي', filters: { contentType: ['expert'] } },
      { text: 'الزراعة', filters: { sectors: ['agriculture'] } },
      { text: 'الطاقة المتجددة', filters: { sectors: ['energy'] } },
      { text: 'التمويل', filters: { sectors: ['finance'] } },
    ]

    // Warm cache for popular queries
    for (const partialQuery of popularQueries) {
      const fullQuery: SearchQuery = {
        text: partialQuery.text || '',
        filters: partialQuery.filters || {},
        sortBy: 'relevance',
        pagination: { limit: 20, offset: 0 },
        language: 'ar'
      }

      if (fullQuery.text) {
        queryClient.prefetchQuery({
          queryKey: searchQueryKeys.search(fullQuery),
          queryFn: () => searchService.search(fullQuery),
          staleTime: 10 * 60 * 1000, // Keep warm for 10 minutes
        })
      }
    }
  }

  return { warmPopularQueries }
}