import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { SearchQuery, SearchResults } from '../types'

// Mock Supabase
vi.mock('../../supabase', () => ({
  supabase: {
    rpc: vi.fn(),
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        order: vi.fn(() => ({
          limit: vi.fn(() => ({
            gte: vi.fn(() => ({
              lte: vi.fn(() => ({ data: [], error: null }))
            })),
            data: [],
            error: null
          })),
          gte: vi.fn(() => ({
            lte: vi.fn(() => ({ data: [], error: null }))
          })),
          data: [],
          error: null
        })),
        limit: vi.fn(() => ({
          gte: vi.fn(() => ({
            lte: vi.fn(() => ({ data: [], error: null }))
          })),
          data: [],
          error: null
        })),
        gte: vi.fn(() => ({
          lte: vi.fn(() => ({ data: [], error: null }))
        })),
        data: [],
        error: null
      }))
    }))
  }
}))

// Import after mocking
import { SearchAnalyticsService } from '../SearchAnalytics'
import { supabase } from '../../supabase'

describe('SearchAnalyticsService', () => {
  let analytics: SearchAnalyticsService
  
  beforeEach(() => {
    analytics = new SearchAnalyticsService({
      enableTracking: true,
      batchSize: 2,
      flushInterval: 100,
      enableClickTracking: true,
      enablePerformanceTracking: true
    })
    
    // Reset mocks
    vi.clearAllMocks()
  })
  
  afterEach(() => {
    analytics.destroy()
  })

  const createMockQuery = (text: string): SearchQuery => ({
    text,
    filters: {},
    sortBy: 'relevance',
    pagination: { limit: 10, offset: 0 },
    language: 'ar'
  })

  const createMockResults = (totalCount: number = 5): SearchResults => ({
    items: [],
    totalCount,
    hasMore: false,
    query: createMockQuery('test'),
    executionTime: 150
  })

  describe('search tracking', () => {
    it('should track search queries successfully', async () => {
      const mockAnalyticsId = 'analytics-123'
      vi.mocked(supabase.rpc).mockResolvedValueOnce({ data: mockAnalyticsId, error: null })

      const query = createMockQuery('test search')
      const results = createMockResults(10)

      const analyticsId = await analytics.trackSearch(query, results)

      expect(supabase.rpc).toHaveBeenCalledWith('log_search_analytics', {
        query_text: 'test search',
        filters: {},
        results_count: 10,
        response_time_ms: 150,
        language: 'ar',
        session_id: expect.any(String)
      })

      expect(analyticsId).toBe(mockAnalyticsId)
    })

    it('should handle search tracking errors gracefully', async () => {
      vi.mocked(supabase.rpc).mockResolvedValueOnce({ data: null, error: new Error('Database error') })

      const query = createMockQuery('test search')
      const results = createMockResults()

      const analyticsId = await analytics.trackSearch(query, results)

      expect(analyticsId).toBeNull()
    })

    it('should not track when tracking is disabled', async () => {
      const disabledAnalytics = new SearchAnalyticsService({ enableTracking: false })

      const query = createMockQuery('test search')
      const results = createMockResults()

      const analyticsId = await disabledAnalytics.trackSearch(query, results)

      expect(supabase.rpc).not.toHaveBeenCalled()
      expect(analyticsId).toBeNull()

      disabledAnalytics.destroy()
    })

    it('should generate consistent query hashes', async () => {
      const query1 = createMockQuery('test search')
      const query2 = createMockQuery('test search')
      
      // Access private method for testing
      const hash1 = (analytics as any).generateQueryHash(query1)
      const hash2 = (analytics as any).generateQueryHash(query2)
      
      expect(hash1).toBe(hash2)
      expect(hash1).toMatch(/^[a-z0-9]+$/)
    })

    it('should track performance metrics', async () => {
      vi.mocked(supabase.rpc).mockResolvedValueOnce({ data: 'analytics-123', error: null })

      const query = createMockQuery('performance test')
      const results = createMockResults(25)
      results.executionTime = 250

      const performanceMetrics = {
        queryHash: 'test-hash',
        executionTime: 250,
        cacheHit: false,
        resultsCount: 25,
        timestamp: Date.now()
      }

      await analytics.trackSearch(query, results, performanceMetrics)

      expect(supabase.rpc).toHaveBeenCalledWith('log_search_analytics', {
        query_text: 'performance test',
        filters: {},
        results_count: 25,
        response_time_ms: 250,
        language: 'ar',
        session_id: expect.any(String)
      })
    })
  })

  describe('click tracking', () => {
    it('should track result clicks successfully', async () => {
      vi.mocked(supabase.rpc).mockResolvedValueOnce({ data: null, error: null })

      await analytics.trackResultClick('analytics-123', 'result-456', 'problem', 1)

      expect(supabase.rpc).toHaveBeenCalledWith('update_search_analytics_click', {
        analytics_id: 'analytics-123',
        clicked_result_id: 'result-456',
        clicked_result_type: 'problem'
      })
    })

    it('should handle click tracking errors gracefully', async () => {
      vi.mocked(supabase.rpc).mockResolvedValueOnce({ data: null, error: new Error('Database error') })

      // Should not throw
      await expect(
        analytics.trackResultClick('analytics-123', 'result-456', 'problem', 1)
      ).resolves.toBeUndefined()
    })

    it('should not track clicks when click tracking is disabled', async () => {
      const disabledAnalytics = new SearchAnalyticsService({ enableClickTracking: false })

      await disabledAnalytics.trackResultClick('analytics-123', 'result-456', 'problem', 1)

      expect(supabase.rpc).not.toHaveBeenCalled()

      disabledAnalytics.destroy()
    })

    it('should track clicks with position information', async () => {
      vi.mocked(supabase.rpc).mockResolvedValueOnce({ data: null, error: null })

      await analytics.trackResultClick('analytics-123', 'result-456', 'expert', 3)

      expect(supabase.rpc).toHaveBeenCalledWith('update_search_analytics_click', {
        analytics_id: 'analytics-123',
        clicked_result_id: 'result-456',
        clicked_result_type: 'expert'
      })
    })
  })

  describe('suggestion tracking', () => {
    it('should track suggestion clicks', async () => {
      const consoleSpy = vi.spyOn(console, 'debug').mockImplementation(() => {})

      await analytics.trackSuggestionClick('test suggestion', 'category')

      // Should queue the event (will be flushed when batch size is reached)
      expect(consoleSpy).not.toHaveBeenCalled() // Not flushed yet

      // Add another event to trigger flush
      await analytics.trackSuggestionClick('another suggestion', 'tag')

      // Wait a bit for flush
      await new Promise(resolve => setTimeout(resolve, 10))

      consoleSpy.mockRestore()
    })

    it('should track different suggestion types', async () => {
      await analytics.trackSuggestionClick('technology', 'category')
      await analytics.trackSuggestionClick('javascript', 'tag')
      await analytics.trackSuggestionClick('expert name', 'expert')

      // Events should be queued
      expect(supabase.rpc).not.toHaveBeenCalled() // Only database operations call rpc
    })
  })

  describe('filter tracking', () => {
    it('should track filter applications', async () => {
      await analytics.trackFilterApplied('contentType', ['problem', 'expert'])

      // Event should be queued
      expect(supabase.rpc).not.toHaveBeenCalled() // Only database operations call rpc
    })

    it('should track different filter types', async () => {
      await analytics.trackFilterApplied('sectors', ['technology'])
      await analytics.trackFilterApplied('rating', { min: 4.0 })
      await analytics.trackFilterApplied('dateRange', { from: '2024-01-01', to: '2024-12-31' })

      // Events should be queued
      expect(supabase.rpc).not.toHaveBeenCalled()
    })
  })

  describe('popular queries', () => {
    it('should fetch popular queries successfully', async () => {
      const mockData = [
        {
          query_text: 'popular query',
          query_hash: 'hash123',
          response_time_ms: 100,
          results_count: 5,
          created_at: '2023-01-01T00:00:00Z'
        },
        {
          query_text: 'popular query',
          query_hash: 'hash123',
          response_time_ms: 200,
          results_count: 3,
          created_at: '2023-01-02T00:00:00Z'
        }
      ]

      const mockQuery = {
        select: vi.fn(() => mockQuery),
        order: vi.fn(() => mockQuery),
        gte: vi.fn(() => mockQuery),
        lte: vi.fn(() => mockQuery),
        limit: vi.fn(() => Promise.resolve({ data: mockData, error: null }))
      }

      vi.mocked(supabase.from).mockReturnValueOnce(mockQuery)

      const popularQueries = await analytics.getPopularQueries(5)

      expect(popularQueries).toHaveLength(1)
      expect(popularQueries[0]).toEqual({
        queryText: 'popular query',
        queryHash: 'hash123',
        searchCount: 2,
        avgResponseTime: 150,
        avgResultsCount: 4,
        lastSearched: '2023-01-02T00:00:00Z'
      })
    })

    it('should handle errors when fetching popular queries', async () => {
      const mockQuery = {
        select: vi.fn(() => mockQuery),
        order: vi.fn(() => mockQuery),
        limit: vi.fn(() => Promise.resolve({ data: null, error: new Error('Database error') }))
      }

      vi.mocked(supabase.from).mockReturnValueOnce(mockQuery)

      const popularQueries = await analytics.getPopularQueries()

      expect(popularQueries).toEqual([])
    })

    it('should support time range filtering', async () => {
      const mockData = [
        {
          query_text: 'recent query',
          query_hash: 'hash456',
          response_time_ms: 150,
          results_count: 8,
          created_at: '2024-01-15T00:00:00Z'
        }
      ]

      const mockQuery = {
        select: vi.fn(() => mockQuery),
        order: vi.fn(() => mockQuery),
        gte: vi.fn(() => mockQuery),
        lte: vi.fn(() => mockQuery),
        limit: vi.fn(() => Promise.resolve({ data: mockData, error: null }))
      }

      vi.mocked(supabase.from).mockReturnValueOnce(mockQuery)

      const timeRange = {
        from: new Date('2024-01-01'),
        to: new Date('2024-01-31')
      }

      const popularQueries = await analytics.getPopularQueries(10, timeRange)

      expect(mockQuery.gte).toHaveBeenCalledWith('created_at', '2024-01-01T00:00:00.000Z')
      expect(mockQuery.lte).toHaveBeenCalledWith('created_at', '2024-01-31T00:00:00.000Z')
      expect(popularQueries).toHaveLength(1)
    })
  })

  describe('performance metrics', () => {
    it('should calculate performance metrics correctly', async () => {
      const mockData = [
        {
          response_time_ms: 100,
          results_count: 5,
          filters: { contentType: ['problem'] },
          created_at: '2023-01-01T10:00:00Z'
        },
        {
          response_time_ms: 200,
          results_count: 0,
          filters: { sectors: ['technology'] },
          created_at: '2023-01-01T11:00:00Z'
        },
        {
          response_time_ms: 150,
          results_count: 3,
          filters: { contentType: ['expert'], sectors: ['technology'] },
          created_at: '2023-01-01T12:00:00Z'
        }
      ]

      const mockQuery = {
        select: vi.fn(() => mockQuery),
        gte: vi.fn(() => mockQuery),
        lte: vi.fn(() => mockQuery),
        limit: vi.fn(() => Promise.resolve({ data: mockData, error: null }))
      }

      vi.mocked(supabase.from).mockReturnValueOnce(mockQuery)

      const metrics = await analytics.getPerformanceMetrics()

      expect(metrics.totalSearches).toBe(3)
      expect(metrics.averageResponseTime).toBe(150) // (100 + 200 + 150) / 3
      expect(metrics.zeroResultQueries).toBe(1)
      expect(metrics.popularFilters).toContainEqual({ filter: 'contentType:problem', count: 1 })
      expect(metrics.popularFilters).toContainEqual({ filter: 'sectors:technology', count: 2 })
      expect(metrics.searchesByHour).toContainEqual({ hour: 10, count: 1 })
      expect(metrics.searchesByHour).toContainEqual({ hour: 11, count: 1 })
      expect(metrics.searchesByHour).toContainEqual({ hour: 12, count: 1 })
    })

    it('should handle empty performance data', async () => {
      const mockQuery = {
        select: vi.fn(() => mockQuery),
        gte: vi.fn(() => mockQuery),
        lte: vi.fn(() => mockQuery),
        limit: vi.fn(() => Promise.resolve({ data: [], error: null }))
      }

      vi.mocked(supabase.from).mockReturnValueOnce(mockQuery)

      const metrics = await analytics.getPerformanceMetrics()

      expect(metrics.totalSearches).toBe(0)
      expect(metrics.averageResponseTime).toBe(0)
      expect(metrics.zeroResultQueries).toBe(0)
      expect(metrics.popularFilters).toEqual([])
      expect(metrics.searchesByHour).toEqual([])
    })
  })

  describe('click-through rates', () => {
    it('should calculate CTR correctly', async () => {
      const mockData = [
        {
          clicked_result_id: 'result1',
          clicked_result_type: 'problem',
          results_count: 5
        },
        {
          clicked_result_id: null,
          clicked_result_type: null,
          results_count: 3
        },
        {
          clicked_result_id: 'result2',
          clicked_result_type: 'expert',
          results_count: 8
        }
      ]

      const mockQuery = {
        select: vi.fn(() => mockQuery),
        gte: vi.fn(() => mockQuery),
        lte: vi.fn(() => mockQuery),
        limit: vi.fn(() => Promise.resolve({ data: mockData, error: null }))
      }

      vi.mocked(supabase.from).mockReturnValueOnce(mockQuery)

      const ctrData = await analytics.getClickThroughRates()

      expect(ctrData.overallCTR).toBeCloseTo(0.667, 2) // 2 clicks out of 3 searches
      expect(ctrData.ctrByContentType).toContainEqual({
        contentType: 'problem',
        ctr: 1,
        totalSearches: 1,
        totalClicks: 1
      })
      expect(ctrData.ctrByContentType).toContainEqual({
        contentType: 'expert',
        ctr: 1,
        totalSearches: 1,
        totalClicks: 1
      })
    })

    it('should handle CTR calculation with no clicks', async () => {
      const mockData = [
        {
          clicked_result_id: null,
          clicked_result_type: null,
          results_count: 5
        },
        {
          clicked_result_id: null,
          clicked_result_type: null,
          results_count: 3
        }
      ]

      const mockQuery = {
        select: vi.fn(() => mockQuery),
        gte: vi.fn(() => mockQuery),
        lte: vi.fn(() => mockQuery),
        limit: vi.fn(() => Promise.resolve({ data: mockData, error: null }))
      }

      vi.mocked(supabase.from).mockReturnValueOnce(mockQuery)

      const ctrData = await analytics.getClickThroughRates()

      expect(ctrData.overallCTR).toBe(0)
      expect(ctrData.ctrByContentType).toEqual([])
    })
  })

  describe('session management', () => {
    it('should generate unique session IDs', () => {
      const analytics1 = new SearchAnalyticsService()
      const analytics2 = new SearchAnalyticsService()

      // Access private sessionId for testing
      const sessionId1 = (analytics1 as any).sessionId
      const sessionId2 = (analytics2 as any).sessionId

      expect(sessionId1).not.toBe(sessionId2)
      expect(sessionId1).toMatch(/^session_\d+_[a-z0-9]+$/)

      analytics1.destroy()
      analytics2.destroy()
    })

    it('should maintain session ID throughout service lifecycle', async () => {
      vi.mocked(supabase.rpc).mockResolvedValue({ data: 'analytics-123', error: null })

      const query = createMockQuery('session test')
      const results = createMockResults()

      const sessionId = (analytics as any).sessionId

      await analytics.trackSearch(query, results)

      expect(supabase.rpc).toHaveBeenCalledWith('log_search_analytics', 
        expect.objectContaining({
          session_id: sessionId
        })
      )
    })
  })

  describe('event batching', () => {
    it('should batch events and flush when batch size is reached', async () => {
      const consoleSpy = vi.spyOn(console, 'debug').mockImplementation(() => {})

      // Add events to reach batch size (2)
      await analytics.trackSuggestionClick('suggestion1', 'category')
      await analytics.trackSuggestionClick('suggestion2', 'tag')

      // Wait for potential flush
      await new Promise(resolve => setTimeout(resolve, 10))

      expect(consoleSpy).toHaveBeenCalledWith('Flushing search analytics events:', 2)

      consoleSpy.mockRestore()
    })

    it('should flush events on timer interval', async () => {
      const consoleSpy = vi.spyOn(console, 'debug').mockImplementation(() => {})

      // Add one event (below batch size)
      await analytics.trackSuggestionClick('suggestion1', 'category')

      // Wait for timer flush (100ms interval)
      await new Promise(resolve => setTimeout(resolve, 150))

      expect(consoleSpy).toHaveBeenCalledWith('Flushing search analytics events:', 1)

      consoleSpy.mockRestore()
    })
  })

  describe('cleanup', () => {
    it('should cleanup resources properly', async () => {
      const consoleSpy = vi.spyOn(console, 'debug').mockImplementation(() => {})

      // Add an event to the queue
      await analytics.trackSuggestionClick('test', 'category')

      analytics.destroy()

      // Should flush any remaining events
      expect(consoleSpy).toHaveBeenCalledWith('Flushing search analytics events:', 1)

      consoleSpy.mockRestore()
    })

    it('should handle multiple destroy calls gracefully', () => {
      analytics.destroy()
      
      // Should not throw on second destroy
      expect(() => analytics.destroy()).not.toThrow()
    })
  })

  describe('error handling', () => {
    it('should handle database errors gracefully in performance metrics', async () => {
      const mockQuery = {
        select: vi.fn(() => mockQuery),
        gte: vi.fn(() => mockQuery),
        lte: vi.fn(() => mockQuery),
        limit: vi.fn(() => Promise.resolve({ data: null, error: new Error('Database error') }))
      }

      vi.mocked(supabase.from).mockReturnValueOnce(mockQuery)

      const metrics = await analytics.getPerformanceMetrics()

      expect(metrics).toEqual({
        averageResponseTime: 0,
        totalSearches: 0,
        zeroResultQueries: 0,
        popularFilters: [],
        searchesByHour: []
      })
    })

    it('should handle database errors gracefully in CTR calculation', async () => {
      const mockQuery = {
        select: vi.fn(() => mockQuery),
        gte: vi.fn(() => mockQuery),
        lte: vi.fn(() => mockQuery),
        limit: vi.fn(() => Promise.resolve({ data: null, error: new Error('Database error') }))
      }

      vi.mocked(supabase.from).mockReturnValueOnce(mockQuery)

      const ctrData = await analytics.getClickThroughRates()

      expect(ctrData).toEqual({
        overallCTR: 0,
        ctrByContentType: [],
        ctrByPosition: []
      })
    })
  })
})