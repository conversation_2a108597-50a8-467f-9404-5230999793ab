import { describe, it, expect, beforeEach } from 'vitest'
import { SearchService } from '../SearchService'
import { SearchQuery } from '../types'

describe('SearchService Integration', () => {
  let searchService: SearchService

  beforeEach(() => {
    searchService = new SearchService()
  })

  describe('complete search workflow', () => {
    it('should handle a complete search query with all components', () => {
      const query: SearchQuery = {
        text: 'تطوير البرمجيات',
        filters: {
          contentType: ['problem', 'expert'],
          sectors: ['technology'],
          categories: ['software'],
          status: ['open', 'available'],
          rating: { min: 3.0, max: 5.0 }
        },
        sortBy: 'relevance',
        pagination: { limit: 10, offset: 0 },
        language: 'ar'
      }

      // Should not throw any validation errors
      expect(() => searchService.validateQuery(query)).not.toThrow()
      
      // Should sanitize the query text properly
      const sanitized = searchService.sanitizeQuery(query.text)
      expect(sanitized).toBe('تطوير البرمجيات')
    })

    it('should handle English queries', () => {
      const query: SearchQuery = {
        text: 'software development',
        filters: {
          contentType: ['problem'],
          sectors: ['technology']
        },
        sortBy: 'date_created',
        pagination: { limit: 5, offset: 0 },
        language: 'en'
      }

      expect(() => searchService.validateQuery(query)).not.toThrow()
      
      const sanitized = searchService.sanitizeQuery(query.text)
      expect(sanitized).toBe('software development')
    })

    it('should handle complex filtering scenarios', () => {
      const mockResults = [
        {
          id: '1',
          type: 'problem' as const,
          title: 'Software Bug',
          description: 'Critical software issue',
          category: 'software',
          sector: 'technology',
          tags: ['bug', 'critical'],
          relevanceScore: 0.9,
          metadata: {
            status: 'open',
            urgency: 'critical',
            created_at: '2024-01-01T00:00:00Z',
            rating: 4.0
          }
        },
        {
          id: '2',
          type: 'expert' as const,
          title: 'Software Expert',
          description: 'Experienced developer',
          category: 'software',
          sector: 'technology',
          tags: ['development', 'expert'],
          relevanceScore: 0.8,
          metadata: {
            availability: 'available',
            rating: 4.5,
            created_at: '2024-01-02T00:00:00Z'
          }
        },
        {
          id: '3',
          type: 'solution' as const,
          title: 'Database Solution',
          description: 'Database optimization',
          category: 'database',
          sector: 'technology',
          tags: ['database', 'optimization'],
          relevanceScore: 0.7,
          metadata: {
            status: 'approved',
            rating: 3.5,
            created_at: '2024-01-03T00:00:00Z'
          }
        }
      ]

      // Test filtering by content type
      const problemFilter = { contentType: ['problem' as const] }
      const problemResults = searchService.applyFilters(mockResults, problemFilter)
      expect(problemResults).toHaveLength(1)
      expect(problemResults[0].type).toBe('problem')

      // Test filtering by rating
      const ratingFilter = { rating: { min: 4.0 } }
      const ratingResults = searchService.applyFilters(mockResults, ratingFilter)
      expect(ratingResults).toHaveLength(2) // Problem and Expert have rating >= 4.0

      // Test combined filtering
      const combinedFilter = {
        contentType: ['problem' as const, 'expert' as const],
        rating: { min: 4.0 }
      }
      const combinedResults = searchService.applyFilters(mockResults, combinedFilter)
      expect(combinedResults).toHaveLength(2)
      expect(combinedResults.every(r => r.type === 'problem' || r.type === 'expert')).toBe(true)
      expect(combinedResults.every(r => (r.metadata.rating || 0) >= 4.0)).toBe(true)
    })

    it('should handle sorting with proper tie-breaking', () => {
      const mockResults = [
        {
          id: '1',
          type: 'problem' as const,
          title: 'Problem A',
          description: 'Description A',
          tags: [],
          relevanceScore: 0.8,
          metadata: {
            created_at: '2024-01-01T00:00:00Z',
            rating: 3.0,
            status: 'open'
          }
        },
        {
          id: '2',
          type: 'expert' as const,
          title: 'Expert B',
          description: 'Description B',
          tags: [],
          relevanceScore: 0.8, // Same relevance as Problem A
          metadata: {
            created_at: '2024-01-02T00:00:00Z',
            rating: 4.5,
            availability: 'available'
          }
        }
      ]

      // When sorting by relevance with same scores, problem should come first (higher type priority)
      const relevanceSort = searchService.sortResults(mockResults, 'relevance')
      expect(relevanceSort[0].type).toBe('problem')
      expect(relevanceSort[1].type).toBe('expert')

      // When sorting by rating, expert should come first (higher rating)
      const ratingSort = searchService.sortResults(mockResults, 'rating')
      expect(ratingSort[0].type).toBe('expert')
      expect(ratingSort[1].type).toBe('problem')
    })

    it('should validate edge cases properly', () => {
      // Test minimum query length
      expect(() => {
        searchService.validateQuery({
          text: '',
          filters: {},
          sortBy: 'relevance',
          pagination: { limit: 10, offset: 0 }
        })
      }).toThrow('Search text is required')

      // Test maximum query length
      expect(() => {
        searchService.validateQuery({
          text: 'a'.repeat(501),
          filters: {},
          sortBy: 'relevance',
          pagination: { limit: 10, offset: 0 }
        })
      }).toThrow('must not exceed 500 characters')

      // Test pagination limits
      expect(() => {
        searchService.validateQuery({
          text: 'test',
          filters: {},
          sortBy: 'relevance',
          pagination: { limit: 101, offset: 0 }
        })
      }).toThrow('must not exceed 100')

      expect(() => {
        searchService.validateQuery({
          text: 'test',
          filters: {},
          sortBy: 'relevance',
          pagination: { limit: 10, offset: -1 }
        })
      }).toThrow('must be non-negative')
    })
  })
})