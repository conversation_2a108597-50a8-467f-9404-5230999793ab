import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { <PERSON><PERSON>ache, CacheInvalidator } from '../SearchCache'
import { SearchQuery, SearchResults } from '../types'

describe('SearchCache', () => {
  let cache: SearchCache
  
  beforeEach(() => {
    cache = new SearchCache({
      defaultTTL: 1000, // 1 second for testing
      maxSize: 3,
      cleanupInterval: 100 // 100ms for testing
    })
  })
  
  afterEach(() => {
    cache.destroy()
  })

  const createMockQuery = (text: string): SearchQuery => ({
    text,
    filters: {},
    sortBy: 'relevance',
    pagination: { limit: 10, offset: 0 },
    language: 'ar'
  })

  const createMockResults = (text: string): SearchResults => ({
    items: [
      {
        id: '1',
        type: 'problem',
        title: `Test ${text}`,
        description: 'Test description',
        tags: [],
        relevanceScore: 0.9,
        metadata: { created_at: new Date().toISOString() }
      }
    ],
    totalCount: 1,
    hasMore: false,
    query: createMockQuery(text),
    executionTime: 100
  })

  describe('basic cache operations', () => {
    it('should store and retrieve cached results', async () => {
      const query = createMockQuery('test query')
      const results = createMockResults('test query')
      const key = cache.generateKey(query)

      await cache.set(key, results)
      const retrieved = await cache.get(key)

      expect(retrieved).toEqual(results)
    })

    it('should return null for non-existent keys', async () => {
      const result = await cache.get('non-existent-key')
      expect(result).toBeNull()
    })

    it('should generate consistent keys for identical queries', () => {
      const query1 = createMockQuery('test')
      const query2 = createMockQuery('test')
      
      const key1 = cache.generateKey(query1)
      const key2 = cache.generateKey(query2)
      
      expect(key1).toBe(key2)
    })

    it('should generate different keys for different queries', () => {
      const query1 = createMockQuery('test1')
      const query2 = createMockQuery('test2')
      
      const key1 = cache.generateKey(query1)
      const key2 = cache.generateKey(query2)
      
      expect(key1).not.toBe(key2)
    })
  })

  describe('TTL and expiration', () => {
    it('should expire entries after TTL', async () => {
      const query = createMockQuery('test')
      const results = createMockResults('test')
      const key = cache.generateKey(query)

      await cache.set(key, results, 50) // 50ms TTL
      
      // Should be available immediately
      expect(await cache.get(key)).toEqual(results)
      
      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // Should be expired
      expect(await cache.get(key)).toBeNull()
    })

    it('should update access statistics on get', async () => {
      const query = createMockQuery('test')
      const results = createMockResults('test')
      const key = cache.generateKey(query)

      await cache.set(key, results)
      
      // Access multiple times (set starts with accessCount = 1)
      await cache.get(key) // accessCount = 2
      await cache.get(key) // accessCount = 3
      
      const stats = cache.getStats()
      const entry = stats.entries.find(e => e.key === key)
      
      expect(entry?.accessCount).toBe(3)
    })
  })

  describe('LRU eviction', () => {
    it('should evict least recently used entries when at max size', async () => {
      const queries = [
        createMockQuery('query1'),
        createMockQuery('query2'),
        createMockQuery('query3'),
        createMockQuery('query4') // This should trigger eviction
      ]
      
      const results = queries.map(q => createMockResults(q.text))
      const keys = queries.map(q => cache.generateKey(q))

      // Fill cache to max size with some delay between entries
      await cache.set(keys[0], results[0])
      await new Promise(resolve => setTimeout(resolve, 10))
      
      await cache.set(keys[1], results[1])
      await new Promise(resolve => setTimeout(resolve, 10))
      
      await cache.set(keys[2], results[2])

      // Access first and third entries to make them recently used
      await cache.get(keys[0])
      await cache.get(keys[2])

      // Add fourth entry (should evict second entry as it's LRU)
      await cache.set(keys[3], results[3])

      expect(await cache.get(keys[0])).not.toBeNull() // Recently accessed
      expect(await cache.get(keys[1])).toBeNull() // Should be evicted (LRU)
      expect(await cache.get(keys[2])).not.toBeNull() // Recently accessed
      expect(await cache.get(keys[3])).not.toBeNull() // Newly added
    })
  })

  describe('cache invalidation', () => {
    it('should invalidate entries by content type', async () => {
      const query1 = createMockQuery('test1')
      const query2 = createMockQuery('test2')
      
      const results1 = createMockResults('test1')
      results1.items[0].type = 'problem'
      
      const results2 = createMockResults('test2')
      results2.items[0].type = 'expert'

      const key1 = cache.generateKey(query1)
      const key2 = cache.generateKey(query2)

      await cache.set(key1, results1)
      await cache.set(key2, results2)

      const invalidatedCount = cache.invalidateByContentType('problem')

      expect(invalidatedCount).toBe(1)
      expect(await cache.get(key1)).toBeNull() // Problem entry invalidated
      expect(await cache.get(key2)).not.toBeNull() // Expert entry still there
    })

    it('should invalidate entries by item ID', async () => {
      const query = createMockQuery('test')
      const results = createMockResults('test')
      results.items[0].id = 'specific-id'
      
      const key = cache.generateKey(query)
      await cache.set(key, results)

      const invalidatedCount = cache.invalidateByItemId('specific-id')

      expect(invalidatedCount).toBe(1)
      expect(await cache.get(key)).toBeNull()
    })
  })

  describe('cache statistics', () => {
    it('should provide accurate cache statistics', async () => {
      const query1 = createMockQuery('test1')
      const query2 = createMockQuery('test2')
      const results1 = createMockResults('test1')
      const results2 = createMockResults('test2')

      await cache.set(cache.generateKey(query1), results1)
      await cache.set(cache.generateKey(query2), results2)

      // Access first entry multiple times (set starts with accessCount = 1)
      await cache.get(cache.generateKey(query1)) // accessCount = 2
      await cache.get(cache.generateKey(query1)) // accessCount = 3

      const stats = cache.getStats()

      expect(stats.size).toBe(2)
      expect(stats.maxSize).toBe(3)
      expect(stats.entries).toHaveLength(2)
      
      // First entry should have higher access count
      const firstEntry = stats.entries.find(e => e.key === cache.generateKey(query1))
      expect(firstEntry?.accessCount).toBe(3)
    })
  })

  describe('cleanup', () => {
    it('should clean up expired entries', async () => {
      const query = createMockQuery('test')
      const results = createMockResults('test')
      const key = cache.generateKey(query)

      await cache.set(key, results, 50) // 50ms TTL
      
      // Verify entry exists before expiration
      expect(cache.has(key)).toBe(true)
      
      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 100))
      
      // Entry should still be in cache but expired (cleanup hasn't run yet)
      expect(cache.has(key)).toBe(false) // has() checks expiration
      
      // Manually add an expired entry to test cleanup
      const expiredQuery = createMockQuery('expired')
      const expiredResults = createMockResults('expired')
      const expiredKey = cache.generateKey(expiredQuery)
      
      // Manually insert expired entry by manipulating the cache
      const expiredEntry = {
        data: expiredResults,
        timestamp: Date.now() - 200, // 200ms ago
        ttl: 50, // 50ms TTL (so it's expired)
        accessCount: 1,
        lastAccessed: Date.now() - 200
      }
      
      // Access private cache to insert expired entry
      ;(cache as any).cache.set(expiredKey, expiredEntry)
      
      const cleanedCount = cache.cleanup()
      
      expect(cleanedCount).toBe(1)
      expect(cache.has(expiredKey)).toBe(false)
    })
  })
})

describe('CacheInvalidator', () => {
  let cache: SearchCache
  let invalidator: CacheInvalidator
  
  beforeEach(() => {
    cache = new SearchCache()
    invalidator = new CacheInvalidator(cache)
  })
  
  afterEach(() => {
    cache.destroy()
  })

  it('should invalidate cache on content added', async () => {
    const spy = vi.spyOn(cache, 'invalidateByContentType')
    
    invalidator.onContentAdded('problem', 'technology')
    
    expect(spy).toHaveBeenCalledWith('problem')
  })

  it('should invalidate cache on content updated', async () => {
    const itemSpy = vi.spyOn(cache, 'invalidateByItemId')
    const typeSpy = vi.spyOn(cache, 'invalidateByContentType')
    
    invalidator.onContentUpdated('item-123', 'problem', 'technology')
    
    expect(itemSpy).toHaveBeenCalledWith('item-123')
    expect(typeSpy).toHaveBeenCalledWith('problem')
  })

  it('should invalidate cache on content deleted', async () => {
    const itemSpy = vi.spyOn(cache, 'invalidateByItemId')
    const typeSpy = vi.spyOn(cache, 'invalidateByContentType')
    
    invalidator.onContentDeleted('item-123', 'problem')
    
    expect(itemSpy).toHaveBeenCalledWith('item-123')
    expect(typeSpy).toHaveBeenCalledWith('problem')
  })

  it('should clear all cache entries', async () => {
    const spy = vi.spyOn(cache, 'clear')
    
    invalidator.invalidateAll()
    
    expect(spy).toHaveBeenCalled()
  })
})