import { describe, it, expect, beforeEach, vi } from 'vitest'
import { SearchService } from '../SearchService'
import { SearchAnalyticsService } from '../SearchAnalytics'
import { SearchQuery, SearchResults } from '../types'

// Mock Supabase
vi.mock('../../supabase', () => ({
  supabase: {
    rpc: vi.fn(),
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        order: vi.fn(() => ({
          limit: vi.fn(() => Promise.resolve({ data: [], error: null }))
        }))
      }))
    }))
  }
}))

// Import after mocking
import { supabase } from '../../supabase'

describe('Search Analytics Integration', () => {
  let searchService: SearchService
  let analyticsService: SearchAnalyticsService

  beforeEach(() => {
    // Create fresh instances for each test
    analyticsService = new SearchAnalyticsService({
      enableTracking: true,
      batchSize: 1, // Immediate flush for testing
      flushInterval: 100,
      enableClickTracking: true,
      enablePerformanceTracking: true
    })
    
    searchService = new SearchService(undefined, undefined, analyticsService)
    
    // Reset mocks
    vi.clearAllMocks()
  })

  afterEach(() => {
    analyticsService.destroy()
  })

  const createMockQuery = (): SearchQuery => ({
    text: 'test search query',
    filters: {
      contentType: ['problem'],
      sectors: ['technology']
    },
    sortBy: 'relevance',
    pagination: { limit: 10, offset: 0 },
    language: 'ar'
  })

  describe('end-to-end analytics tracking', () => {
    it('should track search queries through the search service', async () => {
      // Mock successful search analytics logging
      const mockAnalyticsId = 'analytics-123'
      vi.mocked(supabase.rpc).mockResolvedValueOnce({ data: mockAnalyticsId, error: null })

      // Mock search results
      const mockSearchResults: SearchResults = {
        items: [
          {
            id: 'result-1',
            type: 'problem',
            title: 'Test Problem',
            description: 'Test Description',
            tags: ['test'],
            relevanceScore: 0.9,
            metadata: {
              status: 'open',
              created_at: '2024-01-01T00:00:00Z'
            }
          }
        ],
        totalCount: 1,
        hasMore: false,
        query: createMockQuery(),
        executionTime: 150
      }

      // Track search using analytics service directly
      const query = createMockQuery()
      const analyticsId = await analyticsService.trackSearch(query, mockSearchResults)

      // Verify analytics tracking was called
      expect(supabase.rpc).toHaveBeenCalledWith('log_search_analytics', {
        query_text: 'test search query',
        filters: {
          contentType: ['problem'],
          sectors: ['technology']
        },
        results_count: 1,
        response_time_ms: 150,
        language: 'ar',
        session_id: expect.any(String)
      })

      expect(analyticsId).toBe(mockAnalyticsId)
    })

    it('should track result clicks with position information', async () => {
      // Mock successful click tracking
      vi.mocked(supabase.rpc).mockResolvedValueOnce({ data: null, error: null })

      const analyticsId = 'analytics-123'
      const resultId = 'result-456'
      const resultType = 'problem'
      const position = 2

      await analyticsService.trackResultClick(analyticsId, resultId, resultType, position)

      expect(supabase.rpc).toHaveBeenCalledWith('update_search_analytics_click', {
        analytics_id: analyticsId,
        clicked_result_id: resultId,
        clicked_result_type: resultType
      })
    })

    it('should handle analytics failures gracefully', async () => {
      // Mock analytics failure
      vi.mocked(supabase.rpc).mockResolvedValueOnce({ data: null, error: new Error('Analytics error') })

      const query = createMockQuery()
      const mockResults: SearchResults = {
        items: [],
        totalCount: 0,
        hasMore: false,
        query,
        executionTime: 100
      }

      // Should not throw error even if analytics fails
      const analyticsId = await analyticsService.trackSearch(query, mockResults)
      expect(analyticsId).toBeNull()
    })

    it('should track suggestion clicks', async () => {
      const consoleSpy = vi.spyOn(console, 'debug').mockImplementation(() => {})

      await analyticsService.trackSuggestionClick('test suggestion', 'category')

      // Should queue the event for batch processing
      // Wait for potential flush
      await new Promise(resolve => setTimeout(resolve, 10))

      consoleSpy.mockRestore()
    })

    it('should track filter applications', async () => {
      await analyticsService.trackFilterApplied('contentType', ['problem', 'expert'])
      await analyticsService.trackFilterApplied('sectors', ['technology'])

      // Events should be queued for batch processing
      // No direct database calls for filter tracking
      expect(supabase.rpc).not.toHaveBeenCalled()
    })

    it('should generate consistent query hashes', () => {
      const query1 = createMockQuery()
      const query2 = createMockQuery()
      
      // Access private method for testing
      const hash1 = (analyticsService as any).generateQueryHash(query1)
      const hash2 = (analyticsService as any).generateQueryHash(query2)
      
      expect(hash1).toBe(hash2)
      expect(hash1).toMatch(/^[a-z0-9]+$/)
    })

    it('should maintain session consistency', async () => {
      vi.mocked(supabase.rpc).mockResolvedValue({ data: 'analytics-123', error: null })

      const query = createMockQuery()
      const mockResults: SearchResults = {
        items: [],
        totalCount: 0,
        hasMore: false,
        query,
        executionTime: 100
      }

      // Track multiple searches
      await analyticsService.trackSearch(query, mockResults)
      await analyticsService.trackSearch(query, mockResults)

      // Both calls should use the same session ID
      const calls = vi.mocked(supabase.rpc).mock.calls
      expect(calls).toHaveLength(2)
      
      const sessionId1 = calls[0][1].session_id
      const sessionId2 = calls[1][1].session_id
      
      expect(sessionId1).toBe(sessionId2)
      expect(sessionId1).toMatch(/^session_\d+_[a-z0-9]+$/)
    })
  })

  describe('analytics data aggregation', () => {
    it('should aggregate popular queries correctly', async () => {
      const mockData = [
        {
          query_text: 'javascript',
          query_hash: 'hash1',
          response_time_ms: 100,
          results_count: 5,
          created_at: '2024-01-01T00:00:00Z'
        },
        {
          query_text: 'javascript',
          query_hash: 'hash1',
          response_time_ms: 200,
          results_count: 3,
          created_at: '2024-01-02T00:00:00Z'
        },
        {
          query_text: 'python',
          query_hash: 'hash2',
          response_time_ms: 150,
          results_count: 8,
          created_at: '2024-01-01T12:00:00Z'
        }
      ]

      const mockQuery = {
        select: vi.fn(() => mockQuery),
        order: vi.fn(() => mockQuery),
        limit: vi.fn(() => Promise.resolve({ data: mockData, error: null }))
      }

      vi.mocked(supabase.from).mockReturnValueOnce(mockQuery)

      const popularQueries = await analyticsService.getPopularQueries(5)

      expect(popularQueries).toHaveLength(2)
      
      // JavaScript should be first (2 searches)
      expect(popularQueries[0]).toEqual({
        queryText: 'javascript',
        queryHash: 'hash1',
        searchCount: 2,
        avgResponseTime: 150, // (100 + 200) / 2
        avgResultsCount: 4,   // (5 + 3) / 2
        lastSearched: '2024-01-02T00:00:00Z'
      })
      
      // Python should be second (1 search)
      expect(popularQueries[1]).toEqual({
        queryText: 'python',
        queryHash: 'hash2',
        searchCount: 1,
        avgResponseTime: 150,
        avgResultsCount: 8,
        lastSearched: '2024-01-01T12:00:00Z'
      })
    })

    it('should calculate performance metrics correctly', async () => {
      const mockData = [
        {
          response_time_ms: 100,
          results_count: 5,
          filters: { contentType: ['problem'] },
          created_at: '2024-01-01T10:00:00Z'
        },
        {
          response_time_ms: 200,
          results_count: 0, // Zero results query
          filters: { sectors: ['technology'] },
          created_at: '2024-01-01T11:00:00Z'
        },
        {
          response_time_ms: 150,
          results_count: 3,
          filters: { contentType: ['expert'], sectors: ['technology'] },
          created_at: '2024-01-01T12:00:00Z'
        }
      ]

      const mockQuery = {
        select: vi.fn(() => mockQuery),
        limit: vi.fn(() => Promise.resolve({ data: mockData, error: null }))
      }

      vi.mocked(supabase.from).mockReturnValueOnce(mockQuery)

      const metrics = await analyticsService.getPerformanceMetrics()

      expect(metrics.totalSearches).toBe(3)
      expect(metrics.averageResponseTime).toBe(150) // (100 + 200 + 150) / 3
      expect(metrics.zeroResultQueries).toBe(1)
      
      // Check popular filters
      expect(metrics.popularFilters).toContainEqual({ filter: 'contentType:problem', count: 1 })
      expect(metrics.popularFilters).toContainEqual({ filter: 'contentType:expert', count: 1 })
      expect(metrics.popularFilters).toContainEqual({ filter: 'sectors:technology', count: 2 })
      
      // Check searches by hour (using UTC)
      expect(metrics.searchesByHour).toContainEqual({ hour: 10, count: 1 })
      expect(metrics.searchesByHour).toContainEqual({ hour: 11, count: 1 })
      expect(metrics.searchesByHour).toContainEqual({ hour: 12, count: 1 })
    })

    it('should calculate click-through rates correctly', async () => {
      const mockData = [
        {
          clicked_result_id: 'result1',
          clicked_result_type: 'problem',
          results_count: 5
        },
        {
          clicked_result_id: null, // No click
          clicked_result_type: null,
          results_count: 3
        },
        {
          clicked_result_id: 'result2',
          clicked_result_type: 'expert',
          results_count: 8
        },
        {
          clicked_result_id: 'result3',
          clicked_result_type: 'problem',
          results_count: 2
        }
      ]

      const mockQuery = {
        select: vi.fn(() => mockQuery),
        limit: vi.fn(() => Promise.resolve({ data: mockData, error: null }))
      }

      vi.mocked(supabase.from).mockReturnValueOnce(mockQuery)

      const ctrData = await analyticsService.getClickThroughRates()

      expect(ctrData.overallCTR).toBe(0.75) // 3 clicks out of 4 searches
      
      // Check CTR by content type
      const problemCTR = ctrData.ctrByContentType.find(item => item.contentType === 'problem')
      expect(problemCTR).toEqual({
        contentType: 'problem',
        ctr: 1, // 2 clicks out of 2 problem searches
        totalSearches: 2,
        totalClicks: 2
      })
      
      const expertCTR = ctrData.ctrByContentType.find(item => item.contentType === 'expert')
      expect(expertCTR).toEqual({
        contentType: 'expert',
        ctr: 1, // 1 click out of 1 expert search
        totalSearches: 1,
        totalClicks: 1
      })
    })
  })
})