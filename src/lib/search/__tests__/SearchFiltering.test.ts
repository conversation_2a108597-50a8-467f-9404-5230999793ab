import { describe, it, expect, beforeEach } from 'vitest'
import { SearchService } from '../SearchService'
import { SearchFilters } from '../types'

describe('SearchService Filtering Logic', () => {
  let searchService: SearchService

  beforeEach(() => {
    searchService = new SearchService()
  })

  const mockSearchResults = [
    {
      id: '1',
      type: 'problem' as const,
      title: 'Database Performance Issue',
      description: 'Slow queries in production database',
      category: 'database',
      sector: 'technology',
      tags: ['database', 'performance', 'sql'],
      relevanceScore: 0.9,
      metadata: {
        status: 'open',
        urgency: 'high',
        created_at: '2024-01-15T10:30:00Z',
        rating: 4.2
      }
    },
    {
      id: '2',
      type: 'expert' as const,
      title: 'Database Expert',
      description: 'Senior database administrator',
      category: 'database',
      sector: 'technology',
      tags: ['database', 'postgresql', 'optimization'],
      relevanceScore: 0.85,
      metadata: {
        rating: 4.8,
        availability: 'available',
        experience_years: 12,
        created_at: '2024-01-10T08:00:00Z'
      }
    },
    {
      id: '3',
      type: 'solution' as const,
      title: 'Query Optimization Solution',
      description: 'Techniques for optimizing database queries',
      category: 'database',
      sector: 'technology',
      tags: ['optimization', 'indexing', 'performance'],
      relevanceScore: 0.75,
      metadata: {
        status: 'approved',
        rating: 4.0,
        created_at: '2024-01-05T14:20:00Z'
      }
    },
    {
      id: '4',
      type: 'problem' as const,
      title: 'Network Security Issue',
      description: 'Firewall configuration problems',
      category: 'security',
      sector: 'finance',
      tags: ['security', 'firewall', 'network'],
      relevanceScore: 0.7,
      metadata: {
        status: 'closed',
        urgency: 'medium',
        created_at: '2024-01-01T12:00:00Z',
        rating: 3.5
      }
    }
  ]

  describe('Content Type Filtering', () => {
    it('should filter by single content type', () => {
      const filters: SearchFilters = {
        contentType: ['problem']
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(2)
      expect(results.every(r => r.type === 'problem')).toBe(true)
    })

    it('should filter by multiple content types', () => {
      const filters: SearchFilters = {
        contentType: ['problem', 'expert']
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(3)
      expect(results.every(r => r.type === 'problem' || r.type === 'expert')).toBe(true)
    })

    it('should return all results when no content type filter is applied', () => {
      const filters: SearchFilters = {}

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(4)
    })
  })

  describe('Sector Filtering', () => {
    it('should filter by single sector', () => {
      const filters: SearchFilters = {
        sectors: ['technology']
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(3)
      expect(results.every(r => r.sector === 'technology')).toBe(true)
    })

    it('should filter by multiple sectors', () => {
      const filters: SearchFilters = {
        sectors: ['technology', 'finance']
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(4)
    })
  })

  describe('Category Filtering', () => {
    it('should filter by category', () => {
      const filters: SearchFilters = {
        categories: ['database']
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(3)
      expect(results.every(r => r.category === 'database')).toBe(true)
    })
  })

  describe('Status Filtering', () => {
    it('should filter by status for problems and solutions', () => {
      const filters: SearchFilters = {
        status: ['open']
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(1)
      expect(results[0].metadata.status).toBe('open')
    })

    it('should filter by multiple statuses', () => {
      const filters: SearchFilters = {
        status: ['open', 'approved']
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(2)
      expect(results.every(r => 
        r.metadata.status === 'open' || r.metadata.status === 'approved'
      )).toBe(true)
    })
  })

  describe('Rating Filtering', () => {
    it('should filter by minimum rating', () => {
      const filters: SearchFilters = {
        rating: { min: 4.0 }
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(3)
      expect(results.every(r => (r.metadata.rating || 0) >= 4.0)).toBe(true)
    })

    it('should filter by maximum rating', () => {
      const filters: SearchFilters = {
        rating: { max: 4.0 }
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(2)
      expect(results.every(r => (r.metadata.rating || 0) <= 4.0)).toBe(true)
    })

    it('should filter by rating range', () => {
      const filters: SearchFilters = {
        rating: { min: 3.5, max: 4.5 }
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      // Should include items with ratings between 3.5 and 4.5
      expect(results).toHaveLength(3)
      expect(results.every(r => {
        const rating = r.metadata.rating || 0
        return rating >= 3.5 && rating <= 4.5
      })).toBe(true)
    })
  })

  describe('Tag Filtering', () => {
    it('should filter by single tag', () => {
      const filters: SearchFilters = {
        tags: ['database']
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(2)
      expect(results.every(r => r.tags.includes('database'))).toBe(true)
    })

    it('should filter by multiple tags (OR logic)', () => {
      const filters: SearchFilters = {
        tags: ['database', 'security']
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(3)
      expect(results.every(r => 
        r.tags.some(tag => ['database', 'security'].includes(tag))
      )).toBe(true)
    })
  })

  describe('Combined Filtering', () => {
    it('should apply multiple filters together (AND logic)', () => {
      const filters: SearchFilters = {
        contentType: ['problem'],
        sectors: ['technology'],
        rating: { min: 4.0 }
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(1)
      expect(results[0].type).toBe('problem')
      expect(results[0].sector).toBe('technology')
      expect(results[0].metadata.rating).toBeGreaterThanOrEqual(4.0)
    })

    it('should handle complex filtering scenarios', () => {
      const filters: SearchFilters = {
        contentType: ['expert', 'solution'],
        categories: ['database'],
        rating: { min: 4.0 },
        tags: ['optimization']
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(2)
      expect(results.every(r => 
        (r.type === 'expert' || r.type === 'solution') &&
        r.category === 'database' &&
        (r.metadata.rating || 0) >= 4.0 &&
        r.tags.includes('optimization')
      )).toBe(true)
    })

    it('should return empty results when filters are too restrictive', () => {
      const filters: SearchFilters = {
        contentType: ['problem'],
        sectors: ['agriculture'], // No results in agriculture sector
        rating: { min: 5.0 } // No perfect ratings
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(0)
    })
  })

  describe('Availability Filtering (for experts)', () => {
    it('should filter experts by availability', () => {
      const filters: SearchFilters = {
        contentType: ['expert'],
        availability: ['available']
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(1)
      expect(results[0].type).toBe('expert')
      expect(results[0].metadata.availability).toBe('available')
    })
  })

  describe('Urgency Filtering (for problems)', () => {
    it('should filter problems by content type (urgency filtering not yet implemented)', () => {
      const filters: SearchFilters = {
        contentType: ['problem']
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(2)
      expect(results.every(r => r.type === 'problem')).toBe(true)
    })

    it('should filter by content type and status', () => {
      const filters: SearchFilters = {
        contentType: ['problem'],
        status: ['open']
      }

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toHaveLength(1)
      expect(results[0].type).toBe('problem')
      expect(results[0].metadata.status).toBe('open')
    })
  })

  describe('Edge Cases', () => {
    it('should handle empty filters object', () => {
      const filters: SearchFilters = {}

      const results = searchService.applyFilters(mockSearchResults, filters)
      
      expect(results).toEqual(mockSearchResults)
    })

    it('should handle empty results array', () => {
      const filters: SearchFilters = {
        contentType: ['problem']
      }

      const results = searchService.applyFilters([], filters)
      
      expect(results).toHaveLength(0)
    })

    it('should handle missing metadata fields gracefully', () => {
      const incompleteResults = [
        {
          id: '1',
          type: 'problem' as const,
          title: 'Incomplete Problem',
          description: 'Missing metadata',
          category: 'test',
          sector: 'test',
          tags: [],
          relevanceScore: 0.5,
          metadata: {} // Empty metadata
        }
      ]

      const filters: SearchFilters = {
        rating: { min: 3.0 }
      }

      const results = searchService.applyFilters(incompleteResults, filters)
      
      // Should exclude items without rating when rating filter is applied
      expect(results).toHaveLength(0)
    })
  })
})