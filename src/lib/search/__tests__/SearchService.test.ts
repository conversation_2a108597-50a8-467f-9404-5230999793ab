import { describe, it, expect, beforeEach } from 'vitest'
import { SearchService } from '../SearchService'
import { SearchQuery, SearchErrorType, SearchError } from '../types'

describe('SearchService', () => {
  let searchService: SearchService

  beforeEach(() => {
    searchService = new SearchService()
  })

  describe('validateQuery', () => {
    it('should validate a proper search query', () => {
      const query: SearchQuery = {
        text: 'test query',
        filters: {},
        sortBy: 'relevance',
        pagination: { limit: 10, offset: 0 }
      }

      expect(() => searchService.validateQuery(query)).not.toThrow()
    })

    it('should throw error for empty search text', () => {
      const query: SearchQuery = {
        text: '',
        filters: {},
        sortBy: 'relevance',
        pagination: { limit: 10, offset: 0 }
      }

      expect(() => searchService.validateQuery(query)).toThrow(SearchError)
      expect(() => searchService.validateQuery(query)).toThrow('Search text is required')
    })

    it('should throw error for too long search text', () => {
      const query: SearchQuery = {
        text: 'a'.repeat(501), // Exceeds MAX_QUERY_LENGTH
        filters: {},
        sortBy: 'relevance',
        pagination: { limit: 10, offset: 0 }
      }

      expect(() => searchService.validateQuery(query)).toThrow(SearchError)
    })

    it('should throw error for invalid pagination limit', () => {
      const query: SearchQuery = {
        text: 'test',
        filters: {},
        sortBy: 'relevance',
        pagination: { limit: 101, offset: 0 } // Exceeds MAX_LIMIT
      }

      expect(() => searchService.validateQuery(query)).toThrow(SearchError)
    })

    it('should throw error for negative offset', () => {
      const query: SearchQuery = {
        text: 'test',
        filters: {},
        sortBy: 'relevance',
        pagination: { limit: 10, offset: -1 }
      }

      expect(() => searchService.validateQuery(query)).toThrow(SearchError)
    })
  })

  describe('sanitizeQuery', () => {
    it('should remove HTML tags', () => {
      const result = searchService.sanitizeQuery('<script>alert("test")</script>')
      expect(result).toBe('scriptalert(test)/script')
    })

    it('should remove quotes', () => {
      const result = searchService.sanitizeQuery('test "query" with \'quotes\'')
      expect(result).toBe('test query with quotes')
    })

    it('should normalize whitespace', () => {
      const result = searchService.sanitizeQuery('test   query   with    spaces')
      expect(result).toBe('test query with spaces')
    })

    it('should trim whitespace', () => {
      const result = searchService.sanitizeQuery('  test query  ')
      expect(result).toBe('test query')
    })
  })

  describe('applyFilters', () => {
    const mockResults = [
      {
        id: '1',
        type: 'problem' as const,
        title: 'Test Problem',
        description: 'Test Description',
        category: 'tech',
        sector: 'technology',
        tags: ['programming', 'web'],
        relevanceScore: 0.8,
        metadata: {
          status: 'open',
          created_at: '2024-01-01T00:00:00Z'
        }
      },
      {
        id: '2',
        type: 'expert' as const,
        title: 'Test Expert',
        description: 'Expert Description',
        category: 'agriculture',
        sector: 'agriculture',
        tags: ['farming', 'irrigation'],
        relevanceScore: 0.7,
        metadata: {
          rating: 4.5,
          created_at: '2024-01-02T00:00:00Z'
        }
      }
    ]

    it('should filter by content type', () => {
      const filters = { contentType: ['problem' as const] }
      const result = searchService.applyFilters(mockResults, filters)
      
      expect(result).toHaveLength(1)
      expect(result[0].type).toBe('problem')
    })

    it('should filter by sector', () => {
      const filters = { sectors: ['technology'] }
      const result = searchService.applyFilters(mockResults, filters)
      
      expect(result).toHaveLength(1)
      expect(result[0].sector).toBe('technology')
    })

    it('should filter by rating range', () => {
      const filters = { rating: { min: 4.0, max: 5.0 } }
      const result = searchService.applyFilters(mockResults, filters)
      
      // Should include the expert with rating 4.5 and exclude the problem without rating
      expect(result).toHaveLength(1)
      expect(result[0].metadata.rating).toBe(4.5)
    })

    it('should filter by tags', () => {
      const filters = { tags: ['programming'] }
      const result = searchService.applyFilters(mockResults, filters)
      
      expect(result).toHaveLength(1)
      expect(result[0].tags).toContain('programming')
    })
  })

  describe('sortResults', () => {
    const mockResults = [
      {
        id: '1',
        type: 'problem' as const,
        title: 'Problem A',
        description: 'Description A',
        tags: [],
        relevanceScore: 0.6,
        metadata: {
          created_at: '2024-01-01T00:00:00Z',
          rating: 3.0,
          status: 'open'
        }
      },
      {
        id: '2',
        type: 'expert' as const,
        title: 'Expert B',
        description: 'Expert Description B',
        tags: [],
        relevanceScore: 0.9,
        metadata: {
          created_at: '2024-01-02T00:00:00Z',
          rating: 4.5,
          availability: 'available'
        }
      },
      {
        id: '3',
        type: 'solution' as const,
        title: 'Solution C',
        description: 'Solution Description C',
        tags: [],
        relevanceScore: 0.6, // Same as Problem A
        metadata: {
          created_at: '2024-01-03T00:00:00Z',
          rating: 4.0,
          status: 'approved'
        }
      }
    ]

    it('should sort by relevance with tie-breaking', () => {
      const result = searchService.sortResults(mockResults, 'relevance')
      
      expect(result[0].id).toBe('2') // Highest relevance score
      // For items with same relevance (0.6), problem should come before solution due to type priority
      expect(result[1].id).toBe('1') // Problem (higher type priority)
      expect(result[2].id).toBe('3') // Solution (lower type priority)
    })

    it('should sort by date created with relevance tie-breaker', () => {
      const result = searchService.sortResults(mockResults, 'date_created')
      
      expect(result[0].id).toBe('3') // Most recent
      expect(result[1].id).toBe('2') // Second most recent
      expect(result[2].id).toBe('1') // Oldest
    })

    it('should sort by rating with tie-breaking', () => {
      const result = searchService.sortResults(mockResults, 'rating')
      
      expect(result[0].id).toBe('2') // Highest rating (4.5)
      expect(result[1].id).toBe('3') // Second highest rating (4.0)
      expect(result[2].id).toBe('1') // Lowest rating (3.0)
    })

    it('should sort by popularity using calculated popularity score', () => {
      const result = searchService.sortResults(mockResults, 'popularity')
      
      // Expert with high rating should be most popular
      expect(result[0].id).toBe('2')
    })
  })
})