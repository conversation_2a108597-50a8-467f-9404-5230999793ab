# Search System Tests

## Test Status Overview

| Test Suite | Status | Tests | Description |
|------------|--------|-------|-------------|
| `SearchService.test.ts` | ✅ Passing | 17/17 | Core search service validation and filtering |
| `SearchFiltering.test.ts` | ✅ Passing | 22/22 | Advanced filtering logic and edge cases |
| `SearchIntegration.test.ts` | ✅ Passing | 8/8 | Component integration and mocking |
| `DatabaseSearchIntegration.test.ts` | ❌ Failing | 1/11 | **Requires database setup** |
| `SearchWorkflow.test.tsx` | ❌ Not Running | 0/15 | **Requires @testing-library/react** |

## Quick Test Commands

```bash
# Run passing tests only
npm run test src/lib/search/__tests__/SearchService.test.ts
npm run test src/lib/search/__tests__/SearchFiltering.test.ts

# Run all search tests (some will fail)
npm run test src/lib/search/

# Run specific failing test to see errors
npm run test src/lib/search/__tests__/DatabaseSearchIntegration.test.ts
```

## Why Database Tests Are Failing

The `DatabaseSearchIntegration.test.ts` tests are failing because:

1. **Missing PostgreSQL Function**: The `SearchService` calls `supabase.rpc('enhanced_search', ...)` but this function doesn't exist in the database yet.

2. **Incorrect Mocking**: The tests mock the Supabase client but return empty arrays instead of the expected test data.

3. **No Database Schema**: The search system expects specific database tables and indexes that may not be set up.

## How to Fix the Failing Tests

### Option 1: Quick Fix (Recommended for Development)

Update the mock in `DatabaseSearchIntegration.test.ts`:

```typescript
// Replace the current mock with this:
vi.mock('@/lib/database', () => ({
  supabase: {
    rpc: vi.fn().mockImplementation((functionName, params) => {
      if (functionName === 'enhanced_search') {
        // Return mock data that matches the test expectations
        return Promise.resolve({
          data: [
            {
              id: '1',
              content_type: 'problem',
              title: 'مشكلة في تطوير البرمجيات',
              description: 'وصف المشكلة التقنية',
              category: 'تطوير',
              sector: 'technology',
              tags: ['برمجة', 'تطوير'],
              relevance_score: 0.9,
              similarity_score: 0.85,
              metadata: {
                status: 'open',
                urgency: 'high',
                created_at: '2024-01-15T10:30:00Z',
                submitter_name: 'أحمد محمد'
              }
            }
          ],
          error: null
        })
      }
      return Promise.resolve({ data: [], error: null })
    })
  }
}))
```

### Option 2: Full Database Setup (Recommended for Production)

1. **Create the database function** in Supabase SQL Editor:
```sql
CREATE OR REPLACE FUNCTION enhanced_search(
  search_query TEXT,
  content_types TEXT[] DEFAULT NULL,
  -- ... other parameters
)
RETURNS TABLE (
  id TEXT,
  content_type TEXT,
  title TEXT,
  description TEXT,
  category TEXT,
  sector TEXT,
  tags TEXT[],
  relevance_score DECIMAL,
  similarity_score DECIMAL,
  metadata JSONB
)
LANGUAGE plpgsql
AS $$
BEGIN
  -- Implementation here
END;
$$;
```

2. **Set up test database** with proper schema and test data

3. **Configure test environment** with database credentials

## Detailed Setup Instructions

For complete setup instructions, see:
- **[docs/SEARCH_TESTING_REQUIREMENTS.md](../../../docs/SEARCH_TESTING_REQUIREMENTS.md)** - Comprehensive database setup guide
- **[docs/SETUP_AND_TESTING_GUIDE.md](../../../docs/SETUP_AND_TESTING_GUIDE.md)** - General testing setup

## Test Coverage

### ✅ Working Tests Cover:
- Query validation and sanitization
- Content type filtering (problem, expert, solution)
- Sector and category filtering  
- Rating range filtering
- Tag-based filtering
- Combined filtering scenarios
- Sorting algorithms with tie-breaking
- Edge cases and error handling

### ❌ Missing Test Coverage:
- Full-text search with Arabic support
- Database query optimization
- Search result caching
- Search analytics tracking
- Real database integration
- Performance under load

## Contributing

When adding new search tests:

1. **Unit tests** → Add to `SearchService.test.ts` or `SearchFiltering.test.ts`
2. **Integration tests** → Add to `SearchIntegration.test.ts`  
3. **Database tests** → Add to `DatabaseSearchIntegration.test.ts` (fix setup first)
4. **E2E tests** → Add to `SearchWorkflow.test.tsx` (fix dependencies first)

## Troubleshooting

### Common Issues:

**"enhanced_search function does not exist"**
- The database function needs to be created in Supabase

**"Tests return empty results"**  
- Check that mocks are returning the expected test data format

**"Cannot find module '@testing-library/react'"**
- Install missing testing dependencies: `npm install -D @testing-library/react`

**"Database connection failed"**
- Check environment variables and Supabase setup

For more troubleshooting help, see the main testing guide.