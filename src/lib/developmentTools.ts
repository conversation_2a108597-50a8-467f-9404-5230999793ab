/**
 * Development Tools for Error Debugging
 * Provides debugging utilities and development-only error monitoring tools
 */

import { errorLogger } from './errorLogger';
import { importExportMonitor } from './importExportMonitor';
import { translationMonitor } from './translationMonitor';
import { componentMonitor } from './componentMonitor';

interface DebugSession {
  id: string;
  startTime: string;
  endTime?: string;
  errors: any[];
  warnings: any[];
  info: any[];
  performance: any[];
}

class DevelopmentTools {
  private debugSession: DebugSession | null = null;
  private originalConsole: {
    error: typeof console.error;
    warn: typeof console.warn;
    info: typeof console.info;
    log: typeof console.log;
  };

  constructor() {
    this.originalConsole = {
      error: console.error,
      warn: console.warn,
      info: console.info,
      log: console.log,
    };

    if (process.env.NODE_ENV === 'development') {
      this.setupDevelopmentTools();
    }
  }

  private setupDevelopmentTools(): void {
    // Add global debugging utilities
    (window as any).debugTools = {
      // Error monitoring
      getErrorStats: () => errorLogger.getErrorStats(),
      getImportFailures: () => importExportMonitor.getImportFailureStats(),
      getTranslationStats: () => translationMonitor.getMissingKeyStats(),
      getComponentHealth: () => componentMonitor.getAllComponentHealth(),
      
      // Generate reports
      generateErrorReport: () => this.generateComprehensiveReport(),
      generateImportReport: () => importExportMonitor.generateReport(),
      generateTranslationReport: () => translationMonitor.generateReport(),
      generateComponentReport: () => componentMonitor.generateReport(),
      
      // Debug session management
      startDebugSession: () => this.startDebugSession(),
      endDebugSession: () => this.endDebugSession(),
      getDebugSession: () => this.debugSession,
      
      // Utilities
      clearAllData: () => this.clearAllMonitoringData(),
      exportData: () => this.exportMonitoringData(),
      testErrorScenarios: () => this.testErrorScenarios(),
      
      // Component testing
      testComponent: (componentName: string) => this.testComponent(componentName),
      simulateError: (componentName: string, errorMessage: string) => 
        this.simulateComponentError(componentName, errorMessage),
      
      // Translation testing
      testTranslations: (keys: string[]) => this.testTranslationKeys(keys),
      findMissingTranslations: () => this.findMissingTranslations(),
      
      // Import/export testing
      validateModuleExports: (modulePath: string, expectedExports: string[]) =>
        importExportMonitor.validateExports(modulePath, expectedExports),
    };

    // Add keyboard shortcuts for debugging
    this.setupKeyboardShortcuts();
    
    // Add visual debugging indicators
    this.setupVisualIndicators();
  }

  private setupKeyboardShortcuts(): void {
    document.addEventListener('keydown', (event) => {
      // Ctrl+Shift+D: Open debug panel
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        this.openDebugPanel();
      }
      
      // Ctrl+Shift+E: Show error summary
      if (event.ctrlKey && event.shiftKey && event.key === 'E') {
        event.preventDefault();
        this.showErrorSummary();
      }
      
      // Ctrl+Shift+T: Show translation issues
      if (event.ctrlKey && event.shiftKey && event.key === 'T') {
        event.preventDefault();
        this.showTranslationIssues();
      }
      
      // Ctrl+Shift+C: Show component health
      if (event.ctrlKey && event.shiftKey && event.key === 'C') {
        event.preventDefault();
        this.showComponentHealth();
      }
    });
  }

  private setupVisualIndicators(): void {
    // Create debug indicator element
    const indicator = document.createElement('div');
    indicator.id = 'debug-indicator';
    indicator.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #00ff00;
      z-index: 10000;
      cursor: pointer;
      transition: all 0.3s ease;
    `;
    
    indicator.title = 'Debug Tools (Click to open panel)';
    indicator.addEventListener('click', () => this.openDebugPanel());
    
    document.body.appendChild(indicator);

    // Update indicator color based on error status
    setInterval(() => {
      const errorStats = errorLogger.getErrorStats();
      const componentHealth = componentMonitor.getAllComponentHealth();
      const criticalComponents = componentHealth.filter(c => c.status === 'critical').length;
      
      if (criticalComponents > 0 || errorStats.queueSize > 10) {
        indicator.style.background = '#ff0000'; // Red for critical issues
      } else if (errorStats.queueSize > 0) {
        indicator.style.background = '#ffaa00'; // Orange for warnings
      } else {
        indicator.style.background = '#00ff00'; // Green for healthy
      }
    }, 5000);
  }

  startDebugSession(): string {
    const sessionId = `debug-${Date.now()}`;
    this.debugSession = {
      id: sessionId,
      startTime: new Date().toISOString(),
      errors: [],
      warnings: [],
      info: [],
      performance: [],
    };

    // Intercept console methods
    this.interceptConsole();
    
    console.info(`Debug session started: ${sessionId}`);
    return sessionId;
  }

  endDebugSession(): DebugSession | null {
    if (!this.debugSession) return null;

    this.debugSession.endTime = new Date().toISOString();
    
    // Restore original console methods
    this.restoreConsole();
    
    const session = this.debugSession;
    this.debugSession = null;
    
    console.info(`Debug session ended: ${session.id}`);
    return session;
  }

  private interceptConsole(): void {
    if (!this.debugSession) return;

    console.error = (...args) => {
      this.debugSession?.errors.push({
        timestamp: new Date().toISOString(),
        args: args.map(arg => this.serializeArg(arg)),
      });
      this.originalConsole.error(...args);
    };

    console.warn = (...args) => {
      this.debugSession?.warnings.push({
        timestamp: new Date().toISOString(),
        args: args.map(arg => this.serializeArg(arg)),
      });
      this.originalConsole.warn(...args);
    };

    console.info = (...args) => {
      this.debugSession?.info.push({
        timestamp: new Date().toISOString(),
        args: args.map(arg => this.serializeArg(arg)),
      });
      this.originalConsole.info(...args);
    };
  }

  private restoreConsole(): void {
    console.error = this.originalConsole.error;
    console.warn = this.originalConsole.warn;
    console.info = this.originalConsole.info;
  }

  private serializeArg(arg: any): any {
    try {
      if (typeof arg === 'object' && arg !== null) {
        return JSON.parse(JSON.stringify(arg));
      }
      return arg;
    } catch {
      return '[Unserializable Object]';
    }
  }

  private openDebugPanel(): void {
    const report = this.generateComprehensiveReport();
    
    // Create debug panel
    const panel = document.createElement('div');
    panel.id = 'debug-panel';
    panel.style.cssText = `
      position: fixed;
      top: 50px;
      right: 20px;
      width: 400px;
      max-height: 80vh;
      background: white;
      border: 2px solid #333;
      border-radius: 8px;
      padding: 20px;
      z-index: 10001;
      overflow-y: auto;
      font-family: monospace;
      font-size: 12px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    `;

    panel.innerHTML = `
      <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 15px;">
        <h3 style="margin: 0; color: #333;">Debug Panel</h3>
        <button onclick="document.getElementById('debug-panel').remove()" style="background: #ff4444; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer;">×</button>
      </div>
      
      <div style="margin-bottom: 15px;">
        <h4 style="margin: 5px 0; color: #666;">Error Summary</h4>
        <div>Total Errors: ${report.errorSummary.totalErrors}</div>
        <div>Import Failures: ${report.errorSummary.importFailures}</div>
        <div>Missing Translations: ${report.errorSummary.missingTranslations}</div>
        <div>Component Failures: ${report.errorSummary.componentFailures}</div>
      </div>
      
      <div style="margin-bottom: 15px;">
        <h4 style="margin: 5px 0; color: #666;">Component Health</h4>
        ${report.componentHealth.slice(0, 5).map(c => `
          <div style="margin: 2px 0; color: ${c.status === 'critical' ? '#ff0000' : c.status === 'warning' ? '#ff8800' : '#00aa00'}">
            ${c.componentName}: ${c.status} (${(c.failureRate * 100).toFixed(1)}% failure rate)
          </div>
        `).join('')}
      </div>
      
      <div style="margin-bottom: 15px;">
        <h4 style="margin: 5px 0; color: #666;">Quick Actions</h4>
        <button onclick="window.debugTools.clearAllData()" style="margin: 2px; padding: 5px 10px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">Clear Data</button>
        <button onclick="console.log(window.debugTools.generateErrorReport())" style="margin: 2px; padding: 5px 10px; background: #2196F3; color: white; border: none; border-radius: 4px; cursor: pointer;">Log Report</button>
        <button onclick="window.debugTools.exportData()" style="margin: 2px; padding: 5px 10px; background: #FF9800; color: white; border: none; border-radius: 4px; cursor: pointer;">Export Data</button>
      </div>
    `;

    // Remove existing panel if present
    const existingPanel = document.getElementById('debug-panel');
    if (existingPanel) {
      existingPanel.remove();
    }

    document.body.appendChild(panel);
  }

  private showErrorSummary(): void {
    const stats = errorLogger.getErrorStats();
    console.group('🚨 Error Summary');
    console.log('Queue Size:', stats.queueSize);
    console.log('Is Processing:', stats.isProcessing);
    console.log('Total Logged:', stats.totalLogged);
    console.groupEnd();
  }

  private showTranslationIssues(): void {
    const stats = translationMonitor.getMissingKeyStats();
    console.group('🌐 Translation Issues');
    console.log('Total Missing:', stats.totalMissing);
    console.log('Missing by Language:', stats.missingByLanguage);
    console.log('Most Missed Keys:', stats.mostMissedKeys);
    console.groupEnd();
  }

  private showComponentHealth(): void {
    const health = componentMonitor.getAllComponentHealth();
    console.group('🔧 Component Health');
    health.forEach(component => {
      const icon = component.status === 'critical' ? '🔴' : 
                   component.status === 'warning' ? '🟡' : '🟢';
      console.log(`${icon} ${component.componentName}:`, {
        status: component.status,
        failureRate: `${(component.failureRate * 100).toFixed(1)}%`,
        avgRenderTime: `${component.averageRenderTime.toFixed(1)}ms`,
        totalRenders: component.totalRenders,
        totalFailures: component.totalFailures,
      });
    });
    console.groupEnd();
  }

  generateComprehensiveReport(): {
    timestamp: string;
    errorSummary: {
      totalErrors: number;
      importFailures: number;
      missingTranslations: number;
      componentFailures: number;
    };
    componentHealth: ReturnType<typeof componentMonitor.getAllComponentHealth>;
    importFailures: ReturnType<typeof importExportMonitor.getImportFailureStats>;
    translationIssues: ReturnType<typeof translationMonitor.getMissingKeyStats>;
    recommendations: string[];
  } {
    const errorStats = errorLogger.getErrorStats();
    const importStats = importExportMonitor.getImportFailureStats();
    const translationStats = translationMonitor.getMissingKeyStats();
    const componentHealth = componentMonitor.getAllComponentHealth();

    const recommendations: string[] = [];
    
    if (importStats.totalFailures > 0) {
      recommendations.push('Fix import/export issues to improve application stability');
    }
    
    if (translationStats.totalMissing > 0) {
      recommendations.push('Add missing translation keys to improve user experience');
    }
    
    const criticalComponents = componentHealth.filter(c => c.status === 'critical');
    if (criticalComponents.length > 0) {
      recommendations.push(`Address critical component issues: ${criticalComponents.map(c => c.componentName).join(', ')}`);
    }

    return {
      timestamp: new Date().toISOString(),
      errorSummary: {
        totalErrors: errorStats.totalLogged,
        importFailures: importStats.totalFailures,
        missingTranslations: translationStats.totalMissing,
        componentFailures: componentHealth.reduce((sum, c) => sum + c.totalFailures, 0),
      },
      componentHealth,
      importFailures: importStats,
      translationIssues: translationStats,
      recommendations,
    };
  }

  clearAllMonitoringData(): void {
    errorLogger.clearQueue();
    importExportMonitor.clear();
    translationMonitor.clear();
    componentMonitor.clear();
    console.info('All monitoring data cleared');
  }

  exportMonitoringData(): void {
    const data = this.generateComprehensiveReport();
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `debug-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.info('Monitoring data exported');
  }

  testErrorScenarios(): void {
    console.group('🧪 Testing Error Scenarios');
    
    // Test component error
    componentMonitor.logComponentFailure('TestComponent', new Error('Test error'), { testProp: 'value' });
    
    // Test import failure
    importExportMonitor.logImportFailure('./test-module', new Error('Module not found'), 'testExport');
    
    // Test missing translation
    translationMonitor.logMissingKey('test.missing.key', 'en', 'Fallback text');
    
    console.log('Error scenarios tested');
    console.groupEnd();
  }

  testComponent(componentName: string): void {
    console.group(`🔍 Testing Component: ${componentName}`);
    
    const health = componentMonitor.getComponentHealth(componentName);
    console.log('Component Health:', health);
    
    // Simulate render performance test
    const renderTime = Math.random() * 200; // Random render time
    componentMonitor.trackRenderPerformance(componentName, renderTime, 'update');
    
    console.log(`Simulated render time: ${renderTime.toFixed(1)}ms`);
    console.groupEnd();
  }

  simulateComponentError(componentName: string, errorMessage: string): void {
    const error = new Error(errorMessage);
    componentMonitor.logComponentFailure(componentName, error);
    console.warn(`Simulated error in ${componentName}: ${errorMessage}`);
  }

  testTranslationKeys(keys: string[]): void {
    console.group('🌐 Testing Translation Keys');
    
    keys.forEach(key => {
      // Simulate missing key
      translationMonitor.logMissingKey(key, 'en', `Fallback for ${key}`);
      translationMonitor.logMissingKey(key, 'ar', `بديل لـ ${key}`);
    });
    
    console.log(`Tested ${keys.length} translation keys`);
    console.groupEnd();
  }

  findMissingTranslations(): string[] {
    const stats = translationMonitor.getMissingKeyStats();
    const missingKeys = stats.mostMissedKeys.map(k => k.key);
    
    console.group('🔍 Missing Translation Keys');
    console.log('Most missed keys:', missingKeys);
    console.groupEnd();
    
    return missingKeys;
  }
}

// Create singleton instance (only in development)
export const developmentTools = process.env.NODE_ENV === 'development' 
  ? new DevelopmentTools() 
  : null;

// Export utility functions
export const debugUtils = {
  logError: (error: Error, context?: Record<string, any>) => {
    errorLogger.logError({
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      source: 'debugUtils',
      context,
    });
  },
  
  logComponentError: (componentName: string, error: Error, props?: Record<string, any>) => {
    componentMonitor.logComponentFailure(componentName, error, props);
  },
  
  logImportError: (modulePath: string, error: Error, importName?: string) => {
    importExportMonitor.logImportFailure(modulePath, error, importName);
  },
  
  logMissingTranslation: (key: string, language: string, fallback: string) => {
    translationMonitor.logMissingKey(key, language, fallback);
  },
};