/**
 * Syrian Identity Pattern System
 * 
 * Performance-optimized SVG patterns inspired by Syrian cultural heritage.
 * All patterns are designed to be lightweight (≤2KB each) and performant.
 * 
 * Pattern Sources:
 * - Damascus Star: Traditional Islamic geometric pattern from Damascus architecture
 * - Palmyra Columns: Inspired by ancient Palmyra column capitals
 * - Ebla Script: Stylized representation of ancient Ebla cuneiform
 * - Geometric Weave: Traditional Syrian textile patterns
 * 
 * Performance Features:
 * - Optimized SVG markup (≤2KB per pattern)
 * - CSS containment for isolated rendering
 * - Hardware-accelerated animations only
 * - Responsive degradation on mobile
 * - Respects prefers-reduced-motion
 */

// Pattern configuration interface
export interface SyrianPatternConfig {
  id: string;
  name: string;
  description: string;
  culturalSignificance: string;
  svgContent: string;
  size: {
    width: number;
    height: number;
    defaultSize: string; // CSS size value
  };
  performance: {
    estimatedSize: string;
    paintComplexity: 'low' | 'medium' | 'high';
    mobileOptimized: boolean;
  };
  accessibility: {
    isDecorative: boolean;
    ariaHidden: boolean;
    description?: string;
  };
  responsive: {
    disableOnMobile: boolean;
    mobileOpacity: number;
    tabletOpacity: number;
    desktopOpacity: number;
  };
}

/**
 * Damascus Star Pattern
 * Traditional 8-pointed star pattern from Damascus Islamic architecture
 */
export const damascusStarPattern: SyrianPatternConfig = {
  id: 'damascus-star',
  name: 'Damascus Star',
  description: 'Traditional 8-pointed star pattern from Damascus Islamic architecture',
  culturalSignificance: 'Represents the eight-fold path and divine guidance in Islamic art',
  svgContent: `
    <svg viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <pattern id="damascus-star-pattern" x="0" y="0" width="60" height="60" patternUnits="userSpaceOnUse">
          <!-- Central 8-pointed star -->
          <g fill="currentColor" opacity="0.03">
            <!-- Outer star points -->
            <polygon points="30,5 35,20 50,15 40,30 55,35 40,40 50,55 35,50 30,65 25,50 10,55 20,40 5,35 20,30 10,15 25,20"/>
            <!-- Inner diamond -->
            <polygon points="30,15 40,30 30,45 20,30" opacity="0.5"/>
            <!-- Center dot -->
            <circle cx="30" cy="30" r="3" opacity="0.7"/>
          </g>
          <!-- Corner accent stars -->
          <g fill="currentColor" opacity="0.02">
            <polygon points="0,0 5,10 15,5 10,15 20,20 10,25 15,35 5,30 0,40 -5,30 -15,35 -10,25 -20,20 -10,15 -15,5 -5,10"/>
            <polygon points="60,0 65,10 75,5 70,15 80,20 70,25 75,35 65,30 60,40 55,30 45,35 50,25 40,20 50,15 45,5 55,10"/>
            <polygon points="0,60 5,70 15,65 10,75 20,80 10,85 15,95 5,90 0,100 -5,90 -15,95 -10,85 -20,80 -10,75 -15,65 -5,70"/>
            <polygon points="60,60 65,70 75,65 70,75 80,80 70,85 75,95 65,90 60,100 55,90 45,95 50,85 40,80 50,75 45,65 55,70"/>
          </g>
        </pattern>
      </defs>
    </svg>
  `,
  size: {
    width: 60,
    height: 60,
    defaultSize: '60px',
  },
  performance: {
    estimatedSize: '1.2KB',
    paintComplexity: 'medium',
    mobileOptimized: true,
  },
  accessibility: {
    isDecorative: true,
    ariaHidden: true,
  },
  responsive: {
    disableOnMobile: false,
    mobileOpacity: 0.02,
    tabletOpacity: 0.03,
    desktopOpacity: 0.04,
  },
};

/**
 * Palmyra Columns Pattern
 * Inspired by the ancient column capitals of Palmyra
 */
export const palmyraColumnsPattern: SyrianPatternConfig = {
  id: 'palmyra-columns',
  name: 'Palmyra Columns',
  description: 'Stylized pattern inspired by ancient Palmyra column capitals',
  culturalSignificance: 'Honors the ancient Syrian city of Palmyra and its architectural heritage',
  svgContent: `
    <svg viewBox="0 0 80 40" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <pattern id="palmyra-columns-pattern" x="0" y="0" width="80" height="40" patternUnits="userSpaceOnUse">
          <!-- Column capitals -->
          <g fill="currentColor" opacity="0.03">
            <!-- Left column -->
            <rect x="10" y="25" width="8" height="15"/>
            <ellipse cx="14" cy="25" rx="12" ry="4"/>
            <rect x="6" y="21" width="16" height="4" rx="2"/>
            <!-- Decorative capital details -->
            <circle cx="10" cy="23" r="1" opacity="0.5"/>
            <circle cx="18" cy="23" r="1" opacity="0.5"/>
            
            <!-- Right column -->
            <rect x="50" y="25" width="8" height="15"/>
            <ellipse cx="54" cy="25" rx="12" ry="4"/>
            <rect x="46" y="21" width="16" height="4" rx="2"/>
            <!-- Decorative capital details -->
            <circle cx="50" cy="23" r="1" opacity="0.5"/>
            <circle cx="58" cy="23" r="1" opacity="0.5"/>
          </g>
          <!-- Connecting architectural elements -->
          <g fill="currentColor" opacity="0.02">
            <rect x="22" y="22" width="24" height="2"/>
            <rect x="26" y="20" width="16" height="1"/>
          </g>
        </pattern>
      </defs>
    </svg>
  `,
  size: {
    width: 80,
    height: 40,
    defaultSize: '80px',
  },
  performance: {
    estimatedSize: '0.9KB',
    paintComplexity: 'low',
    mobileOptimized: true,
  },
  accessibility: {
    isDecorative: true,
    ariaHidden: true,
  },
  responsive: {
    disableOnMobile: true, // Disable on mobile for performance
    mobileOpacity: 0,
    tabletOpacity: 0.03,
    desktopOpacity: 0.03,
  },
};

/**
 * Ebla Script Pattern
 * Stylized representation of ancient Ebla cuneiform script
 */
export const eblaScriptPattern: SyrianPatternConfig = {
  id: 'ebla-script',
  name: 'Ebla Script',
  description: 'Stylized pattern inspired by ancient Ebla cuneiform script',
  culturalSignificance: 'Celebrates the ancient Syrian kingdom of Ebla and early writing systems',
  svgContent: `
    <svg viewBox="0 0 50 30" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <pattern id="ebla-script-pattern" x="0" y="0" width="50" height="30" patternUnits="userSpaceOnUse">
          <!-- Stylized cuneiform marks -->
          <g fill="currentColor" opacity="0.03">
            <!-- First script group -->
            <g transform="translate(5,5)">
              <rect x="0" y="0" width="2" height="8" rx="1"/>
              <rect x="3" y="2" width="6" height="1" rx="0.5"/>
              <rect x="3" y="5" width="4" height="1" rx="0.5"/>
              <rect x="10" y="0" width="1" height="6" rx="0.5"/>
              <rect x="12" y="3" width="3" height="1" rx="0.5"/>
            </g>
            
            <!-- Second script group -->
            <g transform="translate(25,8)">
              <rect x="0" y="0" width="1" height="6" rx="0.5"/>
              <rect x="2" y="1" width="4" height="1" rx="0.5"/>
              <rect x="2" y="4" width="5" height="1" rx="0.5"/>
              <rect x="8" y="0" width="2" height="7" rx="1"/>
              <rect x="11" y="2" width="3" height="1" rx="0.5"/>
            </g>
            
            <!-- Third script group -->
            <g transform="translate(10,18)">
              <rect x="0" y="0" width="3" height="1" rx="0.5"/>
              <rect x="0" y="3" width="2" height="1" rx="0.5"/>
              <rect x="4" y="0" width="1" height="5" rx="0.5"/>
              <rect x="6" y="2" width="4" height="1" rx="0.5"/>
            </g>
          </g>
        </pattern>
      </defs>
    </svg>
  `,
  size: {
    width: 50,
    height: 30,
    defaultSize: '50px',
  },
  performance: {
    estimatedSize: '1.1KB',
    paintComplexity: 'low',
    mobileOptimized: true,
  },
  accessibility: {
    isDecorative: true,
    ariaHidden: true,
  },
  responsive: {
    disableOnMobile: false,
    mobileOpacity: 0.02,
    tabletOpacity: 0.03,
    desktopOpacity: 0.04,
  },
};

/**
 * Geometric Weave Pattern
 * Traditional Syrian textile-inspired geometric pattern
 */
export const geometricWeavePattern: SyrianPatternConfig = {
  id: 'geometric-weave',
  name: 'Geometric Weave',
  description: 'Traditional Syrian textile-inspired geometric weave pattern',
  culturalSignificance: 'Represents the rich tradition of Syrian textile arts and craftsmanship',
  svgContent: `
    <svg viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <pattern id="geometric-weave-pattern" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
          <!-- Interlocking geometric shapes -->
          <g fill="currentColor" opacity="0.03">
            <!-- Horizontal weave -->
            <rect x="0" y="8" width="40" height="4" opacity="0.6"/>
            <rect x="0" y="20" width="40" height="4" opacity="0.6"/>
            <rect x="0" y="32" width="40" height="4" opacity="0.6"/>
            
            <!-- Vertical weave -->
            <rect x="8" y="0" width="4" height="40" opacity="0.4"/>
            <rect x="20" y="0" width="4" height="40" opacity="0.4"/>
            <rect x="32" y="0" width="4" height="40" opacity="0.4"/>
            
            <!-- Intersection diamonds -->
            <polygon points="10,10 14,6 18,10 14,14" opacity="0.8"/>
            <polygon points="22,10 26,6 30,10 26,14" opacity="0.8"/>
            <polygon points="10,22 14,18 18,22 14,26" opacity="0.8"/>
            <polygon points="22,22 26,18 30,22 26,26" opacity="0.8"/>
            <polygon points="10,34 14,30 18,34 14,38" opacity="0.8"/>
            <polygon points="22,34 26,30 30,34 26,38" opacity="0.8"/>
          </g>
        </pattern>
      </defs>
    </svg>
  `,
  size: {
    width: 40,
    height: 40,
    defaultSize: '40px',
  },
  performance: {
    estimatedSize: '1.0KB',
    paintComplexity: 'high',
    mobileOptimized: false,
  },
  accessibility: {
    isDecorative: true,
    ariaHidden: true,
  },
  responsive: {
    disableOnMobile: true, // Disable on mobile due to complexity
    mobileOpacity: 0,
    tabletOpacity: 0.02,
    desktopOpacity: 0.04,
  },
};

// Export all patterns
export const syrianPatterns = {
  damascusStar: damascusStarPattern,
  palmyraColumns: palmyraColumnsPattern,
  eblaScript: eblaScriptPattern,
  geometricWeave: geometricWeavePattern,
} as const;

// Pattern utility functions
export type SyrianPatternName = keyof typeof syrianPatterns;

export function getSyrianPattern(name: SyrianPatternName): SyrianPatternConfig {
  return syrianPatterns[name];
}

export function getAllSyrianPatterns(): SyrianPatternConfig[] {
  return Object.values(syrianPatterns);
}

export function getPatternsByComplexity(complexity: 'low' | 'medium' | 'high'): SyrianPatternConfig[] {
  return getAllSyrianPatterns().filter(pattern => pattern.performance.paintComplexity === complexity);
}

export function getMobileOptimizedPatterns(): SyrianPatternConfig[] {
  return getAllSyrianPatterns().filter(pattern => pattern.performance.mobileOptimized);
}

// Performance monitoring
export function estimateTotalPatternSize(): string {
  const totalBytes = getAllSyrianPatterns().reduce((total, pattern) => {
    const sizeInBytes = parseFloat(pattern.performance.estimatedSize.replace('KB', '')) * 1024;
    return total + sizeInBytes;
  }, 0);
  
  return `${(totalBytes / 1024).toFixed(1)}KB`;
}

// Export types
export type { SyrianPatternConfig };
