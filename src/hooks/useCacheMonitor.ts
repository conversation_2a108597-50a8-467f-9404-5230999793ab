import { useState, useEffect, useCallback } from 'react';
import { cacheMonitor, CacheStats, CacheMetrics } from '@/lib/cacheMonitor';

// Hook for monitoring cache statistics
export const useCacheStats = (refreshInterval = 5000) => {
  const [stats, setStats] = useState<CacheStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const refreshStats = useCallback(() => {
    try {
      const currentStats = cacheMonitor.getCacheStats();
      setStats(currentStats);
      setIsLoading(false);
    } catch (error) {
      console.error('Failed to get cache stats:', error);
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    // Initial load
    refreshStats();

    // Set up interval for automatic refresh
    const interval = setInterval(refreshStats, refreshInterval);

    return () => clearInterval(interval);
  }, [refreshStats, refreshInterval]);

  return {
    stats,
    isLoading,
    refresh: refreshStats,
  };
};

// Hook for monitoring specific query performance
export const useQueryPerformance = (queryKey: string) => {
  const [performance, setPerformance] = useState<{
    executions: number;
    errors: number;
    averageDuration: number;
    hits: number;
    misses: number;
  } | null>(null);

  const refreshPerformance = useCallback(() => {
    try {
      const queryPerformance = cacheMonitor.getQueryPerformance(queryKey);
      setPerformance(queryPerformance);
    } catch (error) {
      console.error('Failed to get query performance:', error);
    }
  }, [queryKey]);

  useEffect(() => {
    refreshPerformance();
    
    // Refresh every 10 seconds
    const interval = setInterval(refreshPerformance, 10000);
    
    return () => clearInterval(interval);
  }, [refreshPerformance]);

  return {
    performance,
    refresh: refreshPerformance,
  };
};

// Hook for getting cache metrics
export const useCacheMetrics = (refreshInterval = 10000) => {
  const [metrics, setMetrics] = useState<CacheMetrics[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const refreshMetrics = useCallback(() => {
    try {
      const currentMetrics = cacheMonitor.getCacheMetrics();
      setMetrics(currentMetrics);
      setIsLoading(false);
    } catch (error) {
      console.error('Failed to get cache metrics:', error);
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    refreshMetrics();
    
    const interval = setInterval(refreshMetrics, refreshInterval);
    
    return () => clearInterval(interval);
  }, [refreshMetrics, refreshInterval]);

  return {
    metrics,
    isLoading,
    refresh: refreshMetrics,
  };
};

// Hook for getting top performing queries
export const useTopQueries = (limit = 10, refreshInterval = 15000) => {
  const [topQueries, setTopQueries] = useState<Array<{
    queryKey: string;
    hitRate: number;
    executions: number;
    averageDuration: number;
  }>>([]);

  const refreshTopQueries = useCallback(() => {
    try {
      const queries = cacheMonitor.getTopQueries(limit);
      setTopQueries(queries);
    } catch (error) {
      console.error('Failed to get top queries:', error);
    }
  }, [limit]);

  useEffect(() => {
    refreshTopQueries();
    
    const interval = setInterval(refreshTopQueries, refreshInterval);
    
    return () => clearInterval(interval);
  }, [refreshTopQueries, refreshInterval]);

  return {
    topQueries,
    refresh: refreshTopQueries,
  };
};

// Hook for getting problematic queries
export const useProblematicQueries = (limit = 10, refreshInterval = 15000) => {
  const [problematicQueries, setProblematicQueries] = useState<Array<{
    queryKey: string;
    errorRate: number;
    averageDuration: number;
    executions: number;
  }>>([]);

  const refreshProblematicQueries = useCallback(() => {
    try {
      const queries = cacheMonitor.getProblematicQueries(limit);
      setProblematicQueries(queries);
    } catch (error) {
      console.error('Failed to get problematic queries:', error);
    }
  }, [limit]);

  useEffect(() => {
    refreshProblematicQueries();
    
    const interval = setInterval(refreshProblematicQueries, refreshInterval);
    
    return () => clearInterval(interval);
  }, [refreshProblematicQueries, refreshInterval]);

  return {
    problematicQueries,
    refresh: refreshProblematicQueries,
  };
};

// Comprehensive cache monitoring hook
export const useCacheMonitor = (options: {
  refreshInterval?: number;
  includeMetrics?: boolean;
  includeTopQueries?: boolean;
  includeProblematicQueries?: boolean;
  topQueriesLimit?: number;
  problematicQueriesLimit?: number;
} = {}) => {
  const {
    refreshInterval = 10000,
    includeMetrics = false,
    includeTopQueries = false,
    includeProblematicQueries = false,
    topQueriesLimit = 10,
    problematicQueriesLimit = 10,
  } = options;

  const { stats, isLoading: statsLoading, refresh: refreshStats } = useCacheStats(refreshInterval);
  
  const { metrics, isLoading: metricsLoading, refresh: refreshMetrics } = useCacheMetrics(
    includeMetrics ? refreshInterval : 0
  );
  
  const { topQueries, refresh: refreshTopQueries } = useTopQueries(
    topQueriesLimit,
    includeTopQueries ? refreshInterval : 0
  );
  
  const { problematicQueries, refresh: refreshProblematicQueries } = useProblematicQueries(
    problematicQueriesLimit,
    includeProblematicQueries ? refreshInterval : 0
  );

  const refreshAll = useCallback(() => {
    refreshStats();
    if (includeMetrics) refreshMetrics();
    if (includeTopQueries) refreshTopQueries();
    if (includeProblematicQueries) refreshProblematicQueries();
  }, [
    refreshStats,
    refreshMetrics,
    refreshTopQueries,
    refreshProblematicQueries,
    includeMetrics,
    includeTopQueries,
    includeProblematicQueries,
  ]);

  const clearCache = useCallback(() => {
    cacheMonitor.clearStats();
    refreshAll();
  }, [refreshAll]);

  const exportData = useCallback(() => {
    return cacheMonitor.exportCacheData();
  }, []);

  return {
    stats,
    metrics: includeMetrics ? metrics : [],
    topQueries: includeTopQueries ? topQueries : [],
    problematicQueries: includeProblematicQueries ? problematicQueries : [],
    isLoading: statsLoading || (includeMetrics && metricsLoading),
    refresh: refreshAll,
    clearCache,
    exportData,
  };
};

// Hook for cache health monitoring with alerts
export const useCacheHealth = (thresholds: {
  maxErrorRate?: number;
  minHitRate?: number;
  maxAverageDuration?: number;
} = {}) => {
  const {
    maxErrorRate = 10, // 10%
    minHitRate = 70, // 70%
    maxAverageDuration = 2000, // 2 seconds
  } = thresholds;

  const { stats } = useCacheStats();
  const { problematicQueries } = useProblematicQueries(5);

  const [alerts, setAlerts] = useState<Array<{
    type: 'error' | 'warning' | 'info';
    message: string;
    timestamp: number;
  }>>([]);

  useEffect(() => {
    if (!stats) return;

    const newAlerts: typeof alerts = [];

    // Check hit rate
    if (stats.hitRate < minHitRate) {
      newAlerts.push({
        type: 'warning',
        message: `Cache hit rate is low: ${stats.hitRate}% (threshold: ${minHitRate}%)`,
        timestamp: Date.now(),
      });
    }

    // Check for problematic queries
    problematicQueries.forEach(query => {
      if (query.errorRate > maxErrorRate) {
        newAlerts.push({
          type: 'error',
          message: `High error rate for query: ${query.queryKey} (${query.errorRate}%)`,
          timestamp: Date.now(),
        });
      }

      if (query.averageDuration > maxAverageDuration) {
        newAlerts.push({
          type: 'warning',
          message: `Slow query detected: ${query.queryKey} (${query.averageDuration}ms)`,
          timestamp: Date.now(),
        });
      }
    });

    // Check memory usage
    if (stats.memoryUsage.percentage > 90) {
      newAlerts.push({
        type: 'error',
        message: `High memory usage: ${stats.memoryUsage.percentage}%`,
        timestamp: Date.now(),
      });
    } else if (stats.memoryUsage.percentage > 75) {
      newAlerts.push({
        type: 'warning',
        message: `Memory usage is high: ${stats.memoryUsage.percentage}%`,
        timestamp: Date.now(),
      });
    }

    setAlerts(newAlerts);
  }, [stats, problematicQueries, maxErrorRate, minHitRate, maxAverageDuration]);

  const clearAlerts = useCallback(() => {
    setAlerts([]);
  }, []);

  const isHealthy = alerts.length === 0;
  const hasErrors = alerts.some(alert => alert.type === 'error');
  const hasWarnings = alerts.some(alert => alert.type === 'warning');

  return {
    isHealthy,
    hasErrors,
    hasWarnings,
    alerts,
    clearAlerts,
    stats,
  };
};