// Hook that uses the new Context + useReducer implementation
import { useAuthContext } from '@/components/auth/AuthProvider'

export function useAuth() {
  const {
    user,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updateProfile
  } = useAuthContext()

  return {
    user,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updateProfile
  }
}