import { useState, useRef, useCallback } from 'react'

interface CameraOptions {
  facingMode?: 'user' | 'environment'
  width?: number
  height?: number
  quality?: number
}

interface CameraState {
  isSupported: boolean
  isActive: boolean
  error: string | null
  stream: MediaStream | null
}

export function useCamera(options: CameraOptions = {}) {
  const {
    facingMode = 'environment',
    width = 1920,
    height = 1080,
    quality = 0.8
  } = options

  const [state, setState] = useState<CameraState>({
    isSupported: false,
    isActive: false,
    error: null,
    stream: null
  })

  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  // Check if camera is supported
  const checkSupport = useCallback(() => {
    const isSupported = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
    setState(prev => ({ ...prev, isSupported }))
    return isSupported
  }, [])

  // Start camera
  const startCamera = useCallback(async () => {
    if (!checkSupport()) {
      setState(prev => ({ ...prev, error: 'الكاميرا غير مدعومة في هذا المتصفح' }))
      return false
    }

    try {
      setState(prev => ({ ...prev, error: null }))
      
      const constraints: MediaStreamConstraints = {
        video: {
          facingMode,
          width: { ideal: width },
          height: { ideal: height }
        }
      }

      const stream = await navigator.mediaDevices.getUserMedia(constraints)
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        await videoRef.current.play()
      }

      setState(prev => ({
        ...prev,
        isActive: true,
        stream,
        error: null
      }))

      return true
    } catch (error) {
      console.error('Error starting camera:', error)
      let errorMessage = 'فشل في تشغيل الكاميرا'
      
      if (error instanceof Error) {
        if (error.name === 'NotAllowedError') {
          errorMessage = 'تم رفض الوصول إلى الكاميرا. يرجى السماح بالوصول إلى الكاميرا في إعدادات المتصفح'
        } else if (error.name === 'NotFoundError') {
          errorMessage = 'لم يتم العثور على كاميرا متاحة'
        } else if (error.name === 'NotReadableError') {
          errorMessage = 'الكاميرا قيد الاستخدام من قبل تطبيق آخر'
        }
      }

      setState(prev => ({ ...prev, error: errorMessage, isActive: false }))
      return false
    }
  }, [facingMode, width, height, checkSupport])

  // Stop camera
  const stopCamera = useCallback(() => {
    if (state.stream) {
      state.stream.getTracks().forEach(track => track.stop())
    }
    
    if (videoRef.current) {
      videoRef.current.srcObject = null
    }

    setState(prev => ({
      ...prev,
      isActive: false,
      stream: null,
      error: null
    }))
  }, [state.stream])

  // Capture photo
  const capturePhoto = useCallback((): Promise<Blob | null> => {
    return new Promise((resolve) => {
      if (!videoRef.current || !canvasRef.current || !state.isActive) {
        resolve(null)
        return
      }

      const video = videoRef.current
      const canvas = canvasRef.current
      const context = canvas.getContext('2d')

      if (!context) {
        resolve(null)
        return
      }

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight

      // Draw video frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height)

      // Convert to blob
      canvas.toBlob(
        (blob) => resolve(blob),
        'image/jpeg',
        quality
      )
    })
  }, [state.isActive, quality])

  // Switch camera (front/back)
  const switchCamera = useCallback(async () => {
    const newFacingMode = facingMode === 'user' ? 'environment' : 'user'
    stopCamera()
    
    // Wait a bit for cleanup
    setTimeout(() => {
      startCamera()
    }, 100)
  }, [facingMode, stopCamera, startCamera])

  // Get available cameras
  const getAvailableCameras = useCallback(async () => {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices()
      return devices.filter(device => device.kind === 'videoinput')
    } catch (error) {
      console.error('Error getting cameras:', error)
      return []
    }
  }, [])

  return {
    ...state,
    videoRef,
    canvasRef,
    startCamera,
    stopCamera,
    capturePhoto,
    switchCamera,
    getAvailableCameras,
    checkSupport
  }
}

export default useCamera