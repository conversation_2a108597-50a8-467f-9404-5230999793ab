import { useState, useEffect, useCallback } from 'react';

interface ImageOptimizationConfig {
  formats: ('avif' | 'webp' | 'jpeg' | 'png')[];
  sizes: number[];
  quality: number;
  enableLazyLoading: boolean;
  lazyLoadingThreshold: number;
}

interface OptimizedImageSource {
  src: string;
  format: string;
  mimeType: string;
  size?: number;
}

const DEFAULT_CONFIG: ImageOptimizationConfig = {
  formats: ['avif', 'webp', 'jpeg'],
  sizes: [320, 640, 960, 1280, 1920],
  quality: 80,
  enableLazyLoading: true,
  lazyLoadingThreshold: 50
};

// Cache for format support detection
const formatSupportCache = new Map<string, boolean>();

export const useImageOptimization = (config: Partial<ImageOptimizationConfig> = {}) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const [supportedFormats, setSupportedFormats] = useState<string[]>([]);

  // Detect supported image formats
  useEffect(() => {
    const detectFormatSupport = async () => {
      const supported: string[] = [];

      for (const format of finalConfig.formats) {
        // Check cache first
        if (formatSupportCache.has(format)) {
          if (formatSupportCache.get(format)) {
            supported.push(format);
          }
          continue;
        }

        try {
          // Create a test canvas to check format support
          const canvas = document.createElement('canvas');
          canvas.width = 1;
          canvas.height = 1;
          const ctx = canvas.getContext('2d');
          
          if (ctx) {
            // Draw a single pixel
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, 1, 1);
            
            // Try to convert to the format
            const dataUrl = canvas.toDataURL(`image/${format}`, finalConfig.quality / 100);
            const isSupported = dataUrl.startsWith(`data:image/${format}`);
            
            formatSupportCache.set(format, isSupported);
            if (isSupported) {
              supported.push(format);
            }
          }
        } catch (error) {
          formatSupportCache.set(format, false);
        }
      }

      setSupportedFormats(supported);
    };

    if (typeof window !== 'undefined') {
      detectFormatSupport();
    }
  }, [finalConfig.formats, finalConfig.quality]);

  // Generate optimized image sources
  const generateSources = useCallback((
    originalSrc: string,
    requestedSizes?: string
  ): OptimizedImageSource[] => {
    const sources: OptimizedImageSource[] = [];
    
    // Extract base URL without extension
    const lastDotIndex = originalSrc.lastIndexOf('.');
    const baseUrl = lastDotIndex > -1 ? originalSrc.substring(0, lastDotIndex) : originalSrc;
    const originalExtension = lastDotIndex > -1 ? originalSrc.substring(lastDotIndex) : '';

    // Generate sources for each supported format
    supportedFormats.forEach(format => {
      const mimeType = `image/${format}`;
      const extension = format === 'jpeg' ? '.jpg' : `.${format}`;
      
      // For now, we'll use the original URL structure
      // In a production environment, you'd integrate with an image optimization service
      const optimizedSrc = `${baseUrl}${extension}`;
      
      sources.push({
        src: optimizedSrc,
        format,
        mimeType
      });
    });

    // Always include original as fallback
    sources.push({
      src: originalSrc,
      format: 'original',
      mimeType: originalExtension.includes('png') ? 'image/png' : 'image/jpeg'
    });

    return sources;
  }, [supportedFormats]);

  // Generate responsive image sizes
  const generateSizes = useCallback((
    breakpoints?: { [key: string]: number }
  ): string => {
    if (breakpoints) {
      return Object.entries(breakpoints)
        .map(([media, size]) => `${media} ${size}px`)
        .join(', ');
    }

    // Default responsive sizes
    return '(max-width: 320px) 320px, (max-width: 640px) 640px, (max-width: 960px) 960px, (max-width: 1280px) 1280px, 1920px';
  }, []);

  // Check if lazy loading should be enabled
  const shouldLazyLoad = useCallback((priority: boolean = false): boolean => {
    return finalConfig.enableLazyLoading && !priority;
  }, [finalConfig.enableLazyLoading]);

  // Generate blur placeholder data URL
  const generateBlurPlaceholder = useCallback((
    width: number = 10,
    height: number = 10,
    color: string = '#f3f4f6'
  ): string => {
    if (typeof window === 'undefined') {
      return 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==';
    }

    try {
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      
      if (ctx) {
        ctx.fillStyle = color;
        ctx.fillRect(0, 0, width, height);
        return canvas.toDataURL('image/jpeg', 0.1);
      }
    } catch (error) {
      console.warn('Failed to generate blur placeholder:', error);
    }

    // Fallback blur placeholder
    return 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==';
  }, []);

  return {
    supportedFormats,
    generateSources,
    generateSizes,
    shouldLazyLoad,
    generateBlurPlaceholder,
    config: finalConfig
  };
};

export default useImageOptimization;