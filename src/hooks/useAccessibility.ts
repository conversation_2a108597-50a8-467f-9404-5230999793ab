import { useEffect, useRef, useState, useCallback } from 'react';
import { useDeviceType } from './use-mobile';

/**
 * Hook for managing focus and keyboard navigation
 */
export function useFocusManagement() {
  const focusableElementsRef = useRef<HTMLElement[]>([]);
  const currentFocusIndex = useRef<number>(-1);

  const updateFocusableElements = useCallback((container: HTMLElement) => {
    const focusableSelectors = [
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      'a[href]',
      '[tabindex]:not([tabindex="-1"])',
      '[role="button"]:not([disabled])',
      '[role="link"]:not([disabled])',
      '[role="menuitem"]:not([disabled])',
      '[role="tab"]:not([disabled])'
    ].join(', ');

    focusableElementsRef.current = Array.from(
      container.querySelectorAll(focusableSelectors)
    ) as HTMLElement[];
  }, []);

  const focusFirst = useCallback(() => {
    if (focusableElementsRef.current.length > 0) {
      focusableElementsRef.current[0].focus();
      currentFocusIndex.current = 0;
    }
  }, []);

  const focusLast = useCallback(() => {
    const lastIndex = focusableElementsRef.current.length - 1;
    if (lastIndex >= 0) {
      focusableElementsRef.current[lastIndex].focus();
      currentFocusIndex.current = lastIndex;
    }
  }, []);

  const focusNext = useCallback(() => {
    const nextIndex = (currentFocusIndex.current + 1) % focusableElementsRef.current.length;
    if (focusableElementsRef.current[nextIndex]) {
      focusableElementsRef.current[nextIndex].focus();
      currentFocusIndex.current = nextIndex;
    }
  }, []);

  const focusPrevious = useCallback(() => {
    const prevIndex = currentFocusIndex.current <= 0 
      ? focusableElementsRef.current.length - 1 
      : currentFocusIndex.current - 1;
    if (focusableElementsRef.current[prevIndex]) {
      focusableElementsRef.current[prevIndex].focus();
      currentFocusIndex.current = prevIndex;
    }
  }, []);

  const trapFocus = useCallback((event: KeyboardEvent) => {
    if (event.key !== 'Tab') return;

    if (focusableElementsRef.current.length === 0) {
      event.preventDefault();
      return;
    }

    if (event.shiftKey) {
      if (document.activeElement === focusableElementsRef.current[0]) {
        event.preventDefault();
        focusLast();
      }
    } else {
      if (document.activeElement === focusableElementsRef.current[focusableElementsRef.current.length - 1]) {
        event.preventDefault();
        focusFirst();
      }
    }
  }, [focusFirst, focusLast]);

  return {
    updateFocusableElements,
    focusFirst,
    focusLast,
    focusNext,
    focusPrevious,
    trapFocus,
    focusableElements: focusableElementsRef.current
  };
}

/**
 * Hook for managing ARIA announcements
 */
export function useAriaAnnouncements() {
  const announcementRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    // Create announcement region if it doesn't exist
    if (!announcementRef.current) {
      const announcer = document.createElement('div');
      announcer.setAttribute('aria-live', 'polite');
      announcer.setAttribute('aria-atomic', 'true');
      announcer.setAttribute('aria-relevant', 'text');
      announcer.style.position = 'absolute';
      announcer.style.left = '-10000px';
      announcer.style.width = '1px';
      announcer.style.height = '1px';
      announcer.style.overflow = 'hidden';
      document.body.appendChild(announcer);
      announcementRef.current = announcer;
    }

    return () => {
      if (announcementRef.current && document.body.contains(announcementRef.current)) {
        document.body.removeChild(announcementRef.current);
      }
    };
  }, []);

  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (announcementRef.current) {
      announcementRef.current.setAttribute('aria-live', priority);
      announcementRef.current.textContent = message;
      
      // Clear after announcement to allow repeated announcements
      setTimeout(() => {
        if (announcementRef.current) {
          announcementRef.current.textContent = '';
        }
      }, 1000);
    }
  }, []);

  return { announce };
}

/**
 * Hook for ensuring minimum touch target sizes
 */
export function useTouchTargets() {
  const { isTouchDevice } = useDeviceType();
  const MIN_TOUCH_SIZE = 44; // 44px minimum as per WCAG guidelines

  const ensureTouchTarget = useCallback((element: HTMLElement) => {
    if (!isTouchDevice) return;

    const rect = element.getBoundingClientRect();
    const computedStyle = window.getComputedStyle(element);
    
    // Check if element meets minimum size requirements
    if (rect.width < MIN_TOUCH_SIZE || rect.height < MIN_TOUCH_SIZE) {
      // Add padding to meet minimum size
      const paddingX = Math.max(0, (MIN_TOUCH_SIZE - rect.width) / 2);
      const paddingY = Math.max(0, (MIN_TOUCH_SIZE - rect.height) / 2);
      
      element.style.paddingLeft = `${Math.max(parseInt(computedStyle.paddingLeft) || 0, paddingX)}px`;
      element.style.paddingRight = `${Math.max(parseInt(computedStyle.paddingRight) || 0, paddingX)}px`;
      element.style.paddingTop = `${Math.max(parseInt(computedStyle.paddingTop) || 0, paddingY)}px`;
      element.style.paddingBottom = `${Math.max(parseInt(computedStyle.paddingBottom) || 0, paddingY)}px`;
      
      // Ensure minimum dimensions
      element.style.minWidth = `${MIN_TOUCH_SIZE}px`;
      element.style.minHeight = `${MIN_TOUCH_SIZE}px`;
    }
  }, [isTouchDevice]);

  const validateTouchTargets = useCallback((container: HTMLElement) => {
    if (!isTouchDevice) return;

    const interactiveElements = container.querySelectorAll(
      'button, input, select, textarea, a, [role="button"], [role="link"], [tabindex]:not([tabindex="-1"])'
    );

    interactiveElements.forEach((element) => {
      ensureTouchTarget(element as HTMLElement);
    });
  }, [isTouchDevice, ensureTouchTarget]);

  return {
    ensureTouchTarget,
    validateTouchTargets,
    MIN_TOUCH_SIZE
  };
}

/**
 * Hook for managing keyboard shortcuts
 */
export function useKeyboardShortcuts(shortcuts: Record<string, () => void>) {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const key = event.key.toLowerCase();
      const modifiers = {
        ctrl: event.ctrlKey,
        alt: event.altKey,
        shift: event.shiftKey,
        meta: event.metaKey
      };

      // Create shortcut string (e.g., "ctrl+k", "alt+shift+f")
      const shortcutParts = [];
      if (modifiers.ctrl) shortcutParts.push('ctrl');
      if (modifiers.alt) shortcutParts.push('alt');
      if (modifiers.shift) shortcutParts.push('shift');
      if (modifiers.meta) shortcutParts.push('meta');
      shortcutParts.push(key);
      
      const shortcutString = shortcutParts.join('+');
      
      if (shortcuts[shortcutString]) {
        event.preventDefault();
        shortcuts[shortcutString]();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts]);
}

/**
 * Hook for managing form accessibility
 */
export function useFormAccessibility() {
  const generateId = useCallback((prefix: string = 'field') => {
    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  const getAriaDescribedBy = useCallback((fieldId: string, hasError: boolean, hasHelp: boolean) => {
    const describedBy = [];
    if (hasError) describedBy.push(`${fieldId}-error`);
    if (hasHelp) describedBy.push(`${fieldId}-help`);
    return describedBy.length > 0 ? describedBy.join(' ') : undefined;
  }, []);

  const getFieldProps = useCallback((
    fieldId: string,
    label: string,
    options: {
      required?: boolean;
      hasError?: boolean;
      hasHelp?: boolean;
      errorMessage?: string;
      helpText?: string;
    } = {}
  ) => {
    const { required = false, hasError = false, hasHelp = false, errorMessage, helpText } = options;

    return {
      field: {
        id: fieldId,
        'aria-label': label,
        'aria-required': required,
        'aria-invalid': hasError,
        'aria-describedby': getAriaDescribedBy(fieldId, hasError, hasHelp)
      },
      label: {
        htmlFor: fieldId,
        children: label + (required ? ' *' : '')
      },
      error: hasError && errorMessage ? {
        id: `${fieldId}-error`,
        role: 'alert',
        'aria-live': 'polite',
        children: errorMessage
      } : null,
      help: hasHelp && helpText ? {
        id: `${fieldId}-help`,
        children: helpText
      } : null
    };
  }, [getAriaDescribedBy]);

  return {
    generateId,
    getAriaDescribedBy,
    getFieldProps
  };
}

/**
 * Hook for managing color contrast and high contrast mode
 */
export function useColorContrast() {
  const [highContrastMode, setHighContrastMode] = useState(false);

  useEffect(() => {
    // Check for system preference
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    setHighContrastMode(mediaQuery.matches);

    const handleChange = (e: MediaQueryListEvent) => {
      setHighContrastMode(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  const toggleHighContrast = useCallback(() => {
    setHighContrastMode(prev => !prev);
  }, []);

  useEffect(() => {
    if (highContrastMode) {
      document.documentElement.classList.add('high-contrast');
    } else {
      document.documentElement.classList.remove('high-contrast');
    }
  }, [highContrastMode]);

  return {
    highContrastMode,
    toggleHighContrast
  };
}

/**
 * Hook for screen reader announcements (alias for useAriaAnnouncements)
 * Provides a simplified interface for screen reader announcements
 */
export function useScreenReader() {
  const { announce } = useAriaAnnouncements();
  
  const announceToScreenReader = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    announce(message, priority);
  }, [announce]);

  const announceError = useCallback((message: string) => {
    announce(message, 'assertive');
  }, [announce]);

  const announceSuccess = useCallback((message: string) => {
    announce(message, 'polite');
  }, [announce]);

  const announceNavigation = useCallback((message: string) => {
    announce(message, 'polite');
  }, [announce]);

  return {
    announce: announceToScreenReader,
    announceError,
    announceSuccess,
    announceNavigation
  };
}
