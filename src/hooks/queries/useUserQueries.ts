import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { queryKeys, getDefaultQueryOptions, invalidateQueries } from '@/lib/queryClient';
import { supabase } from '@/lib/supabase';

// Types
interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  avatar_url?: string;
  bio?: string;
  expertise?: string[];
  created_at: string;
  updated_at: string;
}

interface UserPreferences {
  id: string;
  user_id: string;
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: {
    email: boolean;
    push: boolean;
    marketing: boolean;
  };
  privacy: {
    profile_visibility: 'public' | 'private';
    show_activity: boolean;
  };
}

interface UserActivity {
  id: string;
  user_id: string;
  action: string;
  resource_type: string;
  resource_id: string;
  metadata?: Record<string, any>;
  created_at: string;
}

// User profile query
export const useUserProfile = (userId: string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.user.profile(userId),
    queryFn: async (): Promise<UserProfile> => {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: enabled && !!userId,
    ...getDefaultQueryOptions('USER'),
  });
};

// Current user profile query
export const useCurrentUserProfile = () => {
  return useQuery({
    queryKey: queryKeys.user.profile('current'),
    queryFn: async (): Promise<UserProfile | null> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return null;
      
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();
      
      if (error) throw error;
      return data;
    },
    ...getDefaultQueryOptions('USER'),
  });
};

// User preferences query
export const useUserPreferences = (userId: string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.user.preferences(userId),
    queryFn: async (): Promise<UserPreferences> => {
      const { data, error } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: enabled && !!userId,
    ...getDefaultQueryOptions('USER'),
  });
};

// User activity query
export const useUserActivity = (userId: string, limit = 20, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.user.activity(userId),
    queryFn: async (): Promise<UserActivity[]> => {
      const { data, error } = await supabase
        .from('user_activity')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);
      
      if (error) throw error;
      return data || [];
    },
    enabled: enabled && !!userId,
    ...getDefaultQueryOptions('DYNAMIC'),
  });
};

// Update user profile mutation
export const useUpdateUserProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (updates: Partial<UserProfile>): Promise<UserProfile> => {
      const { data, error } = await supabase
        .from('profiles')
        .update(updates)
        .eq('id', updates.id)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Update the cache with the new data
      queryClient.setQueryData(queryKeys.user.profile(data.id), data);
      queryClient.setQueryData(queryKeys.user.profile('current'), data);
      
      // Invalidate related queries
      invalidateQueries.user(data.id);
    },
  });
};

// Update user preferences mutation
export const useUpdateUserPreferences = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (updates: Partial<UserPreferences>): Promise<UserPreferences> => {
      const { data, error } = await supabase
        .from('user_preferences')
        .upsert(updates)
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Update the cache with the new data
      queryClient.setQueryData(queryKeys.user.preferences(data.user_id), data);
    },
  });
};

// Delete user account mutation
export const useDeleteUserAccount = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (userId: string): Promise<void> => {
      const { error } = await supabase.auth.admin.deleteUser(userId);
      if (error) throw error;
    },
    onSuccess: (_, userId) => {
      // Clear all user-related cache
      queryClient.removeQueries({ queryKey: queryKeys.user.all });
      // Redirect to login or home page would be handled by the component
    },
  });
};