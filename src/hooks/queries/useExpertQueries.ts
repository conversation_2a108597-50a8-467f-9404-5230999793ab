import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { queryKeys, getDefaultQueryOptions, invalidateQueries } from '@/lib/queryClient';
import { supabase } from '@/lib/supabase';

// Types
interface Expert {
  id: string;
  user_id: string;
  specializations: string[];
  experience_years: number;
  hourly_rate?: number;
  availability_status: 'available' | 'busy' | 'offline';
  rating: number;
  total_reviews: number;
  total_problems_solved: number;
  bio: string;
  certifications?: string[];
  languages: string[];
  timezone: string;
  created_at: string;
  updated_at: string;
  // Joined data
  user?: {
    id: string;
    full_name: string;
    avatar_url?: string;
    email: string;
  };
}

interface ExpertReview {
  id: string;
  expert_id: string;
  reviewer_id: string;
  problem_id: string;
  rating: number;
  comment: string;
  created_at: string;
  // Joined data
  reviewer?: {
    id: string;
    full_name: string;
    avatar_url?: string;
  };
  problem?: {
    id: string;
    title: string;
  };
}

interface ExpertAvailability {
  id: string;
  expert_id: string;
  day_of_week: number; // 0-6 (Sunday-Saturday)
  start_time: string;
  end_time: string;
  is_available: boolean;
  timezone: string;
}

interface ExpertFilters {
  specializations?: string[];
  min_rating?: number;
  max_hourly_rate?: number;
  availability_status?: string;
  languages?: string[];
  search?: string;
}

// Experts list query with infinite scroll
export const useExperts = (filters: ExpertFilters = {}, pageSize = 20) => {
  return useInfiniteQuery({
    queryKey: queryKeys.experts.list(filters),
    queryFn: async ({ pageParam = 0 }): Promise<{ experts: Expert[]; nextCursor?: number }> => {
      let query = supabase
        .from('experts')
        .select(`
          *,
          user:profiles!experts_user_id_fkey(id, full_name, avatar_url, email)
        `)
        .order('rating', { ascending: false })
        .order('total_reviews', { ascending: false })
        .range(pageParam * pageSize, (pageParam + 1) * pageSize - 1);

      // Apply filters
      if (filters.specializations && filters.specializations.length > 0) {
        query = query.overlaps('specializations', filters.specializations);
      }
      if (filters.min_rating) {
        query = query.gte('rating', filters.min_rating);
      }
      if (filters.max_hourly_rate) {
        query = query.lte('hourly_rate', filters.max_hourly_rate);
      }
      if (filters.availability_status) {
        query = query.eq('availability_status', filters.availability_status);
      }
      if (filters.languages && filters.languages.length > 0) {
        query = query.overlaps('languages', filters.languages);
      }
      if (filters.search) {
        query = query.or(`bio.ilike.%${filters.search}%,specializations.cs.{${filters.search}}`);
      }

      const { data, error } = await query;
      
      if (error) throw error;
      
      const experts = data || [];
      const nextCursor = experts.length === pageSize ? pageParam + 1 : undefined;
      
      return { experts, nextCursor };
    },
    getNextPageParam: (lastPage) => lastPage.nextCursor,
    ...getDefaultQueryOptions('USER'),
  });
};

// Single expert query
export const useExpert = (expertId: string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.experts.detail(expertId),
    queryFn: async (): Promise<Expert> => {
      const { data, error } = await supabase
        .from('experts')
        .select(`
          *,
          user:profiles!experts_user_id_fkey(id, full_name, avatar_url, email)
        `)
        .eq('id', expertId)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: enabled && !!expertId,
    ...getDefaultQueryOptions('USER'),
  });
};

// Expert by user ID query
export const useExpertByUserId = (userId: string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.experts.detail(`user-${userId}`),
    queryFn: async (): Promise<Expert | null> => {
      const { data, error } = await supabase
        .from('experts')
        .select(`
          *,
          user:profiles!experts_user_id_fkey(id, full_name, avatar_url, email)
        `)
        .eq('user_id', userId)
        .maybeSingle();
      
      if (error) throw error;
      return data;
    },
    enabled: enabled && !!userId,
    ...getDefaultQueryOptions('USER'),
  });
};

// Expert reviews query
export const useExpertReviews = (expertId: string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.experts.reviews(expertId),
    queryFn: async (): Promise<ExpertReview[]> => {
      const { data, error } = await supabase
        .from('expert_reviews')
        .select(`
          *,
          reviewer:profiles!expert_reviews_reviewer_id_fkey(id, full_name, avatar_url),
          problem:problems!expert_reviews_problem_id_fkey(id, title)
        `)
        .eq('expert_id', expertId)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data || [];
    },
    enabled: enabled && !!expertId,
    ...getDefaultQueryOptions('USER'),
  });
};

// Expert availability query
export const useExpertAvailability = (expertId: string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.experts.availability(expertId),
    queryFn: async (): Promise<ExpertAvailability[]> => {
      const { data, error } = await supabase
        .from('expert_availability')
        .select('*')
        .eq('expert_id', expertId)
        .order('day_of_week', { ascending: true })
        .order('start_time', { ascending: true });
      
      if (error) throw error;
      return data || [];
    },
    enabled: enabled && !!expertId,
    ...getDefaultQueryOptions('USER'),
  });
};

// Top experts query (for homepage/dashboard)
export const useTopExperts = (limit = 10) => {
  return useQuery({
    queryKey: [...queryKeys.experts.all, 'top', limit],
    queryFn: async (): Promise<Expert[]> => {
      const { data, error } = await supabase
        .from('experts')
        .select(`
          *,
          user:profiles!experts_user_id_fkey(id, full_name, avatar_url)
        `)
        .eq('availability_status', 'available')
        .gte('rating', 4.0)
        .order('rating', { ascending: false })
        .order('total_reviews', { ascending: false })
        .limit(limit);
      
      if (error) throw error;
      return data || [];
    },
    ...getDefaultQueryOptions('STATIC'),
  });
};

// Create expert profile mutation
export const useCreateExpertProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (expertData: Omit<Expert, 'id' | 'created_at' | 'updated_at' | 'rating' | 'total_reviews' | 'total_problems_solved'>): Promise<Expert> => {
      const { data, error } = await supabase
        .from('experts')
        .insert({
          ...expertData,
          rating: 0,
          total_reviews: 0,
          total_problems_solved: 0,
        })
        .select(`
          *,
          user:profiles!experts_user_id_fkey(id, full_name, avatar_url, email)
        `)
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Add to cache
      queryClient.setQueryData(queryKeys.experts.detail(data.id), data);
      queryClient.setQueryData(queryKeys.experts.detail(`user-${data.user_id}`), data);
      
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: queryKeys.experts.lists() });
    },
  });
};

// Update expert profile mutation
export const useUpdateExpertProfile = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, ...updates }: Partial<Expert> & { id: string }): Promise<Expert> => {
      const { data, error } = await supabase
        .from('experts')
        .update(updates)
        .eq('id', id)
        .select(`
          *,
          user:profiles!experts_user_id_fkey(id, full_name, avatar_url, email)
        `)
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Update cache
      queryClient.setQueryData(queryKeys.experts.detail(data.id), data);
      queryClient.setQueryData(queryKeys.experts.detail(`user-${data.user_id}`), data);
      
      // Invalidate related queries
      invalidateQueries.experts(data.id);
    },
  });
};

// Update expert availability mutation
export const useUpdateExpertAvailability = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ expertId, availability }: { expertId: string; availability: Omit<ExpertAvailability, 'id' | 'expert_id'>[] }): Promise<ExpertAvailability[]> => {
      // Delete existing availability
      await supabase
        .from('expert_availability')
        .delete()
        .eq('expert_id', expertId);
      
      // Insert new availability
      const { data, error } = await supabase
        .from('expert_availability')
        .insert(availability.map(slot => ({ ...slot, expert_id: expertId })))
        .select();
      
      if (error) throw error;
      return data || [];
    },
    onSuccess: (data, { expertId }) => {
      // Update cache
      queryClient.setQueryData(queryKeys.experts.availability(expertId), data);
    },
  });
};

// Create expert review mutation
export const useCreateExpertReview = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (reviewData: Omit<ExpertReview, 'id' | 'created_at'>): Promise<ExpertReview> => {
      const { data, error } = await supabase
        .from('expert_reviews')
        .insert(reviewData)
        .select(`
          *,
          reviewer:profiles!expert_reviews_reviewer_id_fkey(id, full_name, avatar_url),
          problem:problems!expert_reviews_problem_id_fkey(id, title)
        `)
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Invalidate expert reviews and details
      queryClient.invalidateQueries({ queryKey: queryKeys.experts.reviews(data.expert_id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.experts.detail(data.expert_id) });
      
      // Update expert rating (this would typically be done by a database trigger)
      // For now, we'll just invalidate the expert data
      invalidateQueries.experts(data.expert_id);
    },
  });
};

// Update expert availability status mutation
export const useUpdateExpertAvailabilityStatus = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ expertId, status }: { expertId: string; status: Expert['availability_status'] }): Promise<Expert> => {
      const { data, error } = await supabase
        .from('experts')
        .update({ availability_status: status })
        .eq('id', expertId)
        .select(`
          *,
          user:profiles!experts_user_id_fkey(id, full_name, avatar_url, email)
        `)
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Update cache
      queryClient.setQueryData(queryKeys.experts.detail(data.id), data);
      
      // Invalidate lists to reflect availability changes
      queryClient.invalidateQueries({ queryKey: queryKeys.experts.lists() });
    },
  });
};