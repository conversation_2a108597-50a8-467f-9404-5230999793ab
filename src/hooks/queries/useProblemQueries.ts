import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { queryKeys, getDefaultQueryOptions, invalidateQueries } from '@/lib/queryClient';
import { supabase } from '@/lib/supabase';

// Types
interface Problem {
  id: string;
  title: string;
  description: string;
  category: string;
  tags: string[];
  status: 'open' | 'in_progress' | 'solved' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  user_id: string;
  expert_id?: string;
  attachments?: string[];
  created_at: string;
  updated_at: string;
  // Joined data
  user?: {
    id: string;
    full_name: string;
    avatar_url?: string;
  };
  expert?: {
    id: string;
    full_name: string;
    avatar_url?: string;
  };
  _count?: {
    solutions: number;
    comments: number;
    votes: number;
  };
}

interface Solution {
  id: string;
  problem_id: string;
  user_id: string;
  content: string;
  is_accepted: boolean;
  votes: number;
  created_at: string;
  updated_at: string;
  user?: {
    id: string;
    full_name: string;
    avatar_url?: string;
  };
}

interface Comment {
  id: string;
  problem_id: string;
  solution_id?: string;
  user_id: string;
  content: string;
  created_at: string;
  updated_at: string;
  user?: {
    id: string;
    full_name: string;
    avatar_url?: string;
  };
}

interface ProblemFilters {
  category?: string;
  status?: string;
  priority?: string;
  tags?: string[];
  search?: string;
  user_id?: string;
  expert_id?: string;
}

// Problems list query with infinite scroll
export const useProblems = (filters: ProblemFilters = {}, pageSize = 20) => {
  return useInfiniteQuery({
    queryKey: queryKeys.problems.list(filters),
    queryFn: async ({ pageParam = 0 }): Promise<{ problems: Problem[]; nextCursor?: number }> => {
      let query = supabase
        .from('problems')
        .select(`
          *,
          user:profiles!problems_user_id_fkey(id, full_name, avatar_url),
          expert:profiles!problems_expert_id_fkey(id, full_name, avatar_url),
          _count:solutions(count),
          _count:comments(count)
        `)
        .order('created_at', { ascending: false })
        .range(pageParam * pageSize, (pageParam + 1) * pageSize - 1);

      // Apply filters
      if (filters.category) {
        query = query.eq('category', filters.category);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.priority) {
        query = query.eq('priority', filters.priority);
      }
      if (filters.user_id) {
        query = query.eq('user_id', filters.user_id);
      }
      if (filters.expert_id) {
        query = query.eq('expert_id', filters.expert_id);
      }
      if (filters.tags && filters.tags.length > 0) {
        query = query.contains('tags', filters.tags);
      }
      if (filters.search) {
        query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }

      const { data, error } = await query;
      
      if (error) throw error;
      
      const problems = data || [];
      const nextCursor = problems.length === pageSize ? pageParam + 1 : undefined;
      
      return { problems, nextCursor };
    },
    getNextPageParam: (lastPage) => lastPage.nextCursor,
    ...getDefaultQueryOptions('DYNAMIC'),
  });
};

// Single problem query
export const useProblem = (problemId: string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.problems.detail(problemId),
    queryFn: async (): Promise<Problem> => {
      const { data, error } = await supabase
        .from('problems')
        .select(`
          *,
          user:profiles!problems_user_id_fkey(id, full_name, avatar_url),
          expert:profiles!problems_expert_id_fkey(id, full_name, avatar_url)
        `)
        .eq('id', problemId)
        .single();
      
      if (error) throw error;
      return data;
    },
    enabled: enabled && !!problemId,
    ...getDefaultQueryOptions('DYNAMIC'),
  });
};

// Problem solutions query
export const useProblemSolutions = (problemId: string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.problems.solutions(problemId),
    queryFn: async (): Promise<Solution[]> => {
      const { data, error } = await supabase
        .from('solutions')
        .select(`
          *,
          user:profiles!solutions_user_id_fkey(id, full_name, avatar_url)
        `)
        .eq('problem_id', problemId)
        .order('is_accepted', { ascending: false })
        .order('votes', { ascending: false })
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data || [];
    },
    enabled: enabled && !!problemId,
    ...getDefaultQueryOptions('DYNAMIC'),
  });
};

// Problem comments query
export const useProblemComments = (problemId: string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.problems.comments(problemId),
    queryFn: async (): Promise<Comment[]> => {
      const { data, error } = await supabase
        .from('comments')
        .select(`
          *,
          user:profiles!comments_user_id_fkey(id, full_name, avatar_url)
        `)
        .eq('problem_id', problemId)
        .order('created_at', { ascending: true });
      
      if (error) throw error;
      return data || [];
    },
    enabled: enabled && !!problemId,
    ...getDefaultQueryOptions('DYNAMIC'),
  });
};

// Create problem mutation
export const useCreateProblem = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (problemData: Omit<Problem, 'id' | 'created_at' | 'updated_at'>): Promise<Problem> => {
      const { data, error } = await supabase
        .from('problems')
        .insert(problemData)
        .select(`
          *,
          user:profiles!problems_user_id_fkey(id, full_name, avatar_url)
        `)
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Invalidate problems list queries
      queryClient.invalidateQueries({ queryKey: queryKeys.problems.lists() });
      
      // Add the new problem to the cache
      queryClient.setQueryData(queryKeys.problems.detail(data.id), data);
    },
  });
};

// Update problem mutation
export const useUpdateProblem = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, ...updates }: Partial<Problem> & { id: string }): Promise<Problem> => {
      const { data, error } = await supabase
        .from('problems')
        .update(updates)
        .eq('id', id)
        .select(`
          *,
          user:profiles!problems_user_id_fkey(id, full_name, avatar_url),
          expert:profiles!problems_expert_id_fkey(id, full_name, avatar_url)
        `)
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Update the problem in cache
      queryClient.setQueryData(queryKeys.problems.detail(data.id), data);
      
      // Invalidate related queries
      invalidateQueries.problems(data.id);
    },
  });
};

// Delete problem mutation
export const useDeleteProblem = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (problemId: string): Promise<void> => {
      const { error } = await supabase
        .from('problems')
        .delete()
        .eq('id', problemId);
      
      if (error) throw error;
    },
    onSuccess: (_, problemId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: queryKeys.problems.detail(problemId) });
      queryClient.removeQueries({ queryKey: queryKeys.problems.solutions(problemId) });
      queryClient.removeQueries({ queryKey: queryKeys.problems.comments(problemId) });
      
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: queryKeys.problems.lists() });
    },
  });
};

// Create solution mutation
export const useCreateSolution = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (solutionData: Omit<Solution, 'id' | 'created_at' | 'updated_at' | 'votes'>): Promise<Solution> => {
      const { data, error } = await supabase
        .from('solutions')
        .insert({ ...solutionData, votes: 0 })
        .select(`
          *,
          user:profiles!solutions_user_id_fkey(id, full_name, avatar_url)
        `)
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Invalidate solutions for this problem
      queryClient.invalidateQueries({ queryKey: queryKeys.problems.solutions(data.problem_id) });
      
      // Update problem cache to reflect new solution count
      invalidateQueries.problems(data.problem_id);
    },
  });
};

// Accept solution mutation
export const useAcceptSolution = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ solutionId, problemId }: { solutionId: string; problemId: string }): Promise<void> => {
      // First, unaccept all other solutions for this problem
      await supabase
        .from('solutions')
        .update({ is_accepted: false })
        .eq('problem_id', problemId);
      
      // Then accept the selected solution
      const { error } = await supabase
        .from('solutions')
        .update({ is_accepted: true })
        .eq('id', solutionId);
      
      if (error) throw error;
      
      // Update problem status to solved
      await supabase
        .from('problems')
        .update({ status: 'solved' })
        .eq('id', problemId);
    },
    onSuccess: (_, { problemId }) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.problems.solutions(problemId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.problems.detail(problemId) });
    },
  });
};