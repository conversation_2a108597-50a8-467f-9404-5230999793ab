import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { queryKeys, getDefaultQueryOptions } from '@/lib/queryClient';
import { supabase } from '@/lib/supabase';

// Types
interface SearchResult {
  id: string;
  type: 'problem' | 'expert' | 'solution';
  title: string;
  description: string;
  url: string;
  relevance_score: number;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

interface SearchSuggestion {
  id: string;
  query: string;
  type: 'recent' | 'popular' | 'autocomplete';
  count?: number;
  created_at: string;
}

interface SearchAnalytics {
  total_searches: number;
  popular_queries: Array<{
    query: string;
    count: number;
  }>;
  search_trends: Array<{
    date: string;
    count: number;
  }>;
  avg_results_per_search: number;
  click_through_rate: number;
}

interface SearchFilters {
  type?: 'problem' | 'expert' | 'solution';
  category?: string;
  tags?: string[];
  date_range?: {
    start: string;
    end: string;
  };
  min_relevance?: number;
}

// Global search query
export const useSearch = (
  query: string,
  filters: SearchFilters = {},
  enabled = true
) => {
  return useQuery({
    queryKey: queryKeys.search.results(query, filters),
    queryFn: async (): Promise<SearchResult[]> => {
      if (!query.trim()) return [];

      // Build the search query
      let searchQuery = supabase.rpc('search_content', {
        search_query: query,
        result_limit: 50
      });

      // Apply filters
      if (filters.type) {
        searchQuery = searchQuery.eq('type', filters.type);
      }
      if (filters.min_relevance) {
        searchQuery = searchQuery.gte('relevance_score', filters.min_relevance);
      }

      const { data, error } = await searchQuery;
      
      if (error) throw error;
      
      // Log search for analytics
      await supabase.from('search_logs').insert({
        query: query.trim(),
        filters,
        result_count: data?.length || 0,
        user_id: (await supabase.auth.getUser()).data.user?.id,
      });

      return data || [];
    },
    enabled: enabled && !!query.trim(),
    ...getDefaultQueryOptions('SEARCH'),
  });
};

// Search suggestions query
export const useSearchSuggestions = (query: string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.search.suggestions(query),
    queryFn: async (): Promise<SearchSuggestion[]> => {
      if (!query.trim()) {
        // Return recent searches if no query
        const { data, error } = await supabase
          .from('search_suggestions')
          .select('*')
          .eq('type', 'recent')
          .order('created_at', { ascending: false })
          .limit(10);
        
        if (error) throw error;
        return data || [];
      }

      // Get autocomplete suggestions
      const { data, error } = await supabase
        .from('search_suggestions')
        .select('*')
        .ilike('query', `${query}%`)
        .eq('type', 'autocomplete')
        .order('count', { ascending: false })
        .limit(10);
      
      if (error) throw error;
      return data || [];
    },
    enabled: enabled,
    ...getDefaultQueryOptions('SEARCH'),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Popular searches query
export const usePopularSearches = (limit = 10) => {
  return useQuery({
    queryKey: [...queryKeys.search.all, 'popular', limit],
    queryFn: async (): Promise<SearchSuggestion[]> => {
      const { data, error } = await supabase
        .from('search_suggestions')
        .select('*')
        .eq('type', 'popular')
        .order('count', { ascending: false })
        .limit(limit);
      
      if (error) throw error;
      return data || [];
    },
    ...getDefaultQueryOptions('STATIC'),
  });
};

// Search analytics query (admin only)
export const useSearchAnalytics = (
  dateRange: { start: string; end: string },
  enabled = true
) => {
  return useQuery({
    queryKey: [...queryKeys.search.analytics(), dateRange],
    queryFn: async (): Promise<SearchAnalytics> => {
      const { data, error } = await supabase.rpc('get_search_analytics', {
        start_date: dateRange.start,
        end_date: dateRange.end
      });
      
      if (error) throw error;
      return data;
    },
    enabled: enabled,
    ...getDefaultQueryOptions('DYNAMIC'),
  });
};

// Advanced search query with full-text search
export const useAdvancedSearch = (
  searchParams: {
    query: string;
    title_only?: boolean;
    exact_phrase?: boolean;
    exclude_words?: string[];
    include_tags?: string[];
    exclude_tags?: string[];
  },
  filters: SearchFilters = {},
  enabled = true
) => {
  return useQuery({
    queryKey: [...queryKeys.search.all, 'advanced', searchParams, filters],
    queryFn: async (): Promise<SearchResult[]> => {
      if (!searchParams.query.trim()) return [];

      const { data, error } = await supabase.rpc('advanced_search', {
        search_params: searchParams,
        filters,
        result_limit: 100
      });
      
      if (error) throw error;
      
      // Log advanced search
      await supabase.from('search_logs').insert({
        query: searchParams.query.trim(),
        search_type: 'advanced',
        filters: { ...filters, ...searchParams },
        result_count: data?.length || 0,
        user_id: (await supabase.auth.getUser()).data.user?.id,
      });

      return data || [];
    },
    enabled: enabled && !!searchParams.query.trim(),
    ...getDefaultQueryOptions('SEARCH'),
  });
};

// Search within category query
export const useSearchInCategory = (
  query: string,
  category: string,
  enabled = true
) => {
  return useQuery({
    queryKey: [...queryKeys.search.all, 'category', category, query],
    queryFn: async (): Promise<SearchResult[]> => {
      if (!query.trim()) return [];

      const { data, error } = await supabase.rpc('search_in_category', {
        search_query: query,
        category_name: category,
        result_limit: 30
      });
      
      if (error) throw error;
      return data || [];
    },
    enabled: enabled && !!query.trim() && !!category,
    ...getDefaultQueryOptions('SEARCH'),
  });
};

// Save search query mutation
export const useSaveSearchQuery = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (query: string): Promise<void> => {
      if (!query.trim()) return;

      const { error } = await supabase
        .from('search_suggestions')
        .upsert({
          query: query.trim(),
          type: 'recent',
          count: 1,
        }, {
          onConflict: 'query,type',
          ignoreDuplicates: false
        });
      
      if (error) throw error;
    },
    onSuccess: () => {
      // Invalidate search suggestions to include the new query
      queryClient.invalidateQueries({ queryKey: queryKeys.search.all });
    },
  });
};

// Track search click mutation
export const useTrackSearchClick = () => {
  return useMutation({
    mutationFn: async ({
      query,
      resultId,
      resultType,
      position
    }: {
      query: string;
      resultId: string;
      resultType: string;
      position: number;
    }): Promise<void> => {
      const { error } = await supabase
        .from('search_clicks')
        .insert({
          query: query.trim(),
          result_id: resultId,
          result_type: resultType,
          position,
          user_id: (await supabase.auth.getUser()).data.user?.id,
        });
      
      if (error) throw error;
    },
  });
};

// Clear search history mutation
export const useClearSearchHistory = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (): Promise<void> => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const { error } = await supabase
        .from('search_suggestions')
        .delete()
        .eq('type', 'recent')
        .eq('user_id', user.id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      // Clear search suggestions cache
      queryClient.invalidateQueries({ queryKey: queryKeys.search.all });
    },
  });
};

// Search export mutation (for analytics)
export const useExportSearchData = () => {
  return useMutation({
    mutationFn: async (params: {
      start_date: string;
      end_date: string;
      format: 'csv' | 'json';
    }): Promise<Blob> => {
      const { data, error } = await supabase.rpc('export_search_data', params);
      
      if (error) throw error;
      
      const mimeType = params.format === 'csv' 
        ? 'text/csv' 
        : 'application/json';
      
      return new Blob([data], { type: mimeType });
    },
  });
};