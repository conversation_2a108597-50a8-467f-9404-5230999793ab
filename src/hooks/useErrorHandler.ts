import { useCallback, useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { apiErrorHandler, ApiError, ErrorContext } from '@/lib/apiErrorHandler';
import { errorLogger } from '@/lib/errorLogger';
import { useAuth } from '@/contexts/AuthContext';

interface UseErrorHandlerOptions {
  componentName?: string;
  enableReporting?: boolean;
  showToast?: boolean;
  onError?: (error: Error) => void;
}

interface ErrorState {
  error: Error | null;
  isReporting: boolean;
  hasError: boolean;
}

export function useErrorHandler(options: UseErrorHandlerOptions = {}) {
  const {
    componentName = 'unknown',
    enableReporting = true,
    showToast = true,
    onError,
  } = options;

  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [errorState, setErrorState] = useState<ErrorState>({
    error: null,
    isReporting: false,
    hasError: false,
  });

  // Mutation for reporting errors
  const reportErrorMutation = useMutation({
    mutationFn: async (errorData: {
      error: Error;
      context?: Record<string, any>;
    }) => {
      const reportData = {
        error: {
          message: errorData.error.message,
          stack: errorData.error.stack,
          name: errorData.error.name,
        },
        context: {
          componentName,
          timestamp: new Date().toISOString(),
          url: window.location.href,
          userAgent: navigator.userAgent,
          userId: user?.id,
          ...errorData.context,
        },
      };

      await errorLogger.reportError(reportData);
    },
    onSuccess: () => {
      toast.success('Error report submitted successfully');
    },
    onError: (error) => {
      console.error('Failed to report error:', error);
      toast.error('Failed to submit error report');
    },
  });

  // Handle API errors
  const handleApiError = useCallback(
    async (
      error: ApiError,
      context: Partial<ErrorContext> = {},
      options: {
        enableRetry?: boolean;
        customMessage?: string;
        onRetry?: () => Promise<any>;
      } = {}
    ) => {
      const fullContext: ErrorContext = {
        endpoint: context.endpoint || 'unknown',
        method: context.method || 'unknown',
        timestamp: new Date().toISOString(),
        userId: user?.id,
        ...context,
      };

      await apiErrorHandler.handleError(error, fullContext, {
        showToast,
        enableRetry: options.enableRetry,
        customMessage: options.customMessage,
        onRetry: options.onRetry,
      });

      setErrorState({
        error,
        isReporting: false,
        hasError: true,
      });

      onError?.(error);
    },
    [user?.id, showToast, onError]
  );

  // Handle component errors
  const handleComponentError = useCallback(
    async (error: Error, context: Record<string, any> = {}) => {
      console.error(`Component error in ${componentName}:`, error);

      // Log the error
      await errorLogger.logError({
        message: error.message,
        stack: error.stack,
        componentName,
        timestamp: new Date().toISOString(),
        source: 'componentError',
        type: 'componentError',
        context: {
          ...context,
          userId: user?.id,
          url: window.location.href,
        },
      });

      setErrorState({
        error,
        isReporting: false,
        hasError: true,
      });

      if (showToast) {
        toast.error('Component error occurred', {
          description: 'Please try refreshing the page or contact support if the issue persists.',
        });
      }

      onError?.(error);
    },
    [componentName, user?.id, showToast, onError]
  );

  // Handle async operation errors
  const handleAsyncError = useCallback(
    async <T>(
      operation: () => Promise<T>,
      context: {
        operationName?: string;
        onSuccess?: (result: T) => void;
        onError?: (error: Error) => void;
        showSuccessToast?: boolean;
        successMessage?: string;
      } = {}
    ): Promise<T | null> => {
      try {
        const result = await operation();
        
        if (context.showSuccessToast && context.successMessage) {
          toast.success(context.successMessage);
        }
        
        context.onSuccess?.(result);
        return result;
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        
        await errorLogger.logError({
          message: err.message,
          stack: err.stack,
          componentName,
          timestamp: new Date().toISOString(),
          source: 'asyncOperation',
          type: 'asyncError',
          context: {
            operationName: context.operationName,
            userId: user?.id,
            url: window.location.href,
          },
        });

        setErrorState({
          error: err,
          isReporting: false,
          hasError: true,
        });

        if (showToast) {
          toast.error('Operation failed', {
            description: err.message || 'Please try again or contact support if the issue persists.',
          });
        }

        context.onError?.(err);
        onError?.(err);
        
        return null;
      }
    },
    [componentName, user?.id, showToast, onError]
  );

  // Report error manually
  const reportError = useCallback(
    async (error: Error, context: Record<string, any> = {}) => {
      if (!enableReporting) return;

      setErrorState(prev => ({ ...prev, isReporting: true }));
      
      try {
        await reportErrorMutation.mutateAsync({ error, context });
      } finally {
        setErrorState(prev => ({ ...prev, isReporting: false }));
      }
    },
    [enableReporting, reportErrorMutation]
  );

  // Clear error state
  const clearError = useCallback(() => {
    setErrorState({
      error: null,
      isReporting: false,
      hasError: false,
    });
  }, []);

  // Retry with error handling
  const retryWithErrorHandling = useCallback(
    async <T>(
      operation: () => Promise<T>,
      maxRetries: number = 3,
      delay: number = 1000
    ): Promise<T | null> => {
      let lastError: Error | null = null;
      
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          return await operation();
        } catch (error) {
          lastError = error instanceof Error ? error : new Error(String(error));
          
          if (attempt === maxRetries) {
            await handleAsyncError(
              () => Promise.reject(lastError),
              { operationName: `retry-operation-attempt-${attempt}` }
            );
            break;
          }
          
          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delay * attempt));
        }
      }
      
      return null;
    },
    [handleAsyncError]
  );

  // Invalidate queries on error (useful for stale data)
  const invalidateQueriesOnError = useCallback(
    (queryKeys: string[][]) => {
      queryKeys.forEach(queryKey => {
        queryClient.invalidateQueries({ queryKey });
      });
    },
    [queryClient]
  );

  return {
    // Error state
    error: errorState.error,
    hasError: errorState.hasError,
    isReporting: errorState.isReporting,
    
    // Error handlers
    handleApiError,
    handleComponentError,
    handleAsyncError,
    
    // Utilities
    reportError,
    clearError,
    retryWithErrorHandling,
    invalidateQueriesOnError,
    
    // Mutation state
    isReportingError: reportErrorMutation.isPending,
  };
}

// Hook for global error handling
export function useGlobalErrorHandler() {
  const errorHandler = useErrorHandler({
    componentName: 'global',
    enableReporting: true,
    showToast: true,
  });

  // Set up global error listeners
  React.useEffect(() => {
    const handleUnhandledError = (event: ErrorEvent) => {
      errorHandler.handleComponentError(event.error || new Error(event.message), {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      });
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));
      errorHandler.handleAsyncError(() => Promise.reject(error), {
        operationName: 'unhandledPromiseRejection',
      });
    };

    window.addEventListener('error', handleUnhandledError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleUnhandledError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [errorHandler]);

  return errorHandler;
}
