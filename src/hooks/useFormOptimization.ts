import { useCallback, useMemo, useRef, useState, useEffect } from 'react';
import { useOptimizedCallback, useMemoizedValue, useDebouncedValue } from './useOptimization';

/**
 * Hook for optimizing form performance with debounced validation
 */
export function useOptimizedForm<T extends Record<string, any>>(
  initialValues: T,
  validationSchema?: (values: T) => Record<string, string>,
  options: {
    debounceMs?: number;
    validateOnChange?: boolean;
    validateOnBlur?: boolean;
  } = {}
) {
  const { debounceMs = 300, validateOnChange = true, validateOnBlur = true } = options;
  
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Debounced values for validation
  const debouncedValues = useDebouncedValue(values, debounceMs, 'form-validation');
  
  // Memoized validation function
  const validate = useMemoizedValue(
    () => validationSchema || (() => ({})),
    [validationSchema],
    'form-validator'
  );
  
  // Validate form with performance monitoring
  const validateForm = useOptimizedCallback(
    (formValues: T) => {
      const validationErrors = validate(formValues);
      setErrors(validationErrors);
      return Object.keys(validationErrors).length === 0;
    },
    [validate],
    'validate-form'
  );
  
  // Optimized field change handler
  const handleChange = useOptimizedCallback(
    (field: keyof T, value: any) => {
      setValues(prev => ({ ...prev, [field]: value }));
      
      if (validateOnChange) {
        setTouched(prev => ({ ...prev, [field]: true }));
      }
    },
    [validateOnChange],
    'form-change'
  );
  
  // Optimized field blur handler
  const handleBlur = useOptimizedCallback(
    (field: keyof T) => {
      setTouched(prev => ({ ...prev, [field]: true }));
    },
    [],
    'form-blur'
  );
  
  // Optimized submit handler
  const handleSubmit = useOptimizedCallback(
    async (onSubmit: (values: T) => Promise<void> | void) => {
      setIsSubmitting(true);
      
      try {
        const isValid = validateForm(values);
        if (isValid) {
          await onSubmit(values);
        }
      } finally {
        setIsSubmitting(false);
      }
    },
    [values, validateForm],
    'form-submit'
  );
  
  // Reset form
  const reset = useOptimizedCallback(
    (newValues?: Partial<T>) => {
      setValues(newValues ? { ...initialValues, ...newValues } : initialValues);
      setErrors({});
      setTouched({});
      setIsSubmitting(false);
    },
    [initialValues],
    'form-reset'
  );
  
  // Validate on debounced value changes
  useEffect(() => {
    if (validateOnChange && Object.keys(touched).length > 0) {
      validateForm(debouncedValues);
    }
  }, [debouncedValues, validateOnChange, touched, validateForm]);
  
  // Memoized form state
  const formState = useMemoizedValue(
    () => ({
      values,
      errors,
      touched,
      isSubmitting,
      isValid: Object.keys(errors).length === 0,
      isDirty: JSON.stringify(values) !== JSON.stringify(initialValues)
    }),
    [values, errors, touched, isSubmitting, initialValues],
    'form-state'
  );
  
  return {
    ...formState,
    handleChange,
    handleBlur,
    handleSubmit,
    reset,
    validateForm
  };
}

/**
 * Hook for optimizing search input performance
 */
export function useOptimizedSearch(
  onSearch: (query: string) => void,
  options: {
    debounceMs?: number;
    minLength?: number;
    maxLength?: number;
  } = {}
) {
  const { debounceMs = 300, minLength = 0, maxLength = 1000 } = options;
  
  const [query, setQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  
  // Debounced search query
  const debouncedQuery = useDebouncedValue(query, debounceMs, 'search-query');
  
  // Optimized search handler
  const performSearch = useOptimizedCallback(
    async (searchQuery: string) => {
      if (searchQuery.length >= minLength && searchQuery.length <= maxLength) {
        setIsSearching(true);
        try {
          await onSearch(searchQuery);
        } finally {
          setIsSearching(false);
        }
      }
    },
    [onSearch, minLength, maxLength],
    'perform-search'
  );
  
  // Optimized input change handler
  const handleInputChange = useOptimizedCallback(
    (value: string) => {
      setQuery(value);
    },
    [],
    'search-input-change'
  );
  
  // Clear search
  const clearSearch = useOptimizedCallback(
    () => {
      setQuery('');
      setIsSearching(false);
    },
    [],
    'clear-search'
  );
  
  // Perform search when debounced query changes
  useEffect(() => {
    if (debouncedQuery !== query) {
      // Query is still changing, don't search yet
      return;
    }
    
    if (debouncedQuery.trim()) {
      performSearch(debouncedQuery);
    } else {
      setIsSearching(false);
    }
  }, [debouncedQuery, query, performSearch]);
  
  return {
    query,
    debouncedQuery,
    isSearching,
    handleInputChange,
    clearSearch,
    performSearch
  };
}

/**
 * Hook for optimizing filter performance
 */
export function useOptimizedFilters<T extends Record<string, any>>(
  initialFilters: T,
  onFiltersChange?: (filters: T) => void,
  debounceMs: number = 300
) {
  const [filters, setFilters] = useState<T>(initialFilters);
  
  // Debounced filters for external updates
  const debouncedFilters = useDebouncedValue(filters, debounceMs, 'filter-updates');
  
  // Optimized filter change handler
  const updateFilter = useOptimizedCallback(
    (key: keyof T, value: any) => {
      setFilters(prev => ({ ...prev, [key]: value }));
    },
    [],
    'update-filter'
  );
  
  // Optimized multiple filter update
  const updateFilters = useOptimizedCallback(
    (newFilters: Partial<T>) => {
      setFilters(prev => ({ ...prev, ...newFilters }));
    },
    [],
    'update-filters'
  );
  
  // Reset filters
  const resetFilters = useOptimizedCallback(
    () => {
      setFilters(initialFilters);
    },
    [initialFilters],
    'reset-filters'
  );
  
  // Check if filters have active values
  const hasActiveFilters = useMemoizedValue(
    () => {
      return Object.values(filters).some(value => {
        if (typeof value === 'string') return value.trim() !== '';
        if (typeof value === 'number') return value !== 0;
        if (Array.isArray(value)) return value.length > 0;
        return value != null;
      });
    },
    Object.values(filters),
    'has-active-filters'
  );
  
  // Notify external handler of filter changes
  useEffect(() => {
    if (onFiltersChange) {
      onFiltersChange(debouncedFilters);
    }
  }, [debouncedFilters, onFiltersChange]);
  
  return {
    filters,
    debouncedFilters,
    hasActiveFilters,
    updateFilter,
    updateFilters,
    resetFilters
  };
}

/**
 * Hook for optimizing list performance with virtualization support
 */
export function useOptimizedList<T>(
  items: T[],
  options: {
    pageSize?: number;
    enableVirtualization?: boolean;
    itemHeight?: number;
  } = {}
) {
  const { pageSize = 50, enableVirtualization = false, itemHeight = 100 } = options;
  
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: pageSize });
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Memoized visible items
  const visibleItems = useMemoizedValue(
    () => {
      if (enableVirtualization) {
        return items.slice(visibleRange.start, visibleRange.end);
      }
      return items.slice(0, visibleRange.end);
    },
    [items, visibleRange.start, visibleRange.end, enableVirtualization],
    'visible-items'
  );
  
  // Optimized scroll handler for virtualization
  const handleScroll = useOptimizedCallback(
    (scrollTop: number, containerHeight: number) => {
      if (!enableVirtualization) return;
      
      const startIndex = Math.floor(scrollTop / itemHeight);
      const endIndex = Math.min(
        startIndex + Math.ceil(containerHeight / itemHeight) + 5, // Buffer
        items.length
      );
      
      setVisibleRange({ start: startIndex, end: endIndex });
    },
    [enableVirtualization, itemHeight, items.length],
    'list-scroll'
  );
  
  // Load more items
  const loadMore = useOptimizedCallback(
    () => {
      setVisibleRange(prev => ({
        ...prev,
        end: Math.min(prev.end + pageSize, items.length)
      }));
    },
    [pageSize, items.length],
    'load-more'
  );
  
  return {
    visibleItems,
    visibleRange,
    containerRef,
    handleScroll,
    loadMore,
    hasMore: visibleRange.end < items.length
  };
}
