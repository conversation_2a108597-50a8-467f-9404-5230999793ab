import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { translationMonitor } from '@/lib/translationMonitor';

type Language = 'ar' | 'en';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
  isRTL: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Translation dictionaries
const translations = {
  ar: {
    // Header Navigation
    'nav.home': 'الرئيسية',
    'nav.problems': 'المشاكل',
    'nav.experts': 'الخبراء',
    'nav.submit_problem': 'إرسال مشكلة',
    'nav.search': 'البحث',
    'nav.admin_panel': 'لوحة الإدارة',
    'nav.profile': 'الملف الشخصي',
    'nav.settings': 'الإعدادات',
    'nav.user_management': 'إدارة المستخدمين',
    'nav.analytics': 'التحليلات',
    'nav.logout': 'تسجيل الخروج',
    'nav.login': 'تسجيل الدخول',
    'nav.register': 'إنشاء حساب',
    'nav.menu': 'القائمة',

    // Brand
    'brand.title': 'مركز سوريا الذكي',
    'brand.subtitle': 'منصة الحلول التقنية',

    // User Roles
    'role.admin': 'مدير',
    'role.expert': 'خبير',
    'role.ministry_user': 'موظف وزارة',

    // Problems Page
    'problems.title': 'المشاكل التقنية',
    'problems.subtitle': 'تصفح وابحث في المشاكل التقنية المطروحة من قبل الوزارات والمؤسسات',
    'problems.add_problem': 'إضافة مشكلة',
    'problems.search_filter': 'البحث والتصفية',
    'problems.search_placeholder': 'ابحث في العناوين، الأوصاف، أو الكلمات المفتاحية...',
    'problems.status': 'الحالة',
    'problems.priority': 'الأولوية',
    'problems.category': 'الفئة التقنية',
    'problems.sector': 'القطاع',
    'problems.sort': 'الترتيب',
    'problems.all_statuses': 'جميع الحالات',
    'problems.all_priorities': 'جميع الأولويات',
    'problems.all_categories': 'جميع الفئات',
    'problems.all_sectors': 'جميع القطاعات',
    'problems.newest_first': 'الأحدث أولاً',
    'problems.oldest_first': 'الأقدم أولاً',
    'problems.title_az': 'العنوان (أ-ي)',
    'problems.title_za': 'العنوان (ي-أ)',
    'problems.active_filters': 'المرشحات النشطة',
    'problems.clear_filters': 'مسح جميع المرشحات',
    'problems.loading': 'جاري التحميل...',
    'problems.no_problems': 'لا توجد مشاكل',
    'problems.no_results': 'لا توجد مشاكل تطابق معايير البحث المحددة',
    'problems.no_problems_yet': 'لا توجد مشاكل مطروحة حالياً',
    'problems.add_new_problem': 'إضافة مشكلة جديدة',
    'problems.by': 'بواسطة',
    'problems.solutions_count': 'حل',
    'problems.attachment': 'مرفق',
    'problems.attachments': 'مرفقات',

    // Problem Status
    'status.open': 'مفتوحة',
    'status.in_progress': 'قيد المعالجة',
    'status.resolved': 'محلولة',
    'status.closed': 'مغلقة',

    // Priority Levels
    'priority.low': 'منخفضة',
    'priority.medium': 'متوسطة',
    'priority.high': 'عالية',
    'priority.critical': 'حرجة',

    // Problem Submission
    'submit.title': 'إرسال مشكلة تقنية جديدة',
    'submit.subtitle': 'اشرح المشكلة التقنية التي تواجهها بالتفصيل ليتمكن الخبراء من تقديم أفضل الحلول',
    'submit.problem_title': 'عنوان المشكلة',
    'submit.problem_title_required': 'عنوان المشكلة *',
    'submit.problem_description': 'وصف تفصيلي للمشكلة',
    'submit.problem_description_required': 'وصف تفصيلي للمشكلة *',
    'submit.technical_category': 'الفئة التقنية',
    'submit.technical_category_required': 'الفئة التقنية *',
    'submit.sector_required': 'القطاع *',
    'submit.priority_level': 'مستوى الأولوية',
    'submit.keywords': 'الكلمات المفتاحية (اختياري)',
    'submit.attachments': 'المرفقات (اختياري)',
    'submit.cancel': 'إلغاء',
    'submit.submit_problem': 'إرسال المشكلة',
    'submit.submitting': 'جاري الإرسال...',
    'submit.add_keyword': 'إضافة',
    'submit.keyword_placeholder': 'أضف كلمة مفتاحية',
    'submit.keywords_count': 'كلمات مفتاحية',
    'submit.min_chars': 'الحد الأدنى',
    'submit.chars': 'حرف',

    // Expert Directory
    'experts.title': 'الخبراء المتاحون',
    'experts.advanced_filter': 'فلترة متقدمة',
    'experts.search_placeholder': 'ابحث عن خبير بالاسم، المهارات، أو التخصص...',
    'experts.sort_by': 'ترتيب حسب',
    'experts.rating': 'التقييم',
    'experts.contributions': 'عدد المساهمات',
    'experts.response_time': 'وقت الاستجابة',
    'experts.experience': 'سنوات الخبرة',
    'experts.no_results': 'لا توجد نتائج',
    'experts.no_experts_found': 'لم يتم العثور على خبراء يطابقون معايير البحث الحالية',
    'experts.clear_filters': 'مسح الفلاتر',
    'experts.view_profile': 'عرض الملف الشخصي',
    'experts.expertise_areas': 'مجالات الخبرة',
    'experts.main_skills': 'المهارات الرئيسية',
    'experts.years_experience': 'سنة خبرة',
    'experts.hours': 'ساعة',
    'experts.contributions_count': 'مساهمة',
    'experts.joined': 'انضم في',

    // Availability Status
    'availability.available': 'متاح',
    'availability.busy': 'مشغول',
    'availability.unavailable': 'غير متاح',

    // Common
    'common.loading': 'جاري التحميل...',
    'common.error': 'خطأ',
    'common.success': 'نجح',
    'common.cancel': 'إلغاء',
    'common.save': 'حفظ',
    'common.edit': 'تعديل',
    'common.delete': 'حذف',
    'common.view': 'عرض',
    'common.back': 'العودة',
    'common.next': 'التالي',
    'common.previous': 'السابق',
    'common.close': 'إغلاق',
    'common.confirm': 'تأكيد',
    'common.yes': 'نعم',
    'common.no': 'لا',
    'common.required': 'مطلوب',
    'common.optional': 'اختياري',

    // Time
    'time.now': 'الآن',
    'time.minute_ago': 'منذ دقيقة',
    'time.minutes_ago': 'منذ {count} دقيقة',
    'time.hour_ago': 'منذ ساعة',
    'time.hours_ago': 'ساعة مضت',
    'time.day_ago': 'منذ يوم',
    'time.days_ago': 'يوم مضى',
    'time.week_ago': 'منذ أسبوع',
    'time.weeks_ago': 'أسبوع مضى',
    'time.month_ago': 'منذ شهر',
    'time.months_ago': 'منذ {count} شهر',
    'time.year_ago': 'منذ سنة',
    'time.years_ago': 'منذ {count} سنة',

    // Homepage
    'homepage.hero_title': 'نجمع الخبراء السوريين لحل التحديات التقنية',
    'homepage.hero_subtitle': 'منصة شاملة تربط بين المشاكل التقنية والحلول المبتكرة من خلال شبكة خبراء محليين وعالميين',
    'homepage.stats.problems_solved': 'المشاكل المحلولة',
    'homepage.stats.registered_experts': 'الخبراء المسجلين',
    'homepage.stats.presentations': 'العروض التقديمية',
    'homepage.stats.qa': 'الأسئلة والأجوبة',
    'homepage.browse_by_category': 'تصفح حسب الفئة',
    'homepage.entries_available': 'مدخلة متاحة',
    'homepage.browse_now': 'تصفح الآن',
    'homepage.latest_contributions': 'أحدث المساهمات',
    'homepage.view_all': 'عرض الكل',
    'homepage.view_details': 'عرض التفاصيل',
    'homepage.cta_title': 'شارك خبرتك وساعد في حل التحديات التقنية',
    'homepage.cta_subtitle': 'انضم إلى مجتمع الخبراء السوريين وساهم في بناء مستقبل تقني أفضل',
    'homepage.view_experts': 'عرض الخبراء',
    'homepage.browse_problems': 'تصفح المشاكل',
    'homepage.submit_problem': 'اطرح مشكلة',
    'homepage.join_expert': 'انضم كخبير',
    'homepage.footer.title': 'مركز سوريا الذكي',
    'homepage.footer.description': 'منصة تجمع الخبراء والحلول التقنية لخدمة المجتمع السوري',
    'homepage.footer.quick_links': 'روابط سريعة',
    'homepage.footer.experts': 'الخبراء',
    'homepage.footer.problems': 'المشاكل',
    'homepage.footer.solutions': 'الحلول',
    'homepage.footer.seminars': 'الندوات',
    'homepage.footer.sectors': 'القطاعات',
    'homepage.footer.health': 'الصحة',
    'homepage.footer.education': 'التعليم',
    'homepage.footer.industry': 'الصناعة',
    'homepage.footer.agriculture': 'الزراعة',
    'homepage.footer.contact': 'تواصل معنا',
    'homepage.footer.copyright': '© 2024 مركز سوريا الذكي. جميع الحقوق محفوظة.',

    // Categories
    'categories.ministries': 'الوزارات',
    'categories.industry': 'القطاعات الصناعية',
    'categories.tech': 'التقنيات',
    'categories.education': 'التعليم',

    // Authentication
    'auth.login_description': 'أدخل بياناتك للوصول إلى منصة الحلول التقنية',
    'auth.email': 'البريد الإلكتروني',
    'auth.password': 'كلمة المرور',
    'auth.email_placeholder': '<EMAIL>',
    'auth.password_placeholder': 'أدخل كلمة المرور',
    'auth.hide_password': 'إخفاء كلمة المرور',
    'auth.show_password': 'إظهار كلمة المرور',
    'auth.logging_in': 'جاري تسجيل الدخول...',
    'auth.forgot_password': 'نسيت كلمة المرور؟',
    'auth.no_account': 'ليس لديك حساب؟',
    'auth.login_success': 'تم تسجيل الدخول بنجاح',
    'auth.invalid_credentials': 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
    'auth.email_not_confirmed': 'يرجى تأكيد البريد الإلكتروني أولاً',
    'auth.passwords_dont_match': 'كلمات المرور غير متطابقة',
    'auth.password_min_length': 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
    'auth.name_required': 'الاسم مطلوب',
    'auth.location_required': 'الموقع مطلوب',
    'auth.creating_account': 'جاري إنشاء الحساب...',
    'auth.registration_failed': 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى',
    'auth.email_already_registered': 'هذا البريد الإلكتروني مسجل مسبقاً',
    'auth.invalid_email': 'البريد الإلكتروني غير صحيح',
    'auth.registration_success': 'تم إنشاء الحساب بنجاح! يرجى التحقق من بريدك الإلكتروني لتأكيد الحساب',
    'auth.register_description': 'انضم إلى منصة الحلول التقنية وساهم في حل التحديات التقنية',
    'auth.full_name': 'الاسم الكامل',
    'auth.full_name_placeholder': 'أحمد محمد',
    'auth.password_min_chars': '6 أحرف على الأقل',
    'auth.confirm_password': 'تأكيد كلمة المرور',
    'auth.confirm_password_placeholder': 'أعد إدخال كلمة المرور',
    'auth.have_account': 'لديك حساب بالفعل؟',

    // Error Messages
    'errors.inline_error_description': 'حدث خطأ أثناء تحميل هذا المكون',
    'errors.component_failed': 'فشل في تحميل المكون',
    'errors.network_error': 'خطأ في الشبكة',
    'errors.authentication_failed': 'فشل في المصادقة',
    'errors.permission_denied': 'تم رفض الإذن',
    'errors.not_found': 'غير موجود',
    'errors.server_error': 'خطأ في الخادم',
    'errors.validation_error': 'خطأ في التحقق',
    'errors.timeout': 'انتهت مهلة الطلب',
    'errors.unknown_error': 'خطأ غير معروف',
    'errors.try_again': 'حاول مرة أخرى',
    'errors.contact_support': 'اتصل بالدعم الفني',
    'errors.reload_page': 'إعادة تحميل الصفحة',
    'errors.page_error_title': 'حدث خطأ في هذه الصفحة',
    'errors.component_error_title': 'حدث خطأ في المكون',
    'errors.inline_error_title': 'خطأ',
    'errors.generic_error_title': 'حدث خطأ',
    'errors.page_error_description': 'واجهنا خطأ غير متوقع أثناء تحميل هذه الصفحة. يرجى المحاولة مرة أخرى أو العودة إلى الصفحة الرئيسية.',
    'errors.component_error_description': 'فشل هذا المكون في التحميل بشكل صحيح. يمكنك المحاولة مرة أخرى أو الاستمرار في استخدام أجزاء أخرى من التطبيق.',
    'errors.generic_error_description': 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
    'errors.retry_button': 'حاول مرة أخرى',
    'errors.reload_button': 'إعادة تحميل الصفحة',
    'errors.home_button': 'الصفحة الرئيسية',
    'errors.report_button': 'الإبلاغ عن المشكلة',
    'errors.reporting_button': 'جاري الإبلاغ...',
    'errors.hide_details': 'إخفاء التفاصيل',
    'errors.show_details': 'إظهار التفاصيل',
    'errors.error_message': 'رسالة الخطأ',
    'errors.component_stack': 'مكدس المكونات',
    'errors.help_text': 'إذا استمرت هذه المشكلة، يرجى الاتصال بالدعم الفني أو المحاولة مرة أخرى لاحقاً.',
    'errors.search_failed': 'فشل في البحث',
    'errors.using_fallback_data': 'سيتم استخدام البيانات الاحتياطية',
    'errors.data_fetch_failed': 'فشل في جلب البيانات',
    'errors.please_try_again': 'يرجى المحاولة مرة أخرى',
    'errors.critical_error': 'خطأ حرج',

    // Accessibility
    'accessibility.screen_reader_announcement': 'إعلان لقارئ الشاشة',
    'accessibility.loading_content': 'جاري تحميل المحتوى',
    'accessibility.content_loaded': 'تم تحميل المحتوى',
    'accessibility.error_occurred': 'حدث خطأ',
    'accessibility.navigation_menu': 'قائمة التنقل',
    'accessibility.main_content': 'المحتوى الرئيسي',
    'accessibility.search_results': 'نتائج البحث',
    'accessibility.form_validation_error': 'خطأ في التحقق من النموذج',
    'accessibility.required_field': 'حقل مطلوب',
    'accessibility.optional_field': 'حقل اختياري',
    'accessibility.button_pressed': 'تم الضغط على الزر',
    'accessibility.link_opened': 'تم فتح الرابط',
    'accessibility.modal_opened': 'تم فتح النافذة المنبثقة',
    'accessibility.modal_closed': 'تم إغلاق النافذة المنبثقة',
    'accessibility.page_changed': 'تم تغيير الصفحة',
    'accessibility.filter_applied': 'تم تطبيق المرشح',
    'accessibility.sort_changed': 'تم تغيير الترتيب',
    'accessibility.data_updated': 'تم تحديث البيانات',
    'accessibility.search_focused': 'تم التركيز على البحث',
    'accessibility.navigating_new_problem': 'الانتقال إلى صفحة المشكلة الجديدة',
    'accessibility.filters_cleared': 'تم مسح جميع المرشحات',
    'accessibility.skip_to_content': 'تخطي إلى المحتوى',
    'accessibility.keyboard_shortcut_new': 'اختصار لوحة المفاتيح: {shortcut}',
    'accessibility.filters_description': 'استخدم هذه المرشحات لتضييق نطاق البحث في المشاكل',
    'accessibility.loading_problems': 'جاري تحميل المشاكل',
    'accessibility.problems_count': 'تم العثور على {count} مشكلة',
    'accessibility.problem_card_description': 'مشكلة: {title}، الأولوية: {urgency}، الحالة: {status}',
    'accessibility.status_badge': 'الحالة: {status}',
    'accessibility.urgency_badge': 'الأولوية: {urgency}',
  },
  en: {
    // Header Navigation
    'nav.home': 'Home',
    'nav.problems': 'Problems',
    'nav.experts': 'Experts',
    'nav.submit_problem': 'Submit Problem',
    'nav.search': 'Search',
    'nav.admin_panel': 'Admin Panel',
    'nav.profile': 'Profile',
    'nav.settings': 'Settings',
    'nav.user_management': 'User Management',
    'nav.analytics': 'Analytics',
    'nav.logout': 'Logout',
    'nav.login': 'Login',
    'nav.register': 'Register',
    'nav.menu': 'Menu',

    // Brand
    'brand.title': 'Syria Smart Center',
    'brand.subtitle': 'Technical Solutions Platform',

    // User Roles
    'role.admin': 'Administrator',
    'role.expert': 'Expert',
    'role.ministry_user': 'Ministry Employee',

    // Problems Page
    'problems.title': 'Technical Problems',
    'problems.subtitle': 'Browse and search technical problems submitted by ministries and institutions',
    'problems.add_problem': 'Add Problem',
    'problems.search_filter': 'Search and Filter',
    'problems.search_placeholder': 'Search in titles, descriptions, or keywords...',
    'problems.status': 'Status',
    'problems.priority': 'Priority',
    'problems.category': 'Technical Category',
    'problems.sector': 'Sector',
    'problems.sort': 'Sort',
    'problems.all_statuses': 'All Statuses',
    'problems.all_priorities': 'All Priorities',
    'problems.all_categories': 'All Categories',
    'problems.all_sectors': 'All Sectors',
    'problems.newest_first': 'Newest First',
    'problems.oldest_first': 'Oldest First',
    'problems.title_az': 'Title (A-Z)',
    'problems.title_za': 'Title (Z-A)',
    'problems.active_filters': 'Active Filters',
    'problems.clear_filters': 'Clear All Filters',
    'problems.loading': 'Loading...',
    'problems.no_problems': 'No Problems',
    'problems.no_results': 'No problems match the current search criteria',
    'problems.no_problems_yet': 'No problems have been submitted yet',
    'problems.add_new_problem': 'Add New Problem',
    'problems.by': 'by',
    'problems.solutions_count': 'solution',
    'problems.attachment': 'attachment',
    'problems.attachments': 'attachments',

    // Problem Status
    'status.open': 'Open',
    'status.in_progress': 'In Progress',
    'status.resolved': 'Resolved',
    'status.closed': 'Closed',

    // Priority Levels
    'priority.low': 'Low',
    'priority.medium': 'Medium',
    'priority.high': 'High',
    'priority.critical': 'Critical',

    // Problem Submission
    'submit.title': 'Submit New Technical Problem',
    'submit.subtitle': 'Describe your technical problem in detail so experts can provide the best solutions',
    'submit.problem_title': 'Problem Title',
    'submit.problem_title_required': 'Problem Title *',
    'submit.problem_description': 'Detailed Problem Description',
    'submit.problem_description_required': 'Detailed Problem Description *',
    'submit.technical_category': 'Technical Category',
    'submit.technical_category_required': 'Technical Category *',
    'submit.sector_required': 'Sector *',
    'submit.priority_level': 'Priority Level',
    'submit.keywords': 'Keywords (Optional)',
    'submit.attachments': 'Attachments (Optional)',
    'submit.cancel': 'Cancel',
    'submit.submit_problem': 'Submit Problem',
    'submit.submitting': 'Submitting...',
    'submit.add_keyword': 'Add',
    'submit.keyword_placeholder': 'Add keyword',
    'submit.keywords_count': 'keywords',
    'submit.min_chars': 'minimum',
    'submit.chars': 'characters',

    // Expert Directory
    'experts.title': 'Available Experts',
    'experts.advanced_filter': 'Advanced Filter',
    'experts.search_placeholder': 'Search for expert by name, skills, or specialization...',
    'experts.sort_by': 'Sort by',
    'experts.rating': 'Rating',
    'experts.contributions': 'Contributions',
    'experts.response_time': 'Response Time',
    'experts.experience': 'Years of Experience',
    'experts.no_results': 'No Results',
    'experts.no_experts_found': 'No experts found matching the current search criteria',
    'experts.clear_filters': 'Clear Filters',
    'experts.view_profile': 'View Profile',
    'experts.expertise_areas': 'Expertise Areas',
    'experts.main_skills': 'Main Skills',
    'experts.years_experience': 'years experience',
    'experts.hours': 'hours',
    'experts.contributions_count': 'contributions',
    'experts.joined': 'Joined',

    // Availability Status
    'availability.available': 'Available',
    'availability.busy': 'Busy',
    'availability.unavailable': 'Unavailable',

    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.view': 'View',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.close': 'Close',
    'common.confirm': 'Confirm',
    'common.yes': 'Yes',
    'common.no': 'No',
    'common.required': 'Required',
    'common.optional': 'Optional',

    // Time
    'time.now': 'now',
    'time.minute_ago': 'a minute ago',
    'time.minutes_ago': '{count} minutes ago',
    'time.hour_ago': 'an hour ago',
    'time.hours_ago': 'hours ago',
    'time.day_ago': 'a day ago',
    'time.days_ago': 'days ago',
    'time.week_ago': 'a week ago',
    'time.weeks_ago': 'weeks ago',
    'time.month_ago': 'a month ago',
    'time.months_ago': '{count} months ago',
    'time.year_ago': 'a year ago',
    'time.years_ago': '{count} years ago',

    // Homepage
    'homepage.hero_title': 'Connecting Syrian Experts to Solve Technical Challenges',
    'homepage.hero_subtitle': 'A comprehensive platform linking technical problems with innovative solutions through a network of local and global experts',
    'homepage.stats.problems_solved': 'Problems Solved',
    'homepage.stats.registered_experts': 'Registered Experts',
    'homepage.stats.presentations': 'Presentations',
    'homepage.stats.qa': 'Q&A',
    'homepage.browse_by_category': 'Browse by Category',
    'homepage.entries_available': 'entries available',
    'homepage.browse_now': 'Browse Now',
    'homepage.latest_contributions': 'Latest Contributions',
    'homepage.view_all': 'View All',
    'homepage.view_details': 'View Details',
    'homepage.cta_title': 'Share Your Expertise and Help Solve Technical Challenges',
    'homepage.cta_subtitle': 'Join the Syrian experts community and contribute to building a better technical future',
    'homepage.view_experts': 'View Experts',
    'homepage.browse_problems': 'Browse Problems',
    'homepage.submit_problem': 'Submit Problem',
    'homepage.join_expert': 'Join as Expert',
    'homepage.footer.title': 'Syria Smart Center',
    'homepage.footer.description': 'A platform that brings together experts and technical solutions to serve the Syrian community',
    'homepage.footer.quick_links': 'Quick Links',
    'homepage.footer.experts': 'Experts',
    'homepage.footer.problems': 'Problems',
    'homepage.footer.solutions': 'Solutions',
    'homepage.footer.seminars': 'Seminars',
    'homepage.footer.sectors': 'Sectors',
    'homepage.footer.health': 'Health',
    'homepage.footer.education': 'Education',
    'homepage.footer.industry': 'Industry',
    'homepage.footer.agriculture': 'Agriculture',
    'homepage.footer.contact': 'Contact Us',
    'homepage.footer.copyright': '© 2024 Syria Smart Center. All rights reserved.',

    // Categories
    'categories.ministries': 'Ministries',
    'categories.industry': 'Industrial Sectors',
    'categories.tech': 'Technologies',
    'categories.education': 'Education',

    // Authentication
    'auth.login_description': 'Enter your credentials to access the technical solutions platform',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.email_placeholder': '<EMAIL>',
    'auth.password_placeholder': 'Enter your password',
    'auth.hide_password': 'Hide password',
    'auth.show_password': 'Show password',
    'auth.logging_in': 'Logging in...',
    'auth.forgot_password': 'Forgot password?',
    'auth.no_account': "Don't have an account?",
    'auth.login_success': 'Login successful',
    'auth.invalid_credentials': 'Invalid email or password',
    'auth.email_not_confirmed': 'Please confirm your email first',
    'auth.passwords_dont_match': 'Passwords do not match',
    'auth.password_min_length': 'Password must be at least 6 characters',
    'auth.name_required': 'Name is required',
    'auth.location_required': 'Location is required',
    'auth.creating_account': 'Creating account...',
    'auth.registration_failed': 'Registration failed. Please try again.',
    'auth.email_already_registered': 'This email is already registered',
    'auth.invalid_email': 'Invalid email address',
    'auth.registration_success': 'Account created successfully! Please check your email to confirm your account.',
    'auth.register_description': 'Join the technical solutions platform and contribute to solving technical challenges',
    'auth.full_name': 'Full Name',
    'auth.full_name_placeholder': 'Ahmad Mohammad',
    'auth.password_min_chars': 'At least 6 characters',
    'auth.confirm_password': 'Confirm Password',
    'auth.confirm_password_placeholder': 'Re-enter your password',
    'auth.have_account': 'Already have an account?',

    // Error Messages
    'errors.inline_error_description': 'An error occurred while loading this component',
    'errors.component_failed': 'Component failed to load',
    'errors.network_error': 'Network error',
    'errors.authentication_failed': 'Authentication failed',
    'errors.permission_denied': 'Permission denied',
    'errors.not_found': 'Not found',
    'errors.server_error': 'Server error',
    'errors.validation_error': 'Validation error',
    'errors.timeout': 'Request timeout',
    'errors.unknown_error': 'Unknown error',
    'errors.try_again': 'Try again',
    'errors.contact_support': 'Contact support',
    'errors.reload_page': 'Reload page',
    'errors.page_error_title': 'Something went wrong with this page',
    'errors.component_error_title': 'Component error occurred',
    'errors.inline_error_title': 'Error',
    'errors.generic_error_title': 'An error occurred',
    'errors.page_error_description': 'We encountered an unexpected error while loading this page. Please try refreshing or go back to the homepage.',
    'errors.component_error_description': 'This component failed to load properly. You can try refreshing the page or continue using other parts of the application.',
    'errors.generic_error_description': 'An unexpected error occurred. Please try again.',
    'errors.retry_button': 'Try Again',
    'errors.reload_button': 'Reload Page',
    'errors.home_button': 'Go Home',
    'errors.report_button': 'Report Issue',
    'errors.reporting_button': 'Reporting...',
    'errors.hide_details': 'Hide Details',
    'errors.show_details': 'Show Details',
    'errors.error_message': 'Error Message',
    'errors.component_stack': 'Component Stack',
    'errors.help_text': 'If this problem persists, please contact support or try again later.',
    'errors.search_failed': 'Search failed',
    'errors.using_fallback_data': 'Using fallback data',
    'errors.data_fetch_failed': 'Failed to fetch data',
    'errors.please_try_again': 'Please try again',
    'errors.critical_error': 'Critical error',

    // Accessibility
    'accessibility.screen_reader_announcement': 'Screen reader announcement',
    'accessibility.loading_content': 'Loading content',
    'accessibility.content_loaded': 'Content loaded',
    'accessibility.error_occurred': 'An error occurred',
    'accessibility.navigation_menu': 'Navigation menu',
    'accessibility.main_content': 'Main content',
    'accessibility.search_results': 'Search results',
    'accessibility.form_validation_error': 'Form validation error',
    'accessibility.required_field': 'Required field',
    'accessibility.optional_field': 'Optional field',
    'accessibility.button_pressed': 'Button pressed',
    'accessibility.link_opened': 'Link opened',
    'accessibility.modal_opened': 'Modal opened',
    'accessibility.modal_closed': 'Modal closed',
    'accessibility.page_changed': 'Page changed',
    'accessibility.filter_applied': 'Filter applied',
    'accessibility.sort_changed': 'Sort changed',
    'accessibility.data_updated': 'Data updated',
    'accessibility.search_focused': 'Search focused',
    'accessibility.navigating_new_problem': 'Navigating to new problem page',
    'accessibility.filters_cleared': 'All filters cleared',
    'accessibility.skip_to_content': 'Skip to content',
    'accessibility.keyboard_shortcut_new': 'Keyboard shortcut: {shortcut}',
    'accessibility.filters_description': 'Use these filters to narrow down the search for problems',
    'accessibility.loading_problems': 'Loading problems',
    'accessibility.problems_count': 'Found {count} problems',
    'accessibility.problem_card_description': 'Problem: {title}, Priority: {urgency}, Status: {status}',
    'accessibility.status_badge': 'Status: {status}',
    'accessibility.urgency_badge': 'Priority: {urgency}',
  }
};

interface LanguageProviderProps {
  children: ReactNode;
}

export function LanguageProvider({ children }: LanguageProviderProps) {
  const [language, setLanguage] = useState<Language>(() => {
    // Get saved language from localStorage or default to Arabic
    const saved = localStorage.getItem('language') as Language;
    return saved || 'ar';
  });

  const isRTL = language === 'ar';

  useEffect(() => {
    // Save language preference
    localStorage.setItem('language', language);
    
    // Update document direction and language
    document.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = language;
    
    // Update body class for styling
    document.body.classList.toggle('rtl', isRTL);
    document.body.classList.toggle('ltr', !isRTL);
  }, [language, isRTL]);

  const t = (key: string, params?: Record<string, string | number>): string => {
    // Use the translation monitor for enhanced translation with monitoring
    return translationMonitor.translate(translations, key, language, params);
  };

  const value: LanguageContextType = {
    language,
    setLanguage,
    t,
    isRTL
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage(): LanguageContextType {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}