import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

interface AccessibilitySettings {
  highContrast: boolean
  fontSize: 'small' | 'medium' | 'large' | 'extra-large'
  reducedMotion: boolean
}

interface AccessibilityContextType {
  settings: AccessibilitySettings
  updateSettings: (settings: Partial<AccessibilitySettings>) => void
  toggleHighContrast: () => void
  increaseFontSize: () => void
  decreaseFontSize: () => void
}

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined)

const FONT_SIZE_MAP = {
  'small': '14px',
  'medium': '16px',
  'large': '18px',
  'extra-large': '20px'
}

const FONT_SIZE_ORDER = ['small', 'medium', 'large', 'extra-large'] as const

export function AccessibilityProvider({ children }: { children: ReactNode }) {
  const [settings, setSettings] = useState<AccessibilitySettings>(() => {
    // Load from localStorage or use defaults
    const saved = localStorage.getItem('accessibility-settings')
    if (saved) {
      try {
        return JSON.parse(saved)
      } catch (error) {
        console.warn('Failed to parse accessibility settings:', error)
      }
    }
    
    return {
      highContrast: false,
      fontSize: 'medium' as const,
      reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches
    }
  })

  const updateSettings = (newSettings: Partial<AccessibilitySettings>) => {
    setSettings(prev => {
      const updated = { ...prev, ...newSettings }
      localStorage.setItem('accessibility-settings', JSON.stringify(updated))
      return updated
    })
  }

  const toggleHighContrast = () => {
    updateSettings({ highContrast: !settings.highContrast })
  }

  const increaseFontSize = () => {
    const currentIndex = FONT_SIZE_ORDER.indexOf(settings.fontSize)
    if (currentIndex < FONT_SIZE_ORDER.length - 1) {
      updateSettings({ fontSize: FONT_SIZE_ORDER[currentIndex + 1] })
    }
  }

  const decreaseFontSize = () => {
    const currentIndex = FONT_SIZE_ORDER.indexOf(settings.fontSize)
    if (currentIndex > 0) {
      updateSettings({ fontSize: FONT_SIZE_ORDER[currentIndex - 1] })
    }
  }

  // Apply settings to document
  useEffect(() => {
    const root = document.documentElement
    
    // Apply font size
    root.style.fontSize = FONT_SIZE_MAP[settings.fontSize]
    
    // Apply high contrast
    if (settings.highContrast) {
      root.classList.add('high-contrast')
    } else {
      root.classList.remove('high-contrast')
    }
    
    // Apply reduced motion
    if (settings.reducedMotion) {
      root.classList.add('reduced-motion')
    } else {
      root.classList.remove('reduced-motion')
    }
  }, [settings])

  return (
    <AccessibilityContext.Provider value={{
      settings,
      updateSettings,
      toggleHighContrast,
      increaseFontSize,
      decreaseFontSize
    }}>
      {children}
    </AccessibilityContext.Provider>
  )
}

export function useAccessibility() {
  const context = useContext(AccessibilityContext)
  if (context === undefined) {
    throw new Error('useAccessibility must be used within an AccessibilityProvider')
  }
  return context
}

// Accessibility toolbar component
export function AccessibilityToolbar() {
  const { settings, toggleHighContrast, increaseFontSize, decreaseFontSize } = useAccessibility()
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div className="fixed top-4 left-4 z-50">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="bg-blue-600 text-white p-2 rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-label="إعدادات إمكانية الوصول"
        title="إعدادات إمكانية الوصول"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
        </svg>
      </button>
      
      {isOpen && (
        <div className="absolute top-12 left-0 bg-white border border-gray-200 rounded-lg shadow-lg p-4 min-w-[200px]">
          <h3 className="text-sm font-medium text-gray-900 mb-3">إعدادات إمكانية الوصول</h3>
          
          <div className="space-y-3">
            {/* High Contrast Toggle */}
            <div className="flex items-center justify-between">
              <label htmlFor="high-contrast" className="text-sm text-gray-700">
                تباين عالي
              </label>
              <button
                id="high-contrast"
                onClick={toggleHighContrast}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                  settings.highContrast ? 'bg-blue-600' : 'bg-gray-200'
                }`}
                role="switch"
                aria-checked={settings.highContrast}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.highContrast ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
            
            {/* Font Size Controls */}
            <div>
              <label className="text-sm text-gray-700 block mb-2">حجم الخط</label>
              <div className="flex items-center gap-2">
                <button
                  onClick={decreaseFontSize}
                  disabled={settings.fontSize === 'small'}
                  className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed rounded"
                  aria-label="تصغير الخط"
                >
                  أ-
                </button>
                <span className="text-xs text-gray-600 min-w-[60px] text-center">
                  {settings.fontSize === 'small' ? 'صغير' :
                   settings.fontSize === 'medium' ? 'متوسط' :
                   settings.fontSize === 'large' ? 'كبير' : 'كبير جداً'}
                </span>
                <button
                  onClick={increaseFontSize}
                  disabled={settings.fontSize === 'extra-large'}
                  className="px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed rounded"
                  aria-label="تكبير الخط"
                >
                  أ+
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}