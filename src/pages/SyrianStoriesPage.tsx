/**
 * Syrian Stories Page
 * 
 * Development page for viewing Syrian identity component documentation.
 * This page should be removed or protected in production.
 */

import React from 'react';
import { SyrianStoriesIndex } from '@/components/stories/SyrianStoriesIndex';

export default function SyrianStoriesPage() {
  // Show warning in production
  if (process.env.NODE_ENV === 'production') {
    return (
      <div className="min-h-screen flex items-center justify-center p-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Stories Not Available</h1>
          <p className="text-muted-foreground">
            Syrian component stories are only available in development mode.
          </p>
        </div>
      </div>
    );
  }

  return <SyrianStoriesIndex />;
}
