/**
 * Expert Matching Preferences Page
 * Allows experts to configure their matching preferences
 */

import React, { useState, useEffect } from 'react';
import { useAuthContext } from '@/components/auth/AuthProvider';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Settings, AlertCircle } from 'lucide-react';
import { ExpertMatchingPreferences } from '@/components/experts/ExpertMatchingPreferences';
import { expertOperations } from '@/lib/database';
import type { ExpertData } from '@/types/user';

export default function ExpertMatchingPreferencesPage() {
  const { user } = useAuthContext();
  const navigate = useNavigate();
  const [expertProfile, setExpertProfile] = useState<ExpertData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      loadExpertProfile();
    }
  }, [user]);

  const loadExpertProfile = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const { data, error } = await expertOperations.getExpertProfile(user.id);
      
      if (error) {
        throw new Error(error.message);
      }
      
      setExpertProfile(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load expert profile');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading expert profile...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !expertProfile) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-12 text-center">
            <AlertCircle className="h-16 w-16 text-red-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Expert Profile Required
            </h3>
            <p className="text-gray-600 mb-4">
              You need to create an expert profile before configuring matching preferences.
            </p>
            <Button onClick={() => navigate('/experts/profile/create')}>
              Create Expert Profile
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/experts/dashboard')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Dashboard
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Matching Preferences</h1>
            <p className="text-gray-600 mt-1">
              Configure how the system matches you with relevant problems
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Settings className="h-6 w-6 text-gray-400" />
        </div>
      </div>

      {/* Preferences Component */}
      <ExpertMatchingPreferences
        expertId={expertProfile.id}
        onPreferencesUpdate={(preferences) => {
          // Could show a success message or redirect
          console.log('Preferences updated:', preferences);
        }}
      />

      {/* Help Section */}
      <Card>
        <CardHeader>
          <CardTitle>How Matching Works</CardTitle>
          <CardDescription>
            Understanding the intelligent matching algorithm
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">Matching Factors</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Problem category alignment with your preferences</li>
                <li>• Sector expertise match</li>
                <li>• Your expertise areas and experience level</li>
                <li>• Current availability status</li>
                <li>• Historical response time performance</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Benefits</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Receive only relevant problem matches</li>
                <li>• Control your workload with monthly limits</li>
                <li>• Set compensation expectations</li>
                <li>• Define your availability schedule</li>
                <li>• Improve match quality over time</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}