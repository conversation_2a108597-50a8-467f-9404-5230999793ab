import React from 'react';
import { Layout } from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuthContext, useRoleAccess } from '@/components/auth/AuthProvider';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  FileText, 
  CheckCircle, 
  Clock,
  Shield,
  Calendar,
  Download
} from 'lucide-react';
import { Button } from '@/components/ui/button';

const AdminAnalytics = () => {
  const { isAdmin } = useRoleAccess();

  // Mock analytics data
  const overviewStats = [
    { title: 'إجمالي المستخدمين', value: '1,234', change: '+12%', trend: 'up' },
    { title: 'المشاكل المرسلة', value: '456', change: '+8%', trend: 'up' },
    { title: 'الحلول المقدمة', value: '342', change: '+15%', trend: 'up' },
    { title: 'معدل الحل', value: '75%', change: '+3%', trend: 'up' }
  ];

  const sectorStats = [
    { sector: 'وزارة الصحة', problems: 45, solutions: 38, rate: '84%' },
    { sector: 'وزارة التربية', problems: 32, solutions: 24, rate: '75%' },
    { sector: 'وزارة المالية', problems: 28, solutions: 19, rate: '68%' },
    { sector: 'وزارة الاتصالات', problems: 21, solutions: 18, rate: '86%' },
    { sector: 'القطاع المصرفي', problems: 15, solutions: 12, rate: '80%' }
  ];

  const expertStats = [
    { name: 'د. أحمد الخطيب', solutions: 24, rating: 4.9, sector: 'الصحة' },
    { name: 'م. فاطمة السيد', solutions: 18, rating: 4.7, sector: 'المالية' },
    { name: 'م. محمد العلي', solutions: 15, rating: 4.8, sector: 'التقنية' },
    { name: 'د. سارة أحمد', solutions: 12, rating: 4.6, sector: 'التعليم' },
    { name: 'م. علي حسن', solutions: 10, rating: 4.5, sector: 'الاتصالات' }
  ];

  const monthlyData = [
    { month: 'يناير', problems: 45, solutions: 38, users: 120 },
    { month: 'فبراير', problems: 52, solutions: 44, users: 145 },
    { month: 'مارس', problems: 48, solutions: 41, users: 167 },
    { month: 'أبريل', problems: 61, solutions: 49, users: 189 },
    { month: 'مايو', problems: 58, solutions: 52, users: 210 },
    { month: 'يونيو', problems: 67, solutions: 58, users: 234 }
  ];

  if (!isAdmin) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card>
            <CardContent className="text-center py-12">
              <Shield className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">غير مصرح بالوصول</h2>
              <p className="text-gray-600">هذه الصفحة مخصصة للمديرين فقط</p>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">التحليلات والتقارير</h1>
            <p className="text-gray-600 mt-2">إحصائيات مفصلة عن أداء المنصة</p>
          </div>
          <Button className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            تصدير التقرير
          </Button>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {overviewStats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600 flex items-center gap-1">
                      <TrendingUp className="w-3 h-3" />
                      {stat.change}
                    </p>
                  </div>
                  <BarChart3 className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Sector Performance */}
          <Card>
            <CardHeader>
              <CardTitle>أداء القطاعات</CardTitle>
              <CardDescription>إحصائيات المشاكل والحلول حسب القطاع</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {sectorStats.map((sector, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">{sector.sector}</p>
                      <p className="text-sm text-gray-600">
                        {sector.problems} مشكلة • {sector.solutions} حل
                      </p>
                    </div>
                    <Badge variant={
                      parseInt(sector.rate) >= 80 ? 'default' :
                      parseInt(sector.rate) >= 70 ? 'secondary' : 'destructive'
                    }>
                      {sector.rate}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top Experts */}
          <Card>
            <CardHeader>
              <CardTitle>أفضل الخبراء</CardTitle>
              <CardDescription>الخبراء الأكثر نشاطاً وتقييماً</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {expertStats.map((expert, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium">{expert.name}</p>
                        <p className="text-sm text-gray-600">{expert.sector}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{expert.solutions} حل</p>
                      <p className="text-sm text-gray-600">⭐ {expert.rating}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Monthly Trends */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>الاتجاهات الشهرية</CardTitle>
            <CardDescription>تطور المشاكل والحلول والمستخدمين عبر الأشهر</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <div className="min-w-full">
                <div className="grid grid-cols-6 gap-4">
                  {monthlyData.map((month, index) => (
                    <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-sm font-medium text-gray-600 mb-2">{month.month}</p>
                      <div className="space-y-2">
                        <div className="flex items-center justify-center gap-1">
                          <FileText className="w-3 h-3 text-blue-600" />
                          <span className="text-sm">{month.problems}</span>
                        </div>
                        <div className="flex items-center justify-center gap-1">
                          <CheckCircle className="w-3 h-3 text-green-600" />
                          <span className="text-sm">{month.solutions}</span>
                        </div>
                        <div className="flex items-center justify-center gap-1">
                          <Users className="w-3 h-3 text-purple-600" />
                          <span className="text-sm">{month.users}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Activity Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="w-5 h-5" />
                متوسط وقت الاستجابة
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-blue-600 mb-2">2.4 ساعة</div>
              <p className="text-sm text-gray-600">متوسط الوقت للحصول على أول رد</p>
              <div className="mt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>الصحة</span>
                  <span>1.8 ساعة</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>التعليم</span>
                  <span>2.1 ساعة</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>المالية</span>
                  <span>3.2 ساعة</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                معدل النمو الشهري
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-green-600 mb-2">+18%</div>
              <p className="text-sm text-gray-600">نمو في عدد المستخدمين الجدد</p>
              <div className="mt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>مستخدمين جدد</span>
                  <span className="text-green-600">+24</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>مشاكل جديدة</span>
                  <span className="text-blue-600">+15</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>حلول جديدة</span>
                  <span className="text-purple-600">+18</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5" />
                معدل الرضا
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-yellow-600 mb-2">4.6/5</div>
              <p className="text-sm text-gray-600">متوسط تقييم الحلول المقدمة</p>
              <div className="mt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>⭐⭐⭐⭐⭐</span>
                  <span>68%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>⭐⭐⭐⭐</span>
                  <span>22%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>⭐⭐⭐</span>
                  <span>8%</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default AdminAnalytics;