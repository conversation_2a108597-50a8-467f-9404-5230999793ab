
import { useState } from 'react';
import { Users, HelpCircle, Plus, ChevronRight, ChevronLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';
import { Layout } from '@/components/layout/Layout';
import { useAuthContext, useRoleAccess } from '@/components/auth/AuthProvider';
import { GlobalSearch } from '@/components/search/GlobalSearch';
import { useLanguage } from '@/contexts/LanguageContext';
import { useDeviceType } from '@/hooks/use-mobile';
import { PatternBackground as SyrianPatternBackground } from '@/components/ui/pattern-background';

const Index = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const { user } = useAuthContext();
  const { canCreateProblems, canCreateSolutions } = useRoleAccess();
  const { t, isRTL } = useLanguage();
  const { isMobile } = useDeviceType();

  // Track Syrian identity usage on homepage (placeholder for analytics)
  // useSyrianComponentTracking('Homepage', 'enhanced', 'damascusStar', 'moderate');

  const categories = [
    { id: 'ministries', name: t('categories.ministries'), icon: '🏛️', count: 25 },
    { id: 'industry', name: t('categories.industry'), icon: '🏭', count: 18 },
    { id: 'tech', name: t('categories.tech'), icon: '💻', count: 42 },
    { id: 'education', name: t('categories.education'), icon: '🎓', count: 15 }
  ];

  const recentEntries = [
    {
      id: 1,
      title: 'تطوير نظام إدارة المستشفيات الرقمي',
      category: 'وزارة الصحة',
      type: 'حل تقني',
      expert: 'د. أحمد الخطيب',
      date: '2024-06-15'
    },
    {
      id: 2,
      title: 'أتمتة عمليات التخليص الجمركي',
      category: 'وزارة المالية',
      type: 'مشكلة',
      expert: 'م. فاطمة السيد',
      date: '2024-06-14'
    },
    {
      id: 3,
      title: 'منصة التعليم الإلكتروني التفاعلي',
      category: 'وزارة التربية',
      type: 'عرض تقديمي',
      expert: 'م. محمد العلي',
      date: '2024-06-13'
    }
  ];

  const stats = [
    { label: t('homepage.stats.problems_solved'), value: '156', icon: '✅' },
    { label: t('homepage.stats.registered_experts'), value: '89', icon: '👨‍💻' },
    { label: t('homepage.stats.presentations'), value: '67', icon: '📊' },
    { label: t('homepage.stats.qa'), value: '234', icon: '❓' }
  ];

  return (
    <Layout>
      <div className="bg-gradient-to-br from-blue-50 to-indigo-100 relative overflow-hidden" dir={isRTL ? 'rtl' : 'ltr'}>
        {/* Syrian Pattern Background */}
        <SyrianPatternBackground
          pattern="damascusStar"
          intensity="subtle"
          className="absolute inset-0 opacity-[0.02] pointer-events-none"
        />

        {/* Hero Section */}
        <section className={`relative ${isMobile ? 'py-8 px-4' : 'py-16 px-4 sm:px-6 lg:px-8'}`}>
          <div className="max-w-4xl mx-auto text-center">
            <h2 className={`font-bold text-gray-900 mb-6 ${isMobile ? 'text-2xl' : 'text-4xl'} syrian-text-shadow`}>
              {t('homepage.hero_title')}
            </h2>
            <p className={`text-gray-600 mb-8 ${isMobile ? 'text-base' : 'text-xl'}`}>
              {t('homepage.hero_subtitle')}
            </p>
          
          {/* Search Bar */}
          <div className={`mx-auto mb-8 ${isMobile ? 'max-w-full' : 'max-w-2xl'}`}>
            <GlobalSearch />
          </div>

          {/* Stats */}
          <div className={`grid gap-4 mb-8 ${isMobile ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-4 gap-6 mb-12'}`}>
            {stats.map((stat, index) => (
              <div key={index} className={`bg-white rounded-lg shadow-sm border border-syrian-gold/10 hover:border-syrian-gold/20 transition-colors syrian-hover-lift ${isMobile ? 'p-4' : 'p-6'}`}>
                <div className={`mb-2 ${isMobile ? 'text-xl' : 'text-2xl'}`}>{stat.icon}</div>
                <div className={`font-bold text-syrian-gold mb-1 ${isMobile ? 'text-xl' : 'text-3xl'}`}>{stat.value}</div>
                <div className={`text-gray-600 ${isMobile ? 'text-xs' : 'text-sm'}`}>{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className={`bg-white relative ${isMobile ? 'py-8 px-4' : 'py-12 px-4 sm:px-6 lg:px-8'}`}>
        {/* Subtle pattern for categories section */}
        <SyrianPatternBackground
          pattern="geometricWeave"
          intensity="subtle"
          className="absolute inset-0 opacity-[0.01] pointer-events-none"
        />
        <div className="max-w-7xl mx-auto relative">
          <h3 className={`font-bold text-gray-900 mb-6 text-center syrian-text-shadow ${isMobile ? 'text-xl' : 'text-2xl mb-8'}`}>
            {t('homepage.browse_by_category')}
          </h3>
          <div className={`grid gap-4 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'}`}>
            {categories.map((category) => (
              <Card key={category.id} className="hover:shadow-lg transition-all duration-300 cursor-pointer group touch-manipulation border-syrian-gold/10 hover:border-syrian-gold/30 syrian-hover-lift">
                <CardHeader className={`text-center ${isMobile ? 'p-4' : ''}`}>
                  <div className={`mb-4 group-hover:scale-110 transition-transform duration-300 ${isMobile ? 'text-3xl' : 'text-4xl'}`}>{category.icon}</div>
                  <CardTitle className={`${isMobile ? 'text-base' : 'text-lg'} text-syrian-gold-dark`}>{category.name}</CardTitle>
                  <CardDescription className={isMobile ? 'text-sm' : ''}>
                    {category.count} {t('homepage.entries_available')}
                  </CardDescription>
                </CardHeader>
                <CardContent className={`text-center ${isMobile ? 'p-4 pt-0' : ''}`}>
                  <Button
                    variant="syrian-outline"
                    className={`group-hover:bg-syrian-gold/10 group-hover:border-syrian-gold touch-manipulation ${isMobile ? 'w-full text-sm' : ''}`}
                  >
                    {t('homepage.browse_now')}
                    {isRTL ? (
                      <ChevronLeft className="w-4 h-4 mr-2" />
                    ) : (
                      <ChevronRight className="w-4 h-4 ml-2" />
                    )}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Recent Entries */}
      <section className={`relative ${isMobile ? 'py-8 px-4' : 'py-12 px-4 sm:px-6 lg:px-8'}`}>
        {/* Subtle pattern for recent entries */}
        <SyrianPatternBackground
          pattern="eblaScript"
          intensity="subtle"
          className="absolute inset-0 opacity-[0.01] pointer-events-none"
        />
        <div className="max-w-7xl mx-auto relative">
          <div className={`${isMobile ? 'flex flex-col space-y-4 mb-6' : 'flex justify-between items-center mb-8'}`}>
            <h3 className={`font-bold text-gray-900 syrian-text-shadow ${isMobile ? 'text-xl' : 'text-2xl'}`}>{t('homepage.latest_contributions')}</h3>
            <Button variant="syrian-outline" className={`${isMobile ? 'w-full' : ''} touch-manipulation`}>
              {t('homepage.view_all')}
              {isRTL ? (
                <ChevronRight className="w-4 h-4 mr-2" />
              ) : (
                <ChevronRight className="w-4 h-4 ml-2" />
              )}
            </Button>
          </div>

          <div className={`grid gap-4 ${isMobile ? 'grid-cols-1' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'}`}>
            {recentEntries.map((entry) => (
              <Card key={entry.id} className="hover:shadow-lg transition-all duration-300 border-syrian-gold/10 hover:border-syrian-gold/30 syrian-hover-lift">
                <CardHeader>
                  <div className="flex justify-between items-start mb-2">
                    <Badge variant="secondary" className="bg-syrian-gold/10 text-syrian-gold-dark border-syrian-gold/20">{entry.type}</Badge>
                    <span className="text-sm text-gray-500">{entry.date}</span>
                  </div>
                  <CardTitle className="text-lg leading-relaxed text-syrian-gold-dark">
                    {entry.title}
                  </CardTitle>
                  <CardDescription>{entry.category}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
                    <Users className="w-4 h-4 text-syrian-gold" />
                    <span>{entry.expert}</span>
                  </div>
                  <Button variant="syrian-outline" className="w-full mt-4">
                    {t('homepage.view_details')}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className={`bg-gradient-to-r from-syrian-gold to-syrian-red text-white relative overflow-hidden ${isMobile ? 'py-12 px-4' : 'py-16 px-4 sm:px-6 lg:px-8'}`}>
        {/* Decorative pattern for CTA */}
        <SyrianPatternBackground
          pattern="palmyraColumns"
          intensity="subtle"
          className="absolute inset-0 opacity-[0.05] pointer-events-none"
        />
        <div className="max-w-4xl mx-auto text-center relative">
          <h3 className={`font-bold mb-6 syrian-text-shadow ${isMobile ? 'text-2xl' : 'text-3xl'}`}>
            {t('homepage.cta_title')}
          </h3>
          <p className={`opacity-90 mb-8 ${isMobile ? 'text-base' : 'text-xl'}`}>
            {t('homepage.cta_subtitle')}
          </p>
          <div className={`flex gap-4 justify-center ${isMobile ? 'flex-col' : 'flex-col sm:flex-row'}`}>
            {user ? (
              <>
                {canCreateSolutions && (
                  <Button size="lg" variant="secondary" className="text-blue-600" asChild>
                    <Link to="/experts">
                      <Users className={`w-5 h-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      {t('homepage.view_experts')}
                    </Link>
                  </Button>
                )}
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600" asChild>
                  <Link to="/problems">
                    <HelpCircle className={`w-5 h-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    {t('homepage.browse_problems')}
                  </Link>
                </Button>
                {canCreateProblems && (
                  <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600" asChild>
                    <Link to="/problems/new">
                      <Plus className={`w-5 h-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      {t('homepage.submit_problem')}
                    </Link>
                  </Button>
                )}
              </>
            ) : (
              <>
                <Button size="lg" variant="secondary" className="text-blue-600" asChild>
                  <Link to="/auth/register">
                    <Users className={`w-5 h-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    {t('homepage.join_expert')}
                  </Link>
                </Button>
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600" asChild>
                  <Link to="/problems">
                    <HelpCircle className={`w-5 h-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    {t('homepage.browse_problems')}
                  </Link>
                </Button>
              </>
            )}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
        {/* Footer pattern */}
        <SyrianPatternBackground
          pattern="geometricWeave"
          intensity="subtle"
          className="absolute inset-0 opacity-[0.02] pointer-events-none"
        />
        <div className="max-w-7xl mx-auto relative">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h4 className="text-lg font-semibold mb-4 text-syrian-gold-light">{t('homepage.footer.title')}</h4>
              <p className="text-gray-400">
                {t('homepage.footer.description')}
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4 text-syrian-gold-light">{t('homepage.footer.quick_links')}</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-syrian-gold-light transition-colors">{t('homepage.footer.experts')}</a></li>
                <li><a href="#" className="hover:text-syrian-gold-light transition-colors">{t('homepage.footer.problems')}</a></li>
                <li><a href="#" className="hover:text-syrian-gold-light transition-colors">{t('homepage.footer.solutions')}</a></li>
                <li><a href="#" className="hover:text-syrian-gold-light transition-colors">{t('homepage.footer.seminars')}</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4 text-syrian-gold-light">{t('homepage.footer.sectors')}</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-syrian-gold-light transition-colors">{t('homepage.footer.health')}</a></li>
                <li><a href="#" className="hover:text-syrian-gold-light transition-colors">{t('homepage.footer.education')}</a></li>
                <li><a href="#" className="hover:text-syrian-gold-light transition-colors">{t('homepage.footer.industry')}</a></li>
                <li><a href="#" className="hover:text-syrian-gold-light transition-colors">{t('homepage.footer.agriculture')}</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4 text-syrian-gold-light">{t('homepage.footer.contact')}</h4>
              <p className="text-gray-400 mb-2"><EMAIL></p>
              <p className="text-gray-400">+963 11 123 4567</p>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>{t('homepage.footer.copyright')}</p>
          </div>
        </div>
      </footer>
      </div>
    </Layout>
  );
};

export default Index;
