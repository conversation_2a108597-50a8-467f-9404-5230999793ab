import React from 'react';
import { Layout } from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuthContext, useRoleAccess } from '@/components/auth/AuthProvider';
import { Link } from 'react-router-dom';
import { 
  Users, 
  FileText, 
  BarChart3, 
  Settings, 
  Shield, 
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  UserCheck,
  MessageSquare
} from 'lucide-react';

const AdminPanel = () => {
  const { userData } = useAuthContext();
  const { isAdmin } = useRoleAccess();

  // Mock data for demonstration
  const stats = [
    { label: 'إجمالي المستخدمين', value: '1,234', icon: Users, color: 'text-blue-600', change: '+12%' },
    { label: 'المشاكل النشطة', value: '89', icon: FileText, color: 'text-orange-600', change: '+5%' },
    { label: 'الخبراء المتاحون', value: '156', icon: UserCheck, color: 'text-green-600', change: '+8%' },
    { label: 'الحلول المقدمة', value: '342', icon: CheckCircle, color: 'text-purple-600', change: '+15%' }
  ];

  const recentActivity = [
    { type: 'user_registered', user: 'أحمد محمد', action: 'انضم كخبير جديد', time: 'منذ 5 دقائق', status: 'success' },
    { type: 'problem_submitted', user: 'وزارة الصحة', action: 'أرسلت مشكلة تقنية جديدة', time: 'منذ 15 دقيقة', status: 'info' },
    { type: 'solution_provided', user: 'د. فاطمة السيد', action: 'قدمت حل لمشكلة إدارة المستشفيات', time: 'منذ 30 دقيقة', status: 'success' },
    { type: 'content_flagged', user: 'نظام الإشراف', action: 'تم الإبلاغ عن محتوى مشبوه', time: 'منذ ساعة', status: 'warning' }
  ];

  const pendingActions = [
    { title: 'مراجعة طلبات الخبراء الجدد', count: 12, priority: 'high' },
    { title: 'الموافقة على المشاكل المرسلة', count: 8, priority: 'medium' },
    { title: 'مراجعة المحتوى المبلغ عنه', count: 3, priority: 'high' },
    { title: 'تحديث إعدادات النظام', count: 1, priority: 'low' }
  ];

  if (!isAdmin) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Card>
            <CardContent className="text-center py-12">
              <Shield className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">غير مصرح بالوصول</h2>
              <p className="text-gray-600">هذه الصفحة مخصصة للمديرين فقط</p>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">لوحة الإدارة</h1>
          <p className="text-gray-600 mt-2">مرحباً {userData?.name}، إليك نظرة عامة على المنصة</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600 flex items-center gap-1">
                      <TrendingUp className="w-3 h-3" />
                      {stat.change}
                    </p>
                  </div>
                  <stat.icon className={`w-8 h-8 ${stat.color}`} />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>الإجراءات السريعة</CardTitle>
                <CardDescription>المهام التي تحتاج إلى انتباهك</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button asChild variant="outline" className="h-auto p-4 justify-start">
                    <Link to="/admin/users">
                      <Users className="w-5 h-5 mr-3" />
                      <div className="text-right">
                        <div className="font-medium">إدارة المستخدمين</div>
                        <div className="text-sm text-gray-500">عرض وإدارة حسابات المستخدمين</div>
                      </div>
                    </Link>
                  </Button>

                  <Button asChild variant="outline" className="h-auto p-4 justify-start">
                    <Link to="/admin/analytics">
                      <BarChart3 className="w-5 h-5 mr-3" />
                      <div className="text-right">
                        <div className="font-medium">التحليلات والتقارير</div>
                        <div className="text-sm text-gray-500">إحصائيات مفصلة عن الاستخدام</div>
                      </div>
                    </Link>
                  </Button>

                  <Button asChild variant="outline" className="h-auto p-4 justify-start">
                    <Link to="/settings">
                      <Settings className="w-5 h-5 mr-3" />
                      <div className="text-right">
                        <div className="font-medium">إعدادات النظام</div>
                        <div className="text-sm text-gray-500">تكوين إعدادات المنصة</div>
                      </div>
                    </Link>
                  </Button>

                  <Button asChild variant="outline" className="h-auto p-4 justify-start">
                    <Link to="/admin/moderation">
                      <Shield className="w-5 h-5 mr-3" />
                      <div className="text-right">
                        <div className="font-medium">الإشراف على المحتوى</div>
                        <div className="text-sm text-gray-500">مراجعة والموافقة على المحتوى</div>
                      </div>
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>النشاط الأخير</CardTitle>
                <CardDescription>آخر الأحداث على المنصة</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                      <div className={`w-2 h-2 rounded-full ${
                        activity.status === 'success' ? 'bg-green-500' :
                        activity.status === 'warning' ? 'bg-yellow-500' :
                        'bg-blue-500'
                      }`} />
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.user}</p>
                        <p className="text-sm text-gray-600">{activity.action}</p>
                      </div>
                      <div className="text-xs text-gray-500">{activity.time}</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Pending Actions */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>المهام المعلقة</CardTitle>
                <CardDescription>العناصر التي تحتاج إلى مراجعة</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {pendingActions.map((action, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <p className="text-sm font-medium">{action.title}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge variant={
                            action.priority === 'high' ? 'destructive' :
                            action.priority === 'medium' ? 'default' :
                            'secondary'
                          }>
                            {action.priority === 'high' ? 'عالية' :
                             action.priority === 'medium' ? 'متوسطة' : 'منخفضة'}
                          </Badge>
                          <span className="text-xs text-gray-500">{action.count} عنصر</span>
                        </div>
                      </div>
                      {action.priority === 'high' && (
                        <AlertTriangle className="w-4 h-4 text-red-500" />
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* System Status */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>حالة النظام</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">قاعدة البيانات</span>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm text-green-600">متصلة</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">خدمة البريد الإلكتروني</span>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm text-green-600">تعمل</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">تخزين الملفات</span>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-yellow-500" />
                      <span className="text-sm text-yellow-600">بطيئة</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">النسخ الاحتياطي</span>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm text-green-600">آخر نسخة: اليوم</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default AdminPanel;