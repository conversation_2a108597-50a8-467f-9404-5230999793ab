import React from 'react'
import { RegisterForm } from '@/components/auth/RegisterForm'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useAuthContext } from '@/components/auth/AuthProvider'
import { useEffect } from 'react'

export default function Register() {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const { user } = useAuthContext()
  const redirectTo = searchParams.get('redirect') || '/'

  // Redirect if already authenticated
  useEffect(() => {
    if (user) {
      navigate(redirectTo, { replace: true })
    }
  }, [user, navigate, redirectTo])

  const handleSuccess = () => {
    // After successful registration, redirect to login with success message
    navigate('/auth/login?message=registration_success', { replace: true })
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            مركز سوريا الذكي
          </h1>
          <p className="text-gray-600">
              الذكي انضم إلى منصة الحلول التقنية وساهم في التطوير
          </p>
        </div>
        
        <RegisterForm onSuccess={handleSuccess} />
      </div>
    </div>
  )
}