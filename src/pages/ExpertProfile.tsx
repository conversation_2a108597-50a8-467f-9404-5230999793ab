import { usePara<PERSON>, useNavigate, <PERSON> } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { Award, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { expertOperations } from '@/lib/database';
import { ExpertProfileView } from '@/components/experts/ExpertProfileView';

interface Expert {
  id: string;
  user_id: string;
  expertise_areas: Array<{
    category: string;
    skills: string[];
    proficiencyLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
    yearsOfExperience: number;
  }>;
  experience_years: number;
  availability: 'available' | 'busy' | 'unavailable';
  rating: number;
  total_contributions: number;
  success_rate: number;
  response_time_hours: number;
  portfolio: Array<{
    title: string;
    description: string;
    technologies: string[];
    url?: string;
    completedAt: string;
  }>;
  certifications: Array<{
    name: string;
    issuer: string;
    issuedAt: string;
    expiresAt?: string;
    credentialId?: string;
    url?: string;
  }>;
  users: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    bio?: string;
    location: string;
    organization?: string;
    position?: string;
  };
}

const ExpertProfile = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [expert, setExpert] = useState<Expert | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) {
      setError('معرف الخبير غير صحيح');
      setLoading(false);
      return;
    }

    loadExpertProfile();
  }, [id]);

  const loadExpertProfile = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await expertOperations.getExpertProfile(id!);
      
      if (fetchError) {
        throw new Error(fetchError.message);
      }

      if (!data) {
        setError('لم يتم العثور على الخبير');
        return;
      }

      setExpert(data);
    } catch (error) {
      console.error('Error loading expert profile:', error);
      setError('حدث خطأ أثناء تحميل ملف الخبير');
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ أثناء تحميل ملف الخبير',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50" dir="rtl">
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link to="/experts" className="flex items-center space-x-4 space-x-reverse">
                <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-2 rounded-lg">
                  <Award className="w-6 h-6" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">ملف الخبير</h1>
                  <p className="text-sm text-gray-600">عرض تفاصيل الخبير</p>
                </div>
              </Link>
              <Link to="/experts" className="text-blue-600 hover:text-blue-800 flex items-center">
                <ArrowLeft className="w-4 h-4 ml-2" />
                العودة لدليل الخبراء
              </Link>
            </div>
          </div>
        </header>

        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل ملف الخبير...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !expert) {
    return (
      <div className="min-h-screen bg-gray-50" dir="rtl">
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link to="/experts" className="flex items-center space-x-4 space-x-reverse">
                <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-2 rounded-lg">
                  <Award className="w-6 h-6" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">ملف الخبير</h1>
                  <p className="text-sm text-gray-600">عرض تفاصيل الخبير</p>
                </div>
              </Link>
              <Link to="/experts" className="text-blue-600 hover:text-blue-800 flex items-center">
                <ArrowLeft className="w-4 h-4 ml-2" />
                العودة لدليل الخبراء
              </Link>
            </div>
          </div>
        </header>

        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Award className="w-8 h-8 text-red-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {error || 'لم يتم العثور على الخبير'}
            </h3>
            <p className="text-gray-600 mb-4">
              الخبير المطلوب غير موجود أو تم حذفه
            </p>
            <div className="flex gap-2 justify-center">
              <Button variant="outline" onClick={() => navigate(-1)}>
                العودة
              </Button>
              <Button asChild>
                <Link to="/experts">
                  تصفح الخبراء
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-2 rounded-lg">
                <Award className="w-6 h-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">{expert.users.name}</h1>
                <p className="text-sm text-gray-600">{expert.users.position || 'خبير تقني'}</p>
              </div>
            </div>
            <Link to="/experts" className="text-blue-600 hover:text-blue-800 flex items-center">
              <ArrowLeft className="w-4 h-4 ml-2" />
              العودة لدليل الخبراء
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <ExpertProfileView expert={expert} />
      </div>
    </div>
  );
};

export default ExpertProfile;