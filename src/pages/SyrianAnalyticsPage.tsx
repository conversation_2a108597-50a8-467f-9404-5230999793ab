/**
 * Syrian Analytics Page
 * 
 * Development page for viewing Syrian identity analytics dashboard.
 * This page should be removed or protected in production.
 */

import React, { useEffect } from 'react';
import { SyrianAnalyticsDashboard } from '@/components/analytics/SyrianAnalyticsDashboard';
import { initSyrianAnalyticsDevelopment } from '@/lib/analytics/init';

export default function SyrianAnalyticsPage() {
  // Initialize analytics for development
  useEffect(() => {
    initSyrianAnalyticsDevelopment();
  }, []);

  // Show warning in production
  if (process.env.NODE_ENV === 'production') {
    return (
      <div className="min-h-screen flex items-center justify-center p-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Analytics Not Available</h1>
          <p className="text-muted-foreground">
            Syrian analytics dashboard is only available in development mode.
          </p>
        </div>
      </div>
    );
  }

  return <SyrianAnalyticsDashboard />;
}
