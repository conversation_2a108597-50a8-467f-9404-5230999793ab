import React from 'react';
import { Layout } from '@/components/layout/Layout';
import { LazyProblemDashboard } from '@/components/lazy/LazyProblemDashboard';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { PatternBackground as SyrianPatternBackground } from '@/components/ui/pattern-background';

const Problems = () => {
  return (
    <Layout>
      <div className="relative min-h-screen">
        {/* Syrian pattern background for problems page */}
        <SyrianPatternBackground
          pattern="eblaScript"
          intensity="subtle"
          className="absolute inset-0 opacity-[0.01] pointer-events-none"
        />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative">
          <ErrorBoundary>
            <LazyProblemDashboard />
          </ErrorBoundary>
        </div>
      </div>
    </Layout>
  );
};

export default Problems;