import { Award } from 'lucide-react'
import { Link } from 'react-router-dom'
import { ExpertDashboard } from '@/components/experts/ExpertDashboard'
import { ProtectedRoute } from '@/components/auth/AuthProvider'

const ExpertDashboardPage = () => {
  return (
    <ProtectedRoute requireAuth={true}>
      <div className="min-h-screen bg-gray-50" dir="rtl">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link to="/" className="flex items-center space-x-4 space-x-reverse">
                <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-2 rounded-lg">
                  <Award className="w-6 h-6" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">لوحة تحكم الخبير</h1>
                  <p className="text-sm text-gray-600">إدارة مساهماتك وملفك الشخصي</p>
                </div>
              </Link>
              <div className="flex items-center gap-4">
                <Link to="/experts/matching-preferences" className="text-blue-600 hover:text-blue-800">
                  تفضيلات المطابقة
                </Link>
                <Link to="/experts" className="text-blue-600 hover:text-blue-800">
                  دليل الخبراء
                </Link>
                <Link to="/" className="text-blue-600 hover:text-blue-800">
                  الصفحة الرئيسية
                </Link>
              </div>
            </div>
          </div>
        </header>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <ExpertDashboard />
        </div>
      </div>
    </ProtectedRoute>
  )
}

export default ExpertDashboardPage