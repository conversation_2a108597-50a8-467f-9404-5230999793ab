
import { useState } from 'react';
import { ArrowRight, User, Calendar, Tag, ThumbsUp, MessageCircle, Share2, Download } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Link } from 'react-router-dom';

const EntryDetails = () => {
  const [comment, setComment] = useState('');
  const [likes, setLikes] = useState(24);
  const [isLiked, setIsLiked] = useState(false);

  // Mock data for demonstration
  const entry = {
    id: 1,
    title: 'تطوير نظام إدارة المستشفيات الرقمي',
    type: 'حل تقني',
    category: 'وزارة الصحة',
    description: `
      نظام شامل لإدارة المستشفيات يهدف إلى رقمنة جميع العمليات الطبية والإدارية في المستشفيات السورية. 
      
      يتضمن النظام الوحدات التالية:
      
      1. إدارة المرضى والملفات الطبية
      2. نظام الحجوزات والمواعيد
      3. إدارة المخزون الطبي والأدوية
      4. نظام الفوترة والمحاسبة
      5. تقارير وإحصائيات شاملة
      
      المميزات الرئيسية:
      - واجهة مستخدم باللغة العربية
      - متوافق مع المعايير الطبية الدولية
      - أمان عالي للبيانات الطبية
      - تكامل مع الأجهزة الطبية
      - تقارير في الوقت الفعلي
    `,
    expert: {
      name: 'د. أحمد الخطيب',
      title: 'خبير أنظمة المعلومات الطبية',
      experience: '15 سنة',
      location: 'دمشق، سوريا'
    },
    date: '2024-06-15',
    tags: ['نظم المعلومات الطبية', 'إدارة المستشفيات', 'رقمنة الصحة', 'قواعد البيانات'],
    technologies: ['Python', 'Django', 'PostgreSQL', 'React'],
    files: [
      { name: 'عرض النظام.pdf', size: '2.5 MB', type: 'presentation' },
      { name: 'دليل المستخدم.pdf', size: '1.8 MB', type: 'manual' },
      { name: 'المتطلبات التقنية.docx', size: '0.5 MB', type: 'document' }
    ],
    relatedEntries: [
      { id: 2, title: 'نظام إدارة الصيدليات', type: 'حل تقني' },
      { id: 3, title: 'تحديات الأمن السيبراني في المستشفيات', type: 'مشكلة' },
      { id: 4, title: 'تجربة رقمنة مستشفى الأسد الجامعي', type: 'دراسة حالة' }
    ]
  };

  const comments = [
    {
      id: 1,
      author: 'د. فاطمة السيد',
      content: 'حل ممتاز! هل يمكن تطبيقه في المستشفيات الصغيرة أيضاً؟',
      date: '2024-06-16',
      likes: 5
    },
    {
      id: 2,
      author: 'م. محمد العلي',
      content: 'أود معرفة المزيد عن التكامل مع الأجهزة الطبية. هل لديكم أمثلة؟',
      date: '2024-06-16',
      likes: 3
    }
  ];

  const handleLike = () => {
    setIsLiked(!isLiked);
    setLikes(isLiked ? likes - 1 : likes + 1);
  };

  const handleComment = () => {
    if (comment.trim()) {
      // Add comment logic here
      setComment('');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Link to="/" className="flex items-center text-blue-600 hover:text-blue-800">
              <ArrowRight className="w-5 h-5 ml-2" />
              العودة للصفحة الرئيسية
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Entry Header */}
            <Card>
              <CardHeader>
                <div className="flex flex-wrap gap-2 mb-4">
                  <Badge variant="default">{entry.type}</Badge>
                  <Badge variant="secondary">{entry.category}</Badge>
                </div>
                <CardTitle className="text-2xl leading-relaxed">
                  {entry.title}
                </CardTitle>
                <CardDescription className="flex items-center gap-4 text-base">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    {entry.date}
                  </div>
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4" />
                    {entry.expert.name}
                  </div>
                </CardDescription>
              </CardHeader>
            </Card>

            {/* Entry Content */}
            <Card>
              <CardContent className="pt-6">
                <div className="prose prose-lg max-w-none text-gray-800 leading-relaxed">
                  {entry.description.split('\n').map((paragraph, index) => (
                    <p key={index} className="mb-4 text-right">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Technologies Used */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">التقنيات المستخدمة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {entry.technologies.map((tech, index) => (
                    <Badge key={index} variant="outline">{tech}</Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Files */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">المرفقات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {entry.files.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          📄
                        </div>
                        <div>
                          <p className="font-medium">{file.name}</p>
                          <p className="text-sm text-gray-500">{file.size}</p>
                        </div>
                      </div>
                      <Button size="sm" variant="outline">
                        <Download className="w-4 h-4 ml-2" />
                        تحميل
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Button 
                      variant={isLiked ? "default" : "outline"}
                      onClick={handleLike}
                      className={isLiked ? "bg-red-500 hover:bg-red-600" : ""}
                    >
                      <ThumbsUp className="w-4 h-4 ml-2" />
                      إعجاب ({likes})
                    </Button>
                    <Button variant="outline">
                      <MessageCircle className="w-4 h-4 ml-2" />
                      تعليق ({comments.length})
                    </Button>
                  </div>
                  <Button variant="outline">
                    <Share2 className="w-4 h-4 ml-2" />
                    مشاركة
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Comments Section */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">التعليقات</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Add Comment */}
                <div className="space-y-3">
                  <Textarea
                    placeholder="أضف تعليقك هنا..."
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    rows={3}
                  />
                  <div className="flex justify-end">
                    <Button onClick={handleComment} disabled={!comment.trim()}>
                      إضافة تعليق
                    </Button>
                  </div>
                </div>

                <Separator />

                {/* Comments List */}
                <div className="space-y-4">
                  {comments.map((comment) => (
                    <div key={comment.id} className="flex gap-4">
                      <Avatar>
                        <AvatarFallback>
                          {comment.author.split(' ')[0][0]}{comment.author.split(' ')[1][0]}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="bg-gray-100 rounded-lg p-4">
                          <div className="flex justify-between items-start mb-2">
                            <p className="font-medium text-sm">{comment.author}</p>
                            <span className="text-xs text-gray-500">{comment.date}</span>
                          </div>
                          <p className="text-gray-800">{comment.content}</p>
                        </div>
                        <Button variant="ghost" size="sm" className="mt-2">
                          <ThumbsUp className="w-3 h-3 ml-1" />
                          {comment.likes}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Expert Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">معلومات الخبير</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3">
                  <Avatar className="w-12 h-12">
                    <AvatarFallback className="bg-blue-500 text-white">
                      {entry.expert.name.split(' ')[0][0]}{entry.expert.name.split(' ')[1][0]}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{entry.expert.name}</p>
                    <p className="text-sm text-gray-600">{entry.expert.title}</p>
                  </div>
                </div>
                <div className="space-y-2 text-sm text-gray-600">
                  <p>الخبرة: {entry.expert.experience}</p>
                  <p>الموقع: {entry.expert.location}</p>
                </div>
                <Button className="w-full">
                  <User className="w-4 h-4 ml-2" />
                  عرض الملف الشخصي
                </Button>
              </CardContent>
            </Card>

            {/* Tags */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">الكلمات المفتاحية</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {entry.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="cursor-pointer hover:bg-blue-100">
                      <Tag className="w-3 h-3 ml-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Related Entries */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">محتوى ذو صلة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {entry.relatedEntries.map((related) => (
                    <div key={related.id} className="p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors">
                      <Badge variant="outline" className="text-xs mb-2">{related.type}</Badge>
                      <p className="text-sm font-medium leading-relaxed">{related.title}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EntryDetails;
