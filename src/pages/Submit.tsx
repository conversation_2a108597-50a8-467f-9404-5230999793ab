import { useState } from 'react';
import { FileText, Upload, User, Tag, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';

const Submit = () => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    type: '',
    title: '',
    description: '',
    category: '',
    sector: '',
    expertName: '',
    expertEmail: '',
    tags: [],
    files: []
  });
  const [newTag, setNewTag] = useState('');

  const contentTypes = [
    { value: 'problem', label: 'مشكلة تقنية', icon: '🔧' },
    { value: 'hardware', label: 'حاجة عتاد', icon: '⚙️' },
    { value: 'solution', label: 'حل تقني', icon: '💡' },
    { value: 'presentation', label: 'عرض تقديمي', icon: '📊' },
    { value: 'qa', label: 'سؤال وجواب', icon: '❓' }
  ];

  const sectors = [
    'وزارة الصحة',
    'وزارة التربية',
    'وزارة المالية',
    'وزارة الاتصالات',
    'وزارة الصناعة',
    'وزارة الزراعة',
    'وزارة النقل',
    'القطاع المصرفي',
    'قطاع التأمين',
    'الشركات الناشئة'
  ];

  const categories = [
    'تطوير البرمجيات',
    'أمن المعلومات',
    'قواعد البيانات',
    'الذكاء الاصطناعي',
    'إنترنت الأشياء',
    'الحوسبة السحابية',
    'التجارة الإلكترونية',
    'الواقع المعزز',
    'البلوك تشين',
    'التحليل الرقمي'
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (!formData.type || !formData.title || !formData.description) {
      toast({
        title: "خطأ في الإرسال",
        description: "يرجى ملء جميع الحقول المطلوبة",
        variant: "destructive",
      });
      return;
    }

    // Simulate form submission
    toast({
      title: "تم الإرسال بنجاح",
      description: "شكراً لك! سيتم مراجعة مساهمتك قريباً",
    });

    // Reset form
    setFormData({
      type: '',
      title: '',
      description: '',
      category: '',
      sector: '',
      expertName: '',
      expertEmail: '',
      tags: [],
      files: []
    });
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData({
        ...formData,
        tags: [...formData.tags, newTag.trim()]
      });
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove) => {
    setFormData({
      ...formData,
      tags: formData.tags.filter(tag => tag !== tagToRemove)
    });
  };

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white p-2 rounded-lg">
                <Upload className="w-6 h-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">إضافة محتوى</h1>
                <p className="text-sm text-gray-600">شارك معرفتك وخبرتك</p>
              </div>
            </Link>
            <Link to="/" className="text-blue-600 hover:text-blue-800">
              العودة للصفحة الرئيسية
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Content Type Selection */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            ما نوع المحتوى الذي تريد إضافته؟
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {contentTypes.map((type) => (
              <Card
                key={type.value}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  formData.type === type.value 
                    ? 'ring-2 ring-blue-500 bg-blue-50' 
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => setFormData({...formData, type: type.value})}
              >
                <CardHeader className="text-center">
                  <div className="text-3xl mb-2">{type.icon}</div>
                  <CardTitle className="text-sm">{type.label}</CardTitle>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>

        {formData.type && (
          <form onSubmit={handleSubmit} className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>معلومات أساسية</CardTitle>
                <CardDescription>
                  أدخل المعلومات الأساسية حول المحتوى
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="title">العنوان *</Label>
                  <Input
                    id="title"
                    placeholder="أدخل عنوان واضح ومفصل"
                    value={formData.title}
                    onChange={(e) => setFormData({...formData, title: e.target.value})}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description">الوصف التفصيلي *</Label>
                  <Textarea
                    id="description"
                    placeholder={
                      formData.type === 'hardware' 
                        ? 'وصف نوع العتاد المطلوب، المواصفات، الغرض من الاستخدام، والكمية المطلوبة'
                        : 'اشرح بالتفصيل المشكلة أو الحل أو المحتوى الذي تريد مشاركته'
                    }
                    rows={6}
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="sector">القطاع</Label>
                    <Select value={formData.sector} onValueChange={(value) => setFormData({...formData, sector: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر القطاع" />
                      </SelectTrigger>
                      <SelectContent>
                        {sectors.map(sector => (
                          <SelectItem key={sector} value={sector}>{sector}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="category">الفئة التقنية</Label>
                    <Select value={formData.category} onValueChange={(value) => setFormData({...formData, category: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الفئة التقنية" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map(category => (
                          <SelectItem key={category} value={category}>{category}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>معلومات الخبير</CardTitle>
                <CardDescription>
                  أدخل معلومات الخبير أو المساهم
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="expertName">اسم الخبير</Label>
                    <Input
                      id="expertName"
                      placeholder="د. أحمد محمد"
                      value={formData.expertName}
                      onChange={(e) => setFormData({...formData, expertName: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label htmlFor="expertEmail">البريد الإلكتروني</Label>
                    <Input
                      id="expertEmail"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formData.expertEmail}
                      onChange={(e) => setFormData({...formData, expertEmail: e.target.value})}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>الكلمات المفتاحية</CardTitle>
                <CardDescription>
                  أضف كلمات مفتاحية لتسهيل البحث
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    placeholder="أضف كلمة مفتاحية"
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  />
                  <Button type="button" onClick={addTag} variant="outline">
                    <Tag className="w-4 h-4 ml-2" />
                    إضافة
                  </Button>
                </div>
                
                {formData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="cursor-pointer" onClick={() => removeTag(tag)}>
                        {tag} ×
                      </Badge>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>المرفقات</CardTitle>
                <CardDescription>
                  ارفق ملفات داعمة (اختياري)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 mb-2">اسحب الملفات هنا أو انقر للتحديد</p>
                  <p className="text-sm text-gray-500">PDF, DOC, PPT, أو ملفات الصور</p>
                  <Button type="button" variant="outline" className="mt-4">
                    <Upload className="w-4 h-4 ml-2" />
                    اختر الملفات
                  </Button>
                </div>
              </CardContent>
            </Card>

            <div className="flex justify-between items-center pt-6">
              <Button type="button" variant="outline" asChild>
                <Link to="/">إلغاء</Link>
              </Button>
              <Button type="submit" size="lg" className="bg-green-600 hover:bg-green-700">
                إرسال المساهمة
                <ChevronRight className="w-4 h-4 mr-2" />
              </Button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default Submit;
