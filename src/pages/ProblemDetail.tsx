import { use<PERSON><PERSON><PERSON>, use<PERSON>avi<PERSON>, <PERSON> } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { FileText, ArrowLeft, Users, Zap } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { useAuthContext } from '@/components/auth/AuthProvider';
import { problemOperations } from '@/lib/database';
import { ProblemDetailView } from '@/components/problems/ProblemDetailView';
import { ExpertMatches } from '@/components/experts/ExpertMatches';
import { ExpertMatchingDemo } from '@/components/experts/ExpertMatchingDemo';

interface Problem {
  id: string;
  title: string;
  description: string;
  category: string;
  sector: string;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  submitted_by: string;
  tags: string[];
  attachments: any[];
  created_at: string;
  updated_at: string;
  users?: {
    name: string;
    organization?: string;
  };
  solutions?: any[];
}

const ProblemDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [problem, setProblem] = useState<Problem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!id) {
      setError('معرف المشكلة غير صحيح');
      setLoading(false);
      return;
    }

    loadProblem();
  }, [id]);

  const loadProblem = async () => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: fetchError } = await problemOperations.getProblem(id!);
      
      if (fetchError) {
        throw new Error(fetchError.message);
      }

      if (!data) {
        setError('لم يتم العثور على المشكلة');
        return;
      }

      setProblem(data);
    } catch (error) {
      console.error('Error loading problem:', error);
      setError('حدث خطأ أثناء تحميل المشكلة');
      toast({
        title: 'خطأ في التحميل',
        description: 'حدث خطأ أثناء تحميل تفاصيل المشكلة',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50" dir="rtl">
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link to="/problems" className="flex items-center space-x-4 space-x-reverse">
                <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-2 rounded-lg">
                  <FileText className="w-6 h-6" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">تفاصيل المشكلة</h1>
                  <p className="text-sm text-gray-600">عرض تفاصيل المشكلة والحلول</p>
                </div>
              </Link>
              <Link to="/problems" className="text-blue-600 hover:text-blue-800 flex items-center">
                <ArrowLeft className="w-4 h-4 ml-2" />
                العودة للمشاكل
              </Link>
            </div>
          </div>
        </header>

        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل تفاصيل المشكلة...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !problem) {
    return (
      <div className="min-h-screen bg-gray-50" dir="rtl">
        <header className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <Link to="/problems" className="flex items-center space-x-4 space-x-reverse">
                <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-2 rounded-lg">
                  <FileText className="w-6 h-6" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">تفاصيل المشكلة</h1>
                  <p className="text-sm text-gray-600">عرض تفاصيل المشكلة والحلول</p>
                </div>
              </Link>
              <Link to="/problems" className="text-blue-600 hover:text-blue-800 flex items-center">
                <ArrowLeft className="w-4 h-4 ml-2" />
                العودة للمشاكل
              </Link>
            </div>
          </div>
        </header>

        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <FileText className="w-8 h-8 text-red-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {error || 'لم يتم العثور على المشكلة'}
            </h3>
            <p className="text-gray-600 mb-4">
              المشكلة المطلوبة غير موجودة أو تم حذفها
            </p>
            <div className="flex gap-2 justify-center">
              <Button variant="outline" onClick={() => navigate(-1)}>
                العودة
              </Button>
              <Button asChild>
                <Link to="/problems">
                  تصفح المشاكل
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const { user } = useAuthContext();
  const isAdmin = user?.role === 'admin';

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-2 rounded-lg">
                <FileText className="w-6 h-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900 truncate max-w-md">
                  {problem.title}
                </h1>
                <p className="text-sm text-gray-600">{problem.category}</p>
              </div>
            </div>
            <Link to="/problems" className="text-blue-600 hover:text-blue-800 flex items-center">
              <ArrowLeft className="w-4 h-4 ml-2" />
              العودة للمشاكل
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs defaultValue="details" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="details">تفاصيل المشكلة</TabsTrigger>
            <TabsTrigger value="experts">الخبراء المطابقون</TabsTrigger>
            {isAdmin && (
              <TabsTrigger value="matching">نظام المطابقة</TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="details">
            <ProblemDetailView problem={problem} />
          </TabsContent>

          <TabsContent value="experts" className="space-y-6">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center gap-2 mb-4">
                <Users className="h-5 w-5 text-blue-600" />
                <h2 className="text-xl font-semibold">الخبراء المطابقون</h2>
              </div>
              <p className="text-gray-600 mb-6">
                الخبراء الذين تم مطابقتهم مع هذه المشكلة بناءً على خبرتهم وتفضيلاتهم
              </p>
              <ExpertMatches problemId={problem.id} />
            </div>
          </TabsContent>

          {isAdmin && (
            <TabsContent value="matching" className="space-y-6">
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center gap-2 mb-4">
                  <Zap className="h-5 w-5 text-purple-600" />
                  <h2 className="text-xl font-semibold">نظام المطابقة الذكي</h2>
                </div>
                <p className="text-gray-600 mb-6">
                  اختبر وإدارة نظام المطابقة الذكي للخبراء
                </p>
                <ExpertMatchingDemo problemId={problem.id} />
              </div>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </div>
  );
};

export default ProblemDetail;