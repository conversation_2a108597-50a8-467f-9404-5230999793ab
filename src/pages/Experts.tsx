
import { Award } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { LazyExpertDirectory } from '@/components/lazy/LazyExpertDirectory';
import { PatternBackground as SyrianPatternBackground } from '@/components/ui/pattern-background';

const Experts = () => {
  return (
    <div className="min-h-screen bg-gray-50 relative overflow-hidden" dir="rtl">
      {/* Syrian pattern background */}
      <SyrianPatternBackground
        pattern="palmyraColumns"
        intensity="subtle"
        className="absolute inset-0 opacity-[0.01] pointer-events-none"
      />

      {/* Header */}
      <header className="bg-white shadow-sm border-b relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-gradient-to-r from-syrian-gold to-syrian-red text-white p-2 rounded-lg syrian-hover-lift">
                <Award className="w-6 h-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-syrian-gold-dark syrian-text-shadow">دليل الخبراء</h1>
                <p className="text-sm text-gray-600">شبكة الخبراء التقنيين</p>
              </div>
            </Link>
            <Link to="/" className="text-syrian-gold hover:text-syrian-gold-dark transition-colors">
              العودة للصفحة الرئيسية
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative">
        {/* Expert Directory Component */}
        <LazyExpertDirectory />

        {/* Call to Action */}
        <div className="bg-gradient-to-r from-syrian-gold to-syrian-red text-white rounded-lg p-8 mt-12 text-center relative overflow-hidden">
          {/* CTA pattern background */}
          <SyrianPatternBackground
            pattern="damascusStar"
            intensity="subtle"
            className="absolute inset-0 opacity-[0.05] pointer-events-none"
          />
          <div className="relative">
            <h3 className="text-2xl font-bold mb-4 syrian-text-shadow">
              هل أنت خبير تقني؟
            </h3>
            <p className="text-lg mb-6 opacity-90">
              انضم إلى شبكة الخبراء وساعد في حل التحديات التقنية في سوريا
            </p>
            <Button size="lg" variant="secondary" className="text-syrian-gold-dark syrian-hover-lift" asChild>
              <Link to="/experts/profile/create">
                سجل كخبير
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Experts;
