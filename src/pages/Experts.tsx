
import { Award } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { LazyExpertDirectory } from '@/components/lazy/LazyExpertDirectory';

const Experts = () => {
  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-2 rounded-lg">
                <Award className="w-6 h-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">دليل الخبراء</h1>
                <p className="text-sm text-gray-600">شبكة الخبراء التقنيين</p>
              </div>
            </Link>
            <Link to="/" className="text-blue-600 hover:text-blue-800">
              العودة للصفحة الرئيسية
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Expert Directory Component */}
        <LazyExpertDirectory />

        {/* Call to Action */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg p-8 mt-12 text-center">
          <h3 className="text-2xl font-bold mb-4">
            هل أنت خبير تقني؟
          </h3>
          <p className="text-lg mb-6 opacity-90">
            انضم إلى شبكة الخبراء وساعد في حل التحديات التقنية في سوريا
          </p>
          <Button size="lg" variant="secondary" className="text-blue-600" asChild>
            <Link to="/experts/profile/create">
              سجل كخبير
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Experts;
