/**
 * Syrian Identity Design Tokens - Colors
 * 
 * Authentic Syrian cultural colors inspired by:
 * - Qasioun Mountain (Damascus backdrop)
 * - Traditional Damascus architecture
 * - Syrian flag heritage
 * - Umayyad Mosque patterns
 * - Palmyra stone colors
 * - Ebla archaeological heritage
 * 
 * All colors are WCAG AA compliant and support light/dark themes
 */

@layer base {
  :root {
    /* Syrian Qasioun Gold - Inspired by Qasioun Mountain sunset */
    --syrian-qasioun-gold-50: 45 100% 95%;   /* Very light gold */
    --syrian-qasioun-gold-500: 45 85% 55%;   /* Main gold */
    --syrian-qasioun-gold-900: 45 100% 25%;  /* Deep gold */

    /* Syrian Damascus Red - Traditional architecture brick */
    --syrian-damascus-red-50: 15 80% 95%;    /* Light terracotta */
    --syrian-damascus-red-500: 15 75% 50%;   /* Main red */
    --syrian-damascus-red-900: 15 85% 25%;   /* Deep red */

    /* Syrian Umayyad Green - Mosque garden inspiration */
    --syrian-umayyad-green-50: 140 60% 95%;  /* Light sage */
    --syrian-umayyad-green-500: 140 45% 45%; /* Main green */
    --syrian-umayyad-green-900: 140 60% 20%; /* Deep green */

    /* Syrian Palmyra Stone - Ancient limestone */
    --syrian-palmyra-stone-50: 35 25% 95%;   /* Light stone */
    --syrian-palmyra-stone-500: 35 20% 70%;  /* Main stone */
    --syrian-palmyra-stone-900: 35 30% 30%;  /* Deep stone */

    /* Syrian Ebla Blue - Archaeological heritage */
    --syrian-ebla-blue-50: 210 100% 95%;     /* Light blue */
    --syrian-ebla-blue-500: 210 80% 55%;     /* Main blue */
    --syrian-ebla-blue-900: 210 100% 25%;    /* Deep blue */

    /* Syrian Heritage Purple - Royal Ayyubid inspiration */
    --syrian-heritage-purple-50: 280 60% 95%; /* Light purple */
    --syrian-heritage-purple-500: 280 50% 50%; /* Main purple */
    --syrian-heritage-purple-900: 280 70% 25%; /* Deep purple */

    /* Semantic color mappings for light theme */
    --syrian-primary: var(--syrian-qasioun-gold-500);
    --syrian-primary-foreground: var(--syrian-qasioun-gold-900);
    --syrian-accent: var(--syrian-damascus-red-500);
    --syrian-accent-foreground: var(--syrian-damascus-red-900);
    --syrian-muted: var(--syrian-palmyra-stone-500);
    --syrian-muted-foreground: var(--syrian-palmyra-stone-900);
  }

  .dark {
    /* Adjusted Syrian colors for dark theme */
    --syrian-qasioun-gold-50: 45 80% 85%;    /* Dimmed for dark */
    --syrian-qasioun-gold-500: 45 70% 65%;   /* Brighter for contrast */
    --syrian-qasioun-gold-900: 45 90% 35%;   /* Lighter for readability */

    --syrian-damascus-red-50: 15 60% 85%;
    --syrian-damascus-red-500: 15 65% 60%;
    --syrian-damascus-red-900: 15 75% 35%;

    --syrian-umayyad-green-50: 140 50% 85%;
    --syrian-umayyad-green-500: 140 40% 55%;
    --syrian-umayyad-green-900: 140 50% 30%;

    --syrian-palmyra-stone-50: 35 20% 85%;
    --syrian-palmyra-stone-500: 35 15% 75%;
    --syrian-palmyra-stone-900: 35 25% 40%;

    --syrian-ebla-blue-50: 210 80% 85%;
    --syrian-ebla-blue-500: 210 70% 65%;
    --syrian-ebla-blue-900: 210 90% 35%;

    --syrian-heritage-purple-50: 280 50% 85%;
    --syrian-heritage-purple-500: 280 45% 60%;
    --syrian-heritage-purple-900: 280 60% 35%;

    /* Dark theme semantic mappings */
    --syrian-primary: var(--syrian-qasioun-gold-500);
    --syrian-primary-foreground: var(--syrian-qasioun-gold-50);
    --syrian-accent: var(--syrian-damascus-red-500);
    --syrian-accent-foreground: var(--syrian-damascus-red-50);
    --syrian-muted: var(--syrian-palmyra-stone-500);
    --syrian-muted-foreground: var(--syrian-palmyra-stone-50);
  }
}

/* Utility classes for Syrian colors */
@layer utilities {
  /* Background utilities */
  .bg-syrian-gold { background-color: hsl(var(--syrian-qasioun-gold-500)); }
  .bg-syrian-gold-light { background-color: hsl(var(--syrian-qasioun-gold-50)); }
  .bg-syrian-gold-dark { background-color: hsl(var(--syrian-qasioun-gold-900)); }
  
  .bg-syrian-red { background-color: hsl(var(--syrian-damascus-red-500)); }
  .bg-syrian-red-light { background-color: hsl(var(--syrian-damascus-red-50)); }
  .bg-syrian-red-dark { background-color: hsl(var(--syrian-damascus-red-900)); }
  
  .bg-syrian-green { background-color: hsl(var(--syrian-umayyad-green-500)); }
  .bg-syrian-green-light { background-color: hsl(var(--syrian-umayyad-green-50)); }
  .bg-syrian-green-dark { background-color: hsl(var(--syrian-umayyad-green-900)); }
  
  .bg-syrian-stone { background-color: hsl(var(--syrian-palmyra-stone-500)); }
  .bg-syrian-stone-light { background-color: hsl(var(--syrian-palmyra-stone-50)); }
  .bg-syrian-stone-dark { background-color: hsl(var(--syrian-palmyra-stone-900)); }
  
  .bg-syrian-blue { background-color: hsl(var(--syrian-ebla-blue-500)); }
  .bg-syrian-blue-light { background-color: hsl(var(--syrian-ebla-blue-50)); }
  .bg-syrian-blue-dark { background-color: hsl(var(--syrian-ebla-blue-900)); }
  
  .bg-syrian-purple { background-color: hsl(var(--syrian-heritage-purple-500)); }
  .bg-syrian-purple-light { background-color: hsl(var(--syrian-heritage-purple-50)); }
  .bg-syrian-purple-dark { background-color: hsl(var(--syrian-heritage-purple-900)); }

  /* Text color utilities */
  .text-syrian-gold { color: hsl(var(--syrian-qasioun-gold-500)); }
  .text-syrian-gold-light { color: hsl(var(--syrian-qasioun-gold-50)); }
  .text-syrian-gold-dark { color: hsl(var(--syrian-qasioun-gold-900)); }
  
  .text-syrian-red { color: hsl(var(--syrian-damascus-red-500)); }
  .text-syrian-red-light { color: hsl(var(--syrian-damascus-red-50)); }
  .text-syrian-red-dark { color: hsl(var(--syrian-damascus-red-900)); }
  
  .text-syrian-green { color: hsl(var(--syrian-umayyad-green-500)); }
  .text-syrian-green-light { color: hsl(var(--syrian-umayyad-green-50)); }
  .text-syrian-green-dark { color: hsl(var(--syrian-umayyad-green-900)); }
  
  .text-syrian-stone { color: hsl(var(--syrian-palmyra-stone-500)); }
  .text-syrian-stone-light { color: hsl(var(--syrian-palmyra-stone-50)); }
  .text-syrian-stone-dark { color: hsl(var(--syrian-palmyra-stone-900)); }
  
  .text-syrian-blue { color: hsl(var(--syrian-ebla-blue-500)); }
  .text-syrian-blue-light { color: hsl(var(--syrian-ebla-blue-50)); }
  .text-syrian-blue-dark { color: hsl(var(--syrian-ebla-blue-900)); }
  
  .text-syrian-purple { color: hsl(var(--syrian-heritage-purple-500)); }
  .text-syrian-purple-light { color: hsl(var(--syrian-heritage-purple-50)); }
  .text-syrian-purple-dark { color: hsl(var(--syrian-heritage-purple-900)); }

  /* Border utilities */
  .border-syrian-gold { border-color: hsl(var(--syrian-qasioun-gold-500)); }
  .border-syrian-red { border-color: hsl(var(--syrian-damascus-red-500)); }
  .border-syrian-green { border-color: hsl(var(--syrian-umayyad-green-500)); }
  .border-syrian-stone { border-color: hsl(var(--syrian-palmyra-stone-500)); }
  .border-syrian-blue { border-color: hsl(var(--syrian-ebla-blue-500)); }
  .border-syrian-purple { border-color: hsl(var(--syrian-heritage-purple-500)); }
}
