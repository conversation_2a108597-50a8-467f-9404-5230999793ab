/**
 * Syrian Identity Design Tokens - Motion & Animation
 * 
 * Performance-optimized animation tokens inspired by:
 * - Traditional Syrian dance rhythms
 * - Architectural movement patterns
 * - Natural flow of Arabic calligraphy
 * - Respectful, subtle cultural motion
 * 
 * All animations respect prefers-reduced-motion and performance budgets
 */

@layer base {
  :root {
    /* Syrian Motion Timing - Based on traditional rhythms */
    --syrian-motion-fast: 150ms;      /* Quick interactions */
    --syrian-motion-medium: 250ms;    /* Standard transitions */
    --syrian-motion-slow: 400ms;      /* Deliberate animations */
    --syrian-motion-extra-slow: 600ms; /* Ceremonial pace */

    /* Syrian Easing Functions - Inspired by Arabic calligraphy flow */
    --syrian-ease-gentle: cubic-bezier(0.25, 0.46, 0.45, 0.94);  /* Gentle flow */
    --syrian-ease-confident: cubic-bezier(0.23, 1, 0.32, 1);     /* Confident movement */
    --syrian-ease-traditional: cubic-bezier(0.4, 0, 0.2, 1);     /* Traditional ease */
    --syrian-ease-ceremonial: cubic-bezier(0.19, 1, 0.22, 1);    /* Ceremonial grace */

    /* Performance Constraints */
    --syrian-svg-paint-budget: 16ms;   /* 60fps paint time budget */
    --syrian-animation-delay: 0ms;     /* Default no delay */
    --syrian-animation-fill: both;     /* Fill mode for smooth starts/ends */
  }
}

/* Syrian Animation Utilities */
@layer utilities {
  /* Transition utilities with Syrian timing */
  .transition-syrian-fast {
    transition-duration: var(--syrian-motion-fast);
    transition-timing-function: var(--syrian-ease-gentle);
  }
  
  .transition-syrian-medium {
    transition-duration: var(--syrian-motion-medium);
    transition-timing-function: var(--syrian-ease-confident);
  }
  
  .transition-syrian-slow {
    transition-duration: var(--syrian-motion-slow);
    transition-timing-function: var(--syrian-ease-traditional);
  }
  
  .transition-syrian-ceremonial {
    transition-duration: var(--syrian-motion-extra-slow);
    transition-timing-function: var(--syrian-ease-ceremonial);
  }

  /* Syrian-inspired animations */
  @keyframes syrian-fade-in {
    from {
      opacity: 0;
      transform: translateY(8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes syrian-slide-in-rtl {
    from {
      opacity: 0;
      transform: translateX(16px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes syrian-slide-in-ltr {
    from {
      opacity: 0;
      transform: translateX(-16px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes syrian-pattern-pulse {
    0%, 100% {
      opacity: 0.03;
      transform: scale(1);
    }
    50% {
      opacity: 0.06;
      transform: scale(1.02);
    }
  }

  @keyframes syrian-gold-shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  /* Animation classes */
  .animate-syrian-fade-in {
    animation: syrian-fade-in var(--syrian-motion-medium) var(--syrian-ease-gentle) var(--syrian-animation-fill);
  }

  .animate-syrian-slide-in-rtl {
    animation: syrian-slide-in-rtl var(--syrian-motion-medium) var(--syrian-ease-confident) var(--syrian-animation-fill);
  }

  .animate-syrian-slide-in-ltr {
    animation: syrian-slide-in-ltr var(--syrian-motion-medium) var(--syrian-ease-confident) var(--syrian-animation-fill);
  }

  .animate-syrian-pattern-pulse {
    animation: syrian-pattern-pulse var(--syrian-motion-extra-slow) var(--syrian-ease-gentle) infinite;
  }

  .animate-syrian-gold-shimmer {
    animation: syrian-gold-shimmer 2s var(--syrian-ease-traditional) infinite;
    background: linear-gradient(
      90deg,
      transparent,
      hsl(var(--syrian-qasioun-gold-500) / 0.1),
      transparent
    );
    background-size: 200% 100%;
  }

  /* Hover and interaction states */
  .syrian-hover-lift {
    transition: transform var(--syrian-motion-fast) var(--syrian-ease-gentle),
                box-shadow var(--syrian-motion-fast) var(--syrian-ease-gentle);
  }

  .syrian-hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px hsl(var(--syrian-qasioun-gold-500) / 0.15);
  }

  .syrian-hover-glow {
    transition: box-shadow var(--syrian-motion-medium) var(--syrian-ease-confident);
  }

  .syrian-hover-glow:hover {
    box-shadow: 0 0 20px hsl(var(--syrian-qasioun-gold-500) / 0.3);
  }

  /* Focus states for accessibility */
  .syrian-focus-ring {
    transition: box-shadow var(--syrian-motion-fast) var(--syrian-ease-gentle);
  }

  .syrian-focus-ring:focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px hsl(var(--syrian-qasioun-gold-500) / 0.5);
  }

  /* Text enhancement utilities */
  .syrian-text-shadow {
    text-shadow: 0 1px 2px hsl(var(--syrian-qasioun-gold-500) / 0.1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  :root {
    --syrian-motion-fast: 0ms;
    --syrian-motion-medium: 0ms;
    --syrian-motion-slow: 0ms;
    --syrian-motion-extra-slow: 0ms;
  }

  .animate-syrian-pattern-pulse {
    animation: none;
  }

  .animate-syrian-gold-shimmer {
    animation: none;
    background: none;
  }

  .syrian-hover-lift:hover {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .syrian-hover-glow:hover {
    box-shadow: 0 0 0 2px currentColor;
  }
  
  .syrian-focus-ring:focus-visible {
    box-shadow: 0 0 0 2px currentColor;
  }
}
