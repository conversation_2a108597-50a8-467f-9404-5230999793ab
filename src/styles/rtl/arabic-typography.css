/**
 * Syrian Identity RTL & Arabic Typography
 * 
 * Comprehensive Arabic typography system optimized for Syrian cultural content.
 * Includes proper font loading, RTL layout support, and Arabic text rendering features.
 * 
 * Fonts Used:
 * - Cairo: Modern Arabic font with excellent readability
 * - Noto Ku<PERSON> Arabic: Traditional Kufi-style Arabic font
 * - System fallbacks for performance and compatibility
 */

/* Import Arabic fonts with font-display: swap for performance */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Kufi+Arabic:wght@300;400;500;600;700&display=swap');

@layer base {
  /* Arabic font family definitions */
  :root {
    /* Primary Arabic font stack */
    --font-arabic-primary: 'Cairo', 'Noto Kufi Arabic', 'Tahoma', 'Arial Unicode MS', sans-serif;
    
    /* Traditional Arabic font stack */
    --font-arabic-traditional: 'Noto Kufi Arabic', 'Cairo', 'Tahoma', 'Arial Unicode MS', sans-serif;
    
    /* Arabic font feature settings */
    --arabic-font-features: "liga" 1, "calt" 1, "dlig" 1, "kern" 1;
    
    /* Arabic text metrics */
    --arabic-line-height: 1.6;
    --arabic-letter-spacing: 0.01em;
    --arabic-word-spacing: 0.1em;
  }
}

/* Base Arabic text styling */
.arabic-text {
  font-family: var(--font-arabic-primary);
  font-feature-settings: var(--arabic-font-features);
  line-height: var(--arabic-line-height);
  letter-spacing: var(--arabic-letter-spacing);
  word-spacing: var(--arabic-word-spacing);
  direction: rtl;
  unicode-bidi: embed;
  text-align: right;
  
  /* Optimize for Arabic text rendering */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Traditional Arabic styling (for cultural content) */
.arabic-text-traditional {
  font-family: var(--font-arabic-traditional);
  font-feature-settings: var(--arabic-font-features);
  line-height: 1.7; /* Slightly more spacing for traditional text */
  direction: rtl;
  unicode-bidi: embed;
  text-align: right;
}

/* Arabic headings */
.arabic-heading {
  font-family: var(--font-arabic-primary);
  font-feature-settings: var(--arabic-font-features);
  font-weight: 600;
  line-height: 1.4;
  direction: rtl;
  unicode-bidi: embed;
  text-align: right;
}

.arabic-heading-traditional {
  font-family: var(--font-arabic-traditional);
  font-feature-settings: var(--arabic-font-features);
  font-weight: 500;
  line-height: 1.5;
  direction: rtl;
  unicode-bidi: embed;
  text-align: right;
}

/* Arabic numerals support */
.arabic-numerals {
  font-variant-numeric: normal;
  font-feature-settings: var(--arabic-font-features), "numr" 0, "dnom" 0;
}

.arabic-indic-numerals {
  font-variant-numeric: normal;
  font-feature-settings: var(--arabic-font-features), "anum" 1;
}

/* RTL layout utilities */
.rtl {
  direction: rtl;
  unicode-bidi: embed;
}

.ltr {
  direction: ltr;
  unicode-bidi: embed;
}

.bidi-isolate {
  unicode-bidi: isolate;
}

.bidi-override {
  unicode-bidi: bidi-override;
}

/* RTL-aware spacing utilities */
.rtl-space-x-2 > * + * {
  margin-right: 0.5rem;
  margin-left: 0;
}

.rtl-space-x-4 > * + * {
  margin-right: 1rem;
  margin-left: 0;
}

.rtl-space-x-6 > * + * {
  margin-right: 1.5rem;
  margin-left: 0;
}

/* RTL-aware padding utilities */
.rtl-pl-4 {
  padding-right: 1rem;
  padding-left: 0;
}

.rtl-pr-4 {
  padding-left: 1rem;
  padding-right: 0;
}

.rtl-px-4 {
  padding-right: 1rem;
  padding-left: 1rem;
}

/* RTL-aware margin utilities */
.rtl-ml-4 {
  margin-right: 1rem;
  margin-left: 0;
}

.rtl-mr-4 {
  margin-left: 1rem;
  margin-right: 0;
}

/* RTL-aware border utilities */
.rtl-border-l {
  border-right: 1px solid;
  border-left: none;
}

.rtl-border-r {
  border-left: 1px solid;
  border-right: none;
}

/* RTL-aware positioning */
.rtl-left-0 {
  right: 0;
  left: auto;
}

.rtl-right-0 {
  left: 0;
  right: auto;
}

/* Arabic text size variants */
.arabic-text-xs {
  font-size: 0.75rem;
  line-height: 1.7;
}

.arabic-text-sm {
  font-size: 0.875rem;
  line-height: 1.65;
}

.arabic-text-base {
  font-size: 1rem;
  line-height: 1.6;
}

.arabic-text-lg {
  font-size: 1.125rem;
  line-height: 1.6;
}

.arabic-text-xl {
  font-size: 1.25rem;
  line-height: 1.55;
}

.arabic-text-2xl {
  font-size: 1.5rem;
  line-height: 1.5;
}

.arabic-text-3xl {
  font-size: 1.875rem;
  line-height: 1.45;
}

/* Arabic weight variants */
.arabic-font-light {
  font-weight: 300;
}

.arabic-font-normal {
  font-weight: 400;
}

.arabic-font-medium {
  font-weight: 500;
}

.arabic-font-semibold {
  font-weight: 600;
}

.arabic-font-bold {
  font-weight: 700;
}

/* Mixed content (Arabic + English) */
.mixed-content {
  unicode-bidi: plaintext;
  text-align: start;
}

.mixed-content .arabic {
  direction: rtl;
  unicode-bidi: embed;
}

.mixed-content .english {
  direction: ltr;
  unicode-bidi: embed;
}

/* Form elements RTL support */
.rtl input[type="text"],
.rtl input[type="email"],
.rtl input[type="password"],
.rtl textarea {
  direction: rtl;
  text-align: right;
}

.rtl input[type="number"] {
  direction: ltr; /* Numbers should remain LTR */
  text-align: left;
}

/* RTL-aware flexbox utilities */
.rtl-flex-row-reverse {
  flex-direction: row-reverse;
}

.rtl-justify-start {
  justify-content: flex-end;
}

.rtl-justify-end {
  justify-content: flex-start;
}

.rtl-items-start {
  align-items: flex-end;
}

.rtl-items-end {
  align-items: flex-start;
}

/* RTL-aware grid utilities */
.rtl-grid-flow-col-reverse {
  grid-auto-flow: column reverse;
}

/* Syrian cultural text styling */
.syrian-cultural-text {
  font-family: var(--font-arabic-traditional);
  font-feature-settings: var(--arabic-font-features);
  line-height: 1.7;
  direction: rtl;
  unicode-bidi: embed;
  text-align: right;
  color: hsl(var(--syrian-palmyra-stone-900));
}

.syrian-cultural-text.light-theme {
  color: hsl(var(--syrian-palmyra-stone-900));
}

.syrian-cultural-text.dark-theme {
  color: hsl(var(--syrian-palmyra-stone-50));
}

/* Responsive Arabic typography */
@media (max-width: 640px) {
  .arabic-text {
    font-size: 0.9rem;
    line-height: 1.65;
  }
  
  .arabic-heading {
    font-size: 1.1rem;
    line-height: 1.45;
  }
}

@media (max-width: 480px) {
  .arabic-text {
    font-size: 0.85rem;
    line-height: 1.7;
  }
  
  .arabic-heading {
    font-size: 1rem;
    line-height: 1.5;
  }
}

/* Print styles for Arabic text */
@media print {
  .arabic-text,
  .arabic-heading {
    font-family: 'Times New Roman', serif;
    color: black !important;
    background: transparent !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .arabic-text {
    font-weight: 500;
    letter-spacing: 0.02em;
  }
  
  .arabic-heading {
    font-weight: 700;
  }
}
