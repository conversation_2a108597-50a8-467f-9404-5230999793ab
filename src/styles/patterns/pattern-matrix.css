/**
 * Syrian Identity Pattern Matrix
 * 
 * Performance-optimized CSS for Syrian cultural patterns with responsive degradation.
 * Implements the pattern usage matrix with automatic performance optimization.
 * 
 * Performance Features:
 * - CSS containment for isolated rendering
 * - Hardware acceleration only where beneficial
 * - Automatic pattern degradation on mobile
 * - Respects user motion preferences
 * - Paint time budget enforcement (≤16ms)
 */

@layer components {
  /* Base pattern container */
  .syrian-pattern-container {
    /* CSS containment for performance isolation */
    contain: layout style paint;
    
    /* Ensure patterns don't affect layout */
    position: relative;
    overflow: hidden;
    
    /* Performance optimization */
    will-change: auto; /* Only set when animating */
    backface-visibility: hidden;
    
    /* Accessibility */
    pointer-events: none;
    user-select: none;
  }

  /* Pattern base styles */
  .syrian-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-repeat: repeat;
    background-position: 0 0;
    opacity: var(--pattern-opacity, 0.03);
    
    /* Performance optimization */
    transform: translateZ(0); /* Force hardware acceleration only when needed */
    
    /* Smooth transitions */
    transition: opacity var(--syrian-motion-medium) var(--syrian-ease-gentle);
  }

  /* Damascus Star Pattern */
  .pattern-damascus-star {
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='damascus-star-pattern' x='0' y='0' width='60' height='60' patternUnits='userSpaceOnUse'%3E%3Cg fill='currentColor' opacity='0.03'%3E%3Cpolygon points='30,5 35,20 50,15 40,30 55,35 40,40 50,55 35,50 30,65 25,50 10,55 20,40 5,35 20,30 10,15 25,20'/%3E%3Cpolygon points='30,15 40,30 30,45 20,30' opacity='0.5'/%3E%3Ccircle cx='30' cy='30' r='3' opacity='0.7'/%3E%3C/g%3E%3Cg fill='currentColor' opacity='0.02'%3E%3Cpolygon points='0,0 5,10 15,5 10,15 20,20 10,25 15,35 5,30 0,40 -5,30 -15,35 -10,25 -20,20 -10,15 -15,5 -5,10'/%3E%3Cpolygon points='60,0 65,10 75,5 70,15 80,20 70,25 75,35 65,30 60,40 55,30 45,35 50,25 40,20 50,15 45,5 55,10'/%3E%3Cpolygon points='0,60 5,70 15,65 10,75 20,80 10,85 15,95 5,90 0,100 -5,90 -15,95 -10,85 -20,80 -10,75 -15,65 -5,70'/%3E%3Cpolygon points='60,60 65,70 75,65 70,75 80,80 70,85 75,95 65,90 60,100 55,90 45,95 50,85 40,80 50,75 45,65 55,70'/%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3C/svg%3E");
    background-size: var(--pattern-size, 60px);
    --pattern-opacity: var(--damascus-star-opacity, 0.04);
  }

  /* Palmyra Columns Pattern */
  .pattern-palmyra-columns {
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 80 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='palmyra-columns-pattern' x='0' y='0' width='80' height='40' patternUnits='userSpaceOnUse'%3E%3Cg fill='currentColor' opacity='0.03'%3E%3Crect x='10' y='25' width='8' height='15'/%3E%3Cellipse cx='14' cy='25' rx='12' ry='4'/%3E%3Crect x='6' y='21' width='16' height='4' rx='2'/%3E%3Ccircle cx='10' cy='23' r='1' opacity='0.5'/%3E%3Ccircle cx='18' cy='23' r='1' opacity='0.5'/%3E%3Crect x='50' y='25' width='8' height='15'/%3E%3Cellipse cx='54' cy='25' rx='12' ry='4'/%3E%3Crect x='46' y='21' width='16' height='4' rx='2'/%3E%3Ccircle cx='50' cy='23' r='1' opacity='0.5'/%3E%3Ccircle cx='58' cy='23' r='1' opacity='0.5'/%3E%3C/g%3E%3Cg fill='currentColor' opacity='0.02'%3E%3Crect x='22' y='22' width='24' height='2'/%3E%3Crect x='26' y='20' width='16' height='1'/%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3C/svg%3E");
    background-size: var(--pattern-size, 80px);
    --pattern-opacity: var(--palmyra-columns-opacity, 0.03);
  }

  /* Ebla Script Pattern */
  .pattern-ebla-script {
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 50 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='ebla-script-pattern' x='0' y='0' width='50' height='30' patternUnits='userSpaceOnUse'%3E%3Cg fill='currentColor' opacity='0.03'%3E%3Cg transform='translate(5,5)'%3E%3Crect x='0' y='0' width='2' height='8' rx='1'/%3E%3Crect x='3' y='2' width='6' height='1' rx='0.5'/%3E%3Crect x='3' y='5' width='4' height='1' rx='0.5'/%3E%3Crect x='10' y='0' width='1' height='6' rx='0.5'/%3E%3Crect x='12' y='3' width='3' height='1' rx='0.5'/%3E%3C/g%3E%3Cg transform='translate(25,8)'%3E%3Crect x='0' y='0' width='1' height='6' rx='0.5'/%3E%3Crect x='2' y='1' width='4' height='1' rx='0.5'/%3E%3Crect x='2' y='4' width='5' height='1' rx='0.5'/%3E%3Crect x='8' y='0' width='2' height='7' rx='1'/%3E%3Crect x='11' y='2' width='3' height='1' rx='0.5'/%3E%3C/g%3E%3Cg transform='translate(10,18)'%3E%3Crect x='0' y='0' width='3' height='1' rx='0.5'/%3E%3Crect x='0' y='3' width='2' height='1' rx='0.5'/%3E%3Crect x='4' y='0' width='1' height='5' rx='0.5'/%3E%3Crect x='6' y='2' width='4' height='1' rx='0.5'/%3E%3C/g%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3C/svg%3E");
    background-size: var(--pattern-size, 50px);
    --pattern-opacity: var(--ebla-script-opacity, 0.04);
  }

  /* Geometric Weave Pattern */
  .pattern-geometric-weave {
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='geometric-weave-pattern' x='0' y='0' width='40' height='40' patternUnits='userSpaceOnUse'%3E%3Cg fill='currentColor' opacity='0.03'%3E%3Crect x='0' y='8' width='40' height='4' opacity='0.6'/%3E%3Crect x='0' y='20' width='40' height='4' opacity='0.6'/%3E%3Crect x='0' y='32' width='40' height='4' opacity='0.6'/%3E%3Crect x='8' y='0' width='4' height='40' opacity='0.4'/%3E%3Crect x='20' y='0' width='4' height='40' opacity='0.4'/%3E%3Crect x='32' y='0' width='4' height='40' opacity='0.4'/%3E%3Cpolygon points='10,10 14,6 18,10 14,14' opacity='0.8'/%3E%3Cpolygon points='22,10 26,6 30,10 26,14' opacity='0.8'/%3E%3Cpolygon points='10,22 14,18 18,22 14,26' opacity='0.8'/%3E%3Cpolygon points='22,22 26,18 30,22 26,26' opacity='0.8'/%3E%3Cpolygon points='10,34 14,30 18,34 14,38' opacity='0.8'/%3E%3Cpolygon points='22,34 26,30 30,34 26,38' opacity='0.8'/%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3C/svg%3E");
    background-size: var(--pattern-size, 40px);
    --pattern-opacity: var(--geometric-weave-opacity, 0.04);
  }

  /* Pattern intensity variants */
  .pattern-intensity-subtle {
    --pattern-opacity: 0.02;
  }

  .pattern-intensity-moderate {
    --pattern-opacity: 0.04;
  }

  .pattern-intensity-rich {
    --pattern-opacity: 0.06;
  }

  /* Pattern animation (respects reduced motion) */
  .pattern-animated {
    animation: syrian-pattern-pulse var(--syrian-motion-extra-slow) var(--syrian-ease-gentle) infinite;
  }

  /* Hover effects for interactive patterns */
  .pattern-interactive:hover {
    --pattern-opacity: calc(var(--pattern-opacity, 0.03) * 1.5);
  }

  /* Pattern combinations for layered effects */
  .pattern-layered {
    position: relative;
  }

  .pattern-layered::before,
  .pattern-layered::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  .pattern-layered.damascus-ebla::before {
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='damascus-star-pattern' x='0' y='0' width='60' height='60' patternUnits='userSpaceOnUse'%3E%3Cg fill='currentColor' opacity='0.03'%3E%3Cpolygon points='30,5 35,20 50,15 40,30 55,35 40,40 50,55 35,50 30,65 25,50 10,55 20,40 5,35 20,30 10,15 25,20'/%3E%3Cpolygon points='30,15 40,30 30,45 20,30' opacity='0.5'/%3E%3Ccircle cx='30' cy='30' r='3' opacity='0.7'/%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3C/svg%3E");
    background-size: 60px;
    opacity: 0.02;
  }

  .pattern-layered.damascus-ebla::after {
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 50 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='ebla-script-pattern' x='0' y='0' width='50' height='30' patternUnits='userSpaceOnUse'%3E%3Cg fill='currentColor' opacity='0.03'%3E%3Cg transform='translate(5,5)'%3E%3Crect x='0' y='0' width='2' height='8' rx='1'/%3E%3Crect x='3' y='2' width='6' height='1' rx='0.5'/%3E%3Crect x='3' y='5' width='4' height='1' rx='0.5'/%3E%3Crect x='10' y='0' width='1' height='6' rx='0.5'/%3E%3Crect x='12' y='3' width='3' height='1' rx='0.5'/%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3C/svg%3E");
    background-size: 50px;
    opacity: 0.015;
    transform: rotate(15deg) scale(0.8);
  }
}

/* Responsive Pattern Matrix */
@layer utilities {
  /* Desktop (default) - Full pattern support */
  :root {
    --damascus-star-opacity: 0.04;
    --palmyra-columns-opacity: 0.03;
    --ebla-script-opacity: 0.04;
    --geometric-weave-opacity: 0.04;
    --pattern-size: 60px;
  }

  /* Tablet - Reduced complexity */
  @media (max-width: 1024px) {
    :root {
      --damascus-star-opacity: 0.03;
      --palmyra-columns-opacity: 0.03;
      --ebla-script-opacity: 0.03;
      --geometric-weave-opacity: 0.02;
      --pattern-size: 50px;
    }
    
    /* Disable complex patterns on tablet */
    .pattern-geometric-weave {
      display: none;
    }
  }

  /* Mobile - Minimal patterns only */
  @media (max-width: 768px) {
    :root {
      --damascus-star-opacity: 0.02;
      --palmyra-columns-opacity: 0;
      --ebla-script-opacity: 0.02;
      --geometric-weave-opacity: 0;
      --pattern-size: 40px;
    }
    
    /* Disable complex patterns on mobile */
    .pattern-palmyra-columns,
    .pattern-geometric-weave {
      display: none;
    }
    
    /* Disable animations on mobile */
    .pattern-animated {
      animation: none;
    }
  }

  /* Small mobile - Patterns disabled */
  @media (max-width: 480px) {
    :root {
      --damascus-star-opacity: 0.01;
      --palmyra-columns-opacity: 0;
      --ebla-script-opacity: 0.01;
      --geometric-weave-opacity: 0;
      --pattern-size: 30px;
    }
    
    /* Minimal patterns only */
    .pattern-palmyra-columns,
    .pattern-geometric-weave,
    .pattern-layered {
      display: none;
    }
  }
}

/* Accessibility and Performance */
@layer utilities {
  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .pattern-animated {
      animation: none;
    }
    
    .pattern-interactive:hover {
      transition: none;
    }
    
    .syrian-pattern {
      transition: none;
    }
  }

  /* High contrast mode */
  @media (prefers-contrast: high) {
    .syrian-pattern {
      opacity: 0;
    }
  }

  /* Forced colors mode */
  @media (forced-colors: active) {
    .syrian-pattern {
      display: none;
    }
  }

  /* Print styles */
  @media print {
    .syrian-pattern,
    .pattern-damascus-star,
    .pattern-palmyra-columns,
    .pattern-ebla-script,
    .pattern-geometric-weave {
      display: none !important;
    }
  }

  /* Performance monitoring classes */
  .pattern-performance-monitor {
    /* Add performance monitoring attributes */
    --pattern-paint-start: 0;
    --pattern-paint-budget: 16ms;
  }

  /* GPU acceleration control */
  .pattern-gpu-accelerated {
    transform: translateZ(0);
    will-change: opacity, transform;
  }

  .pattern-cpu-only {
    transform: none;
    will-change: auto;
  }
}
