/* Internationalization Styles for Syria Smart Center */

/* RTL/LTR Base Styles */
.rtl {
  direction: rtl;
  text-align: right;
}

.ltr {
  direction: ltr;
  text-align: left;
}

/* Font Families */
.rtl {
  font-family: 'Segoe UI', '<PERSON>hom<PERSON>', 'Arial', sans-serif;
}

.ltr {
  font-family: 'Inter', 'Segoe UI', 'Roboto', sans-serif;
}

/* Spacing Adjustments for RTL */
.rtl .space-x-2 > * + * {
  margin-right: 0.5rem;
  margin-left: 0;
}

.rtl .space-x-4 > * + * {
  margin-right: 1rem;
  margin-left: 0;
}

.rtl .space-x-reverse > * + * {
  margin-right: 0.5rem;
  margin-left: 0;
}

/* Margin and Padding Adjustments */
.rtl .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

.rtl .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

.rtl .pr-10 {
  padding-right: 0;
  padding-left: 2.5rem;
}

.rtl .pl-10 {
  padding-left: 0;
  padding-right: 2.5rem;
}

/* Icon Positioning for RTL */
.rtl .absolute.right-3 {
  right: auto;
  left: 0.75rem;
}

.rtl .absolute.left-3 {
  left: auto;
  right: 0.75rem;
}

/* Flexbox Direction Adjustments */
.rtl .flex-row-reverse {
  flex-direction: row;
}

.rtl .flex-row {
  flex-direction: row-reverse;
}

/* Text Alignment */
.rtl .text-left {
  text-align: right;
}

.rtl .text-right {
  text-align: left;
}

/* Border Radius for RTL */
.rtl .rounded-l-lg {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

.rtl .rounded-r-lg {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

/* Dropdown Menu Positioning */
.rtl [data-radix-popper-content-wrapper] {
  transform-origin: right top;
}

.ltr [data-radix-popper-content-wrapper] {
  transform-origin: left top;
}

/* Language Switcher Specific Styles */
.language-switcher {
  position: relative;
}

.language-switcher .flag {
  display: inline-block;
  margin-right: 0.5rem;
  font-size: 1.2em;
}

.rtl .language-switcher .flag {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Navigation Link Styles */
.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  user-select: none;
  position: relative;
  z-index: 10;
}

.nav-link:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: rgba(0, 0, 0, 0.9);
}

.nav-link:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Mobile Navigation Adjustments */
@media (max-width: 768px) {
  .rtl .mobile-nav {
    text-align: right;
  }
  
  .ltr .mobile-nav {
    text-align: left;
  }
}

/* Form Input RTL Support */
.rtl input[type="text"],
.rtl input[type="email"],
.rtl input[type="password"],
.rtl textarea,
.rtl select {
  text-align: right;
}

.ltr input[type="text"],
.ltr input[type="email"],
.ltr input[type="password"],
.ltr textarea,
.ltr select {
  text-align: left;
}

/* Search Input Icon Positioning */
.rtl .search-input-container .search-icon {
  right: auto;
  left: 0.75rem;
}

.ltr .search-input-container .search-icon {
  left: auto;
  right: 0.75rem;
}

/* Card and Content Alignment */
.rtl .card-content {
  text-align: right;
}

.ltr .card-content {
  text-align: left;
}

/* Badge and Tag Positioning */
.rtl .badge-container {
  justify-content: flex-start;
}

.ltr .badge-container {
  justify-content: flex-end;
}

/* Animation Adjustments for RTL */
.rtl .slide-in-right {
  animation: slideInLeft 0.3s ease-out;
}

.rtl .slide-in-left {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Tooltip Positioning */
.rtl [data-tooltip] {
  text-align: right;
}

.ltr [data-tooltip] {
  text-align: left;
}

/* Print Styles */
@media print {
  .rtl {
    direction: rtl;
  }
  
  .ltr {
    direction: ltr;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .nav-link:hover {
    background-color: rgba(0, 0, 0, 0.1);
    border: 1px solid currentColor;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    transition: none;
  }
  
  .slide-in-right,
  .slide-in-left {
    animation: none;
  }
}

/* Focus Visible Support */
.nav-link:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Dark Mode Support (if implemented) */
@media (prefers-color-scheme: dark) {
  .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
  }
}