/* High Contrast Mode */
.high-contrast {
  --background: #000000;
  --foreground: #ffffff;
  --card: #1a1a1a;
  --card-foreground: #ffffff;
  --popover: #1a1a1a;
  --popover-foreground: #ffffff;
  --primary: #ffffff;
  --primary-foreground: #000000;
  --secondary: #333333;
  --secondary-foreground: #ffffff;
  --muted: #333333;
  --muted-foreground: #cccccc;
  --accent: #444444;
  --accent-foreground: #ffffff;
  --destructive: #ff0000;
  --destructive-foreground: #ffffff;
  --border: #666666;
  --input: #333333;
  --ring: #ffffff;
}

.high-contrast * {
  border-color: var(--border) !important;
}

.high-contrast input,
.high-contrast textarea,
.high-contrast select {
  background-color: var(--input) !important;
  color: var(--foreground) !important;
  border: 2px solid var(--border) !important;
}

.high-contrast button {
  border: 2px solid var(--border) !important;
}

.high-contrast .bg-white {
  background-color: var(--background) !important;
  color: var(--foreground) !important;
}

.high-contrast .text-gray-600,
.high-contrast .text-gray-500,
.high-contrast .text-gray-400 {
  color: var(--muted-foreground) !important;
}

.high-contrast .bg-gray-50,
.high-contrast .bg-gray-100 {
  background-color: var(--muted) !important;
}

/* Reduced Motion */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Focus indicators for better accessibility */
.high-contrast *:focus {
  outline: 3px solid var(--ring) !important;
  outline-offset: 2px !important;
}

/* Ensure sufficient color contrast for links */
.high-contrast a {
  color: #00ffff !important;
  text-decoration: underline !important;
}

.high-contrast a:visited {
  color: #ff00ff !important;
}

.high-contrast a:hover,
.high-contrast a:focus {
  color: #ffff00 !important;
  background-color: #000000 !important;
}

/* High contrast for form elements */
.high-contrast input:focus,
.high-contrast textarea:focus,
.high-contrast select:focus {
  outline: 3px solid #ffff00 !important;
  outline-offset: 2px !important;
}

/* High contrast for buttons */
.high-contrast button:hover {
  background-color: var(--accent) !important;
  color: var(--accent-foreground) !important;
}

.high-contrast button:focus {
  outline: 3px solid #ffff00 !important;
  outline-offset: 2px !important;
}

/* Ensure icons are visible in high contrast */
.high-contrast svg {
  fill: currentColor !important;
  stroke: currentColor !important;
}

/* High contrast for search suggestions */
.high-contrast [role="listbox"] {
  background-color: var(--card) !important;
  border: 2px solid var(--border) !important;
}

.high-contrast [role="option"]:hover,
.high-contrast [role="option"]:focus {
  background-color: var(--accent) !important;
  color: var(--accent-foreground) !important;
}

/* High contrast for tabs */
.high-contrast [role="tab"][aria-selected="true"] {
  background-color: var(--primary) !important;
  color: var(--primary-foreground) !important;
  border: 2px solid var(--border) !important;
}

.high-contrast [role="tab"]:not([aria-selected="true"]) {
  background-color: var(--secondary) !important;
  color: var(--secondary-foreground) !important;
  border: 2px solid var(--border) !important;
}

/* High contrast for pagination */
.high-contrast [aria-current="page"] {
  background-color: var(--primary) !important;
  color: var(--primary-foreground) !important;
  border: 3px solid var(--ring) !important;
}

/* Responsive font sizes */
@media (max-width: 768px) {
  html[style*="font-size: 18px"] {
    font-size: 16px !important;
  }
  
  html[style*="font-size: 20px"] {
    font-size: 18px !important;
  }
}

/* Skip link for keyboard navigation */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary);
  color: var(--primary-foreground);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  font-weight: bold;
}

.skip-link:focus {
  top: 6px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus trap for modals and dropdowns */
.focus-trap {
  position: relative;
}

.focus-trap::before,
.focus-trap::after {
  content: '';
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

/* Ensure minimum touch target size */
@media (pointer: coarse) {
  button,
  input[type="button"],
  input[type="submit"],
  input[type="reset"],
  [role="button"],
  [role="tab"],
  [role="option"] {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Loading states with better accessibility */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.high-contrast .loading-skeleton {
  background: linear-gradient(90deg, var(--muted) 25%, var(--accent) 50%, var(--muted) 75%);
  background-size: 200% 100%;
}

.reduced-motion .loading-skeleton {
  animation: none;
  background: var(--muted);
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Improved focus indicators */
*:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
  border-radius: 2px;
}

.high-contrast *:focus-visible {
  outline: 3px solid #ffff00;
  outline-offset: 2px;
}

/* Better error states */
.error-state {
  border: 2px solid var(--destructive);
  background-color: rgba(255, 0, 0, 0.1);
}

.high-contrast .error-state {
  border: 3px solid var(--destructive);
  background-color: var(--background);
}

/* Success states */
.success-state {
  border: 2px solid #22c55e;
  background-color: rgba(34, 197, 94, 0.1);
}

.high-contrast .success-state {
  border: 3px solid #00ff00;
  background-color: var(--background);
}