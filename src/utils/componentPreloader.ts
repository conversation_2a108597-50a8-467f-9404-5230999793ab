/**
 * Component Preloader Utility
 * Preloads components based on user navigation patterns and route predictions
 */

import { preloadLibrariesForRoute } from './lazyLibraries';

// Component preload functions
const componentPreloaders = {
  // Problem-related components
  problemDashboard: () => import('@/components/problems/ProblemDashboard'),
  problemList: () => import('@/components/problems/ProblemList'),
  problemCard: () => import('@/components/problems/ProblemCard'),
  problemFilters: () => import('@/components/problems/ProblemFilters'),
  
  // Expert-related components
  expertDirectory: () => import('@/components/experts/ExpertDirectory'),
  expertProfile: () => import('@/components/experts/ExpertProfileView'),
  expertDashboard: () => import('@/components/experts/ExpertDashboard'),
  
  // Admin components
  adminStats: () => import('@/components/admin/AdminStats'),
  contentModeration: () => import('@/components/admin/ContentModeration'),
  userManagement: () => import('@/components/admin/UserManagement'),
  systemSettings: () => import('@/components/admin/SystemSettings'),
  analyticsDashboard: () => import('@/components/admin/AnalyticsDashboard'),
  
  // Search components
  searchResults: () => import('@/components/search/SearchResultsDisplay'),
  searchFilters: () => import('@/components/search/SearchFilters'),
  globalSearch: () => import('@/components/search/GlobalSearch'),
};

// Route-based preloading strategies
export const preloadStrategies = {
  // Preload when user hovers over navigation links
  onNavigationHover: (routeName: string) => {
    switch (routeName) {
      case 'problems':
        componentPreloaders.problemDashboard();
        componentPreloaders.problemList();
        preloadLibrariesForRoute('problems');
        break;
      case 'experts':
        componentPreloaders.expertDirectory();
        preloadLibrariesForRoute('experts');
        break;
      case 'admin':
        componentPreloaders.adminStats();
        preloadLibrariesForRoute('admin');
        break;
      case 'search':
        componentPreloaders.searchResults();
        componentPreloaders.globalSearch();
        break;
    }
  },
  
  // Preload based on user role and likely next actions
  onUserAuthentication: (userRole: string) => {
    // Always preload common components
    componentPreloaders.problemList();
    componentPreloaders.expertDirectory();
    
    if (userRole === 'admin') {
      // Preload admin components for admin users
      componentPreloaders.adminStats();
      componentPreloaders.contentModeration();
      componentPreloaders.userManagement();
      preloadLibrariesForRoute('admin');
    }
    
    if (userRole === 'expert') {
      // Preload expert-specific components
      componentPreloaders.expertDashboard();
      componentPreloaders.expertProfile();
    }
  },
  
  // Preload based on current page context
  onPageLoad: (currentRoute: string) => {
    switch (currentRoute) {
      case '/problems':
        // When on problems page, preload expert directory (likely next step)
        componentPreloaders.expertDirectory();
        break;
      case '/experts':
        // When on experts page, preload problem dashboard
        componentPreloaders.problemDashboard();
        break;
      case '/':
        // On home page, preload most common destinations
        componentPreloaders.problemDashboard();
        componentPreloaders.expertDirectory();
        break;
    }
  },
  
  // Preload during idle time
  onIdle: () => {
    // Use requestIdleCallback to preload during browser idle time
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        // Preload less critical components
        componentPreloaders.searchFilters();
        componentPreloaders.analyticsDashboard();
      });
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(() => {
        componentPreloaders.searchFilters();
        componentPreloaders.analyticsDashboard();
      }, 2000);
    }
  },
  
  // Preload based on user interaction patterns
  onUserInteraction: (interactionType: string, context?: any) => {
    switch (interactionType) {
      case 'search_focus':
        componentPreloaders.searchResults();
        componentPreloaders.searchFilters();
        break;
      case 'problem_card_hover':
        // User is likely to view problem details or look for experts
        componentPreloaders.expertDirectory();
        break;
      case 'expert_card_hover':
        // User might want to see expert profile
        componentPreloaders.expertProfile();
        break;
      case 'admin_menu_hover':
        if (context?.userRole === 'admin') {
          componentPreloaders.adminStats();
          componentPreloaders.contentModeration();
        }
        break;
    }
  }
};

// Preload manager class
export class ComponentPreloadManager {
  private preloadedComponents = new Set<string>();
  private preloadPromises = new Map<string, Promise<any>>();
  
  /**
   * Preload a component by name
   */
  async preload(componentName: keyof typeof componentPreloaders): Promise<void> {
    if (this.preloadedComponents.has(componentName)) {
      return; // Already preloaded
    }
    
    if (this.preloadPromises.has(componentName)) {
      return this.preloadPromises.get(componentName); // Already preloading
    }
    
    const preloadPromise = componentPreloaders[componentName]()
      .then(() => {
        this.preloadedComponents.add(componentName);
        this.preloadPromises.delete(componentName);
      })
      .catch((error) => {
        console.warn(`Failed to preload component ${componentName}:`, error);
        this.preloadPromises.delete(componentName);
      });
    
    this.preloadPromises.set(componentName, preloadPromise);
    return preloadPromise;
  }
  
  /**
   * Preload multiple components
   */
  async preloadMultiple(componentNames: (keyof typeof componentPreloaders)[]): Promise<void> {
    const promises = componentNames.map(name => this.preload(name));
    await Promise.allSettled(promises);
  }
  
  /**
   * Check if a component is preloaded
   */
  isPreloaded(componentName: keyof typeof componentPreloaders): boolean {
    return this.preloadedComponents.has(componentName);
  }
  
  /**
   * Get preload statistics
   */
  getStats() {
    return {
      preloadedCount: this.preloadedComponents.size,
      preloadingCount: this.preloadPromises.size,
      preloadedComponents: Array.from(this.preloadedComponents),
      preloadingComponents: Array.from(this.preloadPromises.keys())
    };
  }
}

// Global preload manager instance
export const preloadManager = new ComponentPreloadManager();

// Hook for using component preloading
export const useComponentPreloader = () => {
  const preload = (componentName: keyof typeof componentPreloaders) => {
    return preloadManager.preload(componentName);
  };
  
  const preloadMultiple = (componentNames: (keyof typeof componentPreloaders)[]) => {
    return preloadManager.preloadMultiple(componentNames);
  };
  
  const isPreloaded = (componentName: keyof typeof componentPreloaders) => {
    return preloadManager.isPreloaded(componentName);
  };
  
  const getStats = () => {
    return preloadManager.getStats();
  };
  
  return {
    preload,
    preloadMultiple,
    isPreloaded,
    getStats
  };
};

// Utility to set up automatic preloading based on navigation events
export const setupNavigationPreloading = () => {
  // Preload on link hover
  document.addEventListener('mouseover', (event) => {
    const target = event.target as HTMLElement;
    const link = target.closest('a[href]') as HTMLAnchorElement;
    
    if (link && link.href) {
      const url = new URL(link.href);
      const pathname = url.pathname;
      
      if (pathname.startsWith('/problems')) {
        preloadStrategies.onNavigationHover('problems');
      } else if (pathname.startsWith('/experts')) {
        preloadStrategies.onNavigationHover('experts');
      } else if (pathname.startsWith('/admin')) {
        preloadStrategies.onNavigationHover('admin');
      } else if (pathname.startsWith('/search')) {
        preloadStrategies.onNavigationHover('search');
      }
    }
  });
  
  // Preload during idle time
  preloadStrategies.onIdle();
  
  // Set up intersection observer for card hover preloading
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const element = entry.target as HTMLElement;
        
        if (element.classList.contains('problem-card')) {
          preloadStrategies.onUserInteraction('problem_card_hover');
        } else if (element.classList.contains('expert-card')) {
          preloadStrategies.onUserInteraction('expert_card_hover');
        }
      }
    });
  }, { threshold: 0.5 });
  
  // Observe cards when they're added to the DOM
  const cardObserver = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement;
          const cards = element.querySelectorAll('.problem-card, .expert-card');
          cards.forEach(card => observer.observe(card));
        }
      });
    });
  });
  
  cardObserver.observe(document.body, { childList: true, subtree: true });
};