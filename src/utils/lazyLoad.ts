import { lazy, ComponentType } from 'react';

/**
 * Enhanced lazy loading utility with preloading capabilities
 */

interface LazyComponentOptions {
  /**
   * Delay before loading the component (in milliseconds)
   * Useful for preventing flash of loading state on fast connections
   */
  delay?: number;
  
  /**
   * Whether to preload the component on hover/focus
   */
  preload?: boolean;
  
  /**
   * Custom retry logic for failed imports
   */
  retries?: number;
}

/**
 * Create a lazy-loaded component with enhanced features
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: LazyComponentOptions = {}
) {
  const { delay = 0, retries = 3 } = options;
  
  // Create a wrapper for the import function with retry logic
  const importWithRetry = async (): Promise<{ default: T }> => {
    let lastError: Error;
    
    for (let i = 0; i <= retries; i++) {
      try {
        // Add artificial delay if specified
        if (delay > 0) {
          await new Promise(resolve => setTimeout(resolve, delay));
        }
        
        return await importFn();
      } catch (error) {
        lastError = error as Error;
        
        // If it's a chunk loading error, wait before retrying
        if (error instanceof Error && error.message.includes('Loading chunk')) {
          await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        }
        
        // Don't retry on the last attempt
        if (i === retries) {
          break;
        }
      }
    }
    
    throw lastError!;
  };
  
  const LazyComponent = lazy(importWithRetry);
  
  // Add preload method to the component
  (LazyComponent as any).preload = importWithRetry;
  
  return LazyComponent;
}

/**
 * Preload multiple components
 */
export function preloadComponents(components: Array<{ preload?: () => Promise<any> }>) {
  return Promise.all(
    components
      .filter(component => component.preload)
      .map(component => component.preload!().catch(() => {})) // Ignore preload failures
  );
}

/**
 * Route-based preloading strategies
 */
export const preloadStrategies = {
  /**
   * Preload components when user hovers over navigation links
   */
  onHover: (componentLoader: () => Promise<any>) => {
    return () => {
      componentLoader().catch(() => {}); // Ignore preload failures
    };
  },
  
  /**
   * Preload components after a delay (idle time)
   */
  onIdle: (componentLoader: () => Promise<any>, delay = 2000) => {
    if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
      window.requestIdleCallback(() => {
        setTimeout(() => {
          componentLoader().catch(() => {});
        }, delay);
      });
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(() => {
        componentLoader().catch(() => {});
      }, delay);
    }
  },
  
  /**
   * Preload components when they're likely to be needed next
   */
  onRouteChange: (componentLoaders: Array<() => Promise<any>>) => {
    // Preload related components when navigating
    return () => {
      componentLoaders.forEach(loader => {
        loader().catch(() => {});
      });
    };
  },
};

/**
 * Common lazy loading configurations for different types of pages
 */
export const lazyConfigs = {
  // Fast loading for frequently accessed pages
  critical: { delay: 0, retries: 3, preload: true },
  
  // Standard loading for regular pages
  standard: { delay: 100, retries: 2, preload: false },
  
  // Slower loading for admin/less frequent pages
  deferred: { delay: 200, retries: 1, preload: false },
};