import React from 'react';
import { SimpleErrorBoundary, withSimpleErrorBoundary } from '@/components/common/SimpleErrorBoundary';
import { SimpleFormErrorBoundary } from '@/components/common/SimpleFormErrorBoundary';

/**
 * Configuration for error boundary wrapping
 */
interface ErrorBoundaryConfig {
  componentName?: string;
  enableReporting?: boolean;
  maxRetries?: number;
  variant?: 'component' | 'inline';
  isolateError?: boolean;
  routeName?: string;
}

/**
 * Wrap a component with appropriate error boundary based on its type
 */
export function wrapWithErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  config: ErrorBoundaryConfig = {}
) {
  const {
    componentName = Component.displayName || Component.name,
    enableReporting = true,
    maxRetries = 3,
    variant = 'component',
    isolateError = true,
    routeName,
  } = config;

  const level = routeName ? 'page' : variant === 'inline' ? 'inline' : 'component';

  return withSimpleErrorBoundary(Component, {
    level,
    maxRetries,
  });
}

/**
 * Decorator for class components
 */
export function ErrorBoundaryDecorator(config: ErrorBoundaryConfig = {}) {
  return function <T extends React.ComponentType<any>>(target: T): T {
    return wrapWithErrorBoundary(target, config) as T;
  };
}

/**
 * Hook to add error boundary to functional components
 */
export function useErrorBoundary(componentName?: string) {
  const [error, setError] = React.useState<Error | null>(null);

  const reportError = React.useCallback((error: Error, context?: Record<string, any>) => {
    console.error(`Error in ${componentName}:`, error);
    setError(error);
  }, [componentName]);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  // Throw error to be caught by error boundary
  if (error) {
    throw error;
  }

  return {
    reportError,
    clearError,
  };
}

/**
 * Higher-order component for critical components that should never fail
 */
export function withCriticalErrorHandling<P extends object>(
  Component: React.ComponentType<P>,
  fallbackComponent?: React.ComponentType<P>
) {
  return React.forwardRef<any, P>((props, ref) => {
    const [hasError, setHasError] = React.useState(false);

    React.useEffect(() => {
      const handleError = (event: ErrorEvent) => {
        console.error('Critical component error:', event.error);
        setHasError(true);
      };

      window.addEventListener('error', handleError);
      return () => window.removeEventListener('error', handleError);
    }, []);

    if (hasError && fallbackComponent) {
      const FallbackComponent = fallbackComponent;
      return React.createElement(FallbackComponent, props);
    }

    return (
      <SimpleErrorBoundary
        level="component"
        maxRetries={1}
      >
        <Component {...props} ref={ref} />
      </SimpleErrorBoundary>
    );
  });
}

/**
 * Async error handler for promises and async operations
 */
export class AsyncErrorHandler {
  private static instance: AsyncErrorHandler;
  private errorHandlers: Map<string, (error: Error) => void> = new Map();

  static getInstance(): AsyncErrorHandler {
    if (!AsyncErrorHandler.instance) {
      AsyncErrorHandler.instance = new AsyncErrorHandler();
    }
    return AsyncErrorHandler.instance;
  }

  registerHandler(key: string, handler: (error: Error) => void) {
    this.errorHandlers.set(key, handler);
  }

  unregisterHandler(key: string) {
    this.errorHandlers.delete(key);
  }

  async handleAsync<T>(
    operation: () => Promise<T>,
    context: string = 'unknown'
  ): Promise<T | null> {
    try {
      return await operation();
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      
      // Try to find a specific handler
      const handler = this.errorHandlers.get(context);
      if (handler) {
        handler(err);
      } else {
        // Default error handling
        console.error(`Async error in ${context}:`, err);
      }
      
      return null;
    }
  }
}

/**
 * Hook for handling async errors
 */
export function useAsyncErrorHandler(context: string = 'component') {
  const handler = React.useMemo(() => AsyncErrorHandler.getInstance(), []);

  const handleAsync = React.useCallback(
    async (operation: () => Promise<any>) => {
      return handler.handleAsync(operation, context);
    },
    [handler, context]
  );

  const registerErrorHandler = React.useCallback(
    (errorHandler: (error: Error) => void) => {
      handler.registerHandler(context, errorHandler);
      
      return () => {
        handler.unregisterHandler(context);
      };
    },
    [handler, context]
  );

  return {
    handleAsync,
    registerErrorHandler,
  };
}

/**
 * Error boundary for specific UI sections
 */
export function SectionErrorBoundary({
  children,
  sectionName,
  fallback,
}: {
  children: React.ReactNode;
  sectionName: string;
  fallback?: React.ReactNode;
}) {
  return (
    <SimpleErrorBoundary
      level="inline"
      maxRetries={1}
    >
      {children}
    </SimpleErrorBoundary>
  );
}

/**
 * Error boundary for form components
 */
export function FormErrorBoundary({
  children,
  formName,
}: {
  children: React.ReactNode;
  formName: string;
}) {
  return (
    <SimpleFormErrorBoundary
      formName={formName}
      maxRetries={2}
    >
      {children}
    </SimpleFormErrorBoundary>
  );
}

/**
 * Error boundary for data display components
 */
export function DataErrorBoundary({
  children,
  dataType,
}: {
  children: React.ReactNode;
  dataType: string;
}) {
  return (
    <SimpleErrorBoundary
      level="inline"
      maxRetries={1}
    >
      {children}
    </SimpleErrorBoundary>
  );
}

/**
 * Utility to create error-safe versions of components
 */
export function createErrorSafeComponent<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    name?: string;
    fallback?: React.ComponentType<P>;
    enableReporting?: boolean;
  } = {}
) {
  const {
    name = Component.displayName || Component.name,
    fallback,
    enableReporting = true,
  } = options;

  return React.forwardRef<any, P>((props, ref) => (
    <SimpleErrorBoundary
      level="component"
      maxRetries={2}
    >
      <Component {...props} ref={ref} />
    </SimpleErrorBoundary>
  ));
}
