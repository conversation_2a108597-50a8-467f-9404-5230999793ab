import React from 'react';

/**
 * Advanced Performance Monitoring System
 * Provides comprehensive performance tracking and optimization insights
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  type: 'timing' | 'counter' | 'gauge' | 'histogram';
  tags?: Record<string, string>;
}

interface ComponentPerformance {
  componentName: string;
  renderCount: number;
  totalRenderTime: number;
  averageRenderTime: number;
  lastRenderTime: number;
  slowRenders: number;
  mountTime: number;
  unmountTime?: number;
  memoryUsage?: number;
}

interface BundleMetrics {
  totalSize: number;
  gzippedSize: number;
  chunkSizes: Record<string, number>;
  loadTimes: Record<string, number>;
  cacheHitRate: number;
}

interface WebVitalsMetrics {
  FCP?: number; // First Contentful Paint
  LCP?: number; // Largest Contentful Paint
  FID?: number; // First Input Delay
  CLS?: number; // Cumulative Layout Shift
  TTFB?: number; // Time to First Byte
  TTI?: number; // Time to Interactive
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetric[] = [];
  private componentMetrics: Map<string, ComponentPerformance> = new Map();
  private bundleMetrics: BundleMetrics | null = null;
  private webVitals: WebVitalsMetrics = {};
  private observers: PerformanceObserver[] = [];
  private isEnabled: boolean = true;
  private maxMetrics: number = 1000;

  private constructor() {
    this.initializeWebVitals();
    this.initializeResourceTiming();
    this.initializeNavigationTiming();
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * Initialize Web Vitals monitoring
   */
  private initializeWebVitals() {
    if (typeof window === 'undefined') return;

    // First Contentful Paint
    this.observePerformanceEntry('paint', (entries) => {
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint');
      if (fcpEntry) {
        this.webVitals.FCP = fcpEntry.startTime;
        this.recordMetric('web_vitals_fcp', fcpEntry.startTime, 'timing');
      }
    });

    // Largest Contentful Paint
    this.observePerformanceEntry('largest-contentful-paint', (entries) => {
      const lcpEntry = entries[entries.length - 1];
      if (lcpEntry) {
        this.webVitals.LCP = lcpEntry.startTime;
        this.recordMetric('web_vitals_lcp', lcpEntry.startTime, 'timing');
      }
    });

    // First Input Delay
    this.observePerformanceEntry('first-input', (entries) => {
      const fidEntry = entries[0];
      if (fidEntry) {
        this.webVitals.FID = fidEntry.processingStart - fidEntry.startTime;
        this.recordMetric('web_vitals_fid', this.webVitals.FID, 'timing');
      }
    });

    // Layout Shift
    this.observePerformanceEntry('layout-shift', (entries) => {
      let clsValue = 0;
      entries.forEach(entry => {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      });
      this.webVitals.CLS = clsValue;
      this.recordMetric('web_vitals_cls', clsValue, 'gauge');
    });
  }

  /**
   * Initialize resource timing monitoring
   */
  private initializeResourceTiming() {
    if (typeof window === 'undefined') return;

    this.observePerformanceEntry('resource', (entries) => {
      entries.forEach(entry => {
        const resource = entry as PerformanceResourceTiming;
        this.recordMetric(`resource_load_time_${this.sanitizeResourceName(resource.name)}`, 
          resource.loadEventEnd - resource.loadEventStart, 'timing');
      });
    });
  }

  /**
   * Initialize navigation timing monitoring
   */
  private initializeNavigationTiming() {
    if (typeof window === 'undefined') return;

    this.observePerformanceEntry('navigation', (entries) => {
      const navigation = entries[0] as PerformanceNavigationTiming;
      if (navigation) {
        this.webVitals.TTFB = navigation.responseStart - navigation.requestStart;
        this.webVitals.TTI = navigation.loadEventEnd - navigation.navigationStart;
        
        this.recordMetric('navigation_ttfb', this.webVitals.TTFB, 'timing');
        this.recordMetric('navigation_tti', this.webVitals.TTI, 'timing');
      }
    });
  }

  /**
   * Observe performance entries
   */
  private observePerformanceEntry(type: string, callback: (entries: PerformanceEntry[]) => void) {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) return;

    try {
      const observer = new PerformanceObserver((list) => {
        callback(list.getEntries());
      });
      observer.observe({ entryTypes: [type] });
      this.observers.push(observer);
    } catch (error) {
      console.warn(`Failed to observe ${type} performance entries:`, error);
    }
  }

  /**
   * Record a performance metric
   */
  recordMetric(name: string, value: number, type: PerformanceMetric['type'], tags?: Record<string, string>) {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      type,
      tags
    };

    this.metrics.push(metric);

    // Limit metrics array size
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Log slow operations in development
    if (process.env.NODE_ENV === 'development' && type === 'timing' && value > 100) {
      console.warn(`Slow operation detected: ${name} took ${value.toFixed(2)}ms`);
    }
  }

  /**
   * Record component performance
   */
  recordComponentPerformance(componentName: string, renderTime: number, isMount: boolean = false) {
    if (!this.isEnabled) return;

    const existing = this.componentMetrics.get(componentName);
    const now = performance.now();

    if (existing) {
      existing.renderCount++;
      existing.totalRenderTime += renderTime;
      existing.averageRenderTime = existing.totalRenderTime / existing.renderCount;
      existing.lastRenderTime = renderTime;
      
      if (renderTime > 16) { // Slow render (more than one frame at 60fps)
        existing.slowRenders++;
      }
    } else {
      const componentPerf: ComponentPerformance = {
        componentName,
        renderCount: 1,
        totalRenderTime: renderTime,
        averageRenderTime: renderTime,
        lastRenderTime: renderTime,
        slowRenders: renderTime > 16 ? 1 : 0,
        mountTime: now
      };
      this.componentMetrics.set(componentName, componentPerf);
    }

    this.recordMetric(`component_render_${componentName}`, renderTime, 'timing', {
      component: componentName,
      isMount: isMount.toString()
    });
  }

  /**
   * Record component unmount
   */
  recordComponentUnmount(componentName: string) {
    const component = this.componentMetrics.get(componentName);
    if (component) {
      component.unmountTime = performance.now();
      const lifetime = component.unmountTime - component.mountTime;
      this.recordMetric(`component_lifetime_${componentName}`, lifetime, 'timing', {
        component: componentName
      });
    }
  }

  /**
   * Record bundle metrics
   */
  recordBundleMetrics(metrics: BundleMetrics) {
    this.bundleMetrics = metrics;
    this.recordMetric('bundle_total_size', metrics.totalSize, 'gauge');
    this.recordMetric('bundle_gzipped_size', metrics.gzippedSize, 'gauge');
    this.recordMetric('bundle_cache_hit_rate', metrics.cacheHitRate, 'gauge');
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary() {
    const summary = {
      webVitals: this.webVitals,
      componentMetrics: Array.from(this.componentMetrics.values()),
      bundleMetrics: this.bundleMetrics,
      totalMetrics: this.metrics.length,
      slowComponents: Array.from(this.componentMetrics.values())
        .filter(comp => comp.averageRenderTime > 16)
        .sort((a, b) => b.averageRenderTime - a.averageRenderTime),
      recentMetrics: this.metrics.slice(-50)
    };

    return summary;
  }

  /**
   * Get metrics by type
   */
  getMetricsByType(type: PerformanceMetric['type']): PerformanceMetric[] {
    return this.metrics.filter(metric => metric.type === type);
  }

  /**
   * Get metrics by name pattern
   */
  getMetricsByName(pattern: string): PerformanceMetric[] {
    const regex = new RegExp(pattern);
    return this.metrics.filter(metric => regex.test(metric.name));
  }

  /**
   * Clear all metrics
   */
  clearMetrics() {
    this.metrics = [];
    this.componentMetrics.clear();
    this.bundleMetrics = null;
    this.webVitals = {};
  }

  /**
   * Enable/disable monitoring
   */
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }

  /**
   * Export metrics for external analysis
   */
  exportMetrics() {
    return {
      metrics: this.metrics,
      componentMetrics: Object.fromEntries(this.componentMetrics),
      bundleMetrics: this.bundleMetrics,
      webVitals: this.webVitals,
      timestamp: Date.now()
    };
  }

  /**
   * Sanitize resource name for metric naming
   */
  private sanitizeResourceName(name: string): string {
    return name.replace(/[^a-zA-Z0-9_]/g, '_').toLowerCase();
  }

  /**
   * Cleanup observers
   */
  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();

// Utility functions
export function measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
  const start = performance.now();
  return fn().finally(() => {
    const duration = performance.now() - start;
    performanceMonitor.recordMetric(name, duration, 'timing');
  });
}

export function measureSync<T>(name: string, fn: () => T): T {
  const start = performance.now();
  try {
    return fn();
  } finally {
    const duration = performance.now() - start;
    performanceMonitor.recordMetric(name, duration, 'timing');
  }
}

// React hook for performance monitoring
export function usePerformanceMonitoring(componentName: string) {
  const startTime = performance.now();

  React.useEffect(() => {
    const mountTime = performance.now() - startTime;
    performanceMonitor.recordComponentPerformance(componentName, mountTime, true);

    return () => {
      performanceMonitor.recordComponentUnmount(componentName);
    };
  }, [componentName, startTime]);

  React.useEffect(() => {
    const renderTime = performance.now() - startTime;
    performanceMonitor.recordComponentPerformance(componentName, renderTime);
  });

  return {
    recordMetric: (name: string, value: number, type: PerformanceMetric['type']) => 
      performanceMonitor.recordMetric(`${componentName}_${name}`, value, type),
    getComponentMetrics: () => performanceMonitor.getPerformanceSummary().componentMetrics
      .find(comp => comp.componentName === componentName)
  };
}
