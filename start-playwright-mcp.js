#!/usr/bin/env node

// Start Playwright MCP server for Kiro
import { spawn } from 'child_process';

console.log('🚀 Starting Playwright MCP server...');

const server = spawn('npx', ['@playwright/mcp', '--config=proxy-mcp.config.json'], {
  stdio: 'inherit',
  env: {
    ...process.env,
    FASTMCP_LOG_LEVEL: 'INFO'
  }
});

server.on('close', (code) => {
  console.log(`\n🔚 Playwright MCP server exited with code ${code}`);
  process.exit(code);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down Playwright MCP server...');
  server.kill('SIGTERM');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down Playwright MCP server...');
  server.kill('SIGTERM');
});