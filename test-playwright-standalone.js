#!/usr/bin/env node

// Test Playwright MCP server standalone
import { spawn } from 'child_process';
import { setTimeout } from 'timers/promises';

console.log('🧪 Testing Playwright MCP server standalone on port 8932...');

const server = spawn('npx', ['@playwright/mcp', '--config=proxy-mcp.config.json'], {
  stdio: 'pipe',
  env: {
    ...process.env,
    FASTMCP_LOG_LEVEL: 'INFO'
  }
});

let serverStarted = false;

server.stdout.on('data', (data) => {
  const output = data.toString();
  console.log('📤 Server output:', output);
  
  if (output.includes('Listening on') || output.includes('8932')) {
    serverStarted = true;
    console.log('✅ Server started successfully!');
  }
});

server.stderr.on('data', (data) => {
  console.error('❌ Server error:', data.toString());
});

server.on('close', (code) => {
  console.log(`🔚 Server process exited with code ${code}`);
});

// Wait 15 seconds then kill the server
setTimeout(15000).then(() => {
  if (serverStarted) {
    console.log('✅ Test passed - server started successfully');
  } else {
    console.log('❌ Test failed - server did not start properly');
  }
  
  server.kill('SIGTERM');
  process.exit(serverStarted ? 0 : 1);
});