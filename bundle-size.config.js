/**
 * Bundle Size Configuration
 * Defines size limits and monitoring rules for different bundle chunks
 */

export const bundleSizeConfig = {
  // Size limits in KB (gzipped)
  limits: {
    // Main application bundle
    main: 200,
    
    // Vendor chunks
    'vendor-react': 150,
    'vendor-ui': 100,
    'vendor-data': 80,
    'vendor-forms': 50,
    'vendor-utils': 60,
    'vendor-charts': 120,
    
    // Route-based chunks
    routeChunk: 100,
    
    // Component chunks
    componentChunk: 50,
    
    // CSS files
    css: 50,
    
    // Total bundle size
    total: 800,
  },
  
  // Warning thresholds (percentage of limit)
  warningThreshold: 0.8, // 80% of limit
  
  // Critical threshold (percentage of limit)
  criticalThreshold: 0.95, // 95% of limit
  
  // Files to exclude from size checking
  exclude: [
    '**/*.map',
    '**/stats.html',
    '**/manifest.json',
  ],
  
  // Compression settings for analysis
  compression: {
    gzip: true,
    brotli: true,
  },
};

/**
 * Bundle analysis utilities
 */
export const bundleAnalysis = {
  /**
   * Check if bundle size exceeds limits
   * @param {Object} stats - Bundle statistics from Vite build
   * @returns {Object} Analysis results
   */
  checkSizeLimits(stats) {
    const results = {
      passed: true,
      warnings: [],
      errors: [],
      summary: {},
    };
    
    if (!stats || !stats.output) {
      results.errors.push('No bundle statistics provided');
      results.passed = false;
      return results;
    }
    
    let totalSize = 0;
    const chunkSizes = {};
    
    // Analyze each chunk
    Object.values(stats.output).forEach(chunk => {
      if (chunk.type === 'chunk') {
        const sizeKB = Math.round((chunk.code?.length || 0) / 1024);
        totalSize += sizeKB;
        chunkSizes[chunk.fileName] = sizeKB;
        
        // Check individual chunk limits
        const chunkName = chunk.fileName.split('-')[0];
        const limit = bundleSizeConfig.limits[chunkName] || bundleSizeConfig.limits.componentChunk;
        
        if (sizeKB > limit) {
          results.errors.push(`Chunk ${chunk.fileName} (${sizeKB}KB) exceeds limit of ${limit}KB`);
          results.passed = false;
        } else if (sizeKB > limit * bundleSizeConfig.warningThreshold) {
          results.warnings.push(`Chunk ${chunk.fileName} (${sizeKB}KB) approaching limit of ${limit}KB`);
        }
      }
    });
    
    // Check total bundle size
    if (totalSize > bundleSizeConfig.limits.total) {
      results.errors.push(`Total bundle size (${totalSize}KB) exceeds limit of ${bundleSizeConfig.limits.total}KB`);
      results.passed = false;
    } else if (totalSize > bundleSizeConfig.limits.total * bundleSizeConfig.warningThreshold) {
      results.warnings.push(`Total bundle size (${totalSize}KB) approaching limit of ${bundleSizeConfig.limits.total}KB`);
    }
    
    results.summary = {
      totalSize,
      chunkSizes,
      chunkCount: Object.keys(chunkSizes).length
    };
    
    return results;
  },
  
  /**
   * Generate size report
   * @param {Object} stats - Bundle statistics
   * @returns {string} Formatted report
   */
  generateReport(stats) {
    const analysis = this.checkSizeLimits(stats);
    
    let report = '📦 Bundle Size Report\n';
    report += '===================\n\n';
    
    if (analysis.summary.totalSize) {
      report += `Total Size: ${analysis.summary.totalSize}KB\n`;
      report += `Chunk Count: ${analysis.summary.chunkCount}\n\n`;
      
      report += 'Chunk Breakdown:\n';
      Object.entries(analysis.summary.chunkSizes)
        .sort(([,a], [,b]) => b - a)
        .forEach(([name, size]) => {
          report += `  ${name}: ${size}KB\n`;
        });
      
      report += '\n';
    }
    
    if (analysis.errors.length > 0) {
      report += '❌ Errors:\n';
      analysis.errors.forEach(error => {
        report += `  - ${error}\n`;
      });
      report += '\n';
    }
    
    if (analysis.warnings.length > 0) {
      report += '⚠️  Warnings:\n';
      analysis.warnings.forEach(warning => {
        report += `  - ${warning}\n`;
      });
      report += '\n';
    }
    
    if (analysis.passed && analysis.warnings.length === 0) {
      report += '✅ All bundle size checks passed!\n';
    }
    
    return report;
  },
  
  /**
   * Write bundle analysis to file
   * @param {Object} stats - Bundle statistics
   * @param {string} outputPath - Path to write the report
   */
  writeReport(stats, outputPath = 'dist/bundle-report.txt') {
    const fs = require('fs');
    const path = require('path');
    
    const report = this.generateReport(stats);
    
    // Ensure directory exists
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(outputPath, report);
    console.log(`Bundle report written to ${outputPath}`);
  }
};