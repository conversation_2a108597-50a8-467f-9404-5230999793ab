# Supabase Backend Infrastructure Setup

This document provides a complete guide for setting up the Supabase backend infrastructure for the Technical Solutions Platform.

## 🚀 Quick Start

1. **Install Dependencies**
   ```bash
   npm install @supabase/supabase-js
   ```

2. **Create Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create a new project
   - Note your project URL and anon key

3. **Configure Environment**
   ```bash
   cp .env.example .env
   # Add your Supabase credentials to .env
   ```

4. **Setup Database**
   - Copy contents of `supabase/schema.sql`
   - Run in Supabase SQL Editor

5. **Test Setup**
   ```bash
   npm run dev
   # Visit http://localhost:5173/supabase-test
   ```

## 📁 Files Created

### Core Configuration
- `src/lib/supabase.ts` - Supabase client configuration and TypeScript types
- `src/lib/database.ts` - Database operations and utilities
- `src/lib/storage.ts` - File upload/download utilities
- `src/hooks/useAuth.ts` - Authentication hook
- `src/components/auth/AuthProvider.tsx` - Authentication context provider

### Database Schema
- `supabase/schema.sql` - Complete database schema with tables, indexes, and RLS policies

### Setup & Testing
- `scripts/setup-supabase.md` - Detailed setup instructions
- `src/components/SupabaseTest.tsx` - Test component to verify setup
- `.env.example` - Environment variables template

## 🗄️ Database Schema

### Tables Created
1. **users** - User profiles (extends Supabase auth.users)
2. **experts** - Expert-specific data and ratings
3. **problems** - Technical problems submitted by ministries
4. **solutions** - Expert responses to problems
5. **webinars** - Webinar content and metadata

### Key Features
- **Row Level Security (RLS)** - Comprehensive security policies
- **Full-text Search** - Arabic and English search support
- **Real-time Subscriptions** - Live updates for collaborative features
- **File Storage** - Organized buckets for different content types
- **Audit Trails** - Automatic timestamps and user tracking

## 🔐 Security Features

### Authentication
- Email/password authentication
- Social provider support (Google, GitHub, etc.)
- Multi-factor authentication ready
- Session management with auto-refresh

### Authorization
- Role-based access control (expert, ministry_user, admin)
- Row-level security policies
- Granular permissions per table
- Secure file upload policies

### Data Protection
- All data encrypted at rest and in transit
- Input validation and sanitization
- File type and size restrictions
- Audit logging for all operations

## 📦 Storage Buckets

### Configured Buckets
1. **attachments** - Problem/solution attachments (10MB limit)
2. **avatars** - User profile pictures (2MB limit)
3. **presentations** - Webinar slides and materials (50MB limit)
4. **webinar-recordings** - Video recordings (1GB limit)

### Storage Policies
- Public read access for appropriate content
- Authenticated upload requirements
- User-specific folder organization
- Admin-only access for sensitive content

## 🔧 API Operations

### User Management
```typescript
import { userOperations } from '@/lib/database'

// Get user profile
const { data, error } = await userOperations.getProfile(userId)

// Update profile
await userOperations.updateProfile(userId, { name: 'New Name' })
```

### Problem Management
```typescript
import { problemOperations } from '@/lib/database'

// Create problem
await problemOperations.createProblem({
  title: 'Network Issue',
  description: 'Server connectivity problems',
  category: 'Infrastructure',
  sector: 'Health',
  submitted_by: userId
})

// Search problems
await problemOperations.searchProblems('network', 'ar')
```

### File Upload
```typescript
import { uploadFile, STORAGE_BUCKETS } from '@/lib/storage'

// Upload attachment
const result = await uploadFile(
  file, 
  STORAGE_BUCKETS.ATTACHMENTS, 
  `problems/${problemId}`
)
```

## 🔄 Real-time Features

### Live Updates
```typescript
import { subscriptions } from '@/lib/database'

// Subscribe to problem updates
const subscription = subscriptions.subscribeToProblems((payload) => {
  console.log('Problem updated:', payload)
})

// Cleanup
subscription.unsubscribe()
```

## 🧪 Testing

### Test Component
Visit `/supabase-test` to verify:
- Database connection
- Authentication flow
- File upload functionality
- Real-time subscriptions

### Manual Testing
```bash
# Test database connection
npm run dev
# Navigate to /supabase-test
# Follow the test instructions
```

## 🚀 Deployment Checklist

### Development Setup
- [ ] Supabase project created
- [ ] Environment variables configured
- [ ] Database schema applied
- [ ] Storage buckets created
- [ ] RLS policies enabled
- [ ] Test component passes all checks

### Production Setup
- [ ] Custom domain configured
- [ ] SSL certificates installed
- [ ] Backup strategy implemented
- [ ] Monitoring enabled
- [ ] Rate limiting configured
- [ ] Security audit completed

## 🔍 Troubleshooting

### Common Issues

1. **Connection Errors**
   - Verify VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY
   - Check network connectivity
   - Ensure project is not paused

2. **Authentication Issues**
   - Verify email confirmation settings
   - Check redirect URLs
   - Ensure RLS policies allow access

3. **File Upload Issues**
   - Check storage bucket policies
   - Verify file size and type limits
   - Ensure user is authenticated

4. **RLS Policy Issues**
   - Test policies with different user roles
   - Check policy conditions
   - Verify user context in policies

### Debug Queries
```sql
-- Check user roles
SELECT id, email, role FROM public.users;

-- Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies WHERE schemaname = 'public';

-- Check storage usage
SELECT bucket_id, count(*), sum(metadata->>'size')::bigint as total_size 
FROM storage.objects GROUP BY bucket_id;
```

## 📚 Next Steps

After completing the Supabase setup:

1. **Test Authentication Flow**
   - Create test accounts
   - Verify role assignments
   - Test password reset

2. **Validate Data Operations**
   - Create sample problems
   - Test expert responses
   - Verify search functionality

3. **Test File Operations**
   - Upload various file types
   - Test file size limits
   - Verify access permissions

4. **Configure Monitoring**
   - Set up error tracking
   - Configure performance monitoring
   - Enable backup procedures

## 🆘 Support

If you encounter issues:

1. Check the [Supabase Documentation](https://supabase.com/docs)
2. Review the setup guide in `scripts/setup-supabase.md`
3. Test with the component at `/supabase-test`
4. Check browser console for detailed error messages

---

**✅ Task Complete**: Supabase backend infrastructure is now set up and ready for development!